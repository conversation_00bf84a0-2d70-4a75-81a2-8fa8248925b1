'use client';

import { ReactNode } from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import { Role } from '@/lib/auth/role-check';

interface RoleGateProps {
  /**
   * The role(s) required to access the content
   */
  allowedRoles: Role | Role[];

  /**
   * The content to render if the user has the required role
   */
  children: ReactNode;

  /**
   * Optional content to render if the user doesn't have the required role
   */
  fallback?: ReactNode;
}

/**
 * RoleGate Component
 *
 * This component conditionally renders content based on the user's role.
 * It can be used to show or hide UI elements based on the user's permissions.
 *
 * @example
 * ```tsx
 * <RoleGate allowedRoles="ADMIN">
 *   <AdminPanel />
 * </RoleGate>
 * ```
 *
 * @example
 * ```tsx
 * <RoleGate
 *   allowedRoles={['ADMIN', 'MANAGER']}
 *   fallback={<AccessDeniedMessage />}
 * >
 *   <ManagerDashboard />
 * </RoleGate>
 * ```
 */
export function RoleGate({ allowedRoles, children, fallback = null }: RoleGateProps) {
  const { userRole, isAuthenticated, isLoading, user } = useAuth();

  console.log('RoleGate: Checking access with:', {
    allowedRoles,
    userRole,
    isAuthenticated,
    isLoading,
    user
  });

  // Show a loading indicator while authentication state is being determined
  if (isLoading) {
    console.log('RoleGate: Still loading authentication state');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-gray-600">Loading authentication...</span>
      </div>
    );
  }

  // If not authenticated, redirect to login or show a message
  if (!isAuthenticated) {
    console.log('RoleGate: User is not authenticated');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Authentication Required</h2>
          <p className="text-gray-500">Please log in to access this page</p>
          <div className="mt-4">
            <a href="/auth/login" className="text-blue-600 hover:text-blue-500">
              Go to Login
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Normalize roles for case-insensitive comparison
  const normalizedUserRole = userRole?.toUpperCase();
  const normalizedAllowedRoles = Array.isArray(allowedRoles)
    ? allowedRoles.map(role => role.toUpperCase())
    : allowedRoles.toUpperCase();

  // Check if the user has the required role (case-insensitive)
  const hasRequiredRole = Array.isArray(normalizedAllowedRoles)
    ? normalizedAllowedRoles.includes(normalizedUserRole as Role)
    : normalizedUserRole === normalizedAllowedRoles;

  console.log(`RoleGate: Access ${hasRequiredRole ? 'granted' : 'denied'} for user with role "${userRole}" (normalized: "${normalizedUserRole}") to content requiring roles [${Array.isArray(allowedRoles) ? allowedRoles.join(', ') : allowedRoles}]`);

  // Render the children if the user has the required role, otherwise render the fallback
  return hasRequiredRole ? <>{children}</> : <>{fallback}</>;
}

/**
 * AdminGate Component
 *
 * This component conditionally renders content for admin users only.
 *
 * @example
 * ```tsx
 * <AdminGate>
 *   <AdminPanel />
 * </AdminGate>
 * ```
 */
export function AdminGate({ children, fallback = null }: Omit<RoleGateProps, 'allowedRoles'>) {
  return <RoleGate allowedRoles="ADMIN" fallback={fallback}>{children}</RoleGate>;
}

/**
 * ManagerGate Component
 *
 * This component conditionally renders content for manager users and above.
 *
 * @example
 * ```tsx
 * <ManagerGate>
 *   <ManagerDashboard />
 * </ManagerGate>
 * ```
 */
export function ManagerGate({ children, fallback = null }: Omit<RoleGateProps, 'allowedRoles'>) {
  return <RoleGate allowedRoles={['ADMIN', 'MANAGER']} fallback={fallback}>{children}</RoleGate>;
}

/**
 * ExecutiveGate Component
 *
 * This component conditionally renders content for executive users and above.
 *
 * @example
 * ```tsx
 * <ExecutiveGate>
 *   <ExecutiveDashboard />
 * </ExecutiveGate>
 * ```
 */
export function ExecutiveGate({ children, fallback = null }: Omit<RoleGateProps, 'allowedRoles'>) {
  return <RoleGate allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']} fallback={fallback}>{children}</RoleGate>;
}
