'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Save, 
  X, 
  AlertCircle, 
  Calendar, 
  DollarSign, 
  User, 
  Building,
  FileText
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { showErrorToast, showSuccessToast } from '@/lib/toast';

// Edit form validation schema
const editContractSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  contactPersonId: z.string().optional(),
  executiveId: z.string().optional(),
  natureOfService: z.string().optional(),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  warningDate: z.string().optional(),
  amount: z.number().min(0, 'Amount must be positive'),
  numberOfServices: z.number().min(1, 'Number of services must be at least 1').optional(),
  numberOfMachines: z.number().min(0, 'Number of machines cannot be negative').optional(),
  totalTonnage: z.number().min(0, 'Total tonnage cannot be negative').optional(),
  contractNumber: z.string().optional(),
  remarks: z.string().optional(),
});

type EditContractFormData = z.infer<typeof editContractSchema>;

interface AMCContract {
  id: string;
  customerId: string;
  customer: {
    id: string;
    name: string;
    email?: string;
  };
  contactPersonId?: string;
  executiveId?: string;
  natureOfService?: string;
  startDate: string;
  endDate: string;
  warningDate?: string;
  amount: number;
  numberOfServices?: number;
  numberOfMachines?: number;
  totalTonnage?: number;
  status: string;
  contractNumber?: string;
  remarks?: string;
  machines?: any[];
  divisions?: any[];
}

interface AMCEditFormProps {
  contract: AMCContract;
  onSuccess: (updatedContract: any) => void;
  onCancel: () => void;
}

/**
 * AMC Edit Form Component
 * 
 * This component provides a form for editing existing AMC contracts.
 * It pre-populates fields with existing data and validates changes.
 */
export function AMCEditForm({ contract, onSuccess, onCancel }: AMCEditFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customers, setCustomers] = useState<any[]>([]);
  const [executives, setExecutives] = useState<any[]>([]);
  const [contactPersons, setContactPersons] = useState<any[]>([]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty }
  } = useForm<EditContractFormData>({
    resolver: zodResolver(editContractSchema),
    defaultValues: {
      customerId: contract.customerId,
      contactPersonId: contract.contactPersonId || 'none',
      executiveId: contract.executiveId || 'none',
      natureOfService: contract.natureOfService || '',
      startDate: format(new Date(contract.startDate), 'yyyy-MM-dd'),
      endDate: format(new Date(contract.endDate), 'yyyy-MM-dd'),
      warningDate: contract.warningDate ? format(new Date(contract.warningDate), 'yyyy-MM-dd') : '',
      amount: contract.amount,
      numberOfServices: contract.numberOfServices || 4,
      numberOfMachines: contract.numberOfMachines || 0,
      totalTonnage: contract.totalTonnage || 0,
      contractNumber: contract.contractNumber || '',
      remarks: contract.remarks || '',
    }
  });

  const watchedCustomerId = watch('customerId');

  // Fetch reference data
  useEffect(() => {
    const fetchReferenceData = async () => {
      try {
        // Fetch customers
        const customersResponse = await fetch('/api/customers', {
          credentials: 'include',
        });
        if (customersResponse.ok) {
          const customersData = await customersResponse.json();
          setCustomers(customersData.customers || []);
        }

        // Fetch executives
        const executivesResponse = await fetch('/api/users?role=EXECUTIVE', {
          credentials: 'include',
        });
        if (executivesResponse.ok) {
          const executivesData = await executivesResponse.json();
          setExecutives(executivesData.users || []);
        }
      } catch (error) {
        console.error('Error fetching reference data:', error);
      }
    };

    fetchReferenceData();
  }, []);

  // Fetch contact persons when customer changes
  useEffect(() => {
    const fetchContactPersons = async () => {
      if (!watchedCustomerId) {
        setContactPersons([]);
        return;
      }

      try {
        const response = await fetch(`/api/customers/${watchedCustomerId}/contacts`, {
          credentials: 'include',
        });
        if (response.ok) {
          const data = await response.json();
          setContactPersons(data.contacts || []);
        }
      } catch (error) {
        console.error('Error fetching contact persons:', error);
      }
    };

    fetchContactPersons();
  }, [watchedCustomerId]);

  // Handle form submission
  const onSubmit = async (data: EditContractFormData) => {
    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/amc/contracts/${contract.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...data,
          warningDate: data.warningDate || null,
          contactPersonId: data.contactPersonId && data.contactPersonId !== 'none' ? data.contactPersonId : null,
          executiveId: data.executiveId && data.executiveId !== 'none' ? data.executiveId : null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update contract');
      }

      const updatedContract = await response.json();
      onSuccess(updatedContract);
    } catch (error) {
      console.error('Error updating contract:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update contract';
      showErrorToast('Update Failed', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Contract Information */}
      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-4 w-4" />
            <span>Contract Information</span>
          </CardTitle>
          <CardDescription className="text-gray-100">
            Update basic contract details and customer information
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Customer Selection */}
            <div className="space-y-2">
              <Label htmlFor="customerId" className="text-black">Customer *</Label>
              <Select value={watchedCustomerId} onValueChange={(value) => setValue('customerId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.customerId && (
                <p className="text-sm text-destructive">{errors.customerId.message}</p>
              )}
            </div>

            {/* Contact Person */}
            <div className="space-y-2">
              <Label htmlFor="contactPersonId" className="text-black">Contact Person</Label>
              <Select value={watch('contactPersonId')} onValueChange={(value) => setValue('contactPersonId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select contact person" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No contact person</SelectItem>
                  {contactPersons.map((contact) => (
                    <SelectItem key={contact.id} value={contact.id}>
                      {contact.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Executive */}
            <div className="space-y-2">
              <Label htmlFor="executiveId" className="text-black">Executive</Label>
              <Select value={watch('executiveId')} onValueChange={(value) => setValue('executiveId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select executive" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No executive assigned</SelectItem>
                  {executives.map((executive) => (
                    <SelectItem key={executive.id} value={executive.id}>
                      {executive.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Contract Number */}
            <div className="space-y-2">
              <Label htmlFor="contractNumber" className="text-black">Contract Number</Label>
              <Input
                id="contractNumber"
                {...register('contractNumber')}
                placeholder="Enter contract number"
                className="text-black"
              />
            </div>
          </div>

          {/* Nature of Service */}
          <div className="space-y-2">
            <Label htmlFor="natureOfService" className="text-black">Nature of Service</Label>
            <Textarea
              id="natureOfService"
              {...register('natureOfService')}
              placeholder="Describe the nature of service"
              className="text-black"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Contract Details */}
      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Contract Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Start Date */}
            <div className="space-y-2">
              <Label htmlFor="startDate" className="text-black">Start Date *</Label>
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
                className="text-black"
              />
              {errors.startDate && (
                <p className="text-sm text-destructive">{errors.startDate.message}</p>
              )}
            </div>

            {/* End Date */}
            <div className="space-y-2">
              <Label htmlFor="endDate" className="text-black">End Date *</Label>
              <Input
                id="endDate"
                type="date"
                {...register('endDate')}
                className="text-black"
              />
              {errors.endDate && (
                <p className="text-sm text-destructive">{errors.endDate.message}</p>
              )}
            </div>

            {/* Warning Date */}
            <div className="space-y-2">
              <Label htmlFor="warningDate" className="text-black">Warning Date</Label>
              <Input
                id="warningDate"
                type="date"
                {...register('warningDate')}
                className="text-black"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Amount */}
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-black">Contract Amount *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                {...register('amount', { valueAsNumber: true })}
                placeholder="Enter contract amount"
                className="text-black"
              />
              {errors.amount && (
                <p className="text-sm text-destructive">{errors.amount.message}</p>
              )}
            </div>

            {/* Number of Services */}
            <div className="space-y-2">
              <Label htmlFor="numberOfServices" className="text-black">Number of Services</Label>
              <Input
                id="numberOfServices"
                type="number"
                {...register('numberOfServices', { valueAsNumber: true })}
                placeholder="Enter number of services"
                className="text-black"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Number of Machines */}
            <div className="space-y-2">
              <Label htmlFor="numberOfMachines" className="text-black">Number of Machines</Label>
              <Input
                id="numberOfMachines"
                type="number"
                {...register('numberOfMachines', { valueAsNumber: true })}
                placeholder="Enter number of machines"
                className="text-black"
              />
            </div>

            {/* Total Tonnage */}
            <div className="space-y-2">
              <Label htmlFor="totalTonnage" className="text-black">Total Tonnage</Label>
              <Input
                id="totalTonnage"
                type="number"
                step="0.01"
                {...register('totalTonnage', { valueAsNumber: true })}
                placeholder="Enter total tonnage"
                className="text-black"
              />
            </div>
          </div>

          {/* Remarks */}
          <div className="space-y-2">
            <Label htmlFor="remarks" className="text-black">Remarks</Label>
            <Textarea
              id="remarks"
              {...register('remarks')}
              placeholder="Enter any additional remarks"
              className="text-black"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Status Information */}
      <Card>
        <CardHeader className="pb-3 bg-secondary text-black">
          <CardTitle>Current Status</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div>
              <Label className="text-black">Contract Status:</Label>
              <Badge className={`ml-2 ${
                contract.status === 'ACTIVE' ? 'bg-green-100 text-green-800' :
                contract.status === 'EXPIRED' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {contract.status}
              </Badge>
            </div>
            <div>
              <Label className="text-black">Machines:</Label>
              <span className="ml-2 text-black">{contract.machines?.length || 0}</span>
            </div>
            <div>
              <Label className="text-black">Divisions:</Label>
              <span className="ml-2 text-black">{contract.divisions?.length || 0}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {isDirty && (
                <Alert className="flex-1">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-black">
                    You have unsaved changes. Make sure to save before leaving this page.
                  </AlertDescription>
                </Alert>
              )}
            </div>
            <div className="flex space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || !isDirty}
              >
                {isSubmitting ? (
                  <>
                    <Save className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Contract
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
