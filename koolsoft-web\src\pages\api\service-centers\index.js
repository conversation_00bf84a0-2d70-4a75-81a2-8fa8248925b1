import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

/**
 * Service Centers API Handler
 * 
 * GET /api/service-centers - Get all service centers with optional filtering
 * POST /api/service-centers - Create a new service center (Admin only)
 */
export default async function handler(req, res) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    switch (req.method) {
      case 'GET':
        return await handleGet(req, res);
      case 'POST':
        return await handlePost(req, res, session);
      case 'PUT':
        return await handlePut(req, res, session);
      case 'DELETE':
        return await handleDelete(req, res, session);
      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Service Centers API Error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  } finally {
    await prisma.$disconnect();
  }
}

async function handleGet(req, res) {
  try {
    const { vendor, city, active = 'true' } = req.query;

    // Build where clause
    const where = {};
    
    if (vendor && vendor !== 'all') {
      where.vendor = vendor;
    }
    
    if (city && city !== 'all') {
      where.city = city;
    }
    
    if (active !== 'all') {
      where.active = active === 'true';
    }

    // Fetch service centers
    const serviceCenters = await prisma.serviceCenter.findMany({
      where,
      orderBy: [
        { vendor: 'asc' },
        { city: 'asc' },
        { name: 'asc' }
      ]
    });

    // Get summary statistics
    const stats = {
      total: serviceCenters.length,
      byVendor: {},
      byCity: {},
      active: serviceCenters.filter(sc => sc.active).length,
      inactive: serviceCenters.filter(sc => !sc.active).length
    };

    // Calculate vendor statistics
    serviceCenters.forEach(sc => {
      const vendor = sc.vendor || 'Unknown';
      stats.byVendor[vendor] = (stats.byVendor[vendor] || 0) + 1;
      
      const city = sc.city || 'Unknown';
      stats.byCity[city] = (stats.byCity[city] || 0) + 1;
    });

    return res.status(200).json({
      serviceCenters,
      stats,
      filters: {
        vendor: vendor || 'all',
        city: city || 'all',
        active: active || 'true'
      }
    });

  } catch (error) {
    console.error('Error fetching service centers:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch service centers',
      details: error.message 
    });
  }
}

async function handlePost(req, res, session) {
  try {
    // Check if user is admin
    if (session.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const {
      name,
      vendor,
      address,
      city,
      state,
      pincode,
      phone,
      email,
      contactPerson,
      active = true
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Service center name is required' });
    }

    // Check for duplicate name
    const existingServiceCenter = await prisma.serviceCenter.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive'
        }
      }
    });

    if (existingServiceCenter) {
      return res.status(400).json({ 
        error: 'A service center with this name already exists' 
      });
    }

    // Create service center
    const serviceCenter = await prisma.serviceCenter.create({
      data: {
        name,
        vendor,
        address,
        city,
        state,
        pincode,
        phone,
        email,
        contactPerson,
        active
      }
    });

    return res.status(201).json({
      message: 'Service center created successfully',
      serviceCenter
    });

  } catch (error) {
    console.error('Error creating service center:', error);
    return res.status(500).json({ 
      error: 'Failed to create service center',
      details: error.message 
    });
  }
}

async function handlePut(req, res, session) {
  try {
    // Check if user is admin or manager
    if (!['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return res.status(403).json({ error: 'Admin or Manager access required' });
    }

    const { id } = req.query;
    if (!id) {
      return res.status(400).json({ error: 'Service center ID is required' });
    }

    const {
      name,
      vendor,
      address,
      city,
      state,
      pincode,
      phone,
      email,
      contactPerson,
      active
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Service center name is required' });
    }

    // Check if service center exists
    const existingServiceCenter = await prisma.serviceCenter.findUnique({
      where: { id }
    });

    if (!existingServiceCenter) {
      return res.status(404).json({ error: 'Service center not found' });
    }

    // Check for duplicate name (excluding current record)
    const duplicateServiceCenter = await prisma.serviceCenter.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive'
        },
        id: {
          not: id
        }
      }
    });

    if (duplicateServiceCenter) {
      return res.status(400).json({
        error: 'A service center with this name already exists'
      });
    }

    // Update service center
    const updatedServiceCenter = await prisma.serviceCenter.update({
      where: { id },
      data: {
        name,
        vendor,
        address,
        city,
        state,
        pincode,
        phone,
        email,
        contactPerson,
        active: active !== undefined ? active : existingServiceCenter.active
      }
    });

    return res.status(200).json({
      message: 'Service center updated successfully',
      serviceCenter: updatedServiceCenter
    });

  } catch (error) {
    console.error('Error updating service center:', error);
    return res.status(500).json({
      error: 'Failed to update service center',
      details: error.message
    });
  }
}

async function handleDelete(req, res, session) {
  try {
    // Check if user is admin
    if (session.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { id } = req.query;
    if (!id) {
      return res.status(400).json({ error: 'Service center ID is required' });
    }

    // Check if service center exists
    const existingServiceCenter = await prisma.serviceCenter.findUnique({
      where: { id }
    });

    if (!existingServiceCenter) {
      return res.status(404).json({ error: 'Service center not found' });
    }

    // Soft delete by setting active to false
    const deletedServiceCenter = await prisma.serviceCenter.update({
      where: { id },
      data: { active: false }
    });

    return res.status(200).json({
      message: 'Service center deleted successfully',
      serviceCenter: deletedServiceCenter
    });

  } catch (error) {
    console.error('Error deleting service center:', error);
    return res.status(500).json({
      error: 'Failed to delete service center',
      details: error.message
    });
  }
}
