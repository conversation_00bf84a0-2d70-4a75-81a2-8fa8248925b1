import { NextRequest, NextResponse } from 'next/server';
import { getCustomerRepository } from '@/lib/repositories';

/**
 * GET /api/customers/[id]/simple
 * Get a specific customer by ID without middleware
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const customerRepository = getCustomerRepository();

    // Get customer with all related data
    const customer = await customerRepository.findWithRelations(id);

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error fetching customer:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer' },
      { status: 500 }
    );
  }
}
