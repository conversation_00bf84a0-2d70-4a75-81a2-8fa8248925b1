/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/app/**/*.{js,ts,jsx,tsx}',
    './src/components/**/*.{js,ts,jsx,tsx}',
    './src/lib/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#0F52BA',
          light: '#3B7DED',
          dark: '#0A3882',
          foreground: '#FFFFFF',
        },
        secondary: {
          DEFAULT: '#f3f4f6',
          light: '#FFFFFF',
          dark: '#d1d5db',
          foreground: '#000000',
        },
        destructive: {
          DEFAULT: '#ef4444',
          light: '#f87171',
          dark: '#b91c1c',
          foreground: '#FFFFFF',
        },
        success: {
          DEFAULT: '#10B981',
          light: '#34D399',
          dark: '#059669',
          foreground: '#FFFFFF',
        },
        warning: {
          DEFAULT: '#F59E0B',
          light: '#FBBF24',
          dark: '#D97706',
          foreground: '#FFFFFF',
        },
        info: {
          DEFAULT: '#3B82F6',
          light: '#60A5FA',
          dark: '#2563EB',
          foreground: '#FFFFFF',
        },
        background: '#f8f9fa',
        foreground: '#000000',
        border: '#d1d5db',
        input: '#d1d5db',
        ring: '#0F52BA',
        muted: {
          DEFAULT: '#f3f4f6',
          foreground: '#000000',
        },
        accent: {
          DEFAULT: '#f3f4f6',
          foreground: '#000000',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        mono: ['JetBrains Mono', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
      },
    },
  },
  plugins: [require('@tailwindcss/forms')],
};
