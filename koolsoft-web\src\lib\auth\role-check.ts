import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

/**
 * Role types for the application
 */
export type Role = 'ADMIN' | 'MANAGER' | 'EXECUTIVE' | 'USER';

/**
 * Check if the user has the required role(s)
 * @param requiredRoles Role or array of roles required for access
 * @param userRole User's current role
 * @returns Boolean indicating if the user has the required role
 */
export function hasRequiredRole(requiredRoles: Role | Role[], userRole?: string): boolean {
  if (!userRole) return false;

  // Convert user role to uppercase for case-insensitive comparison
  const normalizedUserRole = userRole.toUpperCase();

  if (Array.isArray(requiredRoles)) {
    // Convert all required roles to uppercase for case-insensitive comparison
    return requiredRoles.map(role => role.toUpperCase()).includes(normalizedUserRole as Role);
  }

  // Compare with uppercase for case-insensitive comparison
  return normalizedUserRole === requiredRoles.toUpperCase();
}

/**
 * Middleware function to check if the user has the required role(s)
 * @param requiredRoles Role or array of roles required for access
 * @returns Middleware function that checks the user's role
 */
export function withRoleCheck(requiredRoles: Role | Role[]) {
  return async (request: NextRequest, handler: Function) => {
    // Get the current session
    const session = await getServerSession(authOptions);

    // Check if the user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user's role
    const userRole = session.user.role;

    // Check if the user has the required role
    if (!hasRequiredRole(requiredRoles, userRole)) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // User has the required role, proceed with the handler
    return handler();
  };
}

/**
 * Higher-order function to protect API routes with role-based access control
 * @param handler API route handler function
 * @param requiredRoles Role or array of roles required for access
 * @returns Protected API route handler function
 */
export function withRoleProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>,
  requiredRoles: Role | Role[]
) {
  return async (request: NextRequest, context: any) => {
    return withRoleCheck(requiredRoles)(request, () => handler(request, context));
  };
}

/**
 * Higher-order function to protect API routes with admin-only access
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withAdminProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return withRoleProtection(handler, 'ADMIN');
}

/**
 * Higher-order function to protect API routes with manager-only access
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withManagerProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return withRoleProtection(handler, ['ADMIN', 'MANAGER']);
}

/**
 * Higher-order function to protect API routes with executive-only access
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withExecutiveProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return withRoleProtection(handler, ['ADMIN', 'MANAGER', 'EXECUTIVE']);
}

/**
 * Higher-order function to protect API routes with authentication only
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withAuthProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return withRoleProtection(handler, ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']);
}
