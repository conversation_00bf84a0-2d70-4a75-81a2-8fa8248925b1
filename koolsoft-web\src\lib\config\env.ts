import { z } from 'zod';

/**
 * Environment variable validation schema
 * Validates all required environment variables and provides type-safe access
 */
const envSchema = z.object({
  // Application
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  NEXT_PUBLIC_APP_NAME: z.string().default('KoolSoft'),
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NEXT_PUBLIC_API_URL: z.string().url(),

  // Database
  DATABASE_URL: z.string().url(),
  DATABASE_DIRECT_URL: z.string().url().optional(),

  // NextAuth.js
  NEXTAUTH_URL: z.string().url(),
  NEXTAUTH_SECRET: z.string().min(32),
  JWT_SECRET: z.string().min(32),

  // Authentication
  PASSWORD_SALT_ROUNDS: z.coerce.number().int().positive().default(10),

  // Email
  EMAIL_SERVER_HOST: z.string(),
  EMAIL_SERVER_PORT: z.coerce.number().int().positive(),
  EMAIL_SERVER_USER: z.string().email(),
  EMAIL_SERVER_PASSWORD: z.string(),
  EMAIL_FROM: z.string().email(),

  // File Storage
  UPLOAD_DIR: z.string().default('./uploads'),
  MAX_UPLOAD_SIZE: z.coerce.number().int().positive().default(5 * 1024 * 1024), // 5MB

  // Logging
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),

  // Rate Limiting
  RATE_LIMIT_MAX: z.coerce.number().int().positive().default(100),
  RATE_LIMIT_WINDOW_MS: z.coerce.number().int().positive().default(60000), // 1 minute

  // Feature Flags
  FEATURE_MODULE_CONVERSION: z.coerce.boolean().default(true),
  FEATURE_REPORTING: z.coerce.boolean().default(true),
  FEATURE_EMAIL_NOTIFICATIONS: z.coerce.boolean().default(true),
});

/**
 * Parse and validate environment variables
 * Will throw an error if any required variables are missing or invalid
 */
function validateEnv() {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors
        .filter(err => err.code === 'invalid_type' && err.received === 'undefined')
        .map(err => err.path.join('.'));

      const invalidVars = error.errors
        .filter(err => !(err.code === 'invalid_type' && err.received === 'undefined'))
        .map(err => `${err.path.join('.')}: ${err.message}`);

      if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:');
        missingVars.forEach(variable => console.error(`   - ${variable}`));
      }

      if (invalidVars.length > 0) {
        console.error('❌ Invalid environment variables:');
        invalidVars.forEach(variable => console.error(`   - ${variable}`));
      }

      console.error('\nPlease check your .env file and make sure all required variables are set correctly.');
      console.error('See .env.example for reference.');
    } else {
      console.error('❌ Unknown error validating environment variables:', error);
    }
    process.exit(1);
  }
}

/**
 * Validated environment variables
 * Type-safe access to all environment variables
 */
export const env = validateEnv();

/**
 * Environment helper functions
 */
export const isDev = env.NODE_ENV === 'development';
export const isTest = env.NODE_ENV === 'test';
export const isProd = env.NODE_ENV === 'production';

/**
 * Feature flag helper functions
 */
export const features = {
  moduleConversion: env.FEATURE_MODULE_CONVERSION,
  reporting: env.FEATURE_REPORTING,
  emailNotifications: env.FEATURE_EMAIL_NOTIFICATIONS,
};
