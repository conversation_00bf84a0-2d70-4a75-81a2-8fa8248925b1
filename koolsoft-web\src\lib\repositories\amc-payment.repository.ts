import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * AMC Payment Repository
 *
 * This repository handles database operations for the AMC Payment entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class AMCPaymentRepository extends PrismaRepository<
  any,
  string,
  any,
  any
> {
  constructor(prismaClient?: PrismaClient) {
    super('amc_payments');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): AMCPaymentRepository {
    return new AMCPaymentRepository(tx);
  }

  /**
   * Find payments by AMC contract ID
   * @param amcContractId AMC contract ID
   * @param options Query options
   * @returns Promise resolving to an array of payments
   */
  async findByContractId(
    amcContractId: string,
    options?: {
      skip?: number;
      take?: number;
      orderBy?: any;
    }
  ): Promise<any[]> {
    return this.model.findMany({
      where: { amcContractId },
      skip: options?.skip,
      take: options?.take,
      orderBy: options?.orderBy || { paymentDate: 'desc' },
    });
  }

  /**
   * Calculate total paid amount for a contract
   * @param amcContractId AMC contract ID
   * @returns Promise resolving to the total paid amount
   */
  async getTotalPaidAmount(amcContractId: string): Promise<number> {
    const result = await this.model.aggregate({
      where: { amcContractId },
      _sum: { amount: true },
    });
    return result._sum.amount || 0;
  }

  /**
   * Find payments with pagination and filtering
   * @param filters Filter criteria
   * @param options Query options
   * @returns Promise resolving to payments and total count
   */
  async findWithFilters(
    filters: {
      amcContractId?: string;
      paymentMode?: string;
      dateFrom?: Date;
      dateTo?: Date;
      amountMin?: number;
      amountMax?: number;
    },
    options: {
      skip?: number;
      take?: number;
      orderBy?: any;
    } = {}
  ): Promise<{
    payments: any[];
    total: number;
  }> {
    const where: any = {};

    if (filters.amcContractId) {
      where.amcContractId = filters.amcContractId;
    }

    if (filters.paymentMode) {
      where.paymentMode = filters.paymentMode;
    }

    if (filters.dateFrom || filters.dateTo) {
      where.paymentDate = {};
      if (filters.dateFrom) {
        where.paymentDate.gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        where.paymentDate.lte = filters.dateTo;
      }
    }

    if (filters.amountMin !== undefined || filters.amountMax !== undefined) {
      where.amount = {};
      if (filters.amountMin !== undefined) {
        where.amount.gte = filters.amountMin;
      }
      if (filters.amountMax !== undefined) {
        where.amount.lte = filters.amountMax;
      }
    }

    const [payments, total] = await Promise.all([
      this.model.findMany({
        where,
        include: {
          amcContract: {
            include: {
              customer: true,
            },
          },
        },
        skip: options.skip,
        take: options.take,
        orderBy: options.orderBy || { paymentDate: 'desc' },
      }),
      this.model.count({ where }),
    ]);

    return { payments, total };
  }

  /**
   * Generate next receipt number
   * @returns Promise resolving to the next receipt number
   */
  async generateReceiptNumber(): Promise<string> {
    const lastPayment = await this.model.findFirst({
      where: {
        receiptNo: { not: null },
      },
      orderBy: { receiptNo: 'desc' },
    });

    if (!lastPayment?.receiptNo) {
      return 'RCP-000001';
    }

    // Extract number from receipt format (e.g., RCP-000001)
    const match = lastPayment.receiptNo.match(/RCP-(\d+)/);
    if (match) {
      const nextNumber = parseInt(match[1]) + 1;
      return `RCP-${nextNumber.toString().padStart(6, '0')}`;
    }

    return 'RCP-000001';
  }

  /**
   * Validate receipt number uniqueness
   * @param receiptNo Receipt number to validate
   * @param excludeId Payment ID to exclude from validation (for updates)
   * @returns Promise resolving to true if unique, false otherwise
   */
  async isReceiptNumberUnique(receiptNo: string, excludeId?: string): Promise<boolean> {
    const where: Prisma.amc_paymentsWhereInput = { receiptNo };
    if (excludeId) {
      where.id = { not: excludeId };
    }

    const count = await this.model.count({ where });
    return count === 0;
  }

  /**
   * Get payment statistics for a contract
   * @param amcContractId AMC contract ID
   * @returns Promise resolving to payment statistics
   */
  async getPaymentStatistics(amcContractId: string): Promise<{
    totalPaid: number;
    paymentCount: number;
    lastPaymentDate: Date | null;
    averagePaymentAmount: number;
    paymentModes: { mode: string; count: number; total: number }[];
  }> {
    const payments = await this.findByContractId(amcContractId);

    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
    const paymentCount = payments.length;
    const lastPaymentDate = payments.length > 0 ? payments[0].paymentDate : null;
    const averagePaymentAmount = paymentCount > 0 ? totalPaid / paymentCount : 0;

    // Group by payment mode
    const paymentModeMap = new Map<string, { count: number; total: number }>();
    payments.forEach((payment) => {
      const mode = payment.paymentMode || 'UNKNOWN';
      const existing = paymentModeMap.get(mode) || { count: 0, total: 0 };
      paymentModeMap.set(mode, {
        count: existing.count + 1,
        total: existing.total + payment.amount,
      });
    });

    const paymentModes = Array.from(paymentModeMap.entries()).map(([mode, stats]) => ({
      mode,
      count: stats.count,
      total: stats.total,
    }));

    return {
      totalPaid,
      paymentCount,
      lastPaymentDate,
      averagePaymentAmount,
      paymentModes,
    };
  }

  /**
   * Create payment with automatic receipt number generation
   * @param data Payment data
   * @returns Promise resolving to the created payment
   */
  async createWithReceiptNumber(
    data: any
  ): Promise<any> {
    if (!data.receiptNo) {
      data.receiptNo = await this.generateReceiptNumber();
    }

    return this.create(data);
  }

  /**
   * Update contract paid amount after payment changes
   * @param amcContractId AMC contract ID
   * @returns Promise resolving to the updated paid amount
   */
  async updateContractPaidAmount(amcContractId: string): Promise<number> {
    const totalPaid = await this.getTotalPaidAmount(amcContractId);

    // Update the contract's paid amount
    await this.prisma.amc_contracts.update({
      where: { id: amcContractId },
      data: { paidAmount: totalPaid },
    });

    return totalPaid;
  }
}

/**
 * Factory function to get AMC Payment Repository instance
 */
export function getAMCPaymentRepository(): AMCPaymentRepository {
  return new AMCPaymentRepository();
}
