'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Home, Upload, Calendar, FileText } from 'lucide-react';

// Define the form schema
const visitCardFormSchema = z.object({
  customerId: z.string().optional().refine((val) => {
    if (!val) return false; // Required field
    return z.string().uuid().safeParse(val).success; // Must be valid UUID
  }, { message: 'Please select a customer' }),
  userId: z.string().optional().refine((val) => {
    if (!val) return false; // Required field
    return z.string().uuid().safeParse(val).success; // Must be valid UUID
  }, { message: 'Please select a user' }),
  visitDate: z.string().min(1, 'Please select a visit date'),
  purpose: z.string().min(2, 'Purpose must be at least 2 characters').max(200, 'Purpose must be at most 200 characters'),
  notes: z.string().optional(),
  followUpDate: z.string().optional(),
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']).default('PENDING'),
  contactPerson: z.string().optional(),
  contactPhone: z.string().optional(),
  contactEmail: z.string().email('Please enter a valid email').optional().or(z.literal('')),
});

type VisitCardFormValues = z.infer<typeof visitCardFormSchema>;

/**
 * Edit Visit Card Page
 *
 * This page provides a form for editing an existing visit card.
 */
export default function EditVisitCardPage() {
  const router = useRouter();
  const params = useParams();
  const visitCardId = params.id as string;

  const [visitCard, setVisitCard] = useState<any>(null);
  const [customers, setCustomers] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Initialize form with react-hook-form
  const form = useForm<VisitCardFormValues>({
    resolver: zodResolver(visitCardFormSchema),
    defaultValues: {
      customerId: undefined,
      userId: undefined,
      visitDate: '',
      purpose: '',
      notes: '',
      followUpDate: '',
      status: 'PENDING',
      contactPerson: '',
      contactPhone: '',
      contactEmail: '',
    },
    mode: 'onChange', // Enable validation on change
  });

  // Fetch visit card data
  useEffect(() => {
    const fetchVisitCard = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/visit-cards/${visitCardId}`, {
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error('Failed to fetch visit card');
        }

        const data = await response.json();
        setVisitCard(data);

        // Format dates for form
        const formattedVisitDate = data.visitDate
          ? new Date(data.visitDate).toISOString().split('T')[0]
          : '';

        const formattedFollowUpDate = data.followUpDate
          ? new Date(data.followUpDate).toISOString().split('T')[0]
          : '';

        // Set form values
        form.reset({
          customerId: data.customerId || undefined,
          userId: data.userId || undefined,
          visitDate: formattedVisitDate,
          purpose: data.purpose || '',
          notes: data.notes || '',
          followUpDate: formattedFollowUpDate,
          status: data.status || 'PENDING',
          contactPerson: data.contactPerson || '',
          contactPhone: data.contactPhone || '',
          contactEmail: data.contactEmail || '',
        });
      } catch (error: any) {
        console.error('Error fetching visit card:', error);
        setError(error.message || 'Failed to load visit card');
        toast({
          title: 'Error',
          description: 'Failed to load visit card. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchVisitCard();
  }, [visitCardId]); // Removed 'form' to prevent infinite re-renders

  // Fetch customers and users data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch customers
        const customersResponse = await fetch('/api/customers?take=100', {
          credentials: 'include'
        });
        if (customersResponse.ok) {
          const customersData = await customersResponse.json();
          setCustomers(customersData.customers || []);
        }

        // Fetch users
        const usersResponse = await fetch('/api/users?take=100', {
          credentials: 'include'
        });
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setUsers(usersData.users || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load required data. Please try again.',
          variant: 'destructive'
        });
      }
    };

    fetchData();
  }, []);

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle form submission
  const onSubmit = async (data: VisitCardFormValues) => {
    setIsSubmitting(true);

    try {
      let filePath = visitCard.filePath;

      // If a new file is selected, upload it first
      if (selectedFile) {
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('customerId', data.customerId);

        const uploadResponse = await fetch('/api/upload/visit-card', {
          method: 'POST',
          body: formData,
          credentials: 'include'
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload file');
        }

        const uploadResult = await uploadResponse.json();
        filePath = uploadResult.filePath;
      }

      // Update the visit card
      const visitCardData = {
        ...data,
        filePath,
        visitDate: new Date(data.visitDate),
        followUpDate: data.followUpDate ? new Date(data.followUpDate) : undefined
      };

      const updateResponse = await fetch(`/api/visit-cards/${visitCardId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(visitCardData),
        credentials: 'include'
      });

      if (!updateResponse.ok) {
        throw new Error('Failed to update visit card');
      }

      toast({
        title: 'Success',
        description: 'Visit card updated successfully'
      });

      // Redirect to the visit cards list or the customer's visit cards tab
      if (data.customerId) {
        router.push(`/customers/${data.customerId}?tab=visitCards`);
      } else {
        router.push('/visit-cards');
      }
    } catch (error: any) {
      console.error('Error updating visit card:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to update visit card. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-64 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render error state
  if (error || !visitCard) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Error</CardTitle>
            <CardDescription>
              There was a problem loading the visit card
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error || 'Visit card not found'}</span>
            </div>
            <div className="mt-4 flex justify-end">
              <Button onClick={() => router.back()}>Go Back</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">

      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <CardTitle>Edit Visit Card</CardTitle>
          <CardDescription className="text-gray-100">
            Update visit card information
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Customer Selection */}
                <FormField
                  control={form.control}
                  name="customerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer*</FormLabel>
                      <Select
                        disabled={isSubmitting}
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a customer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the customer this visit card is for
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* User Selection */}
                <FormField
                  control={form.control}
                  name="userId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>User*</FormLabel>
                      <Select
                        disabled={isSubmitting}
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a user" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {users.map((user) => (
                            <SelectItem key={user.id} value={user.id}>
                              {user.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the user who created this visit card
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Visit Date */}
                <FormField
                  control={form.control}
                  name="visitDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Visit Date*</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          <Input
                            type="date"
                            disabled={isSubmitting}
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Date when the visit occurred
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        disabled={isSubmitting}
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="PENDING">Pending</SelectItem>
                          <SelectItem value="COMPLETED">Completed</SelectItem>
                          <SelectItem value="CANCELLED">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Current status of the visit
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Purpose */}
                <FormField
                  control={form.control}
                  name="purpose"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Purpose*</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Purpose of the visit"
                          disabled={isSubmitting}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Brief description of the visit purpose
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Notes */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Additional notes about the visit"
                          disabled={isSubmitting}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Any additional information about the visit
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contact Person */}
                <FormField
                  control={form.control}
                  name="contactPerson"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Person</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Name of the contact person"
                          disabled={isSubmitting}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Person contacted during the visit
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contact Phone */}
                <FormField
                  control={form.control}
                  name="contactPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Phone</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Phone number of the contact person"
                          disabled={isSubmitting}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Phone number of the contact person
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contact Email */}
                <FormField
                  control={form.control}
                  name="contactEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Email of the contact person"
                          disabled={isSubmitting}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Email address of the contact person
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Follow-up Date */}
                <FormField
                  control={form.control}
                  name="followUpDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Follow-up Date</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          <Input
                            type="date"
                            disabled={isSubmitting}
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Date for any follow-up actions
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Current File */}
                <div className="md:col-span-2">
                  <FormLabel>Current File</FormLabel>
                  <div className="mt-1 flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-gray-500" />
                    <span className="text-sm">{visitCard.filePath.split('/').pop()}</span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="ml-2"
                      onClick={() => window.open(visitCard.filePath, '_blank')}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                  </div>
                </div>

                {/* File Upload (Optional) */}
                <div className="md:col-span-2">
                  <FormLabel htmlFor="file">Replace File (Optional)</FormLabel>
                  <div className="mt-1 flex items-center">
                    <Input
                      id="file"
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={handleFileChange}
                      disabled={isSubmitting}
                      className="flex-1"
                    />
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    Upload a new file to replace the current one (PDF, JPG, JPEG, or PNG)
                  </p>
                  {selectedFile && (
                    <p className="text-sm text-green-600 mt-1">
                      Selected file: {selectedFile.name}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  disabled={isSubmitting}
                  onClick={() => router.back()}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Visit Card
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
