/**
 * Repository Migration Script
 * 
 * This script helps migrate API routes from using legacy repositories to modern repositories.
 * It scans the API routes directory and updates import statements and method calls.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const readdirAsync = promisify(fs.readdir);
const statAsync = promisify(fs.stat);

// Repository mapping from legacy to modern
const repositoryMapping = {
  // Legacy import: Modern import
  "import { CustomerRepository } from '../../../repositories'": "import { getCustomerRepository } from '@/lib/repositories'",
  "import { AMCContractRepository } from '../../../repositories'": "import { getAMCContractRepository } from '@/lib/repositories'",
  "import { MachineRepository } from '../../../repositories'": "import { getAMCMachineRepository } from '@/lib/repositories'",
  "import { ComponentRepository } from '../../../repositories'": "import { getComponentRepository } from '@/lib/repositories'",
  "import { WarrantyRepository } from '../../../repositories'": "import { getWarrantyRepository } from '@/lib/repositories'",
};

// Method mapping from legacy to modern
const methodMapping = {
  // Legacy repository: { legacy method: modern method }
  'CustomerRepository': {
    'getAllCustomers': 'findAll',
    'getCustomerById': 'findById',
    'getCustomerByOriginalId': 'findByOriginalId',
    'getLegacyCustomer': 'findLegacyById',
    'createCustomer': 'create',
    'updateCustomer': 'update',
  },
  'AMCContractRepository': {
    'getAllAMCContracts': 'findAll',
    'getAMCContractById': 'findById',
    'createAMCContract': 'create',
    'updateAMCContract': 'update',
  },
  'MachineRepository': {
    'getAllAMCMachines': 'findAll',
    'createAMCMachine': 'create',
  },
  'ComponentRepository': {
    'getAllAMCComponents': 'findAll',
    'createAMCComponent': 'create',
  },
  'WarrantyRepository': {
    'getAllWarranties': 'findAll',
    'getWarrantyById': 'findById',
    'createWarranty': 'create',
    'updateWarranty': 'update',
  },
};

// Repository instance variable names
const repositoryInstanceNames = {
  'CustomerRepository': 'customerRepository',
  'AMCContractRepository': 'amcContractRepository',
  'MachineRepository': 'machineRepository',
  'ComponentRepository': 'componentRepository',
  'WarrantyRepository': 'warrantyRepository',
};

/**
 * Recursively scan a directory for files
 * @param {string} dir - Directory to scan
 * @param {Array<string>} fileList - List of files found
 * @returns {Promise<Array<string>>} - List of files found
 */
async function scanDirectory(dir, fileList = []) {
  const files = await readdirAsync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = await statAsync(filePath);
    
    if (stat.isDirectory()) {
      fileList = await scanDirectory(filePath, fileList);
    } else if (file.endsWith('.js') || file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  }
  
  return fileList;
}

/**
 * Update a file to use modern repositories
 * @param {string} filePath - Path to the file
 * @returns {Promise<boolean>} - Whether the file was updated
 */
async function updateFile(filePath) {
  let content = await readFileAsync(filePath, 'utf8');
  let updated = false;
  
  // Check if the file uses legacy repositories
  const usesLegacyRepositories = Object.keys(repositoryMapping).some(legacyImport => 
    content.includes(legacyImport)
  );
  
  if (!usesLegacyRepositories) {
    return false;
  }
  
  console.log(`Updating ${filePath}...`);
  
  // Update import statements
  for (const [legacyImport, modernImport] of Object.entries(repositoryMapping)) {
    if (content.includes(legacyImport)) {
      content = content.replace(legacyImport, modernImport);
      updated = true;
      
      // Get the repository name from the import
      const repoMatch = legacyImport.match(/import \{ (\w+) \}/);
      if (repoMatch) {
        const repoName = repoMatch[1];
        const instanceName = repositoryInstanceNames[repoName];
        
        // Add repository instance creation
        const instanceCreation = `// Get the repository instance\n    const ${instanceName} = ${modernImport.match(/import \{ (\w+) \}/)[1]}();`;
        content = content.replace(/try \{/, `try {\n    ${instanceCreation}`);
        
        // Update method calls
        for (const [legacyMethod, modernMethod] of Object.entries(methodMapping[repoName] || {})) {
          const legacyMethodRegex = new RegExp(`${repoName}\\.${legacyMethod}\\(`, 'g');
          content = content.replace(legacyMethodRegex, `${instanceName}.${modernMethod}(`);
        }
        
        // Update include options format
        if (content.includes('includeCustomer: true')) {
          content = content.replace(
            /{\s*includeCustomer: true(?:,\s*includeContactPerson: true)?(?:,\s*includeMachines: true)?(?:,\s*includeServiceDates: true)?(?:,\s*includePayments: true)?(?:,\s*includeDivisions: true)?\s*}/g,
            '{\n          include: {\n            customer: true' +
            (content.includes('includeContactPerson: true') ? ',\n            contactPerson: true' : '') +
            (content.includes('includeMachines: true') ? ',\n            machines: true' : '') +
            (content.includes('includeServiceDates: true') ? ',\n            serviceDates: true' : '') +
            (content.includes('includePayments: true') ? ',\n            payments: true' : '') +
            (content.includes('includeDivisions: true') ? ',\n            divisions: true' : '') +
            '\n          }\n        }'
          );
        }
      }
    }
  }
  
  if (updated) {
    await writeFileAsync(filePath, content, 'utf8');
    console.log(`Updated ${filePath}`);
  }
  
  return updated;
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('Starting repository migration...');
    
    // Scan API routes directory
    const apiDir = path.join(__dirname, '..', 'src', 'pages', 'api');
    const files = await scanDirectory(apiDir);
    
    console.log(`Found ${files.length} files to check.`);
    
    // Update each file
    let updatedCount = 0;
    for (const file of files) {
      const updated = await updateFile(file);
      if (updated) {
        updatedCount++;
      }
    }
    
    console.log(`Migration complete. Updated ${updatedCount} files.`);
    
    // Suggest next steps
    console.log('\nNext steps:');
    console.log('1. Review the updated files to ensure they work correctly.');
    console.log('2. Run tests to verify the functionality.');
    console.log('3. Remove the legacy repositories directory once all files have been migrated.');
    
  } catch (error) {
    console.error('Error during migration:', error);
  }
}

// Run the script
main();
