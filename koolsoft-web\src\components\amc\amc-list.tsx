'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { showSuccessToast, showErrorToast } from '@/lib/toast';
import {
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  RefreshCw,
  Edit,
  Eye,
  Trash,
  Copy,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  ArrowUpRight,
  FileDown
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, <PERSON>ertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { AMCContract } from '@/lib/hooks/useAMCContracts';
import { Skeleton } from '@/components/ui/skeleton';
import { AMCStatusBadge } from './amc-status-badge';
import { AMCEmptyState } from './amc-empty-state';


// Define the pagination type
interface Pagination {
  skip: number;
  take: number;
  total: number;
}

// Define the component props
interface AMCListProps {
  contracts: AMCContract[];
  isLoading: boolean;
  pagination: Pagination;
  onPaginationChange: (skip: number, take: number) => void;
  onRefresh: () => void;
  onDelete: (id: string) => Promise<boolean>;
  onClearFilters?: () => void;
  isFiltered?: boolean;
  error?: string;
}

/**
 * AMC List Component
 *
 * This component displays a list of AMC contracts with pagination and actions.
 * It follows the UI standards with consistent styling and user interactions.
 */
export function AMCList({
  contracts,
  isLoading,
  pagination,
  onPaginationChange,
  onRefresh,
  onDelete,
  onClearFilters,
  isFiltered = false,
  error
}: AMCListProps) {
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [deleteContractId, setDeleteContractId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);


  // Calculate pagination values
  const { skip, take, total } = pagination;
  const currentPage = Math.floor(skip / take) + 1;
  const totalPages = Math.ceil(total / take);
  const showingFrom = total === 0 ? 0 : skip + 1;
  const showingTo = Math.min(skip + take, total);

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort indicator
  const getSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };

  // Handle pagination
  const handlePrevPage = () => {
    if (skip > 0) {
      onPaginationChange(Math.max(0, skip - take), take);
    }
  };

  const handleNextPage = () => {
    if (skip + take < total) {
      onPaginationChange(skip + take, take);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!deleteContractId) return;

    setIsDeleting(true);
    try {
      const success = await onDelete(deleteContractId);
      if (success) {
        showSuccessToast('Success', 'AMC contract deleted successfully');
        setDeleteContractId(null);
      }
    } finally {
      setIsDeleting(false);
    }
  };



  // Get status badge using the new component
  const getStatusBadge = (status: string) => {
    return <AMCStatusBadge status={status} />;
  };

  // Calculate days remaining
  const getDaysRemaining = (endDate: Date | string) => {
    const end = new Date(endDate);
    const today = new Date();
    const diffTime = end.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get expiry indicator
  const getExpiryIndicator = (endDate: Date | string) => {
    const daysRemaining = getDaysRemaining(endDate);

    if (daysRemaining < 0) {
      return (
        <span className="relative group">
          <XCircle className="h-4 w-4 text-red-500" />
          <span className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Expired
          </span>
        </span>
      );
    } else if (daysRemaining <= 30) {
      return (
        <span className="relative group">
          <AlertTriangle className="h-4 w-4 text-yellow-500" />
          <span className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Expiring in {daysRemaining} days
          </span>
        </span>
      );
    } else {
      return (
        <span className="relative group">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            {daysRemaining} days remaining
          </span>
        </span>
      );
    }
  };

  return (
    <div className="space-y-4">


      {/* Error State */}
      {error && (
        <AMCEmptyState
          type="error"
          message={error}
          isFiltered={isFiltered}
          onClearFilters={onClearFilters}
          onRetry={onRefresh}
        />
      )}

      {/* Empty State */}
      {!isLoading && !error && contracts.length === 0 && (
        <AMCEmptyState
          type={isFiltered ? "no-results" : "no-contracts"}
          isFiltered={isFiltered}
          onClearFilters={onClearFilters}
        />
      )}



      {/* AMC Contracts Table */}
      {(!isLoading || contracts.length > 0) && !error && (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort('contractNumber')}
                >
                  Contract {getSortIndicator('contractNumber')}
                </TableHead>
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort('customer.name')}
                >
                  Customer {getSortIndicator('customer.name')}
                </TableHead>
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort('startDate')}
                >
                  Period {getSortIndicator('startDate')}
                </TableHead>
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort('amount')}
                >
                  Amount {getSortIndicator('amount')}
                </TableHead>
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort('status')}
                >
                  Status {getSortIndicator('status')}
                </TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={`skeleton-${index}`}>
                    <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-40" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                    <TableCell className="text-right"><Skeleton className="h-6 w-16 ml-auto" /></TableCell>
                  </TableRow>
                ))
              ) : contracts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    No AMC contracts found. Try adjusting your filters or create a new contract.
                  </TableCell>
                </TableRow>
              ) : (
                contracts.map((contract) => (
                  <TableRow key={contract.id}>
                    <TableCell className="font-medium">
                      {contract.contractNumber || `AMC-${contract.id.substring(0, 8)}`}
                    </TableCell>
                    <TableCell>
                      {contract.customer?.name || 'Unknown Customer'}
                      {contract.customer?.city && (
                        <div className="text-xs text-gray-500">
                          {contract.customer.city}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <span>{formatDate(contract.startDate)}</span>
                        <ArrowUpRight className="h-3 w-3" />
                        <span>{formatDate(contract.endDate)}</span>
                        {getExpiryIndicator(contract.endDate)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {contract.numberOfMachines} machines, {contract.totalTonnage || 0} tons
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>{formatCurrency(contract.amount)}</div>
                      {contract.paidAmount !== undefined && (
                        <div className="text-xs text-gray-500">
                          Paid: {formatCurrency(contract.paidAmount)}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(contract.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link href={`/amc/contracts/${contract.id}`}>
                              <Eye className="h-4 w-4 mr-2" /> View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/amc/contracts/${contract.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" /> Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/amc/contracts/${contract.id}/renew`}>
                              <Copy className="h-4 w-4 mr-2" /> Renew
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/amc/contracts/${contract.id}/service-dates`}>
                              <Calendar className="h-4 w-4 mr-2" /> Service Dates
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => setDeleteContractId(contract.id)}
                          >
                            <Trash className="h-4 w-4 mr-2" /> Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination */}
      {!isLoading && !error && contracts.length > 0 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-4">
          <div className="text-sm text-gray-500">
            {total > 0 ? (
              <>Showing {showingFrom} to {showingTo} of {total} contracts</>
            ) : (
              'No contracts found'
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevPage}
              disabled={skip === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>
            {totalPages > 1 && (
              <div className="hidden md:flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show first page, last page, current page, and pages around current page
                  const pagesToShow = [1, totalPages, currentPage, currentPage - 1, currentPage + 1];
                  const pageNumber = pagesToShow[i];

                  // Skip if page number is out of range or already shown
                  if (pageNumber < 1 || pageNumber > totalPages || pagesToShow.indexOf(pageNumber) !== i) {
                    return null;
                  }

                  return (
                    <Button
                      key={`page-${pageNumber}`}
                      variant={currentPage === pageNumber ? "default" : "outline"}
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => onPaginationChange((pageNumber - 1) * take, take)}
                    >
                      {pageNumber}
                    </Button>
                  );
                })}
              </div>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={skip + take >= total}
            >
              Next <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              title="Refresh"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteContractId} onOpenChange={(open) => !open && setDeleteContractId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the AMC contract and all related data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
