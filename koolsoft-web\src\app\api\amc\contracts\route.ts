import { NextRequest, NextResponse } from 'next/server';
import { getAMCContractRepository } from '@/lib/repositories';
import { createAMCContractSchema, amcContractFilterSchema } from '@/lib/validations/amc-contract.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { z } from 'zod';

/**
 * GET /api/amc/contracts
 * Get all AMC contracts with optional pagination and filtering
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    console.log('AMC Contracts API: GET request received');
    try {
      console.log('AMC Contracts API: Parsing request parameters');
      const { searchParams } = new URL(request.url);
      const skip = parseInt(searchParams.get('skip') || '0');
      const take = parseInt(searchParams.get('take') || '10');

      console.log('AMC Contracts API: Request parameters:', {
        skip,
        take,
        url: request.url,
        searchParams: Object.fromEntries(searchParams.entries())
      });

      // Extract filter parameters
      const filterParams: Record<string, any> = {};

      // Customer ID filter
      const customerId = searchParams.get('customerId');
      if (customerId && customerId !== 'null') {
        filterParams.customerId = customerId;
      }

      // Executive ID filter
      const executiveId = searchParams.get('executiveId');
      if (executiveId && executiveId !== 'null') {
        filterParams.executiveId = executiveId;
      }

      // Status filter
      const status = searchParams.get('status');
      if (status && status !== 'null') {
        filterParams.status = status;

        // If status is ACTIVE, also check that end date is in the future
        if (status === 'ACTIVE') {
          filterParams.endDate = {
            ...filterParams.endDate,
            gte: new Date()
          };
        }
      }

      // Expiring filter
      const expiring = searchParams.get('expiring');
      if (expiring) {
        const days = parseInt(expiring);
        const today = new Date();
        const futureDate = new Date();
        futureDate.setDate(today.getDate() + days);

        filterParams.endDate = {
          gte: today,
          lte: futureDate,
        };
        filterParams.status = 'ACTIVE';
      }

      // Date range filters
      const startDateFrom = searchParams.get('startDateFrom');
      const startDateTo = searchParams.get('startDateTo');
      const endDateFrom = searchParams.get('endDateFrom');
      const endDateTo = searchParams.get('endDateTo');

      if ((startDateFrom && startDateFrom !== 'null') || (startDateTo && startDateTo !== 'null')) {
        filterParams.startDate = {};
        if (startDateFrom && startDateFrom !== 'null') {
          filterParams.startDate.gte = new Date(startDateFrom);
        }
        if (startDateTo && startDateTo !== 'null') {
          filterParams.startDate.lte = new Date(startDateTo);
        }
      }

      if ((endDateFrom && endDateFrom !== 'null') || (endDateTo && endDateTo !== 'null')) {
        filterParams.endDate = {};
        if (endDateFrom && endDateFrom !== 'null') {
          filterParams.endDate.gte = new Date(endDateFrom);
        }
        if (endDateTo && endDateTo !== 'null') {
          filterParams.endDate.lte = new Date(endDateTo);
        }
      }

      // Search filter (search in contract number, customer name, etc.)
      const search = searchParams.get('search');
      if (search && search !== 'null') {
        filterParams.OR = [
          { contractNumber: { contains: search, mode: 'insensitive' } },
          { customer: { name: { contains: search, mode: 'insensitive' } } },
          { remarks: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Sorting
      const sortField = searchParams.get('sortField') || 'startDate';
      const sortOrder = searchParams.get('sortOrder') || 'desc';
      const orderBy = { [sortField]: sortOrder };

      // Validate filter parameters
      try {
        console.log('AMC Contracts API: Validating filter parameters:', {
          customerId,
          executiveId,
          status,
          startDateFrom,
          startDateTo,
          endDateFrom,
          endDateTo,
          search,
        });

        // Prepare data for validation, converting null strings to actual null values
        const validationData = {
          customerId: customerId === 'null' ? null : customerId,
          executiveId: executiveId === 'null' ? null : executiveId,
          status: status === 'null' ? null : status,
          startDateFrom: startDateFrom && startDateFrom !== 'null' ? new Date(startDateFrom) : null,
          startDateTo: startDateTo && startDateTo !== 'null' ? new Date(startDateTo) : null,
          endDateFrom: endDateFrom && endDateFrom !== 'null' ? new Date(endDateFrom) : null,
          endDateTo: endDateTo && endDateTo !== 'null' ? new Date(endDateTo) : null,
          search: search === 'null' ? null : search,
        };

        // Always validate, but with properly prepared data
        amcContractFilterSchema.parse(validationData);
      } catch (error) {
        if (error instanceof z.ZodError) {
          console.error('AMC Contracts API: Validation error:', error.errors);
          return NextResponse.json(
            { error: 'Invalid filter parameters', details: error.errors },
            { status: 400 }
          );
        }
      }

      console.log('AMC Contracts API: Getting repository');
      const amcContractRepository = getAMCContractRepository();
      console.log('AMC Contracts API: Repository obtained');

      console.log('AMC Contracts API: Fetching contracts with filter:', {
        filterParams,
        skip,
        take,
        orderBy
      });

      // Get contracts with filter
      try {
        const contracts = await amcContractRepository.findWithFilter(
          filterParams,
          skip,
          take,
          orderBy
        );
        console.log('AMC Contracts API: Contracts fetched successfully, count:', contracts.length);

        // Get total count for pagination
        console.log('AMC Contracts API: Counting total contracts with filter');
        const total = await amcContractRepository.countWithFilter(filterParams);
        console.log('AMC Contracts API: Total count:', total);

        return NextResponse.json({
          data: contracts,
          pagination: {
            total,
            skip,
            take,
          },
        });
      } catch (repoError) {
        console.error('Error in repository operations:', repoError);
        throw new Error(`Repository error: ${repoError instanceof Error ? repoError.message : 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error fetching AMC contracts:', error);
      return NextResponse.json(
        { error: 'Failed to fetch AMC contracts', details: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/amc/contracts
 * Create a new AMC contract
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = createAMCContractSchema.parse(body);

        const amcContractRepository = getAMCContractRepository();

        // Create AMC contract with related data
        const contract = await amcContractRepository.createWithRelations(validatedData);

        return NextResponse.json(contract, { status: 201 });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error creating AMC contract:', error);

      if (error instanceof PrismaClientKnownRequestError) {
        // Handle specific Prisma errors
        if (error.code === 'P2002') {
          return NextResponse.json(
            {
              error: 'Duplicate entry',
              details: 'A record with this identifier already exists',
              code: 'DUPLICATE_ERROR'
            },
            { status: 409 }
          );
        }

        if (error.code === 'P2003') {
          return NextResponse.json(
            {
              error: 'Foreign key constraint failed',
              details: 'One of the referenced records does not exist',
              code: 'FOREIGN_KEY_ERROR'
            },
            { status: 400 }
          );
        }
      }

      // Extract error message
      let errorMessage = 'An unexpected error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      return NextResponse.json(
        {
          error: 'Failed to create AMC contract',
          message: process.env.NODE_ENV === 'development' ? errorMessage : 'An unexpected error occurred',
          code: 'INTERNAL_ERROR'
        },
        { status: 500 }
      );
    }
  }
);
