'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { showErrorToast } from '@/lib/toast';
import { AlertCircle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Define the bulk action form schema
const bulkActionSchema = z.object({
  action: z.enum(['ACTIVATE', 'DEACTIVATE', 'CHANGE_ROLE', 'DELETE']),
  role: z.enum(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']).optional(),
}).refine(data => {
  // If action is CHANGE_ROLE, role is required
  if (data.action === 'CHANGE_ROLE') {
    return !!data.role;
  }
  return true;
}, {
  message: "Role is required for this action",
  path: ["role"],
});

type BulkActionFormValues = z.infer<typeof bulkActionSchema>;

interface BulkActionFormProps {
  selectedUsers: string[];
  onSuccess: () => void;
  onCancel: () => void;
}

export function BulkActionForm({ selectedUsers, onSuccess, onCancel }: BulkActionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<BulkActionFormValues>({
    resolver: zodResolver(bulkActionSchema),
    defaultValues: {
      action: 'ACTIVATE',
    },
  });

  // Watch the action field to conditionally show role field
  const watchAction = form.watch('action');

  // Handle form submission
  const onSubmit = async (values: BulkActionFormValues) => {
    // Confirm dangerous actions
    if (values.action === 'DELETE') {
      if (!confirm(`Are you sure you want to delete ${selectedUsers.length} users? This action cannot be undone.`)) {
        return;
      }
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/admin/users/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userIds: selectedUsers,
          action: values.action,
          role: values.role,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to perform bulk action');
      }

      // Success callback will handle the toast notification
      form.reset();
      onSuccess();
    } catch (error: any) {
      console.error('Error performing bulk action:', error);
      showErrorToast('Error', error.message || 'Failed to perform bulk action. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Bulk Action</AlertTitle>
        <AlertDescription>
          You are about to perform an action on {selectedUsers.length} selected users.
        </AlertDescription>
      </Alert>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="action"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Action</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an action" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="ACTIVATE">Activate Users</SelectItem>
                    <SelectItem value="DEACTIVATE">Deactivate Users</SelectItem>
                    <SelectItem value="CHANGE_ROLE">Change Role</SelectItem>
                    <SelectItem value="DELETE">Delete Users</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  {field.value === 'ACTIVATE' && 'Activate the selected users, allowing them to log in.'}
                  {field.value === 'DEACTIVATE' && 'Deactivate the selected users, preventing them from logging in.'}
                  {field.value === 'CHANGE_ROLE' && 'Change the role of the selected users.'}
                  {field.value === 'DELETE' && 'Delete the selected users. This action cannot be undone.'}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {watchAction === 'CHANGE_ROLE' && (
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>New Role</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ADMIN">Admin</SelectItem>
                      <SelectItem value="MANAGER">Manager</SelectItem>
                      <SelectItem value="EXECUTIVE">Executive</SelectItem>
                      <SelectItem value="USER">User</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    This determines what permissions the users will have.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>

            <Button
              type="submit"
              disabled={isSubmitting}
              variant={watchAction === 'DELETE' ? 'destructive' : 'default'}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {watchAction === 'ACTIVATE' && 'Activate Users'}
              {watchAction === 'DEACTIVATE' && 'Deactivate Users'}
              {watchAction === 'CHANGE_ROLE' && 'Change Role'}
              {watchAction === 'DELETE' && 'Delete Users'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
