'use client';

import React, { useState, useEffect } from 'react';
import { useAMCForm, AMCFormStep } from '@/contexts/amc-form-context';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar as CalendarIcon, 
  Plus, 
  Trash2, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Wand2
} from 'lucide-react';
import { format, addDays, addMonths, differenceInDays } from 'date-fns';
import { cn } from '@/lib/utils';

interface ServiceDate {
  id?: string;
  scheduledDate: Date;
  status: 'SCHEDULED' | 'COMPLETED' | 'MISSED';
  technicianId?: string;
  remarks?: string;
  serviceNumber?: number;
}

export function AMCFormStep4() {
  const { state, updateFormData, goToNextStep, goToPreviousStep, validateStep } = useAMCForm();
  const [serviceDates, setServiceDates] = useState<ServiceDate[]>(state.formData.serviceDates || []);
  const [numberOfServices, setNumberOfServices] = useState<number>(4);
  const [serviceInterval, setServiceInterval] = useState<'monthly' | 'quarterly' | 'custom'>('quarterly');
  const [customInterval, setCustomInterval] = useState<number>(90);
  const [showCalendar, setShowCalendar] = useState(false);

  // Update form data when service dates change
  useEffect(() => {
    updateFormData({ serviceDates });
  }, [serviceDates, updateFormData]);

  // Generate service dates automatically
  const generateServiceDates = () => {
    if (!state.formData.startDate || !state.formData.endDate) {
      return;
    }

    const startDate = new Date(state.formData.startDate);
    const endDate = new Date(state.formData.endDate);
    const contractDuration = differenceInDays(endDate, startDate);
    
    let intervalDays: number;
    
    switch (serviceInterval) {
      case 'monthly':
        intervalDays = 30;
        break;
      case 'quarterly':
        intervalDays = 90;
        break;
      case 'custom':
        intervalDays = customInterval;
        break;
      default:
        intervalDays = Math.floor(contractDuration / numberOfServices);
    }

    const newServiceDates: ServiceDate[] = [];
    
    for (let i = 0; i < numberOfServices; i++) {
      const serviceDate = addDays(startDate, i * intervalDays);
      
      // Ensure service date doesn't exceed contract end date
      if (serviceDate <= endDate) {
        newServiceDates.push({
          scheduledDate: serviceDate,
          status: 'SCHEDULED',
          serviceNumber: i + 1,
        });
      }
    }

    setServiceDates(newServiceDates);
  };

  // Add a new service date manually
  const addServiceDate = () => {
    const newServiceDate: ServiceDate = {
      scheduledDate: new Date(),
      status: 'SCHEDULED',
      serviceNumber: serviceDates.length + 1,
    };
    setServiceDates([...serviceDates, newServiceDate]);
  };

  // Remove a service date
  const removeServiceDate = (index: number) => {
    const updatedServiceDates = serviceDates.filter((_, i) => i !== index);
    // Renumber the remaining service dates
    const renumberedServiceDates = updatedServiceDates.map((sd, i) => ({
      ...sd,
      serviceNumber: i + 1,
    }));
    setServiceDates(renumberedServiceDates);
  };

  // Update a service date
  const updateServiceDate = (index: number, updates: Partial<ServiceDate>) => {
    const updatedServiceDates = serviceDates.map((sd, i) => 
      i === index ? { ...sd, ...updates } : sd
    );
    setServiceDates(updatedServiceDates);
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: ServiceDate['status']) => {
    switch (status) {
      case 'SCHEDULED':
        return 'default';
      case 'COMPLETED':
        return 'secondary';
      case 'MISSED':
        return 'destructive';
      default:
        return 'default';
    }
  };

  // Get status icon
  const getStatusIcon = (status: ServiceDate['status']) => {
    switch (status) {
      case 'SCHEDULED':
        return <Clock className="h-3 w-3" />;
      case 'COMPLETED':
        return <CheckCircle className="h-3 w-3" />;
      case 'MISSED':
        return <AlertCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  const handleNext = () => {
    // Validate step
    const isValid = validateStep(AMCFormStep.SERVICE_SCHEDULING, state.formData);
    if (isValid) {
      goToNextStep();
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="text-white">Service Date Scheduling</CardTitle>
            <CardDescription className="text-gray-100">
              Schedule service dates for the AMC contract
            </CardDescription>
          </div>
          <Button 
            variant="secondary" 
            size="sm"
            onClick={generateServiceDates}
            className="flex items-center gap-2"
          >
            <Wand2 className="h-4 w-4" />
            Auto Generate
          </Button>
        </CardHeader>
        <CardContent className="pt-6">
          {/* Service Generation Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
              <Label htmlFor="numberOfServices" className="text-black">
                Number of Services
              </Label>
              <Input
                id="numberOfServices"
                type="number"
                min="1"
                max="12"
                value={numberOfServices}
                onChange={(e) => setNumberOfServices(parseInt(e.target.value) || 1)}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="serviceInterval" className="text-black">
                Service Interval
              </Label>
              <Select value={serviceInterval} onValueChange={(value: any) => setServiceInterval(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select interval" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly (30 days)</SelectItem>
                  <SelectItem value="quarterly">Quarterly (90 days)</SelectItem>
                  <SelectItem value="custom">Custom Interval</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {serviceInterval === 'custom' && (
              <div className="space-y-2">
                <Label htmlFor="customInterval" className="text-black">
                  Custom Interval (days)
                </Label>
                <Input
                  id="customInterval"
                  type="number"
                  min="1"
                  max="365"
                  value={customInterval}
                  onChange={(e) => setCustomInterval(parseInt(e.target.value) || 30)}
                  className="w-full"
                />
              </div>
            )}
          </div>

          <Separator className="my-6" />

          {/* Service Dates List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-black">Scheduled Service Dates</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={addServiceDate}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Service Date
              </Button>
            </div>

            {serviceDates.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-black">
                  No service dates scheduled. Use the "Auto Generate" button or add service dates manually.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-3">
                {serviceDates.map((serviceDate, index) => (
                  <Card key={index} className="border border-gray-200">
                    <CardContent className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        {/* Service Number */}
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-black">
                            Service #{serviceDate.serviceNumber}
                          </span>
                          <Badge 
                            variant={getStatusBadgeVariant(serviceDate.status)}
                            className="flex items-center gap-1"
                          >
                            {getStatusIcon(serviceDate.status)}
                            {serviceDate.status}
                          </Badge>
                        </div>

                        {/* Scheduled Date */}
                        <div className="space-y-1">
                          <Label className="text-xs text-black">Scheduled Date</Label>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  'w-full justify-start text-left font-normal',
                                  !serviceDate.scheduledDate && 'text-muted-foreground'
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {serviceDate.scheduledDate ? (
                                  format(serviceDate.scheduledDate, 'PPP')
                                ) : (
                                  <span>Pick a date</span>
                                )}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={serviceDate.scheduledDate}
                                onSelect={(date) => date && updateServiceDate(index, { scheduledDate: date })}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>

                        {/* Status */}
                        <div className="space-y-1">
                          <Label className="text-xs text-black">Status</Label>
                          <Select 
                            value={serviceDate.status} 
                            onValueChange={(value: any) => updateServiceDate(index, { status: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                              <SelectItem value="COMPLETED">Completed</SelectItem>
                              <SelectItem value="MISSED">Missed</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeServiceDate(index)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={goToPreviousStep}>
          Previous
        </Button>
        <Button onClick={handleNext}>
          Next
        </Button>
      </div>
    </div>
  );
}
