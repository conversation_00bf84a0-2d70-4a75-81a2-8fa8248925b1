'use client';

import { useState, useEffect } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

interface EmailPreviewFormProps {
  templates: any[];
  selectedTemplateId: string | null;
  onTemplateChange: (templateId: string) => void;
  previewData: Record<string, any>;
  onDataChange: (data: Record<string, any>) => void;
}

/**
 * Email Preview Form Component
 *
 * This component provides a form for selecting an email template and
 * entering test data for previewing.
 */
export function EmailPreviewForm({
  templates,
  selectedTemplateId,
  onTemplateChange,
  previewData,
  onDataChange,
}: EmailPreviewFormProps) {
  const [loading, setLoading] = useState(false);
  const [templateVariables, setTemplateVariables] = useState<string[]>([]);

  // Extract variables from selected template
  useEffect(() => {
    if (!selectedTemplateId) return;

    const template = templates.find(t => t.id === selectedTemplateId);
    if (!template) return;

    // Use variables from template if available
    if (template.variables && template.variables.length > 0) {
      setTemplateVariables(template.variables);
      return;
    }

    // Otherwise extract variables from template
    const variableRegex = /{{([^{}]+)}}/g;
    const bodyVariables = [...(template.bodyHtml || template.body || '').matchAll(variableRegex)].map(match => match[1]);
    const subjectVariables = [...template.subject.matchAll(variableRegex)].map(match => match[1]);
    const allVariables = [...new Set([...bodyVariables, ...subjectVariables])];

    setTemplateVariables(allVariables);
  }, [selectedTemplateId, templates]);

  // Handle template selection change
  const handleTemplateChange = (value: string) => {
    onTemplateChange(value);
  };

  // Handle input change for a variable
  const handleInputChange = (variable: string, value: string) => {
    const updatedData = { ...previewData, [variable]: value };
    onDataChange(updatedData);
  };

  // Generate sample data for all variables
  const generateSampleData = () => {
    const sampleData: Record<string, string> = {};

    templateVariables.forEach(variable => {
      // Generate appropriate sample data based on variable name
      if (variable.toLowerCase().includes('name')) {
        sampleData[variable] = 'John Doe';
      } else if (variable.toLowerCase().includes('email')) {
        sampleData[variable] = '<EMAIL>';
      } else if (variable.toLowerCase().includes('date')) {
        sampleData[variable] = new Date().toLocaleDateString();
      } else if (variable.toLowerCase().includes('time')) {
        sampleData[variable] = new Date().toLocaleTimeString();
      } else if (variable.toLowerCase().includes('amount') || variable.toLowerCase().includes('price')) {
        sampleData[variable] = '$1,234.56';
      } else if (variable.toLowerCase().includes('id')) {
        sampleData[variable] = 'ABC-12345';
      } else if (variable.toLowerCase().includes('phone')) {
        sampleData[variable] = '(*************';
      } else if (variable.toLowerCase().includes('address')) {
        sampleData[variable] = '123 Main St, Anytown, CA 12345';
      } else if (variable.toLowerCase().includes('company')) {
        sampleData[variable] = 'ACME Corporation';
      } else if (variable.toLowerCase().includes('description')) {
        sampleData[variable] = 'This is a sample description for testing purposes.';
      } else {
        sampleData[variable] = `Sample ${variable}`;
      }
    });

    onDataChange(sampleData);
  };

  // Clear all data fields
  const clearAllFields = () => {
    const emptyData: Record<string, string> = {};
    templateVariables.forEach(variable => {
      emptyData[variable] = '';
    });
    onDataChange(emptyData);
  };

  // Determine if a variable might contain multi-line text
  const isMultilineVariable = (variable: string) => {
    return variable.toLowerCase().includes('description') ||
           variable.toLowerCase().includes('content') ||
           variable.toLowerCase().includes('message') ||
           variable.toLowerCase().includes('body');
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="template-select" className="text-black">Select Template</Label>
        <Select
          value={selectedTemplateId || ''}
          onValueChange={handleTemplateChange}
        >
          <SelectTrigger id="template-select" className="text-black">
            <SelectValue placeholder="Select a template" />
          </SelectTrigger>
          <SelectContent>
            {templates.map(template => (
              <SelectItem key={template.id} value={template.id}>
                {template.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-black">Template Variables</h3>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={generateSampleData}
            disabled={templateVariables.length === 0}
          >
            Generate Sample Data
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllFields}
            disabled={templateVariables.length === 0}
          >
            Clear All
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-32">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : templateVariables.length > 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {templateVariables.map(variable => (
                <div key={variable} className="space-y-2">
                  <Label htmlFor={`var-${variable}`} className="text-black">
                    {variable}
                  </Label>
                  {isMultilineVariable(variable) ? (
                    <Textarea
                      id={`var-${variable}`}
                      value={previewData[variable] || ''}
                      onChange={(e) => handleInputChange(variable, e.target.value)}
                      className="text-black"
                      rows={3}
                    />
                  ) : (
                    <Input
                      id={`var-${variable}`}
                      value={previewData[variable] || ''}
                      onChange={(e) => handleInputChange(variable, e.target.value)}
                      className="text-black"
                    />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="text-center py-8 text-gray-500">
          {selectedTemplateId ?
            'No variables found in this template.' :
            'Please select a template to see variables.'}
        </div>
      )}
    </div>
  );
}
