import { PrismaClient } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { prisma } from '@/lib/prisma';

/**
 * Base Prisma Repository
 *
 * This class provides a base implementation of the BaseRepository interface using Prisma.
 * It handles common CRUD operations and transaction management.
 */
export abstract class PrismaRepository<T, ID, CreateInput, UpdateInput> implements BaseRepository<T, ID, CreateInput, UpdateInput> {
  protected prisma: PrismaClient;
  protected readonly modelName: string;

  constructor(modelName: string) {
    this.prisma = prisma;
    this.modelName = modelName;
  }

  /**
   * Get the Prisma model delegate for the current model
   * @returns Prisma model delegate
   */
  /**
   * Get a mock model that returns empty results
   * This is used for models that don't exist in the Prisma schema
   */
  protected getMockModel(): any {
    return {
      findMany: async () => [],
      findUnique: async () => null,
      findFirst: async () => null,
      count: async () => 0,
      create: async () => ({ id: '', name: '', description: '' }),
      update: async () => ({ id: '', name: '', description: '' }),
      delete: async () => ({ id: '', name: '', description: '' }),
      deleteMany: async () => ({ count: 0 }),
      updateMany: async () => ({ count: 0 })
    };
  }

  protected get model(): any {
    try {
      // Check if we have a stored model name (for debugging purposes)
      const modelNameToUse = this.hasOwnProperty('_modelName') && (this as any)._modelName
        ? (this as any)._modelName
        : this.modelName;

      console.log(`PrismaRepository.model: Getting model for '${modelNameToUse}'`);
      console.log('PrismaRepository.model: Available models in Prisma:', Object.keys(this.prisma).filter(key => !key.startsWith('$') && typeof (this.prisma as any)[key] === 'object'));

      // Try to get the model from Prisma
      const model = (this.prisma as any)[modelNameToUse];

      // If the model exists, return it
      if (model) {
        console.log(`PrismaRepository.model: Found model '${modelNameToUse}' directly`);
        return model;
      }

      // Log a warning if the model doesn't exist
      console.warn(`Model '${modelNameToUse}' not found in Prisma schema, trying alternative approaches`);

      // Try to map the model name to a table name that might exist in the database
      // For example, 'ServiceVisitType' might map to 'service_visit_types'
      const snakeCaseName = modelNameToUse
        .replace(/([A-Z])/g, '_$1')
        .toLowerCase()
        .replace(/^_/, '');

      console.log(`PrismaRepository.model: Trying snake case name: '${snakeCaseName}'`);
      const model2 = (this.prisma as any)[snakeCaseName];
      if (model2) {
        console.log(`PrismaRepository.model: Found model using snake case: '${snakeCaseName}'`);
        return model2;
      }

      // Try to map the model name to a camel case name that might exist in the database
      // For example, 'service_visit_types' might map to 'serviceVisitTypes'
      const camelCaseName = snakeCaseName
        .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

      console.log(`PrismaRepository.model: Trying camel case name: '${camelCaseName}'`);
      const model3 = (this.prisma as any)[camelCaseName];
      if (model3) {
        console.log(`PrismaRepository.model: Found model using camel case: '${camelCaseName}'`);
        return model3;
      }

      // Try to map the model name to a PascalCase name that might exist in the database
      // For example, 'service_visit_types' might map to 'ServiceVisitType'
      const pascalCaseName = camelCaseName.charAt(0).toUpperCase() + camelCaseName.slice(1);

      console.log(`PrismaRepository.model: Trying pascal case name: '${pascalCaseName}'`);
      const model4 = (this.prisma as any)[pascalCaseName];
      if (model4) {
        console.log(`PrismaRepository.model: Found model using pascal case: '${pascalCaseName}'`);
        return model4;
      }

      // If we still can't find the model, use the mock model as a last resort
      console.warn(`Could not find model '${modelNameToUse}', '${snakeCaseName}', '${camelCaseName}', or '${pascalCaseName}' in Prisma schema, using mock model`);
      return this.getMockModel();
    } catch (error) {
      // If there's an error, log it and return the mock model
      console.error(`Error getting model '${this.modelName}':`, error);
      return this.getMockModel();
    }
  }

  /**
   * Find all entities with optional pagination
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of entities
   */
  async findAll(skip?: number, take?: number): Promise<T[]> {
    return this.model.findMany({
      skip,
      take,
    });
  }

  /**
   * Find entities by a filter condition
   * @param filter Filter condition
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of entities
   */
  async findBy(filter: any, skip?: number, take?: number): Promise<T[]> {
    return this.model.findMany({
      where: filter,
      skip,
      take,
    });
  }

  /**
   * Find a single entity by its ID
   * @param id Entity ID
   * @returns Promise resolving to the entity or null if not found
   */
  async findById(id: ID): Promise<T | null> {
    return this.model.findUnique({
      where: { id },
    });
  }

  /**
   * Find a single entity by a unique condition
   * @param where Unique condition
   * @returns Promise resolving to the entity or null if not found
   */
  async findOne(where: any): Promise<T | null> {
    return this.model.findFirst({
      where,
    });
  }

  /**
   * Count entities matching a filter condition
   * @param filter Filter condition
   * @returns Promise resolving to the count
   */
  async count(filter?: any): Promise<number> {
    return this.model.count({
      where: filter,
    });
  }

  /**
   * Create a new entity
   * @param data Entity data
   * @returns Promise resolving to the created entity
   */
  async create(data: CreateInput): Promise<T> {
    return this.model.create({
      data,
    });
  }

  /**
   * Create multiple entities
   * @param data Array of entity data
   * @returns Promise resolving to the created entities
   */
  async createMany(data: CreateInput[]): Promise<T[]> {
    // Prisma's createMany doesn't return the created records,
    // so we need to create them one by one to return them
    const createdEntities: T[] = [];

    for (const item of data) {
      const entity = await this.create(item);
      createdEntities.push(entity);
    }

    return createdEntities;
  }

  /**
   * Update an entity by its ID
   * @param id Entity ID
   * @param data Update data
   * @returns Promise resolving to the updated entity
   */
  async update(id: ID, data: UpdateInput): Promise<T> {
    return this.model.update({
      where: { id },
      data,
    });
  }

  /**
   * Update multiple entities by a filter condition
   * @param filter Filter condition
   * @param data Update data
   * @returns Promise resolving to the number of updated entities
   */
  async updateMany(filter: any, data: UpdateInput): Promise<number> {
    const result = await this.model.updateMany({
      where: filter,
      data,
    });

    return result.count;
  }

  /**
   * Delete an entity by its ID
   * @param id Entity ID
   * @returns Promise resolving to the deleted entity
   */
  async delete(id: ID): Promise<T> {
    return this.model.delete({
      where: { id },
    });
  }

  /**
   * Delete multiple entities by a filter condition
   * @param filter Filter condition
   * @returns Promise resolving to the number of deleted entities
   */
  async deleteMany(filter: any): Promise<number> {
    const result = await this.model.deleteMany({
      where: filter,
    });

    return result.count;
  }

  /**
   * Execute a function within a transaction
   * @param fn Function to execute within the transaction
   * @returns Promise resolving to the result of the function
   */
  async transaction<R>(fn: (repo: BaseRepository<T, ID, CreateInput, UpdateInput>) => Promise<R>): Promise<R> {
    return this.prisma.$transaction(async (tx) => {
      // Create a new repository instance with the transaction client
      const txRepo = this.createTransactionRepository(tx);

      // Execute the function with the transaction repository
      return fn(txRepo);
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected abstract createTransactionRepository(tx: PrismaClient): BaseRepository<T, ID, CreateInput, UpdateInput>;
}
