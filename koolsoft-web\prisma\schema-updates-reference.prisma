// Additional Reference Tables Models
model SpareType {
  id          String   @id @default(uuid())
  name        String
  description String?
  code        String?
  active      Boolean  @default(true)
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("spare_types")
}

model MeasurementType {
  id          String   @id @default(uuid())
  name        String
  description String?
  code        String?
  active      Boolean  @default(true)
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("measurement_types")
}
