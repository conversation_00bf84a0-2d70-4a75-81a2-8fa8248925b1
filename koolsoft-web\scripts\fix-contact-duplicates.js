const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixContactDuplicates() {
  try {
    console.log('Starting contact duplicate cleanup process...\n');

    // Step 1: Find all duplicate contact groups
    console.log('=== FINDING CONTACT DUPLICATES ===');
    
    // Use a simpler approach to avoid BigInt issues
    const allContacts = await prisma.contact.findMany({
      orderBy: { createdAt: 'asc' }
    });

    console.log(`Total contacts found: ${allContacts.length}`);

    // Group contacts by customer + name + phone + email
    const contactGroups = {};
    allContacts.forEach(contact => {
      const key = `${contact.customerId}-${contact.name}-${contact.phone || 'no-phone'}-${contact.email || 'no-email'}`;
      if (!contactGroups[key]) {
        contactGroups[key] = [];
      }
      contactGroups[key].push(contact);
    });

    // Find duplicate groups
    const duplicateGroups = Object.values(contactGroups).filter(group => group.length > 1);
    
    console.log(`Found ${duplicateGroups.length} duplicate contact groups`);

    if (duplicateGroups.length === 0) {
      console.log('✅ No duplicate contacts found');
      return;
    }

    // Step 2: Remove duplicates (keep oldest)
    console.log('\n=== REMOVING DUPLICATE CONTACTS ===');
    
    let totalDeleted = 0;
    for (const group of duplicateGroups) {
      // Sort by creation date (oldest first)
      group.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      
      // Keep the first (oldest), delete the rest
      const toKeep = group[0];
      const toDelete = group.slice(1);
      
      if (toDelete.length > 0) {
        console.log(`Deleting ${toDelete.length} duplicates for contact "${toKeep.name}" (Customer: ${toKeep.customerId})`);
        
        // Delete the duplicates
        await prisma.contact.deleteMany({
          where: {
            id: {
              in: toDelete.map(c => c.id)
            }
          }
        });
        
        totalDeleted += toDelete.length;
      }
    }

    console.log(`\nTotal duplicate contacts deleted: ${totalDeleted}`);

    // Step 3: Verify cleanup
    console.log('\n=== VERIFICATION ===');
    
    const remainingContacts = await prisma.contact.findMany();
    console.log(`Remaining contacts: ${remainingContacts.length}`);
    
    // Check specific customer
    const specificCustomer = await prisma.customer.findUnique({
      where: { id: '82c3c4ac-3f0c-4aee-b0bb-a5330ba490e9' },
      include: { contacts: true }
    });
    
    if (specificCustomer) {
      console.log(`\nSpecific customer "${specificCustomer.name}" now has ${specificCustomer.contacts.length} contacts:`);
      specificCustomer.contacts.forEach((contact, index) => {
        console.log(`  ${index + 1}. ${contact.name} (${contact.phone || 'no phone'})`);
      });
      
      // Check for remaining duplicates
      const names = specificCustomer.contacts.map(c => c.name);
      const uniqueNames = [...new Set(names)];
      
      if (names.length === uniqueNames.length) {
        console.log('✅ No duplicate names remaining for this customer');
      } else {
        console.log('⚠️ Still has duplicate names');
      }
    }

    console.log('\n✅ Contact duplicate cleanup completed successfully!');

  } catch (error) {
    console.error('Error fixing contact duplicates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixContactDuplicates();
