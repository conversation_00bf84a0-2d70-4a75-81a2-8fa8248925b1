'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { AMCRenewalForm } from '@/components/amc/amc-renewal-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { showErrorToast } from '@/lib/toast';

interface AMCContract {
  id: string;
  customerId: string;
  customer: {
    id: string;
    name: string;
    email?: string;
  };
  contactPersonId?: string;
  executiveId?: string;
  natureOfService?: string;
  startDate: string;
  endDate: string;
  warningDate?: string;
  amount: number;
  numberOfServices?: number;
  numberOfMachines?: number;
  totalTonnage?: number;
  status: string;
  contractNumber?: string;
  remarks?: string;
  machines?: any[];
  divisions?: any[];
}

/**
 * AMC Contract Renewal Page
 * 
 * This page provides a form for renewing existing AMC contracts.
 * It follows the UI standards and implements the requirements from Task 7.6.
 */
export default function AMCRenewalPage() {
  const params = useParams();
  const router = useRouter();
  const [contract, setContract] = useState<AMCContract | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const contractId = params?.id as string;

  // Fetch contract details
  useEffect(() => {
    const fetchContract = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/amc/contracts/${contractId}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('AMC contract not found');
          }
          throw new Error('Failed to fetch contract details');
        }

        const data = await response.json();
        setContract(data);
      } catch (error) {
        console.error('Error fetching contract:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch contract details';
        setError(errorMessage);
        showErrorToast('Error', errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (contractId) {
      fetchContract();
    }
  }, [contractId]);

  // Handle successful renewal
  const handleRenewalSuccess = (renewedContract: any) => {
    router.push(`/amc?renewed=${renewedContract.id}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <div className="flex items-center space-x-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <CardTitle>Loading Contract Details...</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center py-8">
              <p className="text-black">Please wait while we load the contract details.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-destructive text-white">
            <CardTitle>Error Loading Contract</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center py-8 space-y-4">
              <p className="text-black">{error || 'Contract not found'}</p>
              <div className="flex justify-center space-x-4">
                <Button asChild variant="outline">
                  <Link href="/amc">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to AMC List
                  </Link>
                </Button>
                <Button onClick={() => window.location.reload()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle>Renew AMC Contract</CardTitle>
            <CardDescription className="text-gray-100">
              Create a new contract based on the existing contract for {contract.customer.name}
            </CardDescription>
          </div>
          <Button asChild variant="secondary">
            <Link href="/amc">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to AMC List
            </Link>
          </Button>
        </CardHeader>
      </Card>

      {/* Renewal Form */}
      <AMCRenewalForm
        contract={contract}
        onSuccess={handleRenewalSuccess}
      />
    </div>
  );
}
