import type { Metadata } from 'next';
import './globals.css';
import { SessionProvider } from '@/components/providers/SessionProvider';
import { CustomToaster } from '@/components/ui/custom-toaster';

export const metadata: Metadata = {
  title: 'KoolSoft',
  description: 'KoolSoft Management System',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="font-sans antialiased">
        <SessionProvider>{children}</SessionProvider>
        <CustomToaster />
      </body>
    </html>
  );
}
