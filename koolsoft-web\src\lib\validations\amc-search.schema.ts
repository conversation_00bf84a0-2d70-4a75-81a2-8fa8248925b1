import * as z from 'zod';

/**
 * AMC Search Schema
 *
 * This schema defines the validation rules for AMC contract search parameters.
 * It provides comprehensive filtering options for the AMC list page.
 */
export const amcSearchSchema = z.object({
  query: z.string().optional().nullable(),
  customerId: z.string().uuid().optional().nullable(),
  executiveId: z.string().uuid().optional().nullable(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED', 'RENEWED', 'all', '']).optional().nullable(),
  startDateFrom: z.coerce.date().optional().nullable(),
  startDateTo: z.coerce.date().optional().nullable(),
  endDateFrom: z.coerce.date().optional().nullable(),
  endDateTo: z.coerce.date().optional().nullable(),
  amountMin: z.number().optional().nullable(),
  amountMax: z.number().optional().nullable(),
  skip: z.number().int().nonnegative().default(0),
  take: z.number().int().positive().default(10),
  sortField: z.string().default('startDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type AMCSearchParams = z.infer<typeof amcSearchSchema>;

/**
 * Convert search parameters to Prisma filter
 *
 * This function converts the search parameters to a Prisma-compatible filter object.
 * It handles special cases like date ranges and text search.
 */
export function convertSearchToFilter(params: AMCSearchParams): Record<string, any> {
  const filter: Record<string, any> = {};

  // Add customer filter
  if (params.customerId) {
    filter.customerId = params.customerId;
  }

  // Add executive filter
  if (params.executiveId) {
    filter.executiveId = params.executiveId;
  }

  // Add status filter (skip 'all' and empty values)
  if (params.status && params.status !== 'all' && params.status !== '' as any) {
    filter.status = params.status;
  }

  // Add date range filters
  if (params.startDateFrom || params.startDateTo) {
    filter.startDate = {};
    if (params.startDateFrom) {
      filter.startDate.gte = params.startDateFrom;
    }
    if (params.startDateTo) {
      filter.startDate.lte = params.startDateTo;
    }
  }

  if (params.endDateFrom || params.endDateTo) {
    filter.endDate = {};
    if (params.endDateFrom) {
      filter.endDate.gte = params.endDateFrom;
    }
    if (params.endDateTo) {
      filter.endDate.lte = params.endDateTo;
    }
  }

  // Add amount range filters
  if (params.amountMin || params.amountMax) {
    filter.amount = {};
    if (params.amountMin) {
      filter.amount.gte = params.amountMin;
    }
    if (params.amountMax) {
      filter.amount.lte = params.amountMax;
    }
  }

  // Add text search
  if (params.query) {
    filter.OR = [
      // Search in customer name
      {
        customer: {
          name: {
            contains: params.query,
            mode: 'insensitive'
          }
        }
      },
      // Search in contact person name
      {
        contactPerson: {
          name: {
            contains: params.query,
            mode: 'insensitive'
          }
        }
      },
      // Search in nature of service
      {
        natureOfService: {
          contains: params.query,
          mode: 'insensitive'
        }
      },
      // Search in contract number if it exists
      {
        id: {
          contains: params.query,
          mode: 'insensitive'
        }
      }
    ];
  }

  return filter;
}
