import { NextRequest, NextResponse } from 'next/server';
import { getAMCContractRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';

/**
 * GET /api/amc/contracts/expiring
 * Get AMC contracts that are expiring within a specified number of days
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const days = parseInt(searchParams.get('days') || '30');
      const skip = parseInt(searchParams.get('skip') || '0');
      const take = parseInt(searchParams.get('take') || '10');
      
      const amcContractRepository = getAMCContractRepository();
      
      // Get expiring contracts
      const contracts = await amcContractRepository.findExpiring(days, skip, take);
      
      // Count expiring contracts
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);
      
      const total = await amcContractRepository.count({
        endDate: {
          gte: today,
          lte: futureDate,
        },
        status: 'ACTIVE',
      });
      
      return NextResponse.json({
        data: contracts,
        pagination: {
          total,
          skip,
          take,
        },
        meta: {
          days,
        },
      });
    } catch (error) {
      console.error('Error fetching expiring AMC contracts:', error);
      return NextResponse.json(
        { error: 'Failed to fetch expiring AMC contracts' },
        { status: 500 }
      );
    }
  }
);
