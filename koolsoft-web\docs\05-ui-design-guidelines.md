# KoolSoft UI/UX Design Guidelines

This document provides comprehensive UI/UX design guidelines for the KoolSoft modernization project, ensuring a consistent, accessible, and user-friendly interface across the application. It includes detailed wireframes for module conversion workflows, email functionality components, and integration with the reporting system.

## Design Principles

1. **Simplicity**: Keep interfaces clean and focused on essential functionality
2. **Consistency**: Maintain consistent patterns, components, and terminology
3. **Efficiency**: Optimize workflows to minimize clicks and user effort
4. **Accessibility**: Ensure the application is usable by people with diverse abilities
5. **Responsiveness**: Design for all device sizes and orientations

## Color Palette

### Primary Colors

| Color Name | Hex Code | Usage |
|------------|----------|-------|
| Primary | #0F52BA | Primary buttons, links, active states |
| Primary Light | #3B7DED | Hover states, backgrounds |
| Primary Dark | #0A3882 | Pressed states, text on light backgrounds |

### Secondary Colors

| Color Name | Hex Code | Usage |
|------------|----------|-------|
| Secondary | #FF8C00 | Call-to-action elements, highlights |
| Secondary Light | #FFA333 | Hover states, backgrounds |
| Secondary Dark | #CC7000 | Pressed states, text on light backgrounds |

### Neutral Colors

| Color Name | Hex Code | Usage |
|------------|----------|-------|
| White | #FFFFFF | Backgrounds, text on dark colors |
| Gray-100 | #F3F4F6 | Backgrounds, dividers |
| Gray-200 | #E5E7EB | Borders, disabled states |
| Gray-300 | #D1D5DB | Disabled text, icons |
| Gray-400 | #9CA3AF | Secondary text |
| Gray-500 | #6B7280 | Placeholder text |
| Gray-600 | #4B5563 | Body text |
| Gray-700 | #374151 | Headings |
| Gray-800 | #1F2937 | Primary text |
| Gray-900 | #111827 | Emphasized text |
| Black | #000000 | Text on light backgrounds |

### Semantic Colors

| Color Name | Hex Code | Usage |
|------------|----------|-------|
| Success | #10B981 | Success messages, positive indicators |
| Warning | #F59E0B | Warning messages, caution indicators |
| Error | #EF4444 | Error messages, destructive actions |
| Info | #3B82F6 | Information messages, neutral indicators |

## Typography

### Font Family

```css
/* Tailwind CSS configuration */
fontFamily: {
  sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
  mono: ['JetBrains Mono', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
}
```

### Font Sizes

| Name | Size | Line Height | Usage |
|------|------|-------------|-------|
| xs | 0.75rem (12px) | 1rem (16px) | Small labels, footnotes |
| sm | 0.875rem (14px) | 1.25rem (20px) | Secondary text, captions |
| base | 1rem (16px) | 1.5rem (24px) | Body text |
| lg | 1.125rem (18px) | 1.75rem (28px) | Large body text |
| xl | 1.25rem (20px) | 1.75rem (28px) | Subheadings |
| 2xl | 1.5rem (24px) | 2rem (32px) | Section headings |
| 3xl | 1.875rem (30px) | 2.25rem (36px) | Page headings |
| 4xl | 2.25rem (36px) | 2.5rem (40px) | Large headings |

### Font Weights

| Name | Weight | Usage |
|------|--------|-------|
| light | 300 | Body text in some contexts |
| normal | 400 | Body text, regular content |
| medium | 500 | Emphasis, subheadings |
| semibold | 600 | Headings, important text |
| bold | 700 | Strong emphasis, primary headings |

## Spacing System

Use a consistent spacing scale based on 4px increments:

| Name | Size | Usage |
|------|------|-------|
| px | 1px | Borders |
| 0.5 | 0.125rem (2px) | Very tight spacing |
| 1 | 0.25rem (4px) | Tight spacing |
| 2 | 0.5rem (8px) | Default spacing between related items |
| 3 | 0.75rem (12px) | Spacing between related items |
| 4 | 1rem (16px) | Standard spacing between components |
| 5 | 1.25rem (20px) | Larger spacing |
| 6 | 1.5rem (24px) | Section spacing |
| 8 | 2rem (32px) | Large section spacing |
| 10 | 2.5rem (40px) | Very large spacing |
| 12 | 3rem (48px) | Page section spacing |
| 16 | 4rem (64px) | Major page section spacing |

## Component Guidelines

### Buttons

#### Primary Button

```jsx
<button className="bg-primary hover:bg-primary-light text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors">
  Primary Button
</button>
```

#### Secondary Button

```jsx
<button className="bg-white hover:bg-gray-100 text-primary border border-primary font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors">
  Secondary Button
</button>
```

#### Danger Button

```jsx
<button className="bg-error hover:bg-error-dark text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-error focus:ring-opacity-50 transition-colors">
  Danger Button
</button>
```

#### Disabled Button

```jsx
<button className="bg-gray-200 text-gray-400 font-medium py-2 px-4 rounded cursor-not-allowed" disabled>
  Disabled Button
</button>
```

### Form Inputs

#### Text Input

```jsx
<div className="mb-4">
  <label htmlFor="name" className="block text-gray-700 text-sm font-medium mb-2">
    Name
  </label>
  <input
    type="text"
    id="name"
    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
    placeholder="Enter your name"
  />
</div>
```

#### Select Input

```jsx
<div className="mb-4">
  <label htmlFor="country" className="block text-gray-700 text-sm font-medium mb-2">
    Country
  </label>
  <select
    id="country"
    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
  >
    <option value="">Select a country</option>
    <option value="us">United States</option>
    <option value="ca">Canada</option>
    <option value="mx">Mexico</option>
  </select>
</div>
```

#### Checkbox

```jsx
<div className="mb-4">
  <label className="flex items-center">
    <input
      type="checkbox"
      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
    />
    <span className="ml-2 text-gray-700">Remember me</span>
  </label>
</div>
```

### Tables

```jsx
<div className="overflow-x-auto">
  <table className="min-w-full divide-y divide-gray-200">
    <thead className="bg-gray-50">
      <tr>
        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Name
        </th>
        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Email
        </th>
        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Status
        </th>
        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Actions
        </th>
      </tr>
    </thead>
    <tbody className="bg-white divide-y divide-gray-200">
      <tr>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          John Doe
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          <EMAIL>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            Active
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          <button className="text-primary hover:text-primary-dark">Edit</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
```

### Cards

```jsx
<div className="bg-white rounded-lg shadow-md overflow-hidden">
  <div className="px-6 py-4">
    <h3 className="text-lg font-semibold text-gray-800 mb-2">Card Title</h3>
    <p className="text-gray-600">Card content goes here. This is a basic card component.</p>
  </div>
  <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
    <button className="text-primary hover:text-primary-dark font-medium">
      Card Action
    </button>
  </div>
</div>
```

## Layout Guidelines

### Page Layout

```jsx
<div className="min-h-screen bg-gray-100">
  {/* Header */}
  <header className="bg-white shadow-sm">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between h-16">
        {/* Logo and navigation */}
        <div className="flex">
          <div className="flex-shrink-0 flex items-center">
            <img className="h-8 w-auto" src="/logo.svg" alt="KoolSoft" />
          </div>
          <nav className="ml-6 flex space-x-8">
            {/* Navigation items */}
          </nav>
        </div>

        {/* User menu */}
        <div className="flex items-center">
          {/* User dropdown */}
        </div>
      </div>
    </div>
  </header>

  {/* Main content */}
  <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div className="bg-white shadow-sm rounded-lg p-6">
      {/* Page content */}
    </div>
  </main>

  {/* Footer */}
  <footer className="bg-white border-t border-gray-200 mt-auto">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <p className="text-gray-500 text-center">
        &copy; {new Date().getFullYear()} KoolSoft. All rights reserved.
      </p>
    </div>
  </footer>
</div>
```

### Responsive Breakpoints

| Name | Width | Description |
|------|-------|-------------|
| sm | 640px | Small devices (mobile) |
| md | 768px | Medium devices (tablets) |
| lg | 1024px | Large devices (laptops) |
| xl | 1280px | Extra large devices (desktops) |
| 2xl | 1536px | Extra extra large devices (large desktops) |

## Accessibility Guidelines

### Color Contrast

- Maintain a minimum contrast ratio of 4.5:1 for normal text
- Maintain a minimum contrast ratio of 3:1 for large text (18pt or 14pt bold)
- Use the WebAIM Contrast Checker to verify contrast ratios

### Keyboard Navigation

- Ensure all interactive elements are keyboard accessible
- Use proper focus styles for interactive elements
- Maintain a logical tab order

### Screen Readers

- Use semantic HTML elements (e.g., `<button>`, `<nav>`, `<main>`)
- Include proper ARIA attributes when necessary
- Provide alternative text for images

### Form Accessibility

- Associate labels with form controls using the `for` attribute
- Group related form controls with `<fieldset>` and `<legend>`
- Provide clear error messages and validation feedback

## Animation Guidelines

### Transitions

- Use subtle transitions for state changes (hover, focus, etc.)
- Keep transition durations between 150ms and 300ms
- Use appropriate easing functions (e.g., ease-in-out)

```css
.transition-standard {
  transition: all 200ms ease-in-out;
}
```

### Loading States

- Use skeleton screens for content loading
- Implement loading spinners for actions
- Provide feedback for long-running operations

## Icon System

- Use a consistent icon set (Heroicons recommended)
- Maintain consistent icon sizes
- Ensure icons have appropriate alternative text

```jsx
import { HomeIcon, UserIcon, CogIcon } from '@heroicons/react/outline';

// Example usage
<HomeIcon className="h-5 w-5 text-gray-500" aria-hidden="true" />
```

## Email Functionality Components

The KoolSoft application requires email functionality for various purposes including notifications, reports, and service updates. This section provides guidelines and components for implementing email features.

### Email Template System

The email system will use React-based components to generate HTML emails with a consistent design:

```jsx
// components/email/EmailTemplate.tsx
export function EmailTemplate({
  title,
  preheader,
  children,
  footerText = "© KoolSoft. All rights reserved."
}) {
  return (
    <div style={{
      fontFamily: 'Inter, Helvetica, Arial, sans-serif',
      backgroundColor: '#f9fafb',
      padding: '20px',
      maxWidth: '600px',
      margin: '0 auto',
    }}>
      <table width="100%" cellPadding="0" cellSpacing="0" style={{ width: '100%' }}>
        <tr>
          <td style={{
            backgroundColor: '#0F52BA',
            padding: '20px',
            textAlign: 'center',
            color: 'white',
            borderTopLeftRadius: '4px',
            borderTopRightRadius: '4px',
          }}>
            <img
              src="https://example.com/logo-white.png"
              alt="KoolSoft Logo"
              style={{ height: '40px', width: 'auto' }}
            />
          </td>
        </tr>
        <tr>
          <td style={{
            backgroundColor: 'white',
            padding: '30px',
            borderBottomLeftRadius: '4px',
            borderBottomRightRadius: '4px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          }}>
            {preheader && (
              <div style={{
                display: 'none',
                maxHeight: 0,
                overflow: 'hidden',
              }}>
                {preheader}
              </div>
            )}

            <h1 style={{
              color: '#1F2937',
              fontSize: '24px',
              fontWeight: 600,
              marginTop: 0,
              marginBottom: '24px',
            }}>
              {title}
            </h1>

            <div style={{ color: '#4B5563', fontSize: '16px' }}>
              {children}
            </div>
          </td>
        </tr>
        <tr>
          <td style={{
            textAlign: 'center',
            padding: '20px',
            color: '#6B7280',
            fontSize: '14px',
          }}>
            {footerText}
          </td>
        </tr>
      </table>
    </div>
  );
}
```

### Email Types and Templates

#### 1. Service Notification Email

```jsx
// components/email/ServiceNotificationEmail.tsx
import { EmailTemplate } from './EmailTemplate';
import { formatDate } from '@/lib/utils/formatters';

export function ServiceNotificationEmail({
  customerName,
  serviceDatetime,
  serviceType,
  technicianName,
  serviceId,
  contactPhone,
}) {
  const preheader = `Your service appointment is scheduled for ${formatDate(serviceDatetime)}`;

  return (
    <EmailTemplate
      title="Service Appointment Confirmation"
      preheader={preheader}
    >
      <p>Dear {customerName},</p>

      <p>This is to confirm your upcoming service appointment:</p>

      <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '24px' }}>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Service ID:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{serviceId}</td>
        </tr>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Date & Time:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{formatDate(serviceDatetime)}</td>
        </tr>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Service Type:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{serviceType}</td>
        </tr>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Technician:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{technicianName}</td>
        </tr>
      </table>

      <p>If you need to reschedule or have any questions, please contact us at {contactPhone}.</p>

      <div style={{
        marginTop: '24px',
        textAlign: 'center',
      }}>
        <a
          href={`https://example.com/service/${serviceId}`}
          style={{
            backgroundColor: '#0F52BA',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '4px',
            textDecoration: 'none',
            fontWeight: 500,
            display: 'inline-block',
          }}
        >
          View Service Details
        </a>
      </div>

      <p style={{ marginTop: '24px' }}>Thank you for choosing KoolSoft for your service needs.</p>

      <p>Best regards,<br />The KoolSoft Team</p>
    </EmailTemplate>
  );
}
```

#### 2. AMC Renewal Reminder Email

```jsx
// components/email/AMCRenewalEmail.tsx
import { EmailTemplate } from './EmailTemplate';
import { formatDate, formatCurrency } from '@/lib/utils/formatters';

export function AMCRenewalEmail({
  customerName,
  amcId,
  expiryDate,
  renewalAmount,
  machineCount,
  contactPerson,
  contactPhone,
}) {
  const preheader = `Your AMC contract #${amcId} expires on ${formatDate(expiryDate)}`;
  const daysRemaining = Math.ceil((new Date(expiryDate) - new Date()) / (1000 * 60 * 60 * 24));

  return (
    <EmailTemplate
      title="AMC Renewal Reminder"
      preheader={preheader}
    >
      <p>Dear {customerName},</p>

      <p>This is a friendly reminder that your Annual Maintenance Contract (AMC) is due for renewal:</p>

      <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '24px' }}>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>AMC ID:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{amcId}</td>
        </tr>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Expiry Date:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{formatDate(expiryDate)} ({daysRemaining} days remaining)</td>
        </tr>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Machines Covered:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{machineCount}</td>
        </tr>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Renewal Amount:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{formatCurrency(renewalAmount)}</td>
        </tr>
      </table>

      <p>Renewing your AMC ensures continued maintenance support for your equipment, minimizing downtime and extending the life of your machines.</p>

      <div style={{
        marginTop: '24px',
        textAlign: 'center',
      }}>
        <a
          href={`https://example.com/amc/${amcId}/renew`}
          style={{
            backgroundColor: '#0F52BA',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '4px',
            textDecoration: 'none',
            fontWeight: 500,
            display: 'inline-block',
          }}
        >
          Renew AMC Now
        </a>
      </div>

      <p style={{ marginTop: '24px' }}>If you have any questions about your renewal, please contact {contactPerson} at {contactPhone}.</p>

      <p>Thank you for your continued trust in KoolSoft.</p>

      <p>Best regards,<br />The KoolSoft Team</p>
    </EmailTemplate>
  );
}
```

#### 3. Report Email

```jsx
// components/email/ReportEmail.tsx
import { EmailTemplate } from './EmailTemplate';

export function ReportEmail({
  recipientName,
  reportName,
  reportDate,
  reportDescription,
  reportUrl,
  attachmentName,
}) {
  const preheader = `Your ${reportName} report for ${reportDate} is ready`;

  return (
    <EmailTemplate
      title={`${reportName} Report`}
      preheader={preheader}
    >
      <p>Dear {recipientName},</p>

      <p>Your requested report is now available:</p>

      <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '24px' }}>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Report:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{reportName}</td>
        </tr>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Date:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{reportDate}</td>
        </tr>
        <tr>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB', fontWeight: 600 }}>Description:</td>
          <td style={{ padding: '8px', borderBottom: '1px solid #E5E7EB' }}>{reportDescription}</td>
        </tr>
      </table>

      <p>You can access the report by clicking the button below or find it attached to this email.</p>

      <div style={{
        marginTop: '24px',
        textAlign: 'center',
      }}>
        <a
          href={reportUrl}
          style={{
            backgroundColor: '#0F52BA',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '4px',
            textDecoration: 'none',
            fontWeight: 500,
            display: 'inline-block',
          }}
        >
          View Report Online
        </a>
      </div>

      <p style={{ marginTop: '24px' }}>If you have any questions about this report, please contact our support team.</p>

      <p>Best regards,<br />The KoolSoft Team</p>
    </EmailTemplate>
  );
}
```

### Email Sending Implementation

The application will use the Nodemailer library with React Email for sending emails:

```typescript
// lib/email/sendEmail.ts
import nodemailer from 'nodemailer';
import { renderToStaticMarkup } from 'react-dom/server';

// Configure transporter
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

// Function to send email
export async function sendEmail({
  to,
  subject,
  component,
  attachments = [],
}) {
  // Convert React component to HTML
  const html = renderToStaticMarkup(component);

  // Send email
  const info = await transporter.sendMail({
    from: `"KoolSoft" <${process.env.EMAIL_FROM}>`,
    to,
    subject,
    html,
    attachments,
  });

  return info;
}
```

### Email Notification Settings UI

The application will include a UI for users to manage their email notification preferences:

```jsx
// components/settings/EmailNotificationSettings.tsx
import { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';

export function EmailNotificationSettings({ initialSettings, onSave }) {
  const [settings, setSettings] = useState(initialSettings);

  const handleToggle = (key) => {
    setSettings({
      ...settings,
      [key]: !settings[key],
    });
  };

  const handleSave = async () => {
    try {
      await onSave(settings);
    } catch (error) {
      console.error('Error saving notification settings:', error);
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-800">Email Notification Settings</h2>

      <div className="bg-white rounded-lg shadow-sm p-6 space-y-4">
        <div className="flex items-center justify-between py-3 border-b border-gray-200">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Service Appointment Reminders</h3>
            <p className="text-sm text-gray-500">Receive notifications about upcoming service appointments</p>
          </div>
          <Switch
            checked={settings.serviceReminders}
            onCheckedChange={() => handleToggle('serviceReminders')}
          />
        </div>

        <div className="flex items-center justify-between py-3 border-b border-gray-200">
          <div>
            <h3 className="text-sm font-medium text-gray-900">AMC Renewal Notifications</h3>
            <p className="text-sm text-gray-500">Receive reminders when AMCs are due for renewal</p>
          </div>
          <Switch
            checked={settings.amcRenewalReminders}
            onCheckedChange={() => handleToggle('amcRenewalReminders')}
          />
        </div>

        <div className="flex items-center justify-between py-3 border-b border-gray-200">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Report Notifications</h3>
            <p className="text-sm text-gray-500">Receive emails when reports are generated</p>
          </div>
          <Switch
            checked={settings.reportNotifications}
            onCheckedChange={() => handleToggle('reportNotifications')}
          />
        </div>

        <div className="flex items-center justify-between py-3 border-b border-gray-200">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Service Completion Notifications</h3>
            <p className="text-sm text-gray-500">Receive notifications when services are completed</p>
          </div>
          <Switch
            checked={settings.serviceCompletionNotifications}
            onCheckedChange={() => handleToggle('serviceCompletionNotifications')}
          />
        </div>

        <div className="flex items-center justify-between py-3">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Marketing Updates</h3>
            <p className="text-sm text-gray-500">Receive promotional emails and product updates</p>
          </div>
          <Switch
            checked={settings.marketingEmails}
            onCheckedChange={() => handleToggle('marketingEmails')}
          />
        </div>
      </div>

      <div className="flex justify-end">
        <Button onClick={handleSave}>
          Save Preferences
        </Button>
      </div>
    </div>
  );
}
```

## Implementation with Tailwind CSS

### Tailwind Configuration

Create a `tailwind.config.js` file with the following configuration:

```javascript
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#0F52BA',
          light: '#3B7DED',
          dark: '#0A3882',
        },
        secondary: {
          DEFAULT: '#FF8C00',
          light: '#FFA333',
          dark: '#CC7000',
        },
        success: '#10B981',
        warning: '#F59E0B',
        error: '#EF4444',
        info: '#3B82F6',
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        mono: ['JetBrains Mono', 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
};
```

### Component Library

Consider using a Tailwind-based component library like Headless UI or Radix UI for complex components:

```bash
npm install @headlessui/react @tailwindcss/forms
```

## Reporting System Integration

The KoolSoft application includes a comprehensive reporting system that has been migrated from Crystal Reports to modern web-based reports. This section provides guidelines for integrating the reporting system with the UI. For a complete inventory of reports and detailed migration strategies, refer to [06-reporting-guide.md](./06-reporting-guide.md).

### Report Viewer Component

The Report Viewer component provides a consistent interface for viewing and interacting with reports:

```jsx
// components/reports/ReportViewer.tsx
import { useState } from 'react';
import { ReportParameters } from '@/components/reports/common/ReportParameters';
import { ReportToolbar } from '@/components/reports/common/ReportToolbar';
import { Spinner } from '@/components/ui/spinner';

export function ReportViewer({
  reportType,
  title,
  description,
  defaultParameters = {},
}) {
  const [parameters, setParameters] = useState(defaultParameters);
  const [reportData, setReportData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleGenerateReport = async (params) => {
    setParameters(params);
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/reports/${reportType}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });
      // Note: This endpoint is implemented in app/api/reports/[reportType]/route.ts

      if (!response.ok) {
        throw new Error('Failed to generate report');
      }

      const data = await response.json();
      setReportData(data);
    } catch (err) {
      console.error('Error generating report:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const renderReportContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-64">
          <Spinner className="h-8 w-8 text-primary" />
          <span className="ml-2 text-gray-600">Generating report...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 text-red-700">
          <p className="font-medium">Error generating report</p>
          <p>{error}</p>
        </div>
      );
    }

    if (!reportData) {
      return (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 text-blue-700">
          <p>Set parameters and click "Generate Report" to view the report.</p>
        </div>
      );
    }

    // Render the appropriate report component based on reportType
    const ReportComponent = getReportComponent(reportType);
    return <ReportComponent data={reportData} parameters={parameters} />;
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h1 className="text-2xl font-semibold text-gray-800 mb-2">{title}</h1>
        {description && <p className="text-gray-600 mb-4">{description}</p>}

        <ReportParameters
          reportType={reportType}
          defaultValues={parameters}
          onSubmit={handleGenerateReport}
        />
      </div>

      {(reportData || isLoading || error) && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <ReportToolbar
            reportData={reportData}
            reportType={reportType}
            title={title}
            parameters={parameters}
          />

          <div className="mt-4">
            {renderReportContent()}
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function to get the appropriate report component
function getReportComponent(reportType) {
  const reportComponents = {
    'amc-summary': AMCSummaryReport,
    'amc-detail': AMCDetailReport,
    'service-summary': ServiceSummaryReport,
    // Add more report components as needed
  };

  return reportComponents[reportType] || DefaultReport;
}
```

### Report Dashboard

The Report Dashboard provides a centralized interface for accessing all reports:

```jsx
// components/reports/ReportDashboard.tsx
import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search } from '@/components/ui/search';
import Link from 'next/link';

export function ReportDashboard() {
  const [searchQuery, setSearchQuery] = useState('');

  const reportCategories = [
    {
      id: 'amc',
      name: 'AMC Reports',
      reports: [
        { id: 'amc-summary', name: 'AMC Summary', description: 'Summary of all AMC contracts' },
        { id: 'amc-detail', name: 'AMC Detail', description: 'Detailed report of a specific AMC contract' },
        { id: 'amc-payment', name: 'AMC Payment', description: 'Payment details for AMC contracts' },
        { id: 'amc-renewal', name: 'AMC Renewal Due', description: 'List of AMCs due for renewal' },
        { id: 'amc-service', name: 'AMC Service Schedule', description: 'Service schedule for AMCs' },
      ],
    },
    {
      id: 'warranty',
      name: 'Warranty Reports',
      reports: [
        { id: 'inw-summary', name: 'In-Warranty Summary', description: 'Summary of all in-warranty products' },
        { id: 'inw-detail', name: 'In-Warranty Detail', description: 'Detailed report of a specific in-warranty' },
        { id: 'inw-expiry', name: 'In-Warranty Expiry', description: 'List of warranties nearing expiry' },
        { id: 'otw-summary', name: 'Out-Warranty Summary', description: 'Summary of all out-of-warranty products' },
        { id: 'otw-detail', name: 'Out-Warranty Detail', description: 'Detailed report of a specific out-of-warranty' },
      ],
    },
    {
      id: 'service',
      name: 'Service Reports',
      reports: [
        { id: 'service-summary', name: 'Service Summary', description: 'Summary of all service reports' },
        { id: 'service-detail', name: 'Service Detail', description: 'Detailed report of a specific service' },
        { id: 'service-executive', name: 'Service by Executive', description: 'Services grouped by executive' },
        { id: 'service-customer', name: 'Service by Customer', description: 'Services grouped by customer' },
        { id: 'service-product', name: 'Service by Product', description: 'Services grouped by product' },
      ],
    },
    {
      id: 'sales',
      name: 'Sales Reports',
      reports: [
        { id: 'sales-lead', name: 'Sales Lead Summary', description: 'Summary of all sales leads' },
        { id: 'sales-pipeline', name: 'Sales Pipeline', description: 'Sales pipeline analysis' },
        { id: 'sales-forecast', name: 'Sales Forecast', description: 'Sales forecast report' },
        { id: 'sales-executive', name: 'Sales by Executive', description: 'Sales grouped by executive' },
        { id: 'sales-product', name: 'Sales by Product', description: 'Sales grouped by product' },
      ],
    },
    {
      id: 'admin',
      name: 'Administrative Reports',
      reports: [
        { id: 'customer-list', name: 'Customer List', description: 'List of all customers' },
        { id: 'executive-performance', name: 'Executive Performance', description: 'Performance analysis of executives' },
        { id: 'product-inventory', name: 'Product Inventory', description: 'Inventory status of products' },
        { id: 'financial-summary', name: 'Financial Summary', description: 'Financial summary report' },
        { id: 'user-activity', name: 'User Activity', description: 'User activity log report' },
      ],
    },
  ];

  const filteredCategories = reportCategories.map(category => ({
    ...category,
    reports: category.reports.filter(report =>
      report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      report.description.toLowerCase().includes(searchQuery.toLowerCase())
    ),
  })).filter(category => category.reports.length > 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-800">Reports</h1>
        <Search
          placeholder="Search reports..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <Tabs defaultValue="amc">
        <TabsList className="mb-4">
          {reportCategories.map(category => (
            <TabsTrigger key={category.id} value={category.id}>
              {category.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {reportCategories.map(category => (
          <TabsContent key={category.id} value={category.id}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {category.reports
                .filter(report =>
                  report.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  report.description.toLowerCase().includes(searchQuery.toLowerCase())
                )
                .map(report => (
                  <Link key={report.id} href={`/reports/${report.id}`}>
                    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                      <h3 className="text-lg font-medium text-gray-800">{report.name}</h3>
                      <p className="text-gray-600 text-sm mt-1">{report.description}</p>
                    </Card>
                  </Link>
                ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
```

### Report Export Options

The Report Toolbar component provides options for exporting reports in different formats:

```jsx
// components/reports/ReportToolbar.tsx
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ChevronDownIcon, PrinterIcon, DownloadIcon, MailIcon, RefreshIcon } from '@heroicons/react/outline';
import { exportToPdf, exportToExcel } from '@/lib/reports/exporters';
import { sendReportByEmail } from '@/lib/reports/emailSender';

export function ReportToolbar({ reportData, reportType, title, parameters }) {
  if (!reportData) return null;

  const handleExportPdf = () => {
    exportToPdf(title, reportData, parameters);
  };

  const handleExportExcel = () => {
    exportToExcel(title, reportData, parameters);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleEmailReport = async () => {
    try {
      await sendReportByEmail({
        reportType,
        reportData,
        title,
        parameters,
      });
      alert('Report sent successfully');
    } catch (error) {
      console.error('Error sending report by email:', error);
      alert('Failed to send report by email');
    }
  };

  return (
    <div className="flex justify-between items-center pb-4 border-b border-gray-200">
      <h2 className="text-xl font-medium text-gray-800">{title}</h2>

      <div className="flex space-x-2">
        <Button variant="outline" size="sm" onClick={handlePrint}>
          <PrinterIcon className="h-4 w-4 mr-1" />
          Print
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <DownloadIcon className="h-4 w-4 mr-1" />
              Export
              <ChevronDownIcon className="h-4 w-4 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={handleExportPdf}>
              Export as PDF
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleExportExcel}>
              Export as Excel
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button variant="outline" size="sm" onClick={handleEmailReport}>
          <MailIcon className="h-4 w-4 mr-1" />
          Email
        </Button>
      </div>
    </div>
  );
}
```

### Mobile-Responsive Reports

Reports should be designed to be responsive and usable on mobile devices:

1. Tables should horizontally scroll when they don't fit on small screens:
```jsx
<div className="overflow-x-auto -mx-4 sm:mx-0">
  <ReportTable
    columns={columns}
    data={data}
  />
</div>
```

2. Charts should resize appropriately for different screen sizes:
```jsx
<div className="w-full h-[300px] md:h-[400px]">
  <ReportChart
    type="bar"
    data={chartData}
    options={{
      responsive: true,
      maintainAspectRatio: false,
    }}
  />
</div>
```

3. Report parameters should stack vertically on small screens:
```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Parameter fields */}
</div>
```

4. Export options should be accessible via a dropdown menu on mobile:
```jsx
<div className="hidden sm:flex space-x-2">
  <Button variant="outline" size="sm" onClick={handlePrint}>
    <PrinterIcon className="h-4 w-4 mr-1" />
    Print
  </Button>
  <Button variant="outline" size="sm" onClick={handleExportPdf}>
    <DownloadIcon className="h-4 w-4 mr-1" />
    Export PDF
  </Button>
</div>
<div className="sm:hidden">
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="outline" size="sm">
        <MoreVerticalIcon className="h-4 w-4" />
        Options
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent>
      <DropdownMenuItem onClick={handlePrint}>
        <PrinterIcon className="h-4 w-4 mr-2" />
        Print
      </DropdownMenuItem>
      <DropdownMenuItem onClick={handleExportPdf}>
        <DownloadIcon className="h-4 w-4 mr-2" />
        Export PDF
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</div>
```

### Report Scheduling Interface

The Report Scheduling interface allows users to schedule reports to be generated and sent automatically:

```jsx
// components/reports/ReportScheduler.tsx
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { DatePicker } from '@/components/ui/date-picker';

export function ReportScheduler({ reportType, reportName }) {
  const [schedule, setSchedule] = useState({
    frequency: 'weekly',
    dayOfWeek: 1, // Monday
    dayOfMonth: 1,
    time: '08:00',
    startDate: new Date(),
    endDate: null,
    recipients: '',
    format: 'pdf',
    active: true,
  });

  const handleChange = (field, value) => {
    setSchedule({
      ...schedule,
      [field]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/reports/schedules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportType,
          reportName,
          schedule,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to schedule report');
      }

      alert('Report scheduled successfully');
    } catch (error) {
      console.error('Error scheduling report:', error);
      alert('Failed to schedule report');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Schedule Report: {reportName}</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Frequency
            </label>
            <Select
              value={schedule.frequency}
              onChange={(e) => handleChange('frequency', e.target.value)}
              className="w-full"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </Select>
          </div>

          {schedule.frequency === 'weekly' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Day of Week
              </label>
              <Select
                value={schedule.dayOfWeek}
                onChange={(e) => handleChange('dayOfWeek', parseInt(e.target.value))}
                className="w-full"
              >
                <option value={1}>Monday</option>
                <option value={2}>Tuesday</option>
                <option value={3}>Wednesday</option>
                <option value={4}>Thursday</option>
                <option value={5}>Friday</option>
                <option value={6}>Saturday</option>
                <option value={0}>Sunday</option>
              </Select>
            </div>
          )}

          {schedule.frequency === 'monthly' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Day of Month
              </label>
              <Select
                value={schedule.dayOfMonth}
                onChange={(e) => handleChange('dayOfMonth', parseInt(e.target.value))}
                className="w-full"
              >
                {[...Array(31)].map((_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {i + 1}
                  </option>
                ))}
              </Select>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Time
            </label>
            <Input
              type="time"
              value={schedule.time}
              onChange={(e) => handleChange('time', e.target.value)}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <DatePicker
              selected={schedule.startDate}
              onChange={(date) => handleChange('startDate', date)}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date (Optional)
            </label>
            <DatePicker
              selected={schedule.endDate}
              onChange={(date) => handleChange('endDate', date)}
              isClearable
              placeholderText="No end date"
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Recipients (comma-separated emails)
            </label>
            <Input
              type="text"
              value={schedule.recipients}
              onChange={(e) => handleChange('recipients', e.target.value)}
              placeholder="<EMAIL>, <EMAIL>"
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Format
            </label>
            <Select
              value={schedule.format}
              onChange={(e) => handleChange('format', e.target.value)}
              className="w-full"
            >
              <option value="pdf">PDF</option>
              <option value="excel">Excel</option>
              <option value="both">Both PDF and Excel</option>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={schedule.active}
              onCheckedChange={(checked) => handleChange('active', checked)}
            />
            <label className="text-sm font-medium text-gray-700">
              Active
            </label>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit">
          Schedule Report
        </Button>
      </div>
    </form>
  );
}
```

## Design-to-Code Workflow

1. Create reusable components based on these guidelines
2. Store components in the `components/ui` directory
3. Document component usage with JSDoc comments
4. Create a component storybook for reference (optional)

## Module Conversion Workflows

This section provides wireframes and implementation guidelines for converting key modules from the legacy VB6 application to modern React components.

### Customer Management Module Workflow

The Customer Management module allows users to create, view, edit, and delete customer records.

#### Customer List View

```jsx
// components/customers/CustomerList.tsx
export function CustomerList() {
  const [customers, setCustomers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    async function fetchCustomers() {
      setIsLoading(true);
      try {
        const response = await fetch('/api/customers');
        const data = await response.json();
        setCustomers(data);
      } catch (error) {
        console.error('Error fetching customers:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchCustomers();
  }, []);

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.contactPerson.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.phone.includes(searchQuery)
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-800">Customers</h1>
        <Link href="/customers/new">
          <a className="bg-primary hover:bg-primary-light text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 transition-colors">
            Add New Customer
          </a>
        </Link>
      </div>

      <div className="relative">
        <input
          type="text"
          placeholder="Search customers..."
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <SearchIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <Spinner className="h-8 w-8 text-primary" />
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contact Person
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phone
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCustomers.map((customer) => (
                <tr key={customer.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {customer.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {customer.contactPerson}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {customer.phone}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {customer.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 space-x-2">
                    <Link href={`/customers/${customer.id}`}>
                      <a className="text-primary hover:text-primary-dark">View</a>
                    </Link>
                    <Link href={`/customers/${customer.id}/edit`}>
                      <a className="text-primary hover:text-primary-dark">Edit</a>
                    </Link>
                    <button
                      className="text-error hover:text-error-dark"
                      onClick={() => handleDeleteCustomer(customer.id)}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
```

#### Customer Detail Wireframe

```
+-----------------------------------------------+
|                                               |
|  Customer Details                      Edit   |
|                                               |
|  +-------------------+  +-------------------+ |
|  | General Info      |  | Contact Info      | |
|  +-------------------+  +-------------------+ |
|  | Name: ABC Corp    |  | Contact: John Doe | |
|  | Type: Corporate   |  | Phone: 555-1234   | |
|  | Industry: Tech    |  | Email: <EMAIL>  | |
|  | GST No: GSTIN123  |  | Address: 123 St   | |
|  +-------------------+  +-------------------+ |
|                                               |
|  +-------------------------------------------+|
|  | Related Records                           ||
|  +-------------------------------------------+|
|  | AMCs (3)  | Warranties (2) | Services (5) ||
|  +-------------------------------------------+|
|                                               |
|  [View AMCs] [View Warranties] [View Services]|
|                                               |
+-----------------------------------------------+
```

### AMC Management Module Workflow

The AMC (Annual Maintenance Contract) module allows users to create and manage maintenance contracts.

#### AMC Creation Workflow

1. **Step 1: Select Customer**
   - Search and select customer from dropdown
   - Option to create new customer if not found

2. **Step 2: Contract Details**
   - Enter contract start and end dates
   - Enter contract amount and payment terms
   - Select contract type and priority

3. **Step 3: Add Machines**
   - Add machines covered under the AMC
   - Option to add new machines if not in database
   - Specify service frequency for each machine

4. **Step 4: Review and Create**
   - Review all contract details
   - Generate contract document
   - Save and create AMC record

#### AMC Form Wireframe

```
+-----------------------------------------------+
|                                               |
|  Create AMC Contract                          |
|                                               |
|  +-------------------------------------------+|
|  | Step 1 | Step 2 | Step 3 | Step 4         ||
|  +-------------------------------------------+|
|                                               |
|  Customer Information:                        |
|  +-------------------------------------------+|
|  | Customer: [Dropdown Search ▼]             ||
|  | Contact Person: John Doe                  ||
|  | Phone: 555-1234                           ||
|  | Email: <EMAIL>                   ||
|  +-------------------------------------------+|
|                                               |
|  Contract Details:                            |
|  +-------------------------------------------+|
|  | Start Date: [Date Picker]                 ||
|  | End Date: [Date Picker]                   ||
|  | Contract Amount: [Input Field]            ||
|  | Payment Terms: [Dropdown ▼]               ||
|  | Contract Type: [Dropdown ▼]               ||
|  | Priority: [Dropdown ▼]                    ||
|  +-------------------------------------------+|
|                                               |
|  [Previous]                [Next]             |
|                                               |
+-----------------------------------------------+
```

### Service Module Workflow

The Service module allows technicians to log service visits and track service history.

#### Service Request Workflow

1. **Step 1: Identify Customer/Machine**
   - Search by customer name, phone, or machine serial number
   - Select from AMC or warranty coverage if applicable

2. **Step 2: Log Service Request**
   - Enter complaint details
   - Assign priority and technician
   - Schedule service date and time

3. **Step 3: Service Execution**
   - Technician logs work performed
   - Records parts replaced
   - Captures customer signature

4. **Step 4: Closure**
   - Mark service as complete
   - Generate service report
   - Send to customer via email

#### Service Form Wireframe

```
+-----------------------------------------------+
|                                               |
|  Service Request                              |
|                                               |
|  +-------------------------------------------+|
|  | Customer: ABC Corporation                 ||
|  | Machine: HP LaserJet Pro M404             ||
|  | Serial No: SN12345678                     ||
|  | Coverage: Under AMC (Valid till 31/12/23) ||
|  +-------------------------------------------+|
|                                               |
|  Service Details:                             |
|  +-------------------------------------------+|
|  | Complaint: [Textarea for description]     ||
|  |                                           ||
|  | Priority: [Dropdown ▼]                    ||
|  | Assigned To: [Dropdown ▼]                 ||
|  | Scheduled Date: [Date Picker]             ||
|  | Scheduled Time: [Time Picker]             ||
|  +-------------------------------------------+|
|                                               |
|  [Cancel]                [Create Service]     |
|                                               |
+-----------------------------------------------+
```

## Legacy UI Migration Mapping

| Legacy UI Element | Modern Equivalent |
|-------------------|-------------------|
| VB6 Form | React component with form elements |
| Tab control | Tabbed interface with state management |
| List view | React table component with sorting and pagination |
| Combo box | Select dropdown with search functionality |
| Command button | Primary or secondary button component |
| Text box | Input component with validation |
| Check box | Checkbox component |
| Option button | Radio button component |
| Frame | Card or panel component |
| Menu | Navigation component with dropdowns |

## Example: Converting VB6 Tab Control to React

```jsx
// Legacy VB6 code:
// SSTab1 with tabs "AMC General", "Machine Details", "Payment Details"

// React equivalent:
import { useState } from 'react';
import { Tab } from '@headlessui/react';
import clsx from 'clsx';

export default function AMCForm() {
  const [selectedTab, setSelectedTab] = useState(0);

  return (
    <div className="w-full max-w-3xl mx-auto">
      <Tab.Group selectedIndex={selectedTab} onChange={setSelectedTab}>
        <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1">
          <Tab
            className={({ selected }) =>
              clsx(
                'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-white shadow text-primary'
                  : 'text-gray-600 hover:bg-white/[0.12] hover:text-primary'
              )
            }
          >
            AMC General
          </Tab>
          <Tab
            className={({ selected }) =>
              clsx(
                'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-white shadow text-primary'
                  : 'text-gray-600 hover:bg-white/[0.12] hover:text-primary'
              )
            }
          >
            Machine Details
          </Tab>
          <Tab
            className={({ selected }) =>
              clsx(
                'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-white shadow text-primary'
                  : 'text-gray-600 hover:bg-white/[0.12] hover:text-primary'
              )
            }
          >
            Payment Details
          </Tab>
        </Tab.List>
        <Tab.Panels className="mt-2">
          <Tab.Panel className="rounded-xl bg-white p-3 ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2">
            {/* AMC General form fields */}
          </Tab.Panel>
          <Tab.Panel className="rounded-xl bg-white p-3 ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2">
            {/* Machine Details form fields */}
          </Tab.Panel>
          <Tab.Panel className="rounded-xl bg-white p-3 ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2">
            {/* Payment Details form fields */}
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
}
