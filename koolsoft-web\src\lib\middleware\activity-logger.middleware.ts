import { NextRequest, NextResponse } from 'next/server';
import { getActivityLogService } from '../services/activity-log.service';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * Activity Logger Middleware
 *
 * This middleware logs API requests and responses.
 * It can be used to wrap API route handlers.
 */
export function withActivityLogging(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: {
    action?: string;
    entityType?: string;
    getEntityId?: (req: NextRequest) => Promise<string | undefined> | string | undefined;
    getDetails?: (req: NextRequest, res: NextResponse) => any;
    skipLogging?: (req: NextRequest) => boolean;
  } = {}
) {
  return async (req: NextRequest) => {
    // Skip logging if specified
    if (options.skipLogging && options.skipLogging(req)) {
      return handler(req);
    }

    const startTime = Date.now();
    let response: NextResponse;

    try {
      // Call the original handler
      response = await handler(req);

      // Extract action from options or request
      const action = options.action ||
                    `${req.method} ${new URL(req.url).pathname}`;

      // Extract entity ID if provided - handle both synchronous and asynchronous getEntityId
      let entityId: string | undefined = undefined;
      if (options.getEntityId) {
        const entityIdResult = options.getEntityId(req);
        if (entityIdResult instanceof Promise) {
          entityId = await entityIdResult;
        } else {
          entityId = entityIdResult;
        }
      }

      // Extract details if provided
      const details = options.getDetails ?
                     options.getDetails(req, response) :
                     {
                       method: req.method,
                       url: req.url,
                       status: response.status,
                       duration: Date.now() - startTime,
                     };

      // Log the request
      const activityLogService = getActivityLogService();
      await activityLogService.logApiRequest(
        req,
        action,
        options.entityType,
        entityId,
        details
      );

      return response;
    } catch (error) {
      console.error('Error in activity logger middleware:', error);

      try {
        // Extract entity ID if provided - handle both synchronous and asynchronous getEntityId
        let entityId: string | undefined = undefined;
        if (options.getEntityId) {
          try {
            const entityIdResult = options.getEntityId(req);
            if (entityIdResult instanceof Promise) {
              entityId = await entityIdResult;
            } else {
              entityId = entityIdResult;
            }
          } catch (entityIdError) {
            console.error('Error getting entity ID:', entityIdError);
          }
        }

        // Log the error
        const activityLogService = getActivityLogService();
        await activityLogService.logApiRequest(
          req,
          `ERROR ${req.method} ${new URL(req.url).pathname}`,
          options.entityType,
          entityId,
          {
            method: req.method,
            url: req.url,
            error: error instanceof Error ? error.message : String(error),
            duration: Date.now() - startTime,
          }
        );
      } catch (loggingError) {
        console.error('Error logging API request error:', loggingError);
      }

      // Re-throw the error
      throw error;
    }
  };
}

/**
 * Activity Logger Middleware Factory
 *
 * This factory creates middleware functions for specific entity types.
 */
export class ActivityLoggerMiddlewareFactory {
  /**
   * Create middleware for user-related routes
   * @param handler Original route handler
   * @param options Additional options
   * @returns Wrapped handler with activity logging
   */
  static forUserRoutes(
    handler: (req: NextRequest) => Promise<NextResponse>,
    options: {
      action?: string;
      getEntityId?: (req: NextRequest) => Promise<string | undefined> | string | undefined;
      getDetails?: (req: NextRequest, res: NextResponse) => any;
      skipLogging?: (req: NextRequest) => boolean;
    } = {}
  ) {
    return withActivityLogging(handler, {
      ...options,
      entityType: 'user',
      getEntityId: options.getEntityId || ((req) => {
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        return pathParts[pathParts.length - 1];
      }),
    });
  }

  /**
   * Create middleware for customer-related routes
   * @param handler Original route handler
   * @param options Additional options
   * @returns Wrapped handler with activity logging
   */
  static forCustomerRoutes(
    handler: (req: NextRequest) => Promise<NextResponse>,
    options: {
      action?: string;
      getEntityId?: (req: NextRequest) => Promise<string | undefined> | string | undefined;
      getDetails?: (req: NextRequest, res: NextResponse) => any;
      skipLogging?: (req: NextRequest) => boolean;
    } = {}
  ) {
    return withActivityLogging(handler, {
      ...options,
      entityType: 'customer',
      getEntityId: options.getEntityId || ((req) => {
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        return pathParts[pathParts.length - 1];
      }),
    });
  }

  /**
   * Create middleware for AMC contract-related routes
   * @param handler Original route handler
   * @param options Additional options
   * @returns Wrapped handler with activity logging
   */
  static forAMCContractRoutes(
    handler: (req: NextRequest) => Promise<NextResponse>,
    options: {
      action?: string;
      getEntityId?: (req: NextRequest) => Promise<string | undefined> | string | undefined;
      getDetails?: (req: NextRequest, res: NextResponse) => any;
      skipLogging?: (req: NextRequest) => boolean;
    } = {}
  ) {
    return withActivityLogging(handler, {
      ...options,
      entityType: 'amc_contract',
      getEntityId: options.getEntityId || ((req) => {
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        return pathParts[pathParts.length - 1];
      }),
    });
  }

  /**
   * Create middleware for authentication-related routes
   * @param handler Original route handler
   * @param options Additional options
   * @returns Wrapped handler with activity logging
   */
  static forAuthRoutes(
    handler: (req: NextRequest) => Promise<NextResponse>,
    options: {
      action?: string;
      getDetails?: (req: NextRequest, res: NextResponse) => any;
      skipLogging?: (req: NextRequest) => boolean;
    } = {}
  ) {
    return withActivityLogging(handler, {
      ...options,
      entityType: 'auth',
      getEntityId: async (req) => {
        try {
          const session = await getServerSession(authOptions);
          return session?.user?.id;
        } catch (error) {
          console.error('Error getting session in forAuthRoutes:', error);
          return undefined;
        }
      },
    });
  }
}
