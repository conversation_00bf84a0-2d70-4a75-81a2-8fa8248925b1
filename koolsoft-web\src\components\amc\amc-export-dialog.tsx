'use client';

import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON>Header, 
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { FileDown, Loader2 } from "lucide-react";

type AMCExportDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onExport: (format: string, fields: string[], includeRelated: boolean) => void;
  isExporting: boolean;
};

/**
 * AMC Export Dialog Component
 * 
 * This component provides a dialog for exporting AMC contracts with various options.
 * It follows the UI standards with consistent styling and clear user instructions.
 */
export function AMCExportDialog({ 
  open, 
  onOpenChange,
  onExport,
  isExporting
}: AMCExportDialogProps) {
  const [format, setFormat] = useState("csv");
  const [includeRelated, setIncludeRelated] = useState(true);
  const [selectedFields, setSelectedFields] = useState([
    "id", "contractNumber", "startDate", "endDate", "amount", "status", "customerName"
  ]);
  
  const availableFields = [
    { id: "id", label: "Contract ID" },
    { id: "contractNumber", label: "Contract Number" },
    { id: "startDate", label: "Start Date" },
    { id: "endDate", label: "End Date" },
    { id: "amount", label: "Amount" },
    { id: "status", label: "Status" },
    { id: "customerName", label: "Customer Name" },
    { id: "customerCity", label: "Customer City" },
    { id: "executiveName", label: "Executive Name" },
    { id: "contactPersonName", label: "Contact Person" },
    { id: "numberOfMachines", label: "Number of Machines" },
    { id: "totalTonnage", label: "Total Tonnage" },
    { id: "paidAmount", label: "Paid Amount" },
    { id: "balanceAmount", label: "Balance Amount" },
    { id: "numberOfServices", label: "Number of Services" },
    { id: "remarks", label: "Remarks" }
  ];
  
  const handleFieldToggle = (field: string) => {
    setSelectedFields(prev => 
      prev.includes(field)
        ? prev.filter(f => f !== field)
        : [...prev, field]
    );
  };
  
  const handleExport = () => {
    onExport(format, selectedFields, includeRelated);
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-black">Export AMC Contracts</DialogTitle>
          <DialogDescription className="text-black">
            Choose your export format and select the fields to include.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4 space-y-6">
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-black">Export Format</h4>
            <RadioGroup
              value={format}
              onValueChange={setFormat}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="csv" id="csv" />
                <Label htmlFor="csv" className="text-black">CSV (Comma Separated Values)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="excel" id="excel" />
                <Label htmlFor="excel" className="text-black">Excel (.xlsx)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="json" id="json" />
                <Label htmlFor="json" className="text-black">JSON</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-black">Fields to Include</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedFields(availableFields.map(f => f.id))}
                className="h-7 text-xs"
              >
                Select All
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {availableFields.map((field) => (
                <div key={field.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`field-${field.id}`}
                    checked={selectedFields.includes(field.id)}
                    onCheckedChange={() => handleFieldToggle(field.id)}
                  />
                  <Label htmlFor={`field-${field.id}`} className="text-sm text-black">
                    {field.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-related"
              checked={includeRelated}
              onCheckedChange={(checked) => setIncludeRelated(!!checked)}
            />
            <Label htmlFor="include-related" className="text-sm text-black">
              Include related data (machines, payments, service dates)
            </Label>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting || selectedFields.length === 0}>
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <FileDown className="h-4 w-4 mr-2" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
