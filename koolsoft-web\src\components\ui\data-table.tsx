'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';

interface Column<T> {
  key?: string;
  label?: string;
  header?: string;
  accessorKey?: string;
  sortable?: boolean;
  render?: (item: T) => React.ReactNode;
  cell?: (props: { row: { original: T } }) => React.ReactNode;
}

interface PaginationConfig {
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number, pageSize: number) => void;
}

interface SortingConfig {
  column: string;
  direction: 'asc' | 'desc';
  onSort: (column: string, direction: 'asc' | 'desc') => void;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: PaginationConfig;
  sorting?: SortingConfig;
  emptyMessage?: string;
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  pagination,
  sorting,
  emptyMessage = 'No data available',
}: DataTableProps<T>) {
  // Helper functions to get column properties with fallbacks
  const getColumnKey = (column: Column<T>, index: number): string => {
    return column.key || column.accessorKey || `column-${index}`;
  };

  const getColumnLabel = (column: Column<T>): string => {
    return column.label || column.header || 'Column';
  };

  const getColumnValue = (item: T, column: Column<T>): React.ReactNode => {
    if (column.render) {
      return column.render(item);
    }
    if (column.cell) {
      return column.cell({ row: { original: item } });
    }
    const key = column.key || column.accessorKey;
    if (key) {
      // Handle nested keys like 'customer.name'
      return key.split('.').reduce((obj, k) => obj?.[k], item);
    }
    return '';
  };

  const handleSort = (column: Column<T>) => {
    if (!column.sortable || !sorting) return;

    const columnKey = column.key || column.accessorKey || '';
    const newDirection =
      sorting.column === columnKey && sorting.direction === 'asc'
        ? 'desc'
        : 'asc';

    sorting.onSort(columnKey, newDirection);
  };

  const getSortIcon = (column: Column<T>) => {
    if (!column.sortable || !sorting) return <ArrowUpDown className="h-4 w-4" />;

    const columnKey = column.key || column.accessorKey || '';
    if (sorting.column === columnKey) {
      return sorting.direction === 'asc'
        ? <ArrowUp className="h-4 w-4" />
        : <ArrowDown className="h-4 w-4" />;
    }

    return <ArrowUpDown className="h-4 w-4" />;
  };

  const totalPages = pagination ? Math.ceil(pagination.total / pagination.pageSize) : 1;
  const currentPage = pagination ? pagination.page : 0;

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="border rounded-lg">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-gray-50">
                  {columns.map((column, index) => (
                    <th key={getColumnKey(column, index)} className="px-4 py-3 text-left">
                      <Skeleton className="h-4 w-20" />
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {[...Array(5)].map((_, rowIndex) => (
                  <tr key={rowIndex} className="border-b">
                    {columns.map((column, colIndex) => (
                      <td key={getColumnKey(column, colIndex)} className="px-4 py-3">
                        <Skeleton className="h-4 w-24" />
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-lg">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-gray-50">
                {columns.map((column, index) => (
                  <th key={getColumnKey(column, index)} className="px-4 py-3 text-left">
                    {column.sortable ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSort(column)}
                        className="h-auto p-0 font-medium text-black hover:text-primary"
                      >
                        {getColumnLabel(column)}
                        <span className="ml-2">
                          {getSortIcon(column)}
                        </span>
                      </Button>
                    ) : (
                      <span className="font-medium text-black">{getColumnLabel(column)}</span>
                    )}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.length === 0 ? (
                <tr>
                  <td colSpan={columns.length} className="px-4 py-8 text-center text-muted-foreground">
                    {emptyMessage}
                  </td>
                </tr>
              ) : (
                data.map((item, rowIndex) => {
                  // Try to get a unique identifier from the item, fallback to index
                  const rowKey = item.id || item.uuid || item.key || `row-${rowIndex}`;
                  return (
                    <tr key={rowKey} className="border-b hover:bg-gray-50">
                      {columns.map((column, colIndex) => (
                        <td key={getColumnKey(column, colIndex)} className="px-4 py-3">
                          {getColumnValue(item, column)}
                        </td>
                      ))}
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {currentPage * pagination.pageSize + 1} to{' '}
            {Math.min((currentPage + 1) * pagination.pageSize, pagination.total)} of{' '}
            {pagination.total} results
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(0, pagination.pageSize)}
              disabled={currentPage === 0}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(currentPage - 1, pagination.pageSize)}
              disabled={currentPage === 0}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <span className="text-sm font-medium">
              Page {currentPage + 1} of {totalPages}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(currentPage + 1, pagination.pageSize)}
              disabled={currentPage >= totalPages - 1}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(totalPages - 1, pagination.pageSize)}
              disabled={currentPage >= totalPages - 1}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
