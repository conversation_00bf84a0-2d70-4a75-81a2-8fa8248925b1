import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Reference Data Repository
 *
 * This repository handles database operations for reference data entities like
 * territories, segments, complaint types, etc.
 */
export class ReferenceDataRepository extends PrismaRepository<
  any,
  string,
  any,
  any
> {
  // Map of reference model IDs to Prisma model names
  // Always use modern table names to prevent duplicates
  private static readonly modelMapping: Record<string, string> = {
    // Modern table names (lowercase)
    'territories': 'Territory',
    'segments': 'Segment',
    'competitors': 'Competitor',
    'divisions': 'divisions',
    'brands': 'brands',
    'serviceCenters': 'serviceCenter',
    'priority-types': 'PriorityType',
    'enquiry-types': 'EnquiryType',
    'deduction-types': 'DeductionType',
    'debit-divisions': 'DebitDivision',
    'account-divisions': 'AccountDivision',
    'spare-parts': 'SparePart',
    'tax-rates': 'TaxRate',
    'transit-damage-types': 'TransitDamageType',
    'user-groups': 'UserGroup',
    'usp-types': 'UspType',
    'visit-types': 'VisitType',

    // Legacy table names (PascalCase) mapped to modern tables
    'serviceVisitType': 'ServiceVisitType',
    'complaintType': 'ComplaintType',
    'complaintNatureType': 'ComplaintNatureType',
    'failureType': 'FailureType',
    'spareType': 'SpareType',
    'measurementType': 'MeasurementType',
    'priorityType': 'PriorityType',
    'priorityTypes': 'PriorityType',
    'enquiryType': 'EnquiryType',
    'enquiryTypes': 'EnquiryType',
    'deductionType': 'DeductionType',
    'deductionTypes': 'DeductionType',
    'debitDivision': 'DebitDivision',
    'debitDivisions': 'DebitDivision',
    'accountDivision': 'AccountDivision',
    'accountDivisions': 'AccountDivision',
    'sparePart': 'SparePart',
    'spareParts': 'SparePart',
    'taxRate': 'TaxRate',
    'taxRates': 'TaxRate',
    'transitDamageType': 'TransitDamageType',
    'transitDamageTypes': 'TransitDamageType',
    'userGroup': 'UserGroup',
    'userGroups': 'UserGroup',
    'uspType': 'UspType',
    'uspTypes': 'UspType',
    'visitType': 'VisitType',
    'visitTypes': 'VisitType',

    // Additional mappings for URL path variations
    'service-visit-types': 'ServiceVisitType',
    'complaint-types': 'ComplaintType',
    'complaint-nature-types': 'ComplaintNatureType',
    'failure-types': 'FailureType',
    'spare-types': 'SpareType',
    'measurement-types': 'MeasurementType',
    'priority-types': 'PriorityType',
    'enquiry-types': 'EnquiryType',
    'deduction-types': 'DeductionType',
    'debit-divisions': 'DebitDivision',
    'account-divisions': 'AccountDivision',
    'spare-parts': 'SparePart',
    'tax-rates': 'TaxRate',
    'transit-damage-types': 'TransitDamageType',
    'user-groups': 'UserGroup',
    'usp-types': 'UspType',
    'visit-types': 'VisitType'
  };

  // List of tables that are known to exist in the database
  // These tables exist in the actual database based on schema.prisma
  private static readonly existingTables: string[] = [
    // Modern tables (lowercase)
    'brands',
    'divisions',
    'serviceCenter',

    // Reference data tables (PascalCase)
    'Territory',
    'Segment',
    'Competitor',
    'ServiceVisitType',
    'ComplaintType',
    'ComplaintNatureType',
    'FailureType',
    'SpareType',
    'MeasurementType',
    'PriorityType',
    'EnquiryType',
    'DeductionType',
    'DebitDivision',
    'AccountDivision',
    'SparePart',
    'TaxRate',
    'TransitDamageType',
    'UserGroup',
    'UspType',
    'VisitType',

    // Actual database table names (snake_case)
    'territories',
    'segments',
    'competitors',
    'service_visit_types',
    'complaint_types',
    'complaint_nature_types',
    'failure_types',
    'spare_types',
    'measurement_types',
    'priority_types',
    'enquiry_types',
    'deduction_types',
    'debit_divisions',
    'account_divisions',
    'spare_parts',
    'tax_rates',
    'transit_damage_types',
    'user_groups',
    'usp_types',
    'visit_types'
  ];

  constructor(modelName: string) {
    if (!modelName) {
      throw new Error('Model name is required');
    }

    // Normalize the model name to handle different formats
    // Convert to lowercase and replace underscores with hyphens
    const normalizedModelName = modelName.toLowerCase().replace(/_/g, '-');

    // Try to find a matching key in our model mapping
    const mappingKey = Object.keys(ReferenceDataRepository.modelMapping).find(key => {
      const normalizedKey = key.toLowerCase().replace(/_/g, '-');
      return normalizedKey === normalizedModelName;
    });

    // Map the model name to the Prisma model name
    const prismaModelName = mappingKey
      ? ReferenceDataRepository.modelMapping[mappingKey]
      : modelName;

    // Always use the mapped model name, even if the table doesn't exist in our list
    // This is because the table might actually exist in the database
    super(prismaModelName);

    // Store the original model name for debugging purposes
    this._modelName = prismaModelName;

    // Check if the table exists in our known list
    // This is just for logging purposes
    const tableExists = ReferenceDataRepository.existingTables.includes(prismaModelName);

    // Also check if the table exists in snake_case format
    const snakeCaseModelName = prismaModelName
      .replace(/([A-Z])/g, '_$1')
      .toLowerCase()
      .replace(/^_/, '');

    const tableExistsSnakeCase = ReferenceDataRepository.existingTables.includes(snakeCaseModelName);

    if (!tableExists && !tableExistsSnakeCase) {
      console.warn(`Table '${prismaModelName}' or '${snakeCaseModelName}' is not in our known list of tables, but we'll try to use it anyway`);
    }
  }

  // Store the original model name for debugging purposes
  private _modelName: string = '';

  /**
   * Get all reference data for a specific model
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of reference data
   */
  async findAll(skip?: number, take?: number): Promise<any[]> {
    // Use the model property which will use the correct modern table
    // based on the modelMapping
    let whereClause = undefined;

    try {
      // Only return active records for models that have an isActive field
      if (await this.hasIsActiveField()) {
        whereClause = { isActive: true };
      }
      // For models with active field, filter by it
      else if (await this.hasActiveField()) {
        whereClause = { active: true };
      }
    } catch (error) {
      console.warn(`Error checking for isActive/active field: ${error.message}`);
      // If we can't determine if the model has an isActive or active field,
      // just don't use a where clause
      whereClause = undefined;
    }

    return this.model.findMany({
      skip,
      take,
      orderBy: { name: 'asc' },
      where: whereClause,
    });
  }

  /**
   * Check if the model has an isActive field
   * @returns True if the model has an isActive field, false otherwise
   */
  private async hasIsActiveField(): Promise<boolean> {
    try {
      // Try to query a single record with isActive field to see if it exists
      await this.model.findFirst({
        select: { id: true },
        where: { isActive: true },
        take: 1
      });
      return true;
    } catch (error) {
      // If the query fails because isActive doesn't exist, return false
      if (error.message.includes('Unknown argument `isActive`')) {
        return false;
      }
      // For any other error, rethrow it
      throw error;
    }
  }

  /**
   * Find reference data by name
   * @param name Name to search for
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param exactMatch Whether to perform an exact match (default: false)
   * @returns Promise resolving to an array of reference data
   */
  async findByName(name: string, skip?: number, take?: number, exactMatch: boolean = false): Promise<any[]> {
    // Create the where condition based on whether we want an exact match
    const nameCondition = exactMatch
      ? { equals: name, mode: 'insensitive' }
      : { contains: name, mode: 'insensitive' };

    // Create the where object with the name condition and optional isActive filter
    const where: any = {
      name: nameCondition,
    };

    try {
      // Add isActive filter for models that have it
      if (await this.hasIsActiveField()) {
        where.isActive = true;
      }
      // Add active filter for models that have it
      else if (await this.hasActiveField()) {
        where.active = true;
      }
    } catch (error) {
      console.warn(`Error checking for isActive/active field: ${error.message}`);
      // If we can't determine if the model has an isActive or active field,
      // just use the name condition
    }

    // Use the model property which will use the correct modern table
    // based on the modelMapping
    return this.model.findMany({
      where,
      skip,
      take,
      orderBy: { name: 'asc' },
    });
  }

  /**
   * Check if the model has an active field
   * @returns True if the model has an active field, false otherwise
   */
  private async hasActiveField(): Promise<boolean> {
    try {
      // Try to query a single record with active field to see if it exists
      await this.model.findFirst({
        select: { id: true },
        where: { active: true },
        take: 1
      });
      return true;
    } catch (error) {
      // If the query fails because active doesn't exist, return false
      if (error.message.includes('Unknown argument `active`')) {
        return false;
      }
      // For any other error, rethrow it
      throw error;
    }
  }

  /**
   * Find active reference data
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of active reference data
   */
  async findActive(skip?: number, take?: number): Promise<any[]> {
    try {
      // For models with isActive field, filter by it
      if (await this.hasIsActiveField()) {
        return this.model.findMany({
          where: { isActive: true },
          skip,
          take,
          orderBy: { name: 'asc' },
        });
      }

      // For models with active field, filter by it
      if (await this.hasActiveField()) {
        return this.model.findMany({
          where: { active: true },
          skip,
          take,
          orderBy: { name: 'asc' },
        });
      }
    } catch (error) {
      console.warn(`Error checking for isActive/active field: ${error.message}`);
      // If we can't determine if the model has an isActive or active field,
      // just return all records
    }

    // For models without isActive or active field, just return all records
    return this.model.findMany({
      skip,
      take,
      orderBy: { name: 'asc' },
    });
  }

  /**
   * Override the count method to handle special cases
   * @param filter Optional filter condition
   * @returns Promise resolving to the count
   */
  async count(filter?: any): Promise<number> {
    // If no filter is provided, add appropriate filter based on model type
    if (!filter) {
      try {
        // For models with isActive field
        if (await this.hasIsActiveField()) {
          filter = { isActive: true };
        }
        // For models with active field
        else if (await this.hasActiveField()) {
          filter = { active: true };
        }
      } catch (error) {
        console.warn(`Error checking for isActive/active field: ${error.message}`);
        // If we can't determine if the model has an isActive or active field,
        // just use no filter
        filter = {};
      }
    }

    // Use the model property which will use the correct modern table
    // based on the modelMapping
    return this.model.count({
      where: filter,
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<any, string, any, any> {
    const repo = new ReferenceDataRepository(this.modelName);
    (repo as any).prisma = tx;
    return repo;
  }

  /**
   * Check if a model name is a valid reference model
   * @param modelName Model name to check
   * @returns True if the model name is valid, false otherwise
   */
  static isValidReferenceModel(modelName: string): boolean {
    if (!modelName) {
      return false;
    }

    // Normalize the model name to handle different formats
    const normalizedModelName = modelName.toLowerCase().replace(/_/g, '-');

    // Check if the model name is in the mapping
    const isInMapping = Object.keys(ReferenceDataRepository.modelMapping).some(key => {
      const normalizedKey = key.toLowerCase().replace(/_/g, '-');
      return normalizedKey === normalizedModelName;
    });

    if (isInMapping) {
      return true;
    }

    // Check if the model name is in the existingTables list
    const isInExistingTables = ReferenceDataRepository.existingTables.some(tableName => {
      const normalizedTableName = tableName.toLowerCase().replace(/_/g, '-');
      return normalizedTableName === normalizedModelName;
    });

    return isInMapping || isInExistingTables;
  }

  /**
   * Check if a table exists in the database
   * @param tableName Table name to check
   * @returns True if the table exists, false otherwise
   */
  static tableExists(tableName: string): boolean {
    if (!tableName) {
      return false;
    }

    // Check if the table name is in the existingTables list
    if (ReferenceDataRepository.existingTables.includes(tableName)) {
      return true;
    }

    // Try to normalize the table name to snake_case
    const snakeCaseTableName = tableName
      .replace(/([A-Z])/g, '_$1')
      .toLowerCase()
      .replace(/^_/, '');

    if (ReferenceDataRepository.existingTables.includes(snakeCaseTableName)) {
      return true;
    }

    // Try to normalize the table name to PascalCase
    const pascalCaseTableName = snakeCaseTableName
      .split('_')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');

    return ReferenceDataRepository.existingTables.includes(pascalCaseTableName);
  }
}
