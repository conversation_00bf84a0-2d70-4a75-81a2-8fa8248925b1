'use client';

import React, { useState, useEffect } from 'react';
import { ComponentList } from '@/components/components/component-list';
import { ComponentForm } from '@/components/components/component-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Wrench, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { Component } from '@/lib/hooks/useComponents';

export default function ComponentsPage() {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null);
  const [selectedMachineId, setSelectedMachineId] = useState<string>('');
  const [componentStats, setComponentStats] = useState({
    total: 0,
    activeWarranty: 0,
    expiredWarranty: 0,
    expiringSoon: 0
  });

  // Load component statistics from API
  useEffect(() => {
    const loadComponentStats = async () => {
      try {
        const response = await fetch('/api/amc/components/statistics', {
          credentials: 'include',
        });

        if (response.ok) {
          const data = await response.json();
          setComponentStats({
            total: data.total || 0,
            activeWarranty: data.activeWarranty || 0,
            expiredWarranty: data.expiredWarranty || 0,
            expiringSoon: data.expiringSoon || 0
          });
        }
      } catch (error) {
        console.error('Error loading component statistics:', error);
      }
    };

    loadComponentStats();
  }, []);

  // Listen for the add component event from the layout
  useEffect(() => {
    const handleAddComponent = () => {
      setShowAddDialog(true);
    };

    window.addEventListener('addComponent', handleAddComponent);
    return () => window.removeEventListener('addComponent', handleAddComponent);
  }, []);

  const handleAddComponent = () => {
    setShowAddDialog(true);
  };

  const handleEditComponent = (component: Component) => {
    setSelectedComponent(component);
    setSelectedMachineId(component.machineId);
    setShowEditDialog(true);
  };

  const handleDeleteComponent = (component: Component) => {
    // Delete is handled in the ComponentList component
  };

  const handleFormSuccess = (component: Component) => {
    setShowAddDialog(false);
    setShowEditDialog(false);
    setSelectedComponent(null);
    setSelectedMachineId('');
    // The list will refresh automatically
  };

  const handleFormCancel = () => {
    setShowAddDialog(false);
    setShowEditDialog(false);
    setSelectedComponent(null);
    setSelectedMachineId('');
  };

  return (
    <div className="space-y-6">
      {/* Component Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Wrench className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-black">Total Components</p>
                <p className="text-2xl font-bold text-black">{componentStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-black">Active Warranty</p>
                <p className="text-2xl font-bold text-black">{componentStats.activeWarranty}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-black">Expiring Soon</p>
                <p className="text-2xl font-bold text-black">{componentStats.expiringSoon}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-black">Expired Warranty</p>
                <p className="text-2xl font-bold text-black">{componentStats.expiredWarranty}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <ComponentList
        showMachineInfo={true}
        onAddComponent={handleAddComponent}
        onEditComponent={handleEditComponent}
        onDeleteComponent={handleDeleteComponent}
      />

      {/* Add Component Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-black">Add New Component</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <ComponentForm
              machineId={selectedMachineId || ''}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Component Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-black">Edit Component</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            {selectedComponent && (
              <ComponentForm
                machineId={selectedMachineId}
                component={selectedComponent}
                onSuccess={handleFormSuccess}
                onCancel={handleFormCancel}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
