import { NextRequest, NextResponse } from 'next/server';
import { getEmailTemplateRepository } from '@/lib/repositories';
import { z } from 'zod';
import { withAdminProtection } from '@/lib/auth/role-check';
import { withActivityLogging } from '@/lib/middleware/activity-logger.middleware';

/**
 * Email template creation schema
 */
const createEmailTemplateSchema = z.object({
  name: z.string().min(2).max(100),
  subject: z.string().min(2).max(200),
  bodyHtml: z.string().min(10),
  bodyText: z.string().min(10),
  description: z.string().optional(),
  variables: z.array(z.string()).optional(),
  category: z.string().optional(),
  isActive: z.boolean().default(true),
});

/**
 * GET /api/admin/email/templates
 * Get all email templates with optional pagination and filtering
 */
async function getEmailTemplates(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');
    const activeOnly = searchParams.get('activeOnly') === 'true';
    const name = searchParams.get('name');

    const emailTemplateRepository = getEmailTemplateRepository();

    let templates = [];
    let total = 0;

    if (name) {
      // Get template by name
      const template = await emailTemplateRepository.findByName(name);
      templates = template ? [template] : [];
      total = template ? 1 : 0;
    } else if (activeOnly) {
      // Get active templates
      templates = await emailTemplateRepository.findActive(skip, take);
      total = await emailTemplateRepository.count({ isActive: true });
    } else {
      // Get all templates
      templates = await emailTemplateRepository.findAll(skip, take);
      total = await emailTemplateRepository.count();
    }

    return NextResponse.json({
      templates,
      meta: {
        total,
        skip,
        take,
      },
    });
  } catch (error) {
    console.error('Error fetching email templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email templates' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/email/templates
 * Create a new email template
 */
async function createEmailTemplate(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = createEmailTemplateSchema.parse(body);

    const emailTemplateRepository = getEmailTemplateRepository();

    // Check if template with the same name already exists
    const existingTemplate = await emailTemplateRepository.findByName(validatedData.name);
    if (existingTemplate) {
      return NextResponse.json(
        { error: `Email template with name '${validatedData.name}' already exists` },
        { status: 400 }
      );
    }

    // Create email template
    const template = await emailTemplateRepository.create(validatedData);

    return NextResponse.json(template, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating email template:', error);
    return NextResponse.json(
      { error: 'Failed to create email template' },
      { status: 500 }
    );
  }
}

// Export the handlers with admin protection and activity logging
export const GET = withAdminProtection(
  withActivityLogging(getEmailTemplates, {
    action: 'view_email_templates',
    entityType: 'email_template',
  })
);

export const POST = withAdminProtection(
  withActivityLogging(createEmailTemplate, {
    action: 'create_email_template',
    entityType: 'email_template',
    getEntityId: async (req) => {
      try {
        const body = await req.json();
        return body.name;
      } catch (error) {
        return undefined;
      }
    },
  })
);
