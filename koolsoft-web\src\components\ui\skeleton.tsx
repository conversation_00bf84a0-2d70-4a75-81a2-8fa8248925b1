import { cn } from "@/lib/utils"

interface SkeletonProps extends React.HTMLAttributes<HTMLElement> {
  /**
   * The HTML element to render the skeleton as.
   * Use 'span' when placing inside paragraph elements to avoid hydration errors.
   * @default "div"
   */
  as?: "div" | "span";
}

function Skeleton({
  className,
  as = "div",
  ...props
}: SkeletonProps) {
  const Component = as;

  return (
    <Component
      className={cn("animate-pulse rounded-md bg-gray-200", className)}
      {...props}
    />
  )
}

/**
 * A skeleton component that renders as a span, safe to use inside paragraph elements.
 */
function SpanSkeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) {
  return (
    <span
      className={cn("animate-pulse rounded-md bg-gray-200 inline-block", className)}
      {...props}
    />
  )
}

export { Skeleton, SpanSkeleton }
