'use client';

import { CustomerForm } from '@/components/customers/customer-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { ManagerGate } from '@/components/auth/role-gate';
import Link from 'next/link';
import { Home } from 'lucide-react';

/**
 * New Customer Page
 *
 * This page allows users with ADMIN or MANAGER roles to create a new customer.
 */
export default function NewCustomerPage() {
  return (
    <ManagerGate fallback={
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
          <p className="text-gray-500">You do not have permission to access this page</p>
          <div className="mt-4">
            <Link href="/dashboard" className="text-blue-600 hover:text-blue-500">
              Return to Dashboard
            </Link>
          </div>
        </div>
      </div>
    }>
      <div className="space-y-6">
        <CustomerForm />
      </div>
    </ManagerGate>
  );
}
