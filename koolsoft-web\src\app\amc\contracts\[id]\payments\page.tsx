'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { PaymentList } from '@/components/payments/payment-list';
import { PaymentForm } from '@/components/payments/payment-form';
import { PaymentSummary } from '@/components/payments/payment-summary';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Payment, usePayments } from '@/lib/hooks/usePayments';

interface AMCContract {
  id: string;
  customer: {
    id: string;
    name: string;
  };
  amount: number;
  startDate: string;
  endDate: string;
  status: string;
}

export default function ContractPaymentsPage() {
  const params = useParams();
  const contractId = params.id as string;
  
  const [contract, setContract] = useState<AMCContract | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  const { deletePayment } = usePayments();

  // Fetch contract details
  useEffect(() => {
    const fetchContract = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/amc/contracts/${contractId}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch contract details');
        }

        const data = await response.json();
        setContract(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    if (contractId) {
      fetchContract();
    }
  }, [contractId]);

  const handleCreatePayment = () => {
    setIsCreateDialogOpen(true);
  };

  const handleEditPayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsEditDialogOpen(true);
  };

  const handleDeletePayment = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
  };

  const handleEditSuccess = () => {
    setIsEditDialogOpen(false);
    setSelectedPayment(null);
  };

  const handleDeleteConfirm = () => {
    if (selectedPayment) {
      deletePayment(selectedPayment.id);
    }
    setIsDeleteDialogOpen(false);
    setSelectedPayment(null);
  };

  const handleDialogClose = () => {
    setIsCreateDialogOpen(false);
    setIsEditDialogOpen(false);
    setSelectedPayment(null);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !contract) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            {error || 'Contract not found'}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Contract Information */}
      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <CardTitle className="text-white">Contract Information</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-black">Customer</p>
              <p className="text-lg text-black">{contract.customer.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-black">Contract Amount</p>
              <p className="text-lg font-semibold text-black">
                ₹{contract.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-black">Start Date</p>
              <p className="text-lg text-black">
                {new Date(contract.startDate).toLocaleDateString('en-IN')}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-black">End Date</p>
              <p className="text-lg text-black">
                {new Date(contract.endDate).toLocaleDateString('en-IN')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Summary */}
      <PaymentSummary
        amcContractId={contractId}
        contractAmount={contract.amount}
        showDetailedStats={true}
      />

      {/* Payment List */}
      <PaymentList
        amcContractId={contractId}
        showContractInfo={false}
        onCreatePayment={handleCreatePayment}
        onEditPayment={handleEditPayment}
        onDeletePayment={handleDeletePayment}
      />

      {/* Create Payment Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-black">Create New Payment</DialogTitle>
          </DialogHeader>
          <PaymentForm
            amcContractId={contractId}
            onSuccess={handleCreateSuccess}
            onCancel={handleDialogClose}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Payment Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-black">Edit Payment</DialogTitle>
          </DialogHeader>
          {selectedPayment && (
            <PaymentForm
              payment={selectedPayment}
              onSuccess={handleEditSuccess}
              onCancel={handleDialogClose}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-black">Delete Payment</AlertDialogTitle>
            <AlertDialogDescription className="text-black">
              Are you sure you want to delete this payment? This action cannot be undone.
            </AlertDialogDescription>
            {selectedPayment && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md">
                <p className="font-medium">
                  Payment: ₹{selectedPayment.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                </p>
                {selectedPayment.receiptNo && (
                  <p className="text-sm text-gray-600">
                    Receipt: {selectedPayment.receiptNo}
                  </p>
                )}
              </div>
            )}
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Payment
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
