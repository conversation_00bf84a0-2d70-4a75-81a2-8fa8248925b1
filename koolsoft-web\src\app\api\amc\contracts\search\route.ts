import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCContractRepository } from '@/lib/repositories';
import { amcSearchSchema, convertSearchToFilter } from '@/lib/validations/amc-search.schema';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { z } from 'zod';

/**
 * POST /api/amc/contracts/search
 * Advanced search for AMC contracts
 *
 * This endpoint provides comprehensive search capabilities for AMC contracts.
 * It supports filtering by various criteria, pagination, and sorting.
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = amcSearchSchema.parse(body);

        const amcContractRepository = getAMCContractRepository();

        // Convert search parameters to Prisma filter
        const filter = convertSearchToFilter(validatedData);

        // Get contracts with pagination and sorting
        const contracts = await amcContractRepository.findWithFilter(
          filter,
          validatedData.skip,
          validatedData.take,
          {
            [validatedData.sortField]: validatedData.sortOrder
          }
        );

        // Count total matching contracts
        const total = await amcContractRepository.countWithFilter(filter);

        return NextResponse.json({
          data: contracts,
          pagination: {
            skip: validatedData.skip,
            take: validatedData.take,
            total
          }
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            {
              error: 'Validation error',
              details: error.errors,
              code: 'VALIDATION_ERROR'
            },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error searching AMC contracts:', error);

      if (error instanceof PrismaClientKnownRequestError) {
        // Handle specific Prisma errors
        if (error.code === 'P2002') {
          return NextResponse.json(
            {
              error: 'Duplicate entry',
              details: 'A record with this identifier already exists',
              code: 'DUPLICATE_ERROR'
            },
            { status: 409 }
          );
        } else if (error.code === 'P2003') {
          return NextResponse.json(
            {
              error: 'Foreign key constraint failed',
              details: `Invalid reference: ${error.meta?.field_name || 'unknown field'}`,
              code: 'FOREIGN_KEY_ERROR'
            },
            { status: 400 }
          );
        } else if (error.code === 'P2025') {
          return NextResponse.json(
            {
              error: 'Record not found',
              details: error.meta?.cause || 'The requested record does not exist',
              code: 'NOT_FOUND_ERROR'
            },
            { status: 404 }
          );
        } else if (error.code === 'P2022') {
          return NextResponse.json(
            {
              error: 'Unknown field in query',
              details: `Field does not exist: ${error.meta?.field_name || 'unknown field'}`,
              code: 'UNKNOWN_FIELD_ERROR'
            },
            { status: 400 }
          );
        }
      }

      // Extract error message
      let errorMessage = 'An unexpected error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      // In production, don't expose detailed error messages unless they're sanitized
      const displayMessage = process.env.NODE_ENV === 'development'
        ? errorMessage
        : 'Failed to search AMC contracts. Please try again or contact support.';

      return NextResponse.json(
        {
          error: 'Failed to search AMC contracts',
          message: displayMessage,
          code: 'INTERNAL_ERROR',
          // Include stack trace in development for debugging
          ...(process.env.NODE_ENV === 'development' && error instanceof Error && { stack: error.stack })
        },
        { status: 500 }
      );
    }
  }
);
