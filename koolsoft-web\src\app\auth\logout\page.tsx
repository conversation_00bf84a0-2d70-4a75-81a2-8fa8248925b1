'use client';

import { useEffect } from 'react';
import { signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';

/**
 * Logout Page Component
 * 
 * This page signs the user out and redirects to the login page.
 */
export default function LogoutPage() {
  const router = useRouter();
  
  useEffect(() => {
    // Sign out the user
    const handleSignOut = async () => {
      await signOut({ redirect: false });
      router.push('/auth/login');
    };
    
    handleSignOut();
  }, [router]);
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Signing out...
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Please wait while we sign you out.
          </p>
        </div>
      </div>
    </div>
  );
}
