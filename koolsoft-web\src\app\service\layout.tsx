'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { Wrench } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * Service Layout Component
 *
 * This component provides a consistent layout for all service-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function ServiceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title based on the pathname
  let pageTitle = 'Service';
  if (pathname !== '/service') {
    if (pathname.includes('/edit')) {
      pageTitle = 'Edit Service';
    } else if (pathname.includes('/new')) {
      pageTitle = 'New Service';
    } else {
      pageTitle = 'Service Details';
    }
  }

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Service', href: '/service', icon: <Wrench className="h-4 w-4" /> }
  ];

  // Add additional breadcrumb for subpages
  if (pathname !== '/service') {
    breadcrumbs.push({ label: pageTitle, current: true });
  }

  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
