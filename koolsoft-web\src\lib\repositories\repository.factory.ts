import { UserRepository } from './user.repository';
import { CustomerRepository } from './customer.repository';
import { AMCContractRepository } from './amc-contract.repository';
import { AMCMachineRepository } from './amc-machine.repository';
import { ReferenceDataRepository } from './reference-data.repository';
import { HistoryDetailRepository } from './history-detail.repository';
import { VisitCardRepository } from './visit-card.repository';
import { EmailTemplateRepository, EmailLogRepository } from './email.repository';
import { ActivityLogRepository } from './activity-log.repository';

/**
 * Repository Factory
 *
 * This factory creates and manages repository instances.
 * It ensures that only one instance of each repository is created.
 */
export class RepositoryFactory {
  private static instance: RepositoryFactory;
  private repositories: Map<string, any> = new Map();

  private constructor() {}

  /**
   * Get the singleton instance of the repository factory
   * @returns Repository factory instance
   */
  public static getInstance(): RepositoryFactory {
    if (!RepositoryFactory.instance) {
      RepositoryFactory.instance = new RepositoryFactory();
    }
    return RepositoryFactory.instance;
  }

  /**
   * Get a repository instance
   * @param repositoryClass Repository class
   * @returns Repository instance
   */
  public getRepository<T>(repositoryClass: new () => T): T {
    const repositoryName = repositoryClass.name;

    if (!this.repositories.has(repositoryName)) {
      this.repositories.set(repositoryName, new repositoryClass());
    }

    return this.repositories.get(repositoryName);
  }

  /**
   * Get the user repository
   * @returns User repository instance
   */
  public getUserRepository(): UserRepository {
    return this.getRepository(UserRepository);
  }

  /**
   * Get the customer repository
   * @returns Customer repository instance
   */
  public getCustomerRepository(): CustomerRepository {
    return this.getRepository(CustomerRepository);
  }

  /**
   * Get the AMC contract repository
   * @returns AMC contract repository instance
   */
  public getAMCContractRepository(): AMCContractRepository {
    return this.getRepository(AMCContractRepository);
  }

  /**
   * Get the AMC machine repository
   * @returns AMC machine repository instance
   */
  public getAMCMachineRepository(): AMCMachineRepository {
    return this.getRepository(AMCMachineRepository);
  }

  /**
   * Get the reference data repository for a specific model
   * @param modelName Name of the reference data model
   * @returns Reference data repository instance
   */
  public getReferenceDataRepository(modelName: string): ReferenceDataRepository {
    if (!ReferenceDataRepository.isValidReferenceModel(modelName)) {
      throw new Error(`Invalid reference model: ${modelName}`);
    }

    const repositoryName = `ReferenceDataRepository_${modelName}`;

    if (!this.repositories.has(repositoryName)) {
      // Find the mapped table name
      const normalizedModelName = modelName.toLowerCase().replace(/_/g, '-');
      const mappingKey = Object.keys(ReferenceDataRepository['modelMapping']).find(key =>
        key.toLowerCase() === normalizedModelName ||
        key.toLowerCase().replace(/_/g, '-') === normalizedModelName
      );

      const tableName = mappingKey
        ? ReferenceDataRepository['modelMapping'][mappingKey]
        : modelName;

      // Check if the table exists in the database
      const tableExists = ReferenceDataRepository.tableExists(tableName);

      // Log a warning if the table doesn't exist
      if (!tableExists) {
        console.warn(`Table '${tableName}' does not exist in the database, using mock model`);
      }

      this.repositories.set(repositoryName, new ReferenceDataRepository(modelName));
    }

    return this.repositories.get(repositoryName);
  }

  /**
   * Get the history detail repository
   * @returns History detail repository instance
   */
  public getHistoryDetailRepository(): HistoryDetailRepository {
    return this.getRepository(HistoryDetailRepository);
  }

  /**
   * Get the visit card repository
   * @returns Visit card repository instance
   */
  public getVisitCardRepository(): VisitCardRepository {
    return this.getRepository(VisitCardRepository);
  }

  /**
   * Get the email template repository
   * @returns Email template repository instance
   */
  public getEmailTemplateRepository(): EmailTemplateRepository {
    const repositoryName = EmailTemplateRepository.name;

    if (!this.repositories.has(repositoryName)) {
      // Import prisma here to avoid circular dependencies
      const { prisma } = require('@/lib/prisma');
      this.repositories.set(repositoryName, new EmailTemplateRepository(prisma));
    }

    return this.repositories.get(repositoryName);
  }

  /**
   * Get the email log repository
   * @returns Email log repository instance
   */
  public getEmailLogRepository(): EmailLogRepository {
    const repositoryName = EmailLogRepository.name;

    if (!this.repositories.has(repositoryName)) {
      // Import prisma here to avoid circular dependencies
      const { prisma } = require('@/lib/prisma');
      this.repositories.set(repositoryName, new EmailLogRepository(prisma));
    }

    return this.repositories.get(repositoryName);
  }

  /**
   * Get the activity log repository
   * @returns Activity log repository instance
   */
  public getActivityLogRepository(): ActivityLogRepository {
    return this.getRepository(ActivityLogRepository);
  }

  /**
   * Clear all repository instances
   * Useful for testing
   */
  public clearRepositories(): void {
    this.repositories.clear();
  }
}

/**
 * Get the repository factory instance
 * @returns Repository factory instance
 */
export function getRepositoryFactory(): RepositoryFactory {
  return RepositoryFactory.getInstance();
}

/**
 * Get the user repository
 * @returns User repository instance
 */
export function getUserRepository(): UserRepository {
  return getRepositoryFactory().getUserRepository();
}

/**
 * Get the customer repository
 * @returns Customer repository instance
 */
export function getCustomerRepository(): CustomerRepository {
  return getRepositoryFactory().getCustomerRepository();
}

/**
 * Get the AMC contract repository
 * @returns AMC contract repository instance
 */
export function getAMCContractRepository(): AMCContractRepository {
  return getRepositoryFactory().getAMCContractRepository();
}

/**
 * Get the AMC machine repository
 * @returns AMC machine repository instance
 */
export function getAMCMachineRepository(): AMCMachineRepository {
  return getRepositoryFactory().getAMCMachineRepository();
}

/**
 * Get the reference data repository for a specific model
 * @param modelName Name of the reference data model
 * @returns Reference data repository instance
 */
export function getReferenceDataRepository(modelName: string): ReferenceDataRepository {
  return getRepositoryFactory().getReferenceDataRepository(modelName);
}

/**
 * Get the history detail repository
 * @returns History detail repository instance
 */
export function getHistoryDetailRepository(): HistoryDetailRepository {
  return getRepositoryFactory().getHistoryDetailRepository();
}

/**
 * Get the visit card repository
 * @returns Visit card repository instance
 */
export function getVisitCardRepository(): VisitCardRepository {
  return getRepositoryFactory().getVisitCardRepository();
}

/**
 * Get the email template repository
 * @returns Email template repository instance
 */
export function getEmailTemplateRepository(): EmailTemplateRepository {
  return getRepositoryFactory().getEmailTemplateRepository();
}

/**
 * Get the email log repository
 * @returns Email log repository instance
 */
export function getEmailLogRepository(): EmailLogRepository {
  return getRepositoryFactory().getEmailLogRepository();
}

/**
 * Get the activity log repository
 * @returns Activity log repository instance
 */
export function getActivityLogRepository(): ActivityLogRepository {
  return getRepositoryFactory().getActivityLogRepository();
}
