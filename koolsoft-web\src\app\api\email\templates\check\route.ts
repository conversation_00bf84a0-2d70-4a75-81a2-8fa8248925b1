import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/email/templates/check
 * Check if email templates exist in the database
 */
export async function GET(request: NextRequest) {
  try {
    // Check if email_templates table exists
    try {
      await prisma.$queryRaw`SELECT 1 FROM email_templates LIMIT 1`;
    } catch (error) {
      return NextResponse.json({
        status: 'error',
        message: 'email_templates table does not exist or cannot be accessed',
        error: String(error),
      }, { status: 500 });
    }
    
    // Count existing templates
    const count = await prisma.EmailTemplate.count();
    
    // Get template list if any exist
    let templates = [];
    if (count > 0) {
      templates = await prisma.EmailTemplate.findMany({
        select: {
          id: true,
          name: true,
          subject: true,
          bodyHtml: false, // Don't include the full HTML body in the response
          bodyText: false, // Don't include the full text body in the response
          description: true,
          variables: true,
          category: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        }
      });
    }
    
    return NextResponse.json({
      status: 'success',
      count,
      templates,
    });
  } catch (error) {
    console.error('Error checking email templates:', error);
    return NextResponse.json({
      status: 'error',
      message: 'Failed to check email templates',
      error: String(error),
    }, { status: 500 });
  }
}
