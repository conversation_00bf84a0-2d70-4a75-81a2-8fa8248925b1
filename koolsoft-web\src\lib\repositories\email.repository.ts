import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Email Template Repository
 *
 * This repository handles database operations for the Email Template entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class EmailTemplateRepository extends PrismaRepository<
  Prisma.EmailTemplateGetPayload<{}>,
  string,
  Prisma.EmailTemplateCreateInput,
  Prisma.EmailTemplateUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('EmailTemplate');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find email template by name
   * @param name Template name
   * @returns Promise resolving to the email template or null if not found
   */
  async findByName(name: string): Promise<Prisma.EmailTemplateGetPayload<{}> | null> {
    return this.model.findFirst({
      where: { name },
    });
  }

  /**
   * Find active email templates
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of active email templates
   */
  async findActive(skip?: number, take?: number): Promise<Prisma.EmailTemplateGetPayload<{}>[]> {
    return this.model.findMany({
      where: { isActive: true },
      skip,
      take,
      orderBy: { name: 'asc' },
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.EmailTemplateGetPayload<{}>,
    string,
    Prisma.EmailTemplateCreateInput,
    Prisma.EmailTemplateUpdateInput
  > {
    const repo = new EmailTemplateRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}

/**
 * Email Log Repository
 *
 * This repository handles database operations for the Email Log entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class EmailLogRepository extends PrismaRepository<
  Prisma.EmailLogGetPayload<{}>,
  string,
  Prisma.EmailLogCreateInput,
  Prisma.EmailLogUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('EmailLog');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find email logs by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of email logs
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<Prisma.EmailLogGetPayload<{}>[]> {
    return this.model.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { sentAt: 'desc' },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Find email logs by date range
   * @param startDate Start date
   * @param endDate End date
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of email logs
   */
  async findByDateRange(startDate: Date, endDate: Date, skip?: number, take?: number): Promise<Prisma.EmailLogGetPayload<{}>[]> {
    return this.model.findMany({
      where: {
        sentAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      skip,
      take,
      orderBy: { sentAt: 'desc' },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Find email logs by user ID
   * @param userId User ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of email logs
   */
  async findByUserId(userId: string, skip?: number, take?: number): Promise<Prisma.EmailLogGetPayload<{}>[]> {
    return this.model.findMany({
      where: { userId },
      skip,
      take,
      orderBy: { sentAt: 'desc' },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Find email logs by status
   * @param status Email log status
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of email logs
   */
  async findByStatus(status: string, skip?: number, take?: number): Promise<Prisma.EmailLogGetPayload<{}>[]> {
    return this.model.findMany({
      where: { status },
      skip,
      take,
      orderBy: { sentAt: 'desc' },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.EmailLogGetPayload<{}>,
    string,
    Prisma.EmailLogCreateInput,
    Prisma.EmailLogUpdateInput
  > {
    const repo = new EmailLogRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
