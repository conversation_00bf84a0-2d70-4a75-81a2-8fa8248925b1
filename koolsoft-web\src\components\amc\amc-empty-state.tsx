'use client';

import { FileX, Search, Plus, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

type AMCEmptyStateProps = {
  type: 'no-results' | 'no-contracts' | 'error';
  message?: string;
  isFiltered?: boolean;
  onClearFilters?: () => void;
  onRetry?: () => void;
};

/**
 * AMC Empty State Component
 * 
 * This component displays appropriate empty state messages based on the context.
 * It follows the UI standards with consistent styling and actionable buttons.
 */
export function AMCEmptyState({ 
  type, 
  message, 
  isFiltered = false,
  onClearFilters,
  onRetry
}: AMCEmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center bg-gray-50 rounded-lg border border-gray-200">
      {type === 'no-results' && (
        <>
          <Search className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-1">No matching contracts found</h3>
          <p className="text-sm text-gray-500 mb-6 max-w-md">
            {message || "Try adjusting your search or filter to find what you're looking for."}
          </p>
          {isFiltered && onClearFilters && (
            <Button variant="outline" onClick={onClearFilters}>
              Clear all filters
            </Button>
          )}
        </>
      )}
      
      {type === 'no-contracts' && (
        <>
          <FileX className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-1">No AMC contracts yet</h3>
          <p className="text-sm text-gray-500 mb-6 max-w-md">
            {message || "Get started by creating your first AMC contract."}
          </p>
          <Button asChild>
            <Link href="/amc/new">
              <Plus className="h-4 w-4 mr-2" />
              Create AMC Contract
            </Link>
          </Button>
        </>
      )}
      
      {type === 'error' && (
        <>
          <FileX className="h-12 w-12 text-red-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-1">Something went wrong</h3>
          <p className="text-sm text-gray-500 mb-6 max-w-md">
            {message || "There was an error loading the AMC contracts. Please try again."}
          </p>
          <div className="flex gap-3">
            <Button onClick={onRetry || (() => window.location.reload())}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
            {isFiltered && onClearFilters && (
              <Button variant="outline" onClick={onClearFilters}>
                Clear filters
              </Button>
            )}
          </div>
        </>
      )}
    </div>
  );
}
