'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { BarChart4 } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * Sales Layout Component
 *
 * This component provides a consistent layout for all sales-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function SalesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title based on the pathname
  let pageTitle = 'Sales';
  if (pathname !== '/sales') {
    if (pathname.includes('/edit')) {
      pageTitle = 'Edit Sales Lead';
    } else if (pathname.includes('/new')) {
      pageTitle = 'New Sales Lead';
    } else {
      pageTitle = 'Sales Lead Details';
    }
  }

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Sales', href: '/sales', icon: <BarChart4 className="h-4 w-4" /> }
  ];

  // Add additional breadcrumb for subpages
  if (pathname !== '/sales') {
    breadcrumbs.push({ label: pageTitle, current: true });
  }

  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
