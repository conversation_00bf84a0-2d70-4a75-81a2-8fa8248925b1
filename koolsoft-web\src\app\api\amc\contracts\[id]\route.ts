import { NextRequest, NextResponse } from 'next/server';
import { getAMCContractRepository } from '@/lib/repositories';
import { updateAMCContractSchema } from '@/lib/validations/amc-contract.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { z } from 'zod';

/**
 * GET /api/amc/contracts/[id]
 * Get a specific AMC contract by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcContractRepository = getAMCContractRepository();

      // Get AMC contract with all related data
      const contract = await amcContractRepository.findWithRelations(id);

      if (!contract) {
        return NextResponse.json(
          { error: 'AMC contract not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(contract);
    } catch (error) {
      console.error('Error fetching AMC contract:', error);
      return NextResponse.json(
        { error: 'Failed to fetch AMC contract' },
        { status: 500 }
      );
    }
  }
);

/**
 * PATCH /api/amc/contracts/[id]
 * Update a specific AMC contract
 */
export const PATCH = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = updateAMCContractSchema.parse(body);

        const amcContractRepository = getAMCContractRepository();

        // Check if AMC contract exists
        const existingContract = await amcContractRepository.findById(id);

        if (!existingContract) {
          return NextResponse.json(
            { error: 'AMC contract not found' },
            { status: 404 }
          );
        }

        // Update AMC contract with related data
        const updatedContract = await amcContractRepository.updateWithRelations(id, validatedData);

        return NextResponse.json(updatedContract);
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error updating AMC contract:', error);

      if (error instanceof PrismaClientKnownRequestError) {
        // Handle specific Prisma errors
        if (error.code === 'P2002') {
          return NextResponse.json(
            {
              error: 'Duplicate entry',
              details: 'A record with this identifier already exists',
              code: 'DUPLICATE_ERROR'
            },
            { status: 409 }
          );
        }

        if (error.code === 'P2003') {
          return NextResponse.json(
            {
              error: 'Foreign key constraint failed',
              details: 'One of the referenced records does not exist',
              code: 'FOREIGN_KEY_ERROR'
            },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        {
          error: 'Failed to update AMC contract',
          message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
          code: 'INTERNAL_ERROR'
        },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/amc/contracts/[id]
 * Delete a specific AMC contract
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcContractRepository = getAMCContractRepository();

      // Check if AMC contract exists
      const existingContract = await amcContractRepository.findById(id);

      if (!existingContract) {
        return NextResponse.json(
          { error: 'AMC contract not found' },
          { status: 404 }
        );
      }

      // Delete AMC contract and related data in a transaction
      await amcContractRepository.prisma.$transaction(async (tx) => {
        // Delete related service dates
        await tx.amc_service_dates.deleteMany({
          where: { amcContractId: id },
        });

        // Delete related payments
        await tx.amc_payments.deleteMany({
          where: { amcContractId: id },
        });

        // Delete related divisions
        await tx.amc_divisions.deleteMany({
          where: { amcContractId: id },
        });

        // Delete related machines and their components
        const machines = await tx.amc_machines.findMany({
          where: { amcContractId: id },
          select: { id: true },
        });

        for (const machine of machines) {
          await tx.amc_components.deleteMany({
            where: { amcMachineId: machine.id },
          });
        }

        await tx.amc_machines.deleteMany({
          where: { amcContractId: id },
        });

        // Finally delete the contract
        await tx.amc_contracts.delete({
          where: { id },
        });
      });

      return NextResponse.json(
        { message: 'AMC contract deleted successfully' },
        { status: 200 }
      );
    } catch (error) {
      console.error('Error deleting AMC contract:', error);

      if (error instanceof PrismaClientKnownRequestError) {
        // Handle specific Prisma errors
        if (error.code === 'P2025') {
          return NextResponse.json(
            {
              error: 'Record not found',
              details: 'The AMC contract you are trying to delete does not exist',
              code: 'NOT_FOUND_ERROR'
            },
            { status: 404 }
          );
        }

        if (error.code === 'P2003') {
          return NextResponse.json(
            {
              error: 'Foreign key constraint failed',
              details: 'This AMC contract cannot be deleted because it is referenced by other records',
              code: 'FOREIGN_KEY_ERROR'
            },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        {
          error: 'Failed to delete AMC contract',
          message: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred',
          code: 'INTERNAL_ERROR'
        },
        { status: 500 }
      );
    }
  }
);
