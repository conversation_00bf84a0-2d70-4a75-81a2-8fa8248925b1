"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-style-singleton";
exports.ids = ["vendor-chunks/react-style-singleton"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-style-singleton/dist/es2015/component.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-style-singleton/dist/es2015/component.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   styleSingleton: () => (/* binding */ styleSingleton)\n/* harmony export */ });\n/* harmony import */ var _hook__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hook */ \"(ssr)/./node_modules/react-style-singleton/dist/es2015/hook.js\");\n\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nvar styleSingleton = function () {\n  var useStyle = (0,_hook__WEBPACK_IMPORTED_MODULE_0__.styleHookSingleton)();\n  var Sheet = function (_a) {\n    var styles = _a.styles,\n      dynamic = _a.dynamic;\n    useStyle(styles, dynamic);\n    return null;\n  };\n  return Sheet;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3R5bGUtc2luZ2xldG9uL2Rpc3QvZXMyMDE1L2NvbXBvbmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxJQUFJQyxjQUFjLEdBQUcsU0FBQUEsQ0FBQSxFQUFZO0VBQ3BDLElBQUlDLFFBQVEsR0FBR0YseURBQWtCLENBQUMsQ0FBQztFQUNuQyxJQUFJRyxLQUFLLEdBQUcsU0FBQUEsQ0FBVUMsRUFBRSxFQUFFO0lBQ3RCLElBQUlDLE1BQU0sR0FBR0QsRUFBRSxDQUFDQyxNQUFNO01BQUVDLE9BQU8sR0FBR0YsRUFBRSxDQUFDRSxPQUFPO0lBQzVDSixRQUFRLENBQUNHLE1BQU0sRUFBRUMsT0FBTyxDQUFDO0lBQ3pCLE9BQU8sSUFBSTtFQUNmLENBQUM7RUFDRCxPQUFPSCxLQUFLO0FBQ2hCLENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxyZWFjdC1zdHlsZS1zaW5nbGV0b25cXGRpc3RcXGVzMjAxNVxcY29tcG9uZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN0eWxlSG9va1NpbmdsZXRvbiB9IGZyb20gJy4vaG9vayc7XG4vKipcbiAqIGNyZWF0ZSBhIENvbXBvbmVudCB0byBhZGQgc3R5bGVzIG9uIGRlbWFuZFxuICogLSBzdHlsZXMgYXJlIGFkZGVkIHdoZW4gZmlyc3QgaW5zdGFuY2UgaXMgbW91bnRlZFxuICogLSBzdHlsZXMgYXJlIHJlbW92ZWQgd2hlbiB0aGUgbGFzdCBpbnN0YW5jZSBpcyB1bm1vdW50ZWRcbiAqIC0gY2hhbmdpbmcgc3R5bGVzIGluIHJ1bnRpbWUgZG9lcyBub3RoaW5nIHVubGVzcyBkeW5hbWljIGlzIHNldC4gQnV0IHdpdGggbXVsdGlwbGUgY29tcG9uZW50cyB0aGF0IGNhbiBsZWFkIHRvIHRoZSB1bmRlZmluZWQgYmVoYXZpb3JcbiAqL1xuZXhwb3J0IHZhciBzdHlsZVNpbmdsZXRvbiA9IGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgdXNlU3R5bGUgPSBzdHlsZUhvb2tTaW5nbGV0b24oKTtcbiAgICB2YXIgU2hlZXQgPSBmdW5jdGlvbiAoX2EpIHtcbiAgICAgICAgdmFyIHN0eWxlcyA9IF9hLnN0eWxlcywgZHluYW1pYyA9IF9hLmR5bmFtaWM7XG4gICAgICAgIHVzZVN0eWxlKHN0eWxlcywgZHluYW1pYyk7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH07XG4gICAgcmV0dXJuIFNoZWV0O1xufTtcbiJdLCJuYW1lcyI6WyJzdHlsZUhvb2tTaW5nbGV0b24iLCJzdHlsZVNpbmdsZXRvbiIsInVzZVN0eWxlIiwiU2hlZXQiLCJfYSIsInN0eWxlcyIsImR5bmFtaWMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-style-singleton/dist/es2015/component.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-style-singleton/dist/es2015/hook.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-style-singleton/dist/es2015/hook.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   styleHookSingleton: () => (/* binding */ styleHookSingleton)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _singleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./singleton */ \"(ssr)/./node_modules/react-style-singleton/dist/es2015/singleton.js\");\n\n\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nvar styleHookSingleton = function () {\n  var sheet = (0,_singleton__WEBPACK_IMPORTED_MODULE_1__.stylesheetSingleton)();\n  return function (styles, isDynamic) {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n      sheet.add(styles);\n      return function () {\n        sheet.remove();\n      };\n    }, [styles && isDynamic]);\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3R5bGUtc2luZ2xldG9uL2Rpc3QvZXMyMDE1L2hvb2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErQjtBQUNtQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxJQUFJRSxrQkFBa0IsR0FBRyxTQUFBQSxDQUFBLEVBQVk7RUFDeEMsSUFBSUMsS0FBSyxHQUFHRiwrREFBbUIsQ0FBQyxDQUFDO0VBQ2pDLE9BQU8sVUFBVUcsTUFBTSxFQUFFQyxTQUFTLEVBQUU7SUFDaENMLDRDQUFlLENBQUMsWUFBWTtNQUN4QkcsS0FBSyxDQUFDSSxHQUFHLENBQUNILE1BQU0sQ0FBQztNQUNqQixPQUFPLFlBQVk7UUFDZkQsS0FBSyxDQUFDSyxNQUFNLENBQUMsQ0FBQztNQUNsQixDQUFDO0lBQ0wsQ0FBQyxFQUFFLENBQUNKLE1BQU0sSUFBSUMsU0FBUyxDQUFDLENBQUM7RUFDN0IsQ0FBQztBQUNMLENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxyZWFjdC1zdHlsZS1zaW5nbGV0b25cXGRpc3RcXGVzMjAxNVxcaG9vay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBzdHlsZXNoZWV0U2luZ2xldG9uIH0gZnJvbSAnLi9zaW5nbGV0b24nO1xuLyoqXG4gKiBjcmVhdGVzIGEgaG9vayB0byBjb250cm9sIHN0eWxlIHNpbmdsZXRvblxuICogQHNlZSB7QGxpbmsgc3R5bGVTaW5nbGV0b259IGZvciBhIHNhZmVyIGNvbXBvbmVudCB2ZXJzaW9uXG4gKiBAZXhhbXBsZVxuICogYGBgdHN4XG4gKiBjb25zdCB1c2VTdHlsZSA9IHN0eWxlSG9va1NpbmdsZXRvbigpO1xuICogLy8vXG4gKiB1c2VTdHlsZSgnYm9keSB7IG92ZXJmbG93OiBoaWRkZW59Jyk7XG4gKi9cbmV4cG9ydCB2YXIgc3R5bGVIb29rU2luZ2xldG9uID0gZnVuY3Rpb24gKCkge1xuICAgIHZhciBzaGVldCA9IHN0eWxlc2hlZXRTaW5nbGV0b24oKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKHN0eWxlcywgaXNEeW5hbWljKSB7XG4gICAgICAgIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICBzaGVldC5hZGQoc3R5bGVzKTtcbiAgICAgICAgICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgc2hlZXQucmVtb3ZlKCk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9LCBbc3R5bGVzICYmIGlzRHluYW1pY10pO1xuICAgIH07XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0Iiwic3R5bGVzaGVldFNpbmdsZXRvbiIsInN0eWxlSG9va1NpbmdsZXRvbiIsInNoZWV0Iiwic3R5bGVzIiwiaXNEeW5hbWljIiwidXNlRWZmZWN0IiwiYWRkIiwicmVtb3ZlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-style-singleton/dist/es2015/hook.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-style-singleton/dist/es2015/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-style-singleton/dist/es2015/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   styleHookSingleton: () => (/* reexport safe */ _hook__WEBPACK_IMPORTED_MODULE_2__.styleHookSingleton),\n/* harmony export */   styleSingleton: () => (/* reexport safe */ _component__WEBPACK_IMPORTED_MODULE_0__.styleSingleton),\n/* harmony export */   stylesheetSingleton: () => (/* reexport safe */ _singleton__WEBPACK_IMPORTED_MODULE_1__.stylesheetSingleton)\n/* harmony export */ });\n/* harmony import */ var _component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./component */ \"(ssr)/./node_modules/react-style-singleton/dist/es2015/component.js\");\n/* harmony import */ var _singleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./singleton */ \"(ssr)/./node_modules/react-style-singleton/dist/es2015/singleton.js\");\n/* harmony import */ var _hook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hook */ \"(ssr)/./node_modules/react-style-singleton/dist/es2015/hook.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3Qtc3R5bGUtc2luZ2xldG9uL2Rpc3QvZXMyMDE1L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2QztBQUNLIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xccmVhY3Qtc3R5bGUtc2luZ2xldG9uXFxkaXN0XFxlczIwMTVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IHN0eWxlU2luZ2xldG9uIH0gZnJvbSAnLi9jb21wb25lbnQnO1xuZXhwb3J0IHsgc3R5bGVzaGVldFNpbmdsZXRvbiB9IGZyb20gJy4vc2luZ2xldG9uJztcbmV4cG9ydCB7IHN0eWxlSG9va1NpbmdsZXRvbiB9IGZyb20gJy4vaG9vayc7XG4iXSwibmFtZXMiOlsic3R5bGVTaW5nbGV0b24iLCJzdHlsZXNoZWV0U2luZ2xldG9uIiwic3R5bGVIb29rU2luZ2xldG9uIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-style-singleton/dist/es2015/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-style-singleton/dist/es2015/singleton.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-style-singleton/dist/es2015/singleton.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stylesheetSingleton: () => (/* binding */ stylesheetSingleton)\n/* harmony export */ });\n/* harmony import */ var get_nonce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! get-nonce */ \"(ssr)/./node_modules/get-nonce/dist/es2015/index.js\");\n\nfunction makeStyleTag() {\n  if (!document) return null;\n  var tag = document.createElement('style');\n  tag.type = 'text/css';\n  var nonce = (0,get_nonce__WEBPACK_IMPORTED_MODULE_0__.getNonce)();\n  if (nonce) {\n    tag.setAttribute('nonce', nonce);\n  }\n  return tag;\n}\nfunction injectStyles(tag, css) {\n  // @ts-ignore\n  if (tag.styleSheet) {\n    // @ts-ignore\n    tag.styleSheet.cssText = css;\n  } else {\n    tag.appendChild(document.createTextNode(css));\n  }\n}\nfunction insertStyleTag(tag) {\n  var head = document.head || document.getElementsByTagName('head')[0];\n  head.appendChild(tag);\n}\nvar stylesheetSingleton = function () {\n  var counter = 0;\n  var stylesheet = null;\n  return {\n    add: function (style) {\n      if (counter == 0) {\n        if (stylesheet = makeStyleTag()) {\n          injectStyles(stylesheet, style);\n          insertStyleTag(stylesheet);\n        }\n      }\n      counter++;\n    },\n    remove: function () {\n      counter--;\n      if (!counter && stylesheet) {\n        stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n        stylesheet = null;\n      }\n    }\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-style-singleton/dist/es2015/singleton.js\n");

/***/ })

};
;