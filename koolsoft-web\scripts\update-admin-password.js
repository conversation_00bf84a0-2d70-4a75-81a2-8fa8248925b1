// Script to update admin password
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Update admin user password to a properly hashed version of 'Admin@123'
    const result = await prisma.$executeRaw`
      UPDATE users 
      SET password = '$2b$10$XJrEhKGrMxfXCn5JDkKzuOewwjzIcgKT1nWgZ1QcFBCjYKjwaXKHa' 
      WHERE email = '<EMAIL>'
    `;
    
    console.log(`Updated ${result} user(s)`);
  } catch (error) {
    console.error('Error updating admin password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
