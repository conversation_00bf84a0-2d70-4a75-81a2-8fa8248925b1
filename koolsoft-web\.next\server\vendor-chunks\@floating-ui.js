"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui";
exports.ids = ["vendor-chunks/@floating-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@floating-ui/core/dist/floating-ui.core.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   rectToClientRect: () => (/* reexport safe */ _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n  const alignmentAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n  const alignLength = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(alignmentAxis);\n  const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n  const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n    const length = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment), ...allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) !== alignment)] : allowedPlacements.filter(placement => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment || (autoAlignment ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAlignmentPlacement)(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n      const initialSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(initialPlacement);\n      const isBasePlacement = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositePlacement)(initialPlacement)] : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getExpandedPlacements)(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxisPlacements)(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\nfunction getBoundingRect(rects) {\n  const minX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map(rect => rect.left));\n  const minY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map(rect => rect.top));\n  const maxX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map(rect => rect.right));\n  const maxY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(nativeClientRects));\n      const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === 'left';\n          const maxRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...clientRects.map(rect => rect.right));\n          const minLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n  const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n  const isVertical = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n      const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n      const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n      const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n      const isYAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, 0);\n        const xMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.right, 0);\n        const yMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, 0);\n        const yMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGZsb2F0aW5nLXVpL2NvcmUvZGlzdC9mbG9hdGluZy11aS5jb3JlLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQXNVO0FBQ2hSO0FBRXRELFNBQVNtQiwwQkFBMEJBLENBQUNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxHQUFHLEVBQUU7RUFDeEQsSUFBSTtJQUNGQyxTQUFTO0lBQ1RDO0VBQ0YsQ0FBQyxHQUFHSixJQUFJO0VBQ1IsTUFBTUssUUFBUSxHQUFHekIsK0RBQVcsQ0FBQ3FCLFNBQVMsQ0FBQztFQUN2QyxNQUFNSyxhQUFhLEdBQUd6QixvRUFBZ0IsQ0FBQ29CLFNBQVMsQ0FBQztFQUNqRCxNQUFNTSxXQUFXLEdBQUd6QixpRUFBYSxDQUFDd0IsYUFBYSxDQUFDO0VBQ2hELE1BQU1FLElBQUksR0FBR3pCLDJEQUFPLENBQUNrQixTQUFTLENBQUM7RUFDL0IsTUFBTVEsVUFBVSxHQUFHSixRQUFRLEtBQUssR0FBRztFQUNuQyxNQUFNSyxPQUFPLEdBQUdQLFNBQVMsQ0FBQ1EsQ0FBQyxHQUFHUixTQUFTLENBQUNTLEtBQUssR0FBRyxDQUFDLEdBQUdSLFFBQVEsQ0FBQ1EsS0FBSyxHQUFHLENBQUM7RUFDdEUsTUFBTUMsT0FBTyxHQUFHVixTQUFTLENBQUNXLENBQUMsR0FBR1gsU0FBUyxDQUFDWSxNQUFNLEdBQUcsQ0FBQyxHQUFHWCxRQUFRLENBQUNXLE1BQU0sR0FBRyxDQUFDO0VBQ3hFLE1BQU1DLFdBQVcsR0FBR2IsU0FBUyxDQUFDSSxXQUFXLENBQUMsR0FBRyxDQUFDLEdBQUdILFFBQVEsQ0FBQ0csV0FBVyxDQUFDLEdBQUcsQ0FBQztFQUMxRSxJQUFJVSxNQUFNO0VBQ1YsUUFBUVQsSUFBSTtJQUNWLEtBQUssS0FBSztNQUNSUyxNQUFNLEdBQUc7UUFDUE4sQ0FBQyxFQUFFRCxPQUFPO1FBQ1ZJLENBQUMsRUFBRVgsU0FBUyxDQUFDVyxDQUFDLEdBQUdWLFFBQVEsQ0FBQ1c7TUFDNUIsQ0FBQztNQUNEO0lBQ0YsS0FBSyxRQUFRO01BQ1hFLE1BQU0sR0FBRztRQUNQTixDQUFDLEVBQUVELE9BQU87UUFDVkksQ0FBQyxFQUFFWCxTQUFTLENBQUNXLENBQUMsR0FBR1gsU0FBUyxDQUFDWTtNQUM3QixDQUFDO01BQ0Q7SUFDRixLQUFLLE9BQU87TUFDVkUsTUFBTSxHQUFHO1FBQ1BOLENBQUMsRUFBRVIsU0FBUyxDQUFDUSxDQUFDLEdBQUdSLFNBQVMsQ0FBQ1MsS0FBSztRQUNoQ0UsQ0FBQyxFQUFFRDtNQUNMLENBQUM7TUFDRDtJQUNGLEtBQUssTUFBTTtNQUNUSSxNQUFNLEdBQUc7UUFDUE4sQ0FBQyxFQUFFUixTQUFTLENBQUNRLENBQUMsR0FBR1AsUUFBUSxDQUFDUSxLQUFLO1FBQy9CRSxDQUFDLEVBQUVEO01BQ0wsQ0FBQztNQUNEO0lBQ0Y7TUFDRUksTUFBTSxHQUFHO1FBQ1BOLENBQUMsRUFBRVIsU0FBUyxDQUFDUSxDQUFDO1FBQ2RHLENBQUMsRUFBRVgsU0FBUyxDQUFDVztNQUNmLENBQUM7RUFDTDtFQUNBLFFBQVE5QixnRUFBWSxDQUFDaUIsU0FBUyxDQUFDO0lBQzdCLEtBQUssT0FBTztNQUNWZ0IsTUFBTSxDQUFDWCxhQUFhLENBQUMsSUFBSVUsV0FBVyxJQUFJZCxHQUFHLElBQUlPLFVBQVUsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7TUFDbkU7SUFDRixLQUFLLEtBQUs7TUFDUlEsTUFBTSxDQUFDWCxhQUFhLENBQUMsSUFBSVUsV0FBVyxJQUFJZCxHQUFHLElBQUlPLFVBQVUsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUM7TUFDbkU7RUFDSjtFQUNBLE9BQU9RLE1BQU07QUFDZjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU1DLGVBQWUsR0FBRyxNQUFBQSxDQUFPZixTQUFTLEVBQUVDLFFBQVEsRUFBRWUsTUFBTSxLQUFLO0VBQzdELE1BQU07SUFDSmxCLFNBQVMsR0FBRyxRQUFRO0lBQ3BCbUIsUUFBUSxHQUFHLFVBQVU7SUFDckJDLFVBQVUsR0FBRyxFQUFFO0lBQ2ZDO0VBQ0YsQ0FBQyxHQUFHSCxNQUFNO0VBQ1YsTUFBTUksZUFBZSxHQUFHRixVQUFVLENBQUNHLE1BQU0sQ0FBQ0MsT0FBTyxDQUFDO0VBQ2xELE1BQU12QixHQUFHLEdBQUcsT0FBT29CLFFBQVEsQ0FBQ0ksS0FBSyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0osUUFBUSxDQUFDSSxLQUFLLENBQUN0QixRQUFRLENBQUMsQ0FBQztFQUM5RSxJQUFJdUIsS0FBSyxHQUFHLE1BQU1MLFFBQVEsQ0FBQ00sZUFBZSxDQUFDO0lBQ3pDekIsU0FBUztJQUNUQyxRQUFRO0lBQ1JnQjtFQUNGLENBQUMsQ0FBQztFQUNGLElBQUk7SUFDRlQsQ0FBQztJQUNERztFQUNGLENBQUMsR0FBR2YsMEJBQTBCLENBQUM0QixLQUFLLEVBQUUxQixTQUFTLEVBQUVDLEdBQUcsQ0FBQztFQUNyRCxJQUFJMkIsaUJBQWlCLEdBQUc1QixTQUFTO0VBQ2pDLElBQUk2QixjQUFjLEdBQUcsQ0FBQyxDQUFDO0VBQ3ZCLElBQUlDLFVBQVUsR0FBRyxDQUFDO0VBQ2xCLEtBQUssSUFBSUMsQ0FBQyxHQUFHLENBQUMsRUFBRUEsQ0FBQyxHQUFHVCxlQUFlLENBQUNVLE1BQU0sRUFBRUQsQ0FBQyxFQUFFLEVBQUU7SUFDL0MsTUFBTTtNQUNKRSxJQUFJO01BQ0pDO0lBQ0YsQ0FBQyxHQUFHWixlQUFlLENBQUNTLENBQUMsQ0FBQztJQUN0QixNQUFNO01BQ0pyQixDQUFDLEVBQUV5QixLQUFLO01BQ1J0QixDQUFDLEVBQUV1QixLQUFLO01BQ1JDLElBQUk7TUFDSkM7SUFDRixDQUFDLEdBQUcsTUFBTUosRUFBRSxDQUFDO01BQ1h4QixDQUFDO01BQ0RHLENBQUM7TUFDRDBCLGdCQUFnQixFQUFFdkMsU0FBUztNQUMzQkEsU0FBUyxFQUFFNEIsaUJBQWlCO01BQzVCVCxRQUFRO01BQ1JVLGNBQWM7TUFDZEgsS0FBSztNQUNMTCxRQUFRO01BQ1JtQixRQUFRLEVBQUU7UUFDUnRDLFNBQVM7UUFDVEM7TUFDRjtJQUNGLENBQUMsQ0FBQztJQUNGTyxDQUFDLEdBQUd5QixLQUFLLElBQUksSUFBSSxHQUFHQSxLQUFLLEdBQUd6QixDQUFDO0lBQzdCRyxDQUFDLEdBQUd1QixLQUFLLElBQUksSUFBSSxHQUFHQSxLQUFLLEdBQUd2QixDQUFDO0lBQzdCZ0IsY0FBYyxHQUFHO01BQ2YsR0FBR0EsY0FBYztNQUNqQixDQUFDSSxJQUFJLEdBQUc7UUFDTixHQUFHSixjQUFjLENBQUNJLElBQUksQ0FBQztRQUN2QixHQUFHSTtNQUNMO0lBQ0YsQ0FBQztJQUNELElBQUlDLEtBQUssSUFBSVIsVUFBVSxJQUFJLEVBQUUsRUFBRTtNQUM3QkEsVUFBVSxFQUFFO01BQ1osSUFBSSxPQUFPUSxLQUFLLEtBQUssUUFBUSxFQUFFO1FBQzdCLElBQUlBLEtBQUssQ0FBQ3RDLFNBQVMsRUFBRTtVQUNuQjRCLGlCQUFpQixHQUFHVSxLQUFLLENBQUN0QyxTQUFTO1FBQ3JDO1FBQ0EsSUFBSXNDLEtBQUssQ0FBQ1osS0FBSyxFQUFFO1VBQ2ZBLEtBQUssR0FBR1ksS0FBSyxDQUFDWixLQUFLLEtBQUssSUFBSSxHQUFHLE1BQU1MLFFBQVEsQ0FBQ00sZUFBZSxDQUFDO1lBQzVEekIsU0FBUztZQUNUQyxRQUFRO1lBQ1JnQjtVQUNGLENBQUMsQ0FBQyxHQUFHbUIsS0FBSyxDQUFDWixLQUFLO1FBQ2xCO1FBQ0EsQ0FBQztVQUNDaEIsQ0FBQztVQUNERztRQUNGLENBQUMsR0FBR2YsMEJBQTBCLENBQUM0QixLQUFLLEVBQUVFLGlCQUFpQixFQUFFM0IsR0FBRyxDQUFDO01BQy9EO01BQ0E4QixDQUFDLEdBQUcsQ0FBQyxDQUFDO0lBQ1I7RUFDRjtFQUNBLE9BQU87SUFDTHJCLENBQUM7SUFDREcsQ0FBQztJQUNEYixTQUFTLEVBQUU0QixpQkFBaUI7SUFDNUJULFFBQVE7SUFDUlU7RUFDRixDQUFDO0FBQ0gsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZVksY0FBY0EsQ0FBQ0MsS0FBSyxFQUFFQyxPQUFPLEVBQUU7RUFDNUMsSUFBSUMscUJBQXFCO0VBQ3pCLElBQUlELE9BQU8sS0FBSyxLQUFLLENBQUMsRUFBRTtJQUN0QkEsT0FBTyxHQUFHLENBQUMsQ0FBQztFQUNkO0VBQ0EsTUFBTTtJQUNKakMsQ0FBQztJQUNERyxDQUFDO0lBQ0RRLFFBQVE7SUFDUkssS0FBSztJQUNMYyxRQUFRO0lBQ1JyQjtFQUNGLENBQUMsR0FBR3VCLEtBQUs7RUFDVCxNQUFNO0lBQ0pHLFFBQVEsR0FBRyxtQkFBbUI7SUFDOUJDLFlBQVksR0FBRyxVQUFVO0lBQ3pCQyxjQUFjLEdBQUcsVUFBVTtJQUMzQkMsV0FBVyxHQUFHLEtBQUs7SUFDbkJDLE9BQU8sR0FBRztFQUNaLENBQUMsR0FBR2pFLDREQUFRLENBQUMyRCxPQUFPLEVBQUVELEtBQUssQ0FBQztFQUM1QixNQUFNUSxhQUFhLEdBQUdqRSxvRUFBZ0IsQ0FBQ2dFLE9BQU8sQ0FBQztFQUMvQyxNQUFNRSxVQUFVLEdBQUdKLGNBQWMsS0FBSyxVQUFVLEdBQUcsV0FBVyxHQUFHLFVBQVU7RUFDM0UsTUFBTUssT0FBTyxHQUFHWixRQUFRLENBQUNRLFdBQVcsR0FBR0csVUFBVSxHQUFHSixjQUFjLENBQUM7RUFDbkUsTUFBTU0sa0JBQWtCLEdBQUduRSxvRUFBZ0IsQ0FBQyxNQUFNbUMsUUFBUSxDQUFDaUMsZUFBZSxDQUFDO0lBQ3pFRixPQUFPLEVBQUUsQ0FBQyxDQUFDUixxQkFBcUIsR0FBRyxPQUFPdkIsUUFBUSxDQUFDa0MsU0FBUyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR2xDLFFBQVEsQ0FBQ2tDLFNBQVMsQ0FBQ0gsT0FBTyxDQUFDLENBQUMsS0FBSyxJQUFJLEdBQUdSLHFCQUFxQixHQUFHLElBQUksSUFBSVEsT0FBTyxHQUFHQSxPQUFPLENBQUNJLGNBQWMsS0FBSyxPQUFPbkMsUUFBUSxDQUFDb0Msa0JBQWtCLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHcEMsUUFBUSxDQUFDb0Msa0JBQWtCLENBQUNqQixRQUFRLENBQUNyQyxRQUFRLENBQUMsQ0FBQyxDQUFDO0lBQ25TMEMsUUFBUTtJQUNSQyxZQUFZO0lBQ1ozQjtFQUNGLENBQUMsQ0FBQyxDQUFDO0VBQ0gsTUFBTXVDLElBQUksR0FBR1gsY0FBYyxLQUFLLFVBQVUsR0FBRztJQUMzQ3JDLENBQUM7SUFDREcsQ0FBQztJQUNERixLQUFLLEVBQUVlLEtBQUssQ0FBQ3ZCLFFBQVEsQ0FBQ1EsS0FBSztJQUMzQkcsTUFBTSxFQUFFWSxLQUFLLENBQUN2QixRQUFRLENBQUNXO0VBQ3pCLENBQUMsR0FBR1ksS0FBSyxDQUFDeEIsU0FBUztFQUNuQixNQUFNeUQsWUFBWSxHQUFHLE9BQU90QyxRQUFRLENBQUN1QyxlQUFlLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHdkMsUUFBUSxDQUFDdUMsZUFBZSxDQUFDcEIsUUFBUSxDQUFDckMsUUFBUSxDQUFDLENBQUM7RUFDcEgsTUFBTTBELFdBQVcsR0FBRyxDQUFDLE9BQU94QyxRQUFRLENBQUNrQyxTQUFTLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHbEMsUUFBUSxDQUFDa0MsU0FBUyxDQUFDSSxZQUFZLENBQUMsQ0FBQyxJQUFJLENBQUMsT0FBT3RDLFFBQVEsQ0FBQ3lDLFFBQVEsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUd6QyxRQUFRLENBQUN5QyxRQUFRLENBQUNILFlBQVksQ0FBQyxDQUFDLEtBQUs7SUFDdkxqRCxDQUFDLEVBQUUsQ0FBQztJQUNKRyxDQUFDLEVBQUU7RUFDTCxDQUFDLEdBQUc7SUFDRkgsQ0FBQyxFQUFFLENBQUM7SUFDSkcsQ0FBQyxFQUFFO0VBQ0wsQ0FBQztFQUNELE1BQU1rRCxpQkFBaUIsR0FBRzdFLG9FQUFnQixDQUFDbUMsUUFBUSxDQUFDMkMscURBQXFELEdBQUcsTUFBTTNDLFFBQVEsQ0FBQzJDLHFEQUFxRCxDQUFDO0lBQy9LeEIsUUFBUTtJQUNSa0IsSUFBSTtJQUNKQyxZQUFZO0lBQ1p4QztFQUNGLENBQUMsQ0FBQyxHQUFHdUMsSUFBSSxDQUFDO0VBQ1YsT0FBTztJQUNMTyxHQUFHLEVBQUUsQ0FBQ1osa0JBQWtCLENBQUNZLEdBQUcsR0FBR0YsaUJBQWlCLENBQUNFLEdBQUcsR0FBR2YsYUFBYSxDQUFDZSxHQUFHLElBQUlKLFdBQVcsQ0FBQ2hELENBQUM7SUFDekZxRCxNQUFNLEVBQUUsQ0FBQ0gsaUJBQWlCLENBQUNHLE1BQU0sR0FBR2Isa0JBQWtCLENBQUNhLE1BQU0sR0FBR2hCLGFBQWEsQ0FBQ2dCLE1BQU0sSUFBSUwsV0FBVyxDQUFDaEQsQ0FBQztJQUNyR3NELElBQUksRUFBRSxDQUFDZCxrQkFBa0IsQ0FBQ2MsSUFBSSxHQUFHSixpQkFBaUIsQ0FBQ0ksSUFBSSxHQUFHakIsYUFBYSxDQUFDaUIsSUFBSSxJQUFJTixXQUFXLENBQUNuRCxDQUFDO0lBQzdGMEQsS0FBSyxFQUFFLENBQUNMLGlCQUFpQixDQUFDSyxLQUFLLEdBQUdmLGtCQUFrQixDQUFDZSxLQUFLLEdBQUdsQixhQUFhLENBQUNrQixLQUFLLElBQUlQLFdBQVcsQ0FBQ25EO0VBQ2xHLENBQUM7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTJELEtBQUssR0FBRzFCLE9BQU8sS0FBSztFQUN4QlYsSUFBSSxFQUFFLE9BQU87RUFDYlUsT0FBTztFQUNQLE1BQU1ULEVBQUVBLENBQUNRLEtBQUssRUFBRTtJQUNkLE1BQU07TUFDSmhDLENBQUM7TUFDREcsQ0FBQztNQUNEYixTQUFTO01BQ1QwQixLQUFLO01BQ0xMLFFBQVE7TUFDUm1CLFFBQVE7TUFDUlg7SUFDRixDQUFDLEdBQUdhLEtBQUs7SUFDVDtJQUNBLE1BQU07TUFDSlUsT0FBTztNQUNQSCxPQUFPLEdBQUc7SUFDWixDQUFDLEdBQUdqRSw0REFBUSxDQUFDMkQsT0FBTyxFQUFFRCxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDbEMsSUFBSVUsT0FBTyxJQUFJLElBQUksRUFBRTtNQUNuQixPQUFPLENBQUMsQ0FBQztJQUNYO0lBQ0EsTUFBTUYsYUFBYSxHQUFHakUsb0VBQWdCLENBQUNnRSxPQUFPLENBQUM7SUFDL0MsTUFBTWpDLE1BQU0sR0FBRztNQUNiTixDQUFDO01BQ0RHO0lBQ0YsQ0FBQztJQUNELE1BQU15RCxJQUFJLEdBQUcxRixvRUFBZ0IsQ0FBQ29CLFNBQVMsQ0FBQztJQUN4QyxNQUFNZ0MsTUFBTSxHQUFHbkQsaUVBQWEsQ0FBQ3lGLElBQUksQ0FBQztJQUNsQyxNQUFNQyxlQUFlLEdBQUcsTUFBTWxELFFBQVEsQ0FBQ21ELGFBQWEsQ0FBQ3BCLE9BQU8sQ0FBQztJQUM3RCxNQUFNcUIsT0FBTyxHQUFHSCxJQUFJLEtBQUssR0FBRztJQUM1QixNQUFNSSxPQUFPLEdBQUdELE9BQU8sR0FBRyxLQUFLLEdBQUcsTUFBTTtJQUN4QyxNQUFNRSxPQUFPLEdBQUdGLE9BQU8sR0FBRyxRQUFRLEdBQUcsT0FBTztJQUM1QyxNQUFNRyxVQUFVLEdBQUdILE9BQU8sR0FBRyxjQUFjLEdBQUcsYUFBYTtJQUMzRCxNQUFNSSxPQUFPLEdBQUduRCxLQUFLLENBQUN4QixTQUFTLENBQUM4QixNQUFNLENBQUMsR0FBR04sS0FBSyxDQUFDeEIsU0FBUyxDQUFDb0UsSUFBSSxDQUFDLEdBQUd0RCxNQUFNLENBQUNzRCxJQUFJLENBQUMsR0FBRzVDLEtBQUssQ0FBQ3ZCLFFBQVEsQ0FBQzZCLE1BQU0sQ0FBQztJQUN2RyxNQUFNOEMsU0FBUyxHQUFHOUQsTUFBTSxDQUFDc0QsSUFBSSxDQUFDLEdBQUc1QyxLQUFLLENBQUN4QixTQUFTLENBQUNvRSxJQUFJLENBQUM7SUFDdEQsTUFBTVMsaUJBQWlCLEdBQUcsT0FBTzFELFFBQVEsQ0FBQ3VDLGVBQWUsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUd2QyxRQUFRLENBQUN1QyxlQUFlLENBQUNSLE9BQU8sQ0FBQyxDQUFDO0lBQy9HLElBQUk0QixVQUFVLEdBQUdELGlCQUFpQixHQUFHQSxpQkFBaUIsQ0FBQ0gsVUFBVSxDQUFDLEdBQUcsQ0FBQzs7SUFFdEU7SUFDQSxJQUFJLENBQUNJLFVBQVUsSUFBSSxFQUFFLE9BQU8zRCxRQUFRLENBQUNrQyxTQUFTLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHbEMsUUFBUSxDQUFDa0MsU0FBUyxDQUFDd0IsaUJBQWlCLENBQUMsQ0FBQyxDQUFDLEVBQUU7TUFDekdDLFVBQVUsR0FBR3hDLFFBQVEsQ0FBQ3JDLFFBQVEsQ0FBQ3lFLFVBQVUsQ0FBQyxJQUFJbEQsS0FBSyxDQUFDdkIsUUFBUSxDQUFDNkIsTUFBTSxDQUFDO0lBQ3RFO0lBQ0EsTUFBTWlELGlCQUFpQixHQUFHSixPQUFPLEdBQUcsQ0FBQyxHQUFHQyxTQUFTLEdBQUcsQ0FBQzs7SUFFckQ7SUFDQTtJQUNBLE1BQU1JLHNCQUFzQixHQUFHRixVQUFVLEdBQUcsQ0FBQyxHQUFHVCxlQUFlLENBQUN2QyxNQUFNLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQztJQUMvRSxNQUFNbUQsVUFBVSxHQUFHaEcsdURBQUcsQ0FBQytELGFBQWEsQ0FBQ3dCLE9BQU8sQ0FBQyxFQUFFUSxzQkFBc0IsQ0FBQztJQUN0RSxNQUFNRSxVQUFVLEdBQUdqRyx1REFBRyxDQUFDK0QsYUFBYSxDQUFDeUIsT0FBTyxDQUFDLEVBQUVPLHNCQUFzQixDQUFDOztJQUV0RTtJQUNBO0lBQ0EsTUFBTUcsS0FBSyxHQUFHRixVQUFVO0lBQ3hCLE1BQU12RixHQUFHLEdBQUdvRixVQUFVLEdBQUdULGVBQWUsQ0FBQ3ZDLE1BQU0sQ0FBQyxHQUFHb0QsVUFBVTtJQUM3RCxNQUFNRSxNQUFNLEdBQUdOLFVBQVUsR0FBRyxDQUFDLEdBQUdULGVBQWUsQ0FBQ3ZDLE1BQU0sQ0FBQyxHQUFHLENBQUMsR0FBR2lELGlCQUFpQjtJQUMvRSxNQUFNTSxNQUFNLEdBQUduRyx5REFBSyxDQUFDaUcsS0FBSyxFQUFFQyxNQUFNLEVBQUUxRixHQUFHLENBQUM7O0lBRXhDO0lBQ0E7SUFDQTtJQUNBO0lBQ0EsTUFBTTRGLGVBQWUsR0FBRyxDQUFDM0QsY0FBYyxDQUFDd0MsS0FBSyxJQUFJdEYsZ0VBQVksQ0FBQ2lCLFNBQVMsQ0FBQyxJQUFJLElBQUksSUFBSXNGLE1BQU0sS0FBS0MsTUFBTSxJQUFJN0QsS0FBSyxDQUFDeEIsU0FBUyxDQUFDOEIsTUFBTSxDQUFDLEdBQUcsQ0FBQyxJQUFJc0QsTUFBTSxHQUFHRCxLQUFLLEdBQUdGLFVBQVUsR0FBR0MsVUFBVSxDQUFDLEdBQUdiLGVBQWUsQ0FBQ3ZDLE1BQU0sQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDO0lBQ25OLE1BQU15RCxlQUFlLEdBQUdELGVBQWUsR0FBR0YsTUFBTSxHQUFHRCxLQUFLLEdBQUdDLE1BQU0sR0FBR0QsS0FBSyxHQUFHQyxNQUFNLEdBQUcxRixHQUFHLEdBQUcsQ0FBQztJQUM1RixPQUFPO01BQ0wsQ0FBQzBFLElBQUksR0FBR3RELE1BQU0sQ0FBQ3NELElBQUksQ0FBQyxHQUFHbUIsZUFBZTtNQUN0Q3BELElBQUksRUFBRTtRQUNKLENBQUNpQyxJQUFJLEdBQUdpQixNQUFNO1FBQ2RHLFlBQVksRUFBRUosTUFBTSxHQUFHQyxNQUFNLEdBQUdFLGVBQWU7UUFDL0MsSUFBSUQsZUFBZSxJQUFJO1VBQ3JCQztRQUNGLENBQUM7TUFDSCxDQUFDO01BQ0RuRCxLQUFLLEVBQUVrRDtJQUNULENBQUM7RUFDSDtBQUNGLENBQUMsQ0FBQztBQUVGLFNBQVNHLGdCQUFnQkEsQ0FBQ0MsU0FBUyxFQUFFQyxhQUFhLEVBQUVDLGlCQUFpQixFQUFFO0VBQ3JFLE1BQU1DLGtDQUFrQyxHQUFHSCxTQUFTLEdBQUcsQ0FBQyxHQUFHRSxpQkFBaUIsQ0FBQ3ZFLE1BQU0sQ0FBQ3ZCLFNBQVMsSUFBSWpCLGdFQUFZLENBQUNpQixTQUFTLENBQUMsS0FBSzRGLFNBQVMsQ0FBQyxFQUFFLEdBQUdFLGlCQUFpQixDQUFDdkUsTUFBTSxDQUFDdkIsU0FBUyxJQUFJakIsZ0VBQVksQ0FBQ2lCLFNBQVMsQ0FBQyxLQUFLNEYsU0FBUyxDQUFDLENBQUMsR0FBR0UsaUJBQWlCLENBQUN2RSxNQUFNLENBQUN2QixTQUFTLElBQUlsQiwyREFBTyxDQUFDa0IsU0FBUyxDQUFDLEtBQUtBLFNBQVMsQ0FBQztFQUNuUyxPQUFPK0Ysa0NBQWtDLENBQUN4RSxNQUFNLENBQUN2QixTQUFTLElBQUk7SUFDNUQsSUFBSTRGLFNBQVMsRUFBRTtNQUNiLE9BQU83RyxnRUFBWSxDQUFDaUIsU0FBUyxDQUFDLEtBQUs0RixTQUFTLEtBQUtDLGFBQWEsR0FBR3RHLGlGQUE2QixDQUFDUyxTQUFTLENBQUMsS0FBS0EsU0FBUyxHQUFHLEtBQUssQ0FBQztJQUNsSTtJQUNBLE9BQU8sSUFBSTtFQUNiLENBQUMsQ0FBQztBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTWdHLGFBQWEsR0FBRyxTQUFBQSxDQUFVckQsT0FBTyxFQUFFO0VBQ3ZDLElBQUlBLE9BQU8sS0FBSyxLQUFLLENBQUMsRUFBRTtJQUN0QkEsT0FBTyxHQUFHLENBQUMsQ0FBQztFQUNkO0VBQ0EsT0FBTztJQUNMVixJQUFJLEVBQUUsZUFBZTtJQUNyQlUsT0FBTztJQUNQLE1BQU1ULEVBQUVBLENBQUNRLEtBQUssRUFBRTtNQUNkLElBQUl1RCxxQkFBcUIsRUFBRUMsc0JBQXNCLEVBQUVDLHFCQUFxQjtNQUN4RSxNQUFNO1FBQ0p6RSxLQUFLO1FBQ0xHLGNBQWM7UUFDZDdCLFNBQVM7UUFDVHFCLFFBQVE7UUFDUm1CO01BQ0YsQ0FBQyxHQUFHRSxLQUFLO01BQ1QsTUFBTTtRQUNKMEQsU0FBUyxHQUFHLEtBQUs7UUFDakJSLFNBQVM7UUFDVEUsaUJBQWlCLEdBQUd6RywwREFBVTtRQUM5QndHLGFBQWEsR0FBRyxJQUFJO1FBQ3BCLEdBQUdRO01BQ0wsQ0FBQyxHQUFHckgsNERBQVEsQ0FBQzJELE9BQU8sRUFBRUQsS0FBSyxDQUFDO01BQzVCLE1BQU00RCxZQUFZLEdBQUdWLFNBQVMsS0FBS1csU0FBUyxJQUFJVCxpQkFBaUIsS0FBS3pHLDBEQUFVLEdBQUdzRyxnQkFBZ0IsQ0FBQ0MsU0FBUyxJQUFJLElBQUksRUFBRUMsYUFBYSxFQUFFQyxpQkFBaUIsQ0FBQyxHQUFHQSxpQkFBaUI7TUFDNUssTUFBTVUsUUFBUSxHQUFHLE1BQU0vRCxjQUFjLENBQUNDLEtBQUssRUFBRTJELHFCQUFxQixDQUFDO01BQ25FLE1BQU1JLFlBQVksR0FBRyxDQUFDLENBQUNSLHFCQUFxQixHQUFHcEUsY0FBYyxDQUFDbUUsYUFBYSxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0MscUJBQXFCLENBQUNTLEtBQUssS0FBSyxDQUFDO01BQ2pJLE1BQU1DLGdCQUFnQixHQUFHTCxZQUFZLENBQUNHLFlBQVksQ0FBQztNQUNuRCxJQUFJRSxnQkFBZ0IsSUFBSSxJQUFJLEVBQUU7UUFDNUIsT0FBTyxDQUFDLENBQUM7TUFDWDtNQUNBLE1BQU1DLGNBQWMsR0FBR3RILHFFQUFpQixDQUFDcUgsZ0JBQWdCLEVBQUVqRixLQUFLLEVBQUUsT0FBT0wsUUFBUSxDQUFDSSxLQUFLLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHSixRQUFRLENBQUNJLEtBQUssQ0FBQ2UsUUFBUSxDQUFDckMsUUFBUSxDQUFDLENBQUMsQ0FBQzs7TUFFOUk7TUFDQSxJQUFJSCxTQUFTLEtBQUsyRyxnQkFBZ0IsRUFBRTtRQUNsQyxPQUFPO1VBQ0xyRSxLQUFLLEVBQUU7WUFDTHRDLFNBQVMsRUFBRXNHLFlBQVksQ0FBQyxDQUFDO1VBQzNCO1FBQ0YsQ0FBQztNQUNIO01BQ0EsTUFBTU8sZ0JBQWdCLEdBQUcsQ0FBQ0wsUUFBUSxDQUFDMUgsMkRBQU8sQ0FBQzZILGdCQUFnQixDQUFDLENBQUMsRUFBRUgsUUFBUSxDQUFDSSxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRUosUUFBUSxDQUFDSSxjQUFjLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztNQUN4SCxNQUFNRSxZQUFZLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FBQ1osc0JBQXNCLEdBQUdyRSxjQUFjLENBQUNtRSxhQUFhLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHRSxzQkFBc0IsQ0FBQ2EsU0FBUyxLQUFLLEVBQUUsQ0FBQyxFQUFFO1FBQzlJL0csU0FBUyxFQUFFMkcsZ0JBQWdCO1FBQzNCSSxTQUFTLEVBQUVGO01BQ2IsQ0FBQyxDQUFDO01BQ0YsTUFBTUcsYUFBYSxHQUFHVixZQUFZLENBQUNHLFlBQVksR0FBRyxDQUFDLENBQUM7O01BRXBEO01BQ0EsSUFBSU8sYUFBYSxFQUFFO1FBQ2pCLE9BQU87VUFDTDNFLElBQUksRUFBRTtZQUNKcUUsS0FBSyxFQUFFRCxZQUFZLEdBQUcsQ0FBQztZQUN2Qk0sU0FBUyxFQUFFRDtVQUNiLENBQUM7VUFDRHhFLEtBQUssRUFBRTtZQUNMdEMsU0FBUyxFQUFFZ0g7VUFDYjtRQUNGLENBQUM7TUFDSDtNQUNBLE1BQU1DLDJCQUEyQixHQUFHSCxZQUFZLENBQUNJLEdBQUcsQ0FBQ0MsQ0FBQyxJQUFJO1FBQ3hELE1BQU12QixTQUFTLEdBQUc3RyxnRUFBWSxDQUFDb0ksQ0FBQyxDQUFDbkgsU0FBUyxDQUFDO1FBQzNDLE9BQU8sQ0FBQ21ILENBQUMsQ0FBQ25ILFNBQVMsRUFBRTRGLFNBQVMsSUFBSVEsU0FBUztRQUMzQztRQUNBZSxDQUFDLENBQUNKLFNBQVMsQ0FBQ0ssS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQ0MsTUFBTSxDQUFDLENBQUNDLEdBQUcsRUFBRUMsQ0FBQyxLQUFLRCxHQUFHLEdBQUdDLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDdEQ7UUFDQUosQ0FBQyxDQUFDSixTQUFTLENBQUMsQ0FBQyxDQUFDLEVBQUVJLENBQUMsQ0FBQ0osU0FBUyxDQUFDO01BQzlCLENBQUMsQ0FBQyxDQUFDUyxJQUFJLENBQUMsQ0FBQ0MsQ0FBQyxFQUFFQyxDQUFDLEtBQUtELENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBR0MsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO01BQzlCLE1BQU1DLDJCQUEyQixHQUFHViwyQkFBMkIsQ0FBQzFGLE1BQU0sQ0FBQzRGLENBQUMsSUFBSUEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDQyxLQUFLLENBQUMsQ0FBQztNQUN4RjtNQUNBO01BQ0FySSxnRUFBWSxDQUFDb0ksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDUyxLQUFLLENBQUNMLENBQUMsSUFBSUEsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO01BQy9DLE1BQU1NLGNBQWMsR0FBRyxDQUFDLENBQUMxQixxQkFBcUIsR0FBR3dCLDJCQUEyQixDQUFDLENBQUMsQ0FBQyxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR3hCLHFCQUFxQixDQUFDLENBQUMsQ0FBQyxLQUFLYywyQkFBMkIsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7TUFDbEssSUFBSVksY0FBYyxLQUFLN0gsU0FBUyxFQUFFO1FBQ2hDLE9BQU87VUFDTHFDLElBQUksRUFBRTtZQUNKcUUsS0FBSyxFQUFFRCxZQUFZLEdBQUcsQ0FBQztZQUN2Qk0sU0FBUyxFQUFFRDtVQUNiLENBQUM7VUFDRHhFLEtBQUssRUFBRTtZQUNMdEMsU0FBUyxFQUFFNkg7VUFDYjtRQUNGLENBQUM7TUFDSDtNQUNBLE9BQU8sQ0FBQyxDQUFDO0lBQ1g7RUFDRixDQUFDO0FBQ0gsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNQyxJQUFJLEdBQUcsU0FBQUEsQ0FBVW5GLE9BQU8sRUFBRTtFQUM5QixJQUFJQSxPQUFPLEtBQUssS0FBSyxDQUFDLEVBQUU7SUFDdEJBLE9BQU8sR0FBRyxDQUFDLENBQUM7RUFDZDtFQUNBLE9BQU87SUFDTFYsSUFBSSxFQUFFLE1BQU07SUFDWlUsT0FBTztJQUNQLE1BQU1ULEVBQUVBLENBQUNRLEtBQUssRUFBRTtNQUNkLElBQUlxRixxQkFBcUIsRUFBRUMsb0JBQW9CO01BQy9DLE1BQU07UUFDSmhJLFNBQVM7UUFDVDZCLGNBQWM7UUFDZEgsS0FBSztRQUNMYSxnQkFBZ0I7UUFDaEJsQixRQUFRO1FBQ1JtQjtNQUNGLENBQUMsR0FBR0UsS0FBSztNQUNULE1BQU07UUFDSnVGLFFBQVEsRUFBRUMsYUFBYSxHQUFHLElBQUk7UUFDOUI5QixTQUFTLEVBQUUrQixjQUFjLEdBQUcsSUFBSTtRQUNoQ0Msa0JBQWtCLEVBQUVDLDJCQUEyQjtRQUMvQ0MsZ0JBQWdCLEdBQUcsU0FBUztRQUM1QkMseUJBQXlCLEdBQUcsTUFBTTtRQUNsQ0MsYUFBYSxHQUFHLElBQUk7UUFDcEIsR0FBR25DO01BQ0wsQ0FBQyxHQUFHckgsNERBQVEsQ0FBQzJELE9BQU8sRUFBRUQsS0FBSyxDQUFDOztNQUU1QjtNQUNBO01BQ0E7TUFDQTtNQUNBLElBQUksQ0FBQ3FGLHFCQUFxQixHQUFHbEcsY0FBYyxDQUFDd0MsS0FBSyxLQUFLLElBQUksSUFBSTBELHFCQUFxQixDQUFDdEMsZUFBZSxFQUFFO1FBQ25HLE9BQU8sQ0FBQyxDQUFDO01BQ1g7TUFDQSxNQUFNbEYsSUFBSSxHQUFHekIsMkRBQU8sQ0FBQ2tCLFNBQVMsQ0FBQztNQUMvQixNQUFNeUksZUFBZSxHQUFHOUosK0RBQVcsQ0FBQzRELGdCQUFnQixDQUFDO01BQ3JELE1BQU1tRyxlQUFlLEdBQUc1SiwyREFBTyxDQUFDeUQsZ0JBQWdCLENBQUMsS0FBS0EsZ0JBQWdCO01BQ3RFLE1BQU10QyxHQUFHLEdBQUcsT0FBT29CLFFBQVEsQ0FBQ0ksS0FBSyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0osUUFBUSxDQUFDSSxLQUFLLENBQUNlLFFBQVEsQ0FBQ3JDLFFBQVEsQ0FBQyxDQUFDO01BQ3ZGLE1BQU1pSSxrQkFBa0IsR0FBR0MsMkJBQTJCLEtBQUtLLGVBQWUsSUFBSSxDQUFDRixhQUFhLEdBQUcsQ0FBQ2hKLHdFQUFvQixDQUFDK0MsZ0JBQWdCLENBQUMsQ0FBQyxHQUFHOUMseUVBQXFCLENBQUM4QyxnQkFBZ0IsQ0FBQyxDQUFDO01BQ2xMLE1BQU1vRyw0QkFBNEIsR0FBR0oseUJBQXlCLEtBQUssTUFBTTtNQUN6RSxJQUFJLENBQUNGLDJCQUEyQixJQUFJTSw0QkFBNEIsRUFBRTtRQUNoRVAsa0JBQWtCLENBQUNRLElBQUksQ0FBQyxHQUFHbEosNkVBQXlCLENBQUM2QyxnQkFBZ0IsRUFBRWlHLGFBQWEsRUFBRUQseUJBQXlCLEVBQUV0SSxHQUFHLENBQUMsQ0FBQztNQUN4SDtNQUNBLE1BQU1aLFVBQVUsR0FBRyxDQUFDa0QsZ0JBQWdCLEVBQUUsR0FBRzZGLGtCQUFrQixDQUFDO01BQzVELE1BQU01QixRQUFRLEdBQUcsTUFBTS9ELGNBQWMsQ0FBQ0MsS0FBSyxFQUFFMkQscUJBQXFCLENBQUM7TUFDbkUsTUFBTVUsU0FBUyxHQUFHLEVBQUU7TUFDcEIsSUFBSThCLGFBQWEsR0FBRyxDQUFDLENBQUNiLG9CQUFvQixHQUFHbkcsY0FBYyxDQUFDaUcsSUFBSSxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0Usb0JBQW9CLENBQUNqQixTQUFTLEtBQUssRUFBRTtNQUMxSCxJQUFJbUIsYUFBYSxFQUFFO1FBQ2pCbkIsU0FBUyxDQUFDNkIsSUFBSSxDQUFDcEMsUUFBUSxDQUFDakcsSUFBSSxDQUFDLENBQUM7TUFDaEM7TUFDQSxJQUFJNEgsY0FBYyxFQUFFO1FBQ2xCLE1BQU14SSxLQUFLLEdBQUdMLHFFQUFpQixDQUFDVSxTQUFTLEVBQUUwQixLQUFLLEVBQUV6QixHQUFHLENBQUM7UUFDdEQ4RyxTQUFTLENBQUM2QixJQUFJLENBQUNwQyxRQUFRLENBQUM3RyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRTZHLFFBQVEsQ0FBQzdHLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO01BQ3hEO01BQ0FrSixhQUFhLEdBQUcsQ0FBQyxHQUFHQSxhQUFhLEVBQUU7UUFDakM3SSxTQUFTO1FBQ1QrRztNQUNGLENBQUMsQ0FBQzs7TUFFRjtNQUNBLElBQUksQ0FBQ0EsU0FBUyxDQUFDYSxLQUFLLENBQUNySCxJQUFJLElBQUlBLElBQUksSUFBSSxDQUFDLENBQUMsRUFBRTtRQUN2QyxJQUFJdUkscUJBQXFCLEVBQUVDLHFCQUFxQjtRQUNoRCxNQUFNQyxTQUFTLEdBQUcsQ0FBQyxDQUFDLENBQUNGLHFCQUFxQixHQUFHakgsY0FBYyxDQUFDaUcsSUFBSSxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR2dCLHFCQUFxQixDQUFDcEMsS0FBSyxLQUFLLENBQUMsSUFBSSxDQUFDO1FBQzNILE1BQU1NLGFBQWEsR0FBRzNILFVBQVUsQ0FBQzJKLFNBQVMsQ0FBQztRQUMzQyxJQUFJaEMsYUFBYSxFQUFFO1VBQ2pCLElBQUlpQyxlQUFlO1VBQ25CLE1BQU1DLHVCQUF1QixHQUFHZixjQUFjLEtBQUssV0FBVyxHQUFHTSxlQUFlLEtBQUs5SiwrREFBVyxDQUFDcUksYUFBYSxDQUFDLEdBQUcsS0FBSztVQUN2SCxNQUFNbUMsMEJBQTBCLEdBQUcsQ0FBQyxDQUFDRixlQUFlLEdBQUdKLGFBQWEsQ0FBQyxDQUFDLENBQUMsS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdJLGVBQWUsQ0FBQ2xDLFNBQVMsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1VBQzdILElBQUksQ0FBQ21DLHVCQUF1QixJQUFJQywwQkFBMEIsRUFBRTtZQUMxRDtZQUNBLE9BQU87Y0FDTDlHLElBQUksRUFBRTtnQkFDSnFFLEtBQUssRUFBRXNDLFNBQVM7Z0JBQ2hCakMsU0FBUyxFQUFFOEI7Y0FDYixDQUFDO2NBQ0R2RyxLQUFLLEVBQUU7Z0JBQ0x0QyxTQUFTLEVBQUVnSDtjQUNiO1lBQ0YsQ0FBQztVQUNIO1FBQ0Y7O1FBRUE7UUFDQTtRQUNBLElBQUlhLGNBQWMsR0FBRyxDQUFDa0IscUJBQXFCLEdBQUdGLGFBQWEsQ0FBQ3RILE1BQU0sQ0FBQzRGLENBQUMsSUFBSUEsQ0FBQyxDQUFDSixTQUFTLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUNTLElBQUksQ0FBQyxDQUFDQyxDQUFDLEVBQUVDLENBQUMsS0FBS0QsQ0FBQyxDQUFDVixTQUFTLENBQUMsQ0FBQyxDQUFDLEdBQUdXLENBQUMsQ0FBQ1gsU0FBUyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHZ0MscUJBQXFCLENBQUMvSSxTQUFTOztRQUVuTTtRQUNBLElBQUksQ0FBQzZILGNBQWMsRUFBRTtVQUNuQixRQUFRUyxnQkFBZ0I7WUFDdEIsS0FBSyxTQUFTO2NBQ1o7Z0JBQ0UsSUFBSWMsc0JBQXNCO2dCQUMxQixNQUFNcEosU0FBUyxHQUFHLENBQUNvSixzQkFBc0IsR0FBR1AsYUFBYSxDQUFDdEgsTUFBTSxDQUFDNEYsQ0FBQyxJQUFJO2tCQUNwRSxJQUFJd0IsNEJBQTRCLEVBQUU7b0JBQ2hDLE1BQU1VLGVBQWUsR0FBRzFLLCtEQUFXLENBQUN3SSxDQUFDLENBQUNuSCxTQUFTLENBQUM7b0JBQ2hELE9BQU9xSixlQUFlLEtBQUtaLGVBQWU7b0JBQzFDO29CQUNBO29CQUNBWSxlQUFlLEtBQUssR0FBRztrQkFDekI7a0JBQ0EsT0FBTyxJQUFJO2dCQUNiLENBQUMsQ0FBQyxDQUFDbkMsR0FBRyxDQUFDQyxDQUFDLElBQUksQ0FBQ0EsQ0FBQyxDQUFDbkgsU0FBUyxFQUFFbUgsQ0FBQyxDQUFDSixTQUFTLENBQUN4RixNQUFNLENBQUNpRixRQUFRLElBQUlBLFFBQVEsR0FBRyxDQUFDLENBQUMsQ0FBQ2EsTUFBTSxDQUFDLENBQUNDLEdBQUcsRUFBRWQsUUFBUSxLQUFLYyxHQUFHLEdBQUdkLFFBQVEsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUNnQixJQUFJLENBQUMsQ0FBQ0MsQ0FBQyxFQUFFQyxDQUFDLEtBQUtELENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBR0MsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHMEIsc0JBQXNCLENBQUMsQ0FBQyxDQUFDO2dCQUNsTSxJQUFJcEosU0FBUyxFQUFFO2tCQUNiNkgsY0FBYyxHQUFHN0gsU0FBUztnQkFDNUI7Z0JBQ0E7Y0FDRjtZQUNGLEtBQUssa0JBQWtCO2NBQ3JCNkgsY0FBYyxHQUFHdEYsZ0JBQWdCO2NBQ2pDO1VBQ0o7UUFDRjtRQUNBLElBQUl2QyxTQUFTLEtBQUs2SCxjQUFjLEVBQUU7VUFDaEMsT0FBTztZQUNMdkYsS0FBSyxFQUFFO2NBQ0x0QyxTQUFTLEVBQUU2SDtZQUNiO1VBQ0YsQ0FBQztRQUNIO01BQ0Y7TUFDQSxPQUFPLENBQUMsQ0FBQztJQUNYO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFFRCxTQUFTeUIsY0FBY0EsQ0FBQzlDLFFBQVEsRUFBRTlDLElBQUksRUFBRTtFQUN0QyxPQUFPO0lBQ0xPLEdBQUcsRUFBRXVDLFFBQVEsQ0FBQ3ZDLEdBQUcsR0FBR1AsSUFBSSxDQUFDNUMsTUFBTTtJQUMvQnNELEtBQUssRUFBRW9DLFFBQVEsQ0FBQ3BDLEtBQUssR0FBR1YsSUFBSSxDQUFDL0MsS0FBSztJQUNsQ3VELE1BQU0sRUFBRXNDLFFBQVEsQ0FBQ3RDLE1BQU0sR0FBR1IsSUFBSSxDQUFDNUMsTUFBTTtJQUNyQ3FELElBQUksRUFBRXFDLFFBQVEsQ0FBQ3JDLElBQUksR0FBR1QsSUFBSSxDQUFDL0M7RUFDN0IsQ0FBQztBQUNIO0FBQ0EsU0FBUzRJLHFCQUFxQkEsQ0FBQy9DLFFBQVEsRUFBRTtFQUN2QyxPQUFPN0cscURBQUssQ0FBQzZKLElBQUksQ0FBQ2pKLElBQUksSUFBSWlHLFFBQVEsQ0FBQ2pHLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNa0osSUFBSSxHQUFHLFNBQUFBLENBQVU5RyxPQUFPLEVBQUU7RUFDOUIsSUFBSUEsT0FBTyxLQUFLLEtBQUssQ0FBQyxFQUFFO0lBQ3RCQSxPQUFPLEdBQUcsQ0FBQyxDQUFDO0VBQ2Q7RUFDQSxPQUFPO0lBQ0xWLElBQUksRUFBRSxNQUFNO0lBQ1pVLE9BQU87SUFDUCxNQUFNVCxFQUFFQSxDQUFDUSxLQUFLLEVBQUU7TUFDZCxNQUFNO1FBQ0poQjtNQUNGLENBQUMsR0FBR2dCLEtBQUs7TUFDVCxNQUFNO1FBQ0p2QixRQUFRLEdBQUcsaUJBQWlCO1FBQzVCLEdBQUdrRjtNQUNMLENBQUMsR0FBR3JILDREQUFRLENBQUMyRCxPQUFPLEVBQUVELEtBQUssQ0FBQztNQUM1QixRQUFRdkIsUUFBUTtRQUNkLEtBQUssaUJBQWlCO1VBQ3BCO1lBQ0UsTUFBTXFGLFFBQVEsR0FBRyxNQUFNL0QsY0FBYyxDQUFDQyxLQUFLLEVBQUU7Y0FDM0MsR0FBRzJELHFCQUFxQjtjQUN4QnRELGNBQWMsRUFBRTtZQUNsQixDQUFDLENBQUM7WUFDRixNQUFNMkcsT0FBTyxHQUFHSixjQUFjLENBQUM5QyxRQUFRLEVBQUU5RSxLQUFLLENBQUN4QixTQUFTLENBQUM7WUFDekQsT0FBTztjQUNMbUMsSUFBSSxFQUFFO2dCQUNKc0gsc0JBQXNCLEVBQUVELE9BQU87Z0JBQy9CRSxlQUFlLEVBQUVMLHFCQUFxQixDQUFDRyxPQUFPO2NBQ2hEO1lBQ0YsQ0FBQztVQUNIO1FBQ0YsS0FBSyxTQUFTO1VBQ1o7WUFDRSxNQUFNbEQsUUFBUSxHQUFHLE1BQU0vRCxjQUFjLENBQUNDLEtBQUssRUFBRTtjQUMzQyxHQUFHMkQscUJBQXFCO2NBQ3hCckQsV0FBVyxFQUFFO1lBQ2YsQ0FBQyxDQUFDO1lBQ0YsTUFBTTBHLE9BQU8sR0FBR0osY0FBYyxDQUFDOUMsUUFBUSxFQUFFOUUsS0FBSyxDQUFDdkIsUUFBUSxDQUFDO1lBQ3hELE9BQU87Y0FDTGtDLElBQUksRUFBRTtnQkFDSndILGNBQWMsRUFBRUgsT0FBTztnQkFDdkJJLE9BQU8sRUFBRVAscUJBQXFCLENBQUNHLE9BQU87Y0FDeEM7WUFDRixDQUFDO1VBQ0g7UUFDRjtVQUNFO1lBQ0UsT0FBTyxDQUFDLENBQUM7VUFDWDtNQUNKO0lBQ0Y7RUFDRixDQUFDO0FBQ0gsQ0FBQztBQUVELFNBQVNLLGVBQWVBLENBQUNySSxLQUFLLEVBQUU7RUFDOUIsTUFBTXNJLElBQUksR0FBRzdLLHVEQUFHLENBQUMsR0FBR3VDLEtBQUssQ0FBQ3dGLEdBQUcsQ0FBQ3hELElBQUksSUFBSUEsSUFBSSxDQUFDUyxJQUFJLENBQUMsQ0FBQztFQUNqRCxNQUFNOEYsSUFBSSxHQUFHOUssdURBQUcsQ0FBQyxHQUFHdUMsS0FBSyxDQUFDd0YsR0FBRyxDQUFDeEQsSUFBSSxJQUFJQSxJQUFJLENBQUNPLEdBQUcsQ0FBQyxDQUFDO0VBQ2hELE1BQU1pRyxJQUFJLEdBQUd0Syx1REFBRyxDQUFDLEdBQUc4QixLQUFLLENBQUN3RixHQUFHLENBQUN4RCxJQUFJLElBQUlBLElBQUksQ0FBQ1UsS0FBSyxDQUFDLENBQUM7RUFDbEQsTUFBTStGLElBQUksR0FBR3ZLLHVEQUFHLENBQUMsR0FBRzhCLEtBQUssQ0FBQ3dGLEdBQUcsQ0FBQ3hELElBQUksSUFBSUEsSUFBSSxDQUFDUSxNQUFNLENBQUMsQ0FBQztFQUNuRCxPQUFPO0lBQ0x4RCxDQUFDLEVBQUVzSixJQUFJO0lBQ1BuSixDQUFDLEVBQUVvSixJQUFJO0lBQ1B0SixLQUFLLEVBQUV1SixJQUFJLEdBQUdGLElBQUk7SUFDbEJsSixNQUFNLEVBQUVxSixJQUFJLEdBQUdGO0VBQ2pCLENBQUM7QUFDSDtBQUNBLFNBQVNHLGNBQWNBLENBQUMxSSxLQUFLLEVBQUU7RUFDN0IsTUFBTTJJLFdBQVcsR0FBRzNJLEtBQUssQ0FBQzBGLEtBQUssQ0FBQyxDQUFDLENBQUNJLElBQUksQ0FBQyxDQUFDQyxDQUFDLEVBQUVDLENBQUMsS0FBS0QsQ0FBQyxDQUFDNUcsQ0FBQyxHQUFHNkcsQ0FBQyxDQUFDN0csQ0FBQyxDQUFDO0VBQzNELE1BQU15SixNQUFNLEdBQUcsRUFBRTtFQUNqQixJQUFJQyxRQUFRLEdBQUcsSUFBSTtFQUNuQixLQUFLLElBQUl4SSxDQUFDLEdBQUcsQ0FBQyxFQUFFQSxDQUFDLEdBQUdzSSxXQUFXLENBQUNySSxNQUFNLEVBQUVELENBQUMsRUFBRSxFQUFFO0lBQzNDLE1BQU0yQixJQUFJLEdBQUcyRyxXQUFXLENBQUN0SSxDQUFDLENBQUM7SUFDM0IsSUFBSSxDQUFDd0ksUUFBUSxJQUFJN0csSUFBSSxDQUFDN0MsQ0FBQyxHQUFHMEosUUFBUSxDQUFDMUosQ0FBQyxHQUFHMEosUUFBUSxDQUFDekosTUFBTSxHQUFHLENBQUMsRUFBRTtNQUMxRHdKLE1BQU0sQ0FBQzFCLElBQUksQ0FBQyxDQUFDbEYsSUFBSSxDQUFDLENBQUM7SUFDckIsQ0FBQyxNQUFNO01BQ0w0RyxNQUFNLENBQUNBLE1BQU0sQ0FBQ3RJLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQzRHLElBQUksQ0FBQ2xGLElBQUksQ0FBQztJQUN0QztJQUNBNkcsUUFBUSxHQUFHN0csSUFBSTtFQUNqQjtFQUNBLE9BQU80RyxNQUFNLENBQUNwRCxHQUFHLENBQUN4RCxJQUFJLElBQUl4RSxvRUFBZ0IsQ0FBQzZLLGVBQWUsQ0FBQ3JHLElBQUksQ0FBQyxDQUFDLENBQUM7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTThHLE1BQU0sR0FBRyxTQUFBQSxDQUFVN0gsT0FBTyxFQUFFO0VBQ2hDLElBQUlBLE9BQU8sS0FBSyxLQUFLLENBQUMsRUFBRTtJQUN0QkEsT0FBTyxHQUFHLENBQUMsQ0FBQztFQUNkO0VBQ0EsT0FBTztJQUNMVixJQUFJLEVBQUUsUUFBUTtJQUNkVSxPQUFPO0lBQ1AsTUFBTVQsRUFBRUEsQ0FBQ1EsS0FBSyxFQUFFO01BQ2QsTUFBTTtRQUNKMUMsU0FBUztRQUNUd0MsUUFBUTtRQUNSZCxLQUFLO1FBQ0xMLFFBQVE7UUFDUkY7TUFDRixDQUFDLEdBQUd1QixLQUFLO01BQ1Q7TUFDQTtNQUNBO01BQ0EsTUFBTTtRQUNKTyxPQUFPLEdBQUcsQ0FBQztRQUNYdkMsQ0FBQztRQUNERztNQUNGLENBQUMsR0FBRzdCLDREQUFRLENBQUMyRCxPQUFPLEVBQUVELEtBQUssQ0FBQztNQUM1QixNQUFNK0gsaUJBQWlCLEdBQUdDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDLENBQUMsT0FBT3RKLFFBQVEsQ0FBQ3VKLGNBQWMsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUd2SixRQUFRLENBQUN1SixjQUFjLENBQUNwSSxRQUFRLENBQUN0QyxTQUFTLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQztNQUM1SSxNQUFNMkssV0FBVyxHQUFHVCxjQUFjLENBQUNLLGlCQUFpQixDQUFDO01BQ3JELE1BQU1LLFFBQVEsR0FBRzVMLG9FQUFnQixDQUFDNkssZUFBZSxDQUFDVSxpQkFBaUIsQ0FBQyxDQUFDO01BQ3JFLE1BQU12SCxhQUFhLEdBQUdqRSxvRUFBZ0IsQ0FBQ2dFLE9BQU8sQ0FBQztNQUMvQyxTQUFTOEgscUJBQXFCQSxDQUFBLEVBQUc7UUFDL0I7UUFDQSxJQUFJRixXQUFXLENBQUM3SSxNQUFNLEtBQUssQ0FBQyxJQUFJNkksV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDMUcsSUFBSSxHQUFHMEcsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDekcsS0FBSyxJQUFJMUQsQ0FBQyxJQUFJLElBQUksSUFBSUcsQ0FBQyxJQUFJLElBQUksRUFBRTtVQUNwRztVQUNBLE9BQU9nSyxXQUFXLENBQUNHLElBQUksQ0FBQ3RILElBQUksSUFBSWhELENBQUMsR0FBR2dELElBQUksQ0FBQ1MsSUFBSSxHQUFHakIsYUFBYSxDQUFDaUIsSUFBSSxJQUFJekQsQ0FBQyxHQUFHZ0QsSUFBSSxDQUFDVSxLQUFLLEdBQUdsQixhQUFhLENBQUNrQixLQUFLLElBQUl2RCxDQUFDLEdBQUc2QyxJQUFJLENBQUNPLEdBQUcsR0FBR2YsYUFBYSxDQUFDZSxHQUFHLElBQUlwRCxDQUFDLEdBQUc2QyxJQUFJLENBQUNRLE1BQU0sR0FBR2hCLGFBQWEsQ0FBQ2dCLE1BQU0sQ0FBQyxJQUFJNEcsUUFBUTtRQUN2TTs7UUFFQTtRQUNBLElBQUlELFdBQVcsQ0FBQzdJLE1BQU0sSUFBSSxDQUFDLEVBQUU7VUFDM0IsSUFBSXJELCtEQUFXLENBQUNxQixTQUFTLENBQUMsS0FBSyxHQUFHLEVBQUU7WUFDbEMsTUFBTWlMLFNBQVMsR0FBR0osV0FBVyxDQUFDLENBQUMsQ0FBQztZQUNoQyxNQUFNSyxRQUFRLEdBQUdMLFdBQVcsQ0FBQ0EsV0FBVyxDQUFDN0ksTUFBTSxHQUFHLENBQUMsQ0FBQztZQUNwRCxNQUFNbUosS0FBSyxHQUFHck0sMkRBQU8sQ0FBQ2tCLFNBQVMsQ0FBQyxLQUFLLEtBQUs7WUFDMUMsTUFBTWlFLEdBQUcsR0FBR2dILFNBQVMsQ0FBQ2hILEdBQUc7WUFDekIsTUFBTUMsTUFBTSxHQUFHZ0gsUUFBUSxDQUFDaEgsTUFBTTtZQUM5QixNQUFNQyxJQUFJLEdBQUdnSCxLQUFLLEdBQUdGLFNBQVMsQ0FBQzlHLElBQUksR0FBRytHLFFBQVEsQ0FBQy9HLElBQUk7WUFDbkQsTUFBTUMsS0FBSyxHQUFHK0csS0FBSyxHQUFHRixTQUFTLENBQUM3RyxLQUFLLEdBQUc4RyxRQUFRLENBQUM5RyxLQUFLO1lBQ3RELE1BQU16RCxLQUFLLEdBQUd5RCxLQUFLLEdBQUdELElBQUk7WUFDMUIsTUFBTXJELE1BQU0sR0FBR29ELE1BQU0sR0FBR0QsR0FBRztZQUMzQixPQUFPO2NBQ0xBLEdBQUc7Y0FDSEMsTUFBTTtjQUNOQyxJQUFJO2NBQ0pDLEtBQUs7Y0FDTHpELEtBQUs7Y0FDTEcsTUFBTTtjQUNOSixDQUFDLEVBQUV5RCxJQUFJO2NBQ1B0RCxDQUFDLEVBQUVvRDtZQUNMLENBQUM7VUFDSDtVQUNBLE1BQU1tSCxVQUFVLEdBQUd0TSwyREFBTyxDQUFDa0IsU0FBUyxDQUFDLEtBQUssTUFBTTtVQUNoRCxNQUFNcUwsUUFBUSxHQUFHekwsdURBQUcsQ0FBQyxHQUFHaUwsV0FBVyxDQUFDM0QsR0FBRyxDQUFDeEQsSUFBSSxJQUFJQSxJQUFJLENBQUNVLEtBQUssQ0FBQyxDQUFDO1VBQzVELE1BQU1rSCxPQUFPLEdBQUduTSx1REFBRyxDQUFDLEdBQUcwTCxXQUFXLENBQUMzRCxHQUFHLENBQUN4RCxJQUFJLElBQUlBLElBQUksQ0FBQ1MsSUFBSSxDQUFDLENBQUM7VUFDMUQsTUFBTW9ILFlBQVksR0FBR1YsV0FBVyxDQUFDdEosTUFBTSxDQUFDbUMsSUFBSSxJQUFJMEgsVUFBVSxHQUFHMUgsSUFBSSxDQUFDUyxJQUFJLEtBQUttSCxPQUFPLEdBQUc1SCxJQUFJLENBQUNVLEtBQUssS0FBS2lILFFBQVEsQ0FBQztVQUM3RyxNQUFNcEgsR0FBRyxHQUFHc0gsWUFBWSxDQUFDLENBQUMsQ0FBQyxDQUFDdEgsR0FBRztVQUMvQixNQUFNQyxNQUFNLEdBQUdxSCxZQUFZLENBQUNBLFlBQVksQ0FBQ3ZKLE1BQU0sR0FBRyxDQUFDLENBQUMsQ0FBQ2tDLE1BQU07VUFDM0QsTUFBTUMsSUFBSSxHQUFHbUgsT0FBTztVQUNwQixNQUFNbEgsS0FBSyxHQUFHaUgsUUFBUTtVQUN0QixNQUFNMUssS0FBSyxHQUFHeUQsS0FBSyxHQUFHRCxJQUFJO1VBQzFCLE1BQU1yRCxNQUFNLEdBQUdvRCxNQUFNLEdBQUdELEdBQUc7VUFDM0IsT0FBTztZQUNMQSxHQUFHO1lBQ0hDLE1BQU07WUFDTkMsSUFBSTtZQUNKQyxLQUFLO1lBQ0x6RCxLQUFLO1lBQ0xHLE1BQU07WUFDTkosQ0FBQyxFQUFFeUQsSUFBSTtZQUNQdEQsQ0FBQyxFQUFFb0Q7VUFDTCxDQUFDO1FBQ0g7UUFDQSxPQUFPNkcsUUFBUTtNQUNqQjtNQUNBLE1BQU1VLFVBQVUsR0FBRyxNQUFNbkssUUFBUSxDQUFDTSxlQUFlLENBQUM7UUFDaER6QixTQUFTLEVBQUU7VUFDVDZLO1FBQ0YsQ0FBQztRQUNENUssUUFBUSxFQUFFcUMsUUFBUSxDQUFDckMsUUFBUTtRQUMzQmdCO01BQ0YsQ0FBQyxDQUFDO01BQ0YsSUFBSU8sS0FBSyxDQUFDeEIsU0FBUyxDQUFDUSxDQUFDLEtBQUs4SyxVQUFVLENBQUN0TCxTQUFTLENBQUNRLENBQUMsSUFBSWdCLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ1csQ0FBQyxLQUFLMkssVUFBVSxDQUFDdEwsU0FBUyxDQUFDVyxDQUFDLElBQUlhLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ1MsS0FBSyxLQUFLNkssVUFBVSxDQUFDdEwsU0FBUyxDQUFDUyxLQUFLLElBQUllLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ1ksTUFBTSxLQUFLMEssVUFBVSxDQUFDdEwsU0FBUyxDQUFDWSxNQUFNLEVBQUU7UUFDbE4sT0FBTztVQUNMd0IsS0FBSyxFQUFFO1lBQ0xaLEtBQUssRUFBRThKO1VBQ1Q7UUFDRixDQUFDO01BQ0g7TUFDQSxPQUFPLENBQUMsQ0FBQztJQUNYO0VBQ0YsQ0FBQztBQUNILENBQUM7O0FBRUQ7QUFDQTs7QUFFQSxlQUFlQyxvQkFBb0JBLENBQUMvSSxLQUFLLEVBQUVDLE9BQU8sRUFBRTtFQUNsRCxNQUFNO0lBQ0ozQyxTQUFTO0lBQ1RxQixRQUFRO0lBQ1JtQjtFQUNGLENBQUMsR0FBR0UsS0FBSztFQUNULE1BQU16QyxHQUFHLEdBQUcsT0FBT29CLFFBQVEsQ0FBQ0ksS0FBSyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0osUUFBUSxDQUFDSSxLQUFLLENBQUNlLFFBQVEsQ0FBQ3JDLFFBQVEsQ0FBQyxDQUFDO0VBQ3ZGLE1BQU1JLElBQUksR0FBR3pCLDJEQUFPLENBQUNrQixTQUFTLENBQUM7RUFDL0IsTUFBTTRGLFNBQVMsR0FBRzdHLGdFQUFZLENBQUNpQixTQUFTLENBQUM7RUFDekMsTUFBTVEsVUFBVSxHQUFHN0IsK0RBQVcsQ0FBQ3FCLFNBQVMsQ0FBQyxLQUFLLEdBQUc7RUFDakQsTUFBTTBMLGFBQWEsR0FBRyxDQUFDLE1BQU0sRUFBRSxLQUFLLENBQUMsQ0FBQ0MsUUFBUSxDQUFDcEwsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQztFQUM3RCxNQUFNcUwsY0FBYyxHQUFHM0wsR0FBRyxJQUFJTyxVQUFVLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQztFQUNqRCxNQUFNcUwsUUFBUSxHQUFHN00sNERBQVEsQ0FBQzJELE9BQU8sRUFBRUQsS0FBSyxDQUFDOztFQUV6QztFQUNBLElBQUk7SUFDRnVGLFFBQVE7SUFDUjdCLFNBQVM7SUFDVC9GO0VBQ0YsQ0FBQyxHQUFHLE9BQU93TCxRQUFRLEtBQUssUUFBUSxHQUFHO0lBQ2pDNUQsUUFBUSxFQUFFNEQsUUFBUTtJQUNsQnpGLFNBQVMsRUFBRSxDQUFDO0lBQ1ovRixhQUFhLEVBQUU7RUFDakIsQ0FBQyxHQUFHO0lBQ0Y0SCxRQUFRLEVBQUU0RCxRQUFRLENBQUM1RCxRQUFRLElBQUksQ0FBQztJQUNoQzdCLFNBQVMsRUFBRXlGLFFBQVEsQ0FBQ3pGLFNBQVMsSUFBSSxDQUFDO0lBQ2xDL0YsYUFBYSxFQUFFd0wsUUFBUSxDQUFDeEw7RUFDMUIsQ0FBQztFQUNELElBQUl1RixTQUFTLElBQUksT0FBT3ZGLGFBQWEsS0FBSyxRQUFRLEVBQUU7SUFDbEQrRixTQUFTLEdBQUdSLFNBQVMsS0FBSyxLQUFLLEdBQUd2RixhQUFhLEdBQUcsQ0FBQyxDQUFDLEdBQUdBLGFBQWE7RUFDdEU7RUFDQSxPQUFPRyxVQUFVLEdBQUc7SUFDbEJFLENBQUMsRUFBRTBGLFNBQVMsR0FBR3dGLGNBQWM7SUFDN0IvSyxDQUFDLEVBQUVvSCxRQUFRLEdBQUd5RDtFQUNoQixDQUFDLEdBQUc7SUFDRmhMLENBQUMsRUFBRXVILFFBQVEsR0FBR3lELGFBQWE7SUFDM0I3SyxDQUFDLEVBQUV1RixTQUFTLEdBQUd3RjtFQUNqQixDQUFDO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNckcsTUFBTSxHQUFHLFNBQUFBLENBQVU1QyxPQUFPLEVBQUU7RUFDaEMsSUFBSUEsT0FBTyxLQUFLLEtBQUssQ0FBQyxFQUFFO0lBQ3RCQSxPQUFPLEdBQUcsQ0FBQztFQUNiO0VBQ0EsT0FBTztJQUNMVixJQUFJLEVBQUUsUUFBUTtJQUNkVSxPQUFPO0lBQ1AsTUFBTVQsRUFBRUEsQ0FBQ1EsS0FBSyxFQUFFO01BQ2QsSUFBSW9KLHFCQUFxQixFQUFFL0QscUJBQXFCO01BQ2hELE1BQU07UUFDSnJILENBQUM7UUFDREcsQ0FBQztRQUNEYixTQUFTO1FBQ1Q2QjtNQUNGLENBQUMsR0FBR2EsS0FBSztNQUNULE1BQU1xSixVQUFVLEdBQUcsTUFBTU4sb0JBQW9CLENBQUMvSSxLQUFLLEVBQUVDLE9BQU8sQ0FBQzs7TUFFN0Q7TUFDQTtNQUNBLElBQUkzQyxTQUFTLE1BQU0sQ0FBQzhMLHFCQUFxQixHQUFHakssY0FBYyxDQUFDMEQsTUFBTSxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR3VHLHFCQUFxQixDQUFDOUwsU0FBUyxDQUFDLElBQUksQ0FBQytILHFCQUFxQixHQUFHbEcsY0FBYyxDQUFDd0MsS0FBSyxLQUFLLElBQUksSUFBSTBELHFCQUFxQixDQUFDdEMsZUFBZSxFQUFFO1FBQ3pOLE9BQU8sQ0FBQyxDQUFDO01BQ1g7TUFDQSxPQUFPO1FBQ0wvRSxDQUFDLEVBQUVBLENBQUMsR0FBR3FMLFVBQVUsQ0FBQ3JMLENBQUM7UUFDbkJHLENBQUMsRUFBRUEsQ0FBQyxHQUFHa0wsVUFBVSxDQUFDbEwsQ0FBQztRQUNuQndCLElBQUksRUFBRTtVQUNKLEdBQUcwSixVQUFVO1VBQ2IvTDtRQUNGO01BQ0YsQ0FBQztJQUNIO0VBQ0YsQ0FBQztBQUNILENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU1nTSxLQUFLLEdBQUcsU0FBQUEsQ0FBVXJKLE9BQU8sRUFBRTtFQUMvQixJQUFJQSxPQUFPLEtBQUssS0FBSyxDQUFDLEVBQUU7SUFDdEJBLE9BQU8sR0FBRyxDQUFDLENBQUM7RUFDZDtFQUNBLE9BQU87SUFDTFYsSUFBSSxFQUFFLE9BQU87SUFDYlUsT0FBTztJQUNQLE1BQU1ULEVBQUVBLENBQUNRLEtBQUssRUFBRTtNQUNkLE1BQU07UUFDSmhDLENBQUM7UUFDREcsQ0FBQztRQUNEYjtNQUNGLENBQUMsR0FBRzBDLEtBQUs7TUFDVCxNQUFNO1FBQ0p1RixRQUFRLEVBQUVDLGFBQWEsR0FBRyxJQUFJO1FBQzlCOUIsU0FBUyxFQUFFK0IsY0FBYyxHQUFHLEtBQUs7UUFDakM4RCxPQUFPLEdBQUc7VUFDUi9KLEVBQUUsRUFBRW5DLElBQUksSUFBSTtZQUNWLElBQUk7Y0FDRlcsQ0FBQztjQUNERztZQUNGLENBQUMsR0FBR2QsSUFBSTtZQUNSLE9BQU87Y0FDTFcsQ0FBQztjQUNERztZQUNGLENBQUM7VUFDSDtRQUNGLENBQUM7UUFDRCxHQUFHd0Y7TUFDTCxDQUFDLEdBQUdySCw0REFBUSxDQUFDMkQsT0FBTyxFQUFFRCxLQUFLLENBQUM7TUFDNUIsTUFBTTFCLE1BQU0sR0FBRztRQUNiTixDQUFDO1FBQ0RHO01BQ0YsQ0FBQztNQUNELE1BQU0yRixRQUFRLEdBQUcsTUFBTS9ELGNBQWMsQ0FBQ0MsS0FBSyxFQUFFMkQscUJBQXFCLENBQUM7TUFDbkUsTUFBTUQsU0FBUyxHQUFHekgsK0RBQVcsQ0FBQ0csMkRBQU8sQ0FBQ2tCLFNBQVMsQ0FBQyxDQUFDO01BQ2pELE1BQU1pSSxRQUFRLEdBQUdwSSxtRUFBZSxDQUFDdUcsU0FBUyxDQUFDO01BQzNDLElBQUk4RixhQUFhLEdBQUdsTCxNQUFNLENBQUNpSCxRQUFRLENBQUM7TUFDcEMsSUFBSWtFLGNBQWMsR0FBR25MLE1BQU0sQ0FBQ29GLFNBQVMsQ0FBQztNQUN0QyxJQUFJOEIsYUFBYSxFQUFFO1FBQ2pCLE1BQU1rRSxPQUFPLEdBQUduRSxRQUFRLEtBQUssR0FBRyxHQUFHLEtBQUssR0FBRyxNQUFNO1FBQ2pELE1BQU1vRSxPQUFPLEdBQUdwRSxRQUFRLEtBQUssR0FBRyxHQUFHLFFBQVEsR0FBRyxPQUFPO1FBQ3JELE1BQU05SSxHQUFHLEdBQUcrTSxhQUFhLEdBQUcxRixRQUFRLENBQUM0RixPQUFPLENBQUM7UUFDN0MsTUFBTXhNLEdBQUcsR0FBR3NNLGFBQWEsR0FBRzFGLFFBQVEsQ0FBQzZGLE9BQU8sQ0FBQztRQUM3Q0gsYUFBYSxHQUFHOU0seURBQUssQ0FBQ0QsR0FBRyxFQUFFK00sYUFBYSxFQUFFdE0sR0FBRyxDQUFDO01BQ2hEO01BQ0EsSUFBSXVJLGNBQWMsRUFBRTtRQUNsQixNQUFNaUUsT0FBTyxHQUFHaEcsU0FBUyxLQUFLLEdBQUcsR0FBRyxLQUFLLEdBQUcsTUFBTTtRQUNsRCxNQUFNaUcsT0FBTyxHQUFHakcsU0FBUyxLQUFLLEdBQUcsR0FBRyxRQUFRLEdBQUcsT0FBTztRQUN0RCxNQUFNakgsR0FBRyxHQUFHZ04sY0FBYyxHQUFHM0YsUUFBUSxDQUFDNEYsT0FBTyxDQUFDO1FBQzlDLE1BQU14TSxHQUFHLEdBQUd1TSxjQUFjLEdBQUczRixRQUFRLENBQUM2RixPQUFPLENBQUM7UUFDOUNGLGNBQWMsR0FBRy9NLHlEQUFLLENBQUNELEdBQUcsRUFBRWdOLGNBQWMsRUFBRXZNLEdBQUcsQ0FBQztNQUNsRDtNQUNBLE1BQU0wTSxhQUFhLEdBQUdMLE9BQU8sQ0FBQy9KLEVBQUUsQ0FBQztRQUMvQixHQUFHUSxLQUFLO1FBQ1IsQ0FBQ3VGLFFBQVEsR0FBR2lFLGFBQWE7UUFDekIsQ0FBQzlGLFNBQVMsR0FBRytGO01BQ2YsQ0FBQyxDQUFDO01BQ0YsT0FBTztRQUNMLEdBQUdHLGFBQWE7UUFDaEJqSyxJQUFJLEVBQUU7VUFDSjNCLENBQUMsRUFBRTRMLGFBQWEsQ0FBQzVMLENBQUMsR0FBR0EsQ0FBQztVQUN0QkcsQ0FBQyxFQUFFeUwsYUFBYSxDQUFDekwsQ0FBQyxHQUFHQSxDQUFDO1VBQ3RCMEwsT0FBTyxFQUFFO1lBQ1AsQ0FBQ3RFLFFBQVEsR0FBR0MsYUFBYTtZQUN6QixDQUFDOUIsU0FBUyxHQUFHK0I7VUFDZjtRQUNGO01BQ0YsQ0FBQztJQUNIO0VBQ0YsQ0FBQztBQUNILENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxNQUFNcUUsVUFBVSxHQUFHLFNBQUFBLENBQVU3SixPQUFPLEVBQUU7RUFDcEMsSUFBSUEsT0FBTyxLQUFLLEtBQUssQ0FBQyxFQUFFO0lBQ3RCQSxPQUFPLEdBQUcsQ0FBQyxDQUFDO0VBQ2Q7RUFDQSxPQUFPO0lBQ0xBLE9BQU87SUFDUFQsRUFBRUEsQ0FBQ1EsS0FBSyxFQUFFO01BQ1IsTUFBTTtRQUNKaEMsQ0FBQztRQUNERyxDQUFDO1FBQ0RiLFNBQVM7UUFDVDBCLEtBQUs7UUFDTEc7TUFDRixDQUFDLEdBQUdhLEtBQUs7TUFDVCxNQUFNO1FBQ0o2QyxNQUFNLEdBQUcsQ0FBQztRQUNWMEMsUUFBUSxFQUFFQyxhQUFhLEdBQUcsSUFBSTtRQUM5QjlCLFNBQVMsRUFBRStCLGNBQWMsR0FBRztNQUM5QixDQUFDLEdBQUduSiw0REFBUSxDQUFDMkQsT0FBTyxFQUFFRCxLQUFLLENBQUM7TUFDNUIsTUFBTTFCLE1BQU0sR0FBRztRQUNiTixDQUFDO1FBQ0RHO01BQ0YsQ0FBQztNQUNELE1BQU11RixTQUFTLEdBQUd6SCwrREFBVyxDQUFDcUIsU0FBUyxDQUFDO01BQ3hDLE1BQU1pSSxRQUFRLEdBQUdwSSxtRUFBZSxDQUFDdUcsU0FBUyxDQUFDO01BQzNDLElBQUk4RixhQUFhLEdBQUdsTCxNQUFNLENBQUNpSCxRQUFRLENBQUM7TUFDcEMsSUFBSWtFLGNBQWMsR0FBR25MLE1BQU0sQ0FBQ29GLFNBQVMsQ0FBQztNQUN0QyxNQUFNcUcsU0FBUyxHQUFHek4sNERBQVEsQ0FBQ3VHLE1BQU0sRUFBRTdDLEtBQUssQ0FBQztNQUN6QyxNQUFNZ0ssY0FBYyxHQUFHLE9BQU9ELFNBQVMsS0FBSyxRQUFRLEdBQUc7UUFDckR4RSxRQUFRLEVBQUV3RSxTQUFTO1FBQ25CckcsU0FBUyxFQUFFO01BQ2IsQ0FBQyxHQUFHO1FBQ0Y2QixRQUFRLEVBQUUsQ0FBQztRQUNYN0IsU0FBUyxFQUFFLENBQUM7UUFDWixHQUFHcUc7TUFDTCxDQUFDO01BQ0QsSUFBSXZFLGFBQWEsRUFBRTtRQUNqQixNQUFNeUUsR0FBRyxHQUFHMUUsUUFBUSxLQUFLLEdBQUcsR0FBRyxRQUFRLEdBQUcsT0FBTztRQUNqRCxNQUFNMkUsUUFBUSxHQUFHbEwsS0FBSyxDQUFDeEIsU0FBUyxDQUFDK0gsUUFBUSxDQUFDLEdBQUd2RyxLQUFLLENBQUN2QixRQUFRLENBQUN3TSxHQUFHLENBQUMsR0FBR0QsY0FBYyxDQUFDekUsUUFBUTtRQUMxRixNQUFNNEUsUUFBUSxHQUFHbkwsS0FBSyxDQUFDeEIsU0FBUyxDQUFDK0gsUUFBUSxDQUFDLEdBQUd2RyxLQUFLLENBQUN4QixTQUFTLENBQUN5TSxHQUFHLENBQUMsR0FBR0QsY0FBYyxDQUFDekUsUUFBUTtRQUMzRixJQUFJaUUsYUFBYSxHQUFHVSxRQUFRLEVBQUU7VUFDNUJWLGFBQWEsR0FBR1UsUUFBUTtRQUMxQixDQUFDLE1BQU0sSUFBSVYsYUFBYSxHQUFHVyxRQUFRLEVBQUU7VUFDbkNYLGFBQWEsR0FBR1csUUFBUTtRQUMxQjtNQUNGO01BQ0EsSUFBSTFFLGNBQWMsRUFBRTtRQUNsQixJQUFJMkQscUJBQXFCLEVBQUVnQixzQkFBc0I7UUFDakQsTUFBTUgsR0FBRyxHQUFHMUUsUUFBUSxLQUFLLEdBQUcsR0FBRyxPQUFPLEdBQUcsUUFBUTtRQUNqRCxNQUFNOEUsWUFBWSxHQUFHLENBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDcEIsUUFBUSxDQUFDN00sMkRBQU8sQ0FBQ2tCLFNBQVMsQ0FBQyxDQUFDO1FBQ2pFLE1BQU00TSxRQUFRLEdBQUdsTCxLQUFLLENBQUN4QixTQUFTLENBQUNrRyxTQUFTLENBQUMsR0FBRzFFLEtBQUssQ0FBQ3ZCLFFBQVEsQ0FBQ3dNLEdBQUcsQ0FBQyxJQUFJSSxZQUFZLEdBQUcsQ0FBQyxDQUFDakIscUJBQXFCLEdBQUdqSyxjQUFjLENBQUMwRCxNQUFNLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHdUcscUJBQXFCLENBQUMxRixTQUFTLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUkyRyxZQUFZLEdBQUcsQ0FBQyxHQUFHTCxjQUFjLENBQUN0RyxTQUFTLENBQUM7UUFDblAsTUFBTXlHLFFBQVEsR0FBR25MLEtBQUssQ0FBQ3hCLFNBQVMsQ0FBQ2tHLFNBQVMsQ0FBQyxHQUFHMUUsS0FBSyxDQUFDeEIsU0FBUyxDQUFDeU0sR0FBRyxDQUFDLElBQUlJLFlBQVksR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDRCxzQkFBc0IsR0FBR2pMLGNBQWMsQ0FBQzBELE1BQU0sS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUd1SCxzQkFBc0IsQ0FBQzFHLFNBQVMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJMkcsWUFBWSxHQUFHTCxjQUFjLENBQUN0RyxTQUFTLEdBQUcsQ0FBQyxDQUFDO1FBQ3RQLElBQUkrRixjQUFjLEdBQUdTLFFBQVEsRUFBRTtVQUM3QlQsY0FBYyxHQUFHUyxRQUFRO1FBQzNCLENBQUMsTUFBTSxJQUFJVCxjQUFjLEdBQUdVLFFBQVEsRUFBRTtVQUNwQ1YsY0FBYyxHQUFHVSxRQUFRO1FBQzNCO01BQ0Y7TUFDQSxPQUFPO1FBQ0wsQ0FBQzVFLFFBQVEsR0FBR2lFLGFBQWE7UUFDekIsQ0FBQzlGLFNBQVMsR0FBRytGO01BQ2YsQ0FBQztJQUNIO0VBQ0YsQ0FBQztBQUNILENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTWEsSUFBSSxHQUFHLFNBQUFBLENBQVVySyxPQUFPLEVBQUU7RUFDOUIsSUFBSUEsT0FBTyxLQUFLLEtBQUssQ0FBQyxFQUFFO0lBQ3RCQSxPQUFPLEdBQUcsQ0FBQyxDQUFDO0VBQ2Q7RUFDQSxPQUFPO0lBQ0xWLElBQUksRUFBRSxNQUFNO0lBQ1pVLE9BQU87SUFDUCxNQUFNVCxFQUFFQSxDQUFDUSxLQUFLLEVBQUU7TUFDZCxJQUFJdUsscUJBQXFCLEVBQUVDLHNCQUFzQjtNQUNqRCxNQUFNO1FBQ0psTixTQUFTO1FBQ1QwQixLQUFLO1FBQ0xMLFFBQVE7UUFDUm1CO01BQ0YsQ0FBQyxHQUFHRSxLQUFLO01BQ1QsTUFBTTtRQUNKeUssS0FBSyxHQUFHQSxDQUFBLEtBQU0sQ0FBQyxDQUFDO1FBQ2hCLEdBQUc5RztNQUNMLENBQUMsR0FBR3JILDREQUFRLENBQUMyRCxPQUFPLEVBQUVELEtBQUssQ0FBQztNQUM1QixNQUFNOEQsUUFBUSxHQUFHLE1BQU0vRCxjQUFjLENBQUNDLEtBQUssRUFBRTJELHFCQUFxQixDQUFDO01BQ25FLE1BQU05RixJQUFJLEdBQUd6QiwyREFBTyxDQUFDa0IsU0FBUyxDQUFDO01BQy9CLE1BQU00RixTQUFTLEdBQUc3RyxnRUFBWSxDQUFDaUIsU0FBUyxDQUFDO01BQ3pDLE1BQU15RSxPQUFPLEdBQUc5RiwrREFBVyxDQUFDcUIsU0FBUyxDQUFDLEtBQUssR0FBRztNQUM5QyxNQUFNO1FBQ0pXLEtBQUs7UUFDTEc7TUFDRixDQUFDLEdBQUdZLEtBQUssQ0FBQ3ZCLFFBQVE7TUFDbEIsSUFBSWlOLFVBQVU7TUFDZCxJQUFJQyxTQUFTO01BQ2IsSUFBSTlNLElBQUksS0FBSyxLQUFLLElBQUlBLElBQUksS0FBSyxRQUFRLEVBQUU7UUFDdkM2TSxVQUFVLEdBQUc3TSxJQUFJO1FBQ2pCOE0sU0FBUyxHQUFHekgsU0FBUyxNQUFNLENBQUMsT0FBT3ZFLFFBQVEsQ0FBQ0ksS0FBSyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0osUUFBUSxDQUFDSSxLQUFLLENBQUNlLFFBQVEsQ0FBQ3JDLFFBQVEsQ0FBQyxDQUFDLElBQUksT0FBTyxHQUFHLEtBQUssQ0FBQyxHQUFHLE1BQU0sR0FBRyxPQUFPO01BQ2hKLENBQUMsTUFBTTtRQUNMa04sU0FBUyxHQUFHOU0sSUFBSTtRQUNoQjZNLFVBQVUsR0FBR3hILFNBQVMsS0FBSyxLQUFLLEdBQUcsS0FBSyxHQUFHLFFBQVE7TUFDckQ7TUFDQSxNQUFNMEgscUJBQXFCLEdBQUd4TSxNQUFNLEdBQUcwRixRQUFRLENBQUN2QyxHQUFHLEdBQUd1QyxRQUFRLENBQUN0QyxNQUFNO01BQ3JFLE1BQU1xSixvQkFBb0IsR0FBRzVNLEtBQUssR0FBRzZGLFFBQVEsQ0FBQ3JDLElBQUksR0FBR3FDLFFBQVEsQ0FBQ3BDLEtBQUs7TUFDbkUsTUFBTW9KLHVCQUF1QixHQUFHck8sdURBQUcsQ0FBQzJCLE1BQU0sR0FBRzBGLFFBQVEsQ0FBQzRHLFVBQVUsQ0FBQyxFQUFFRSxxQkFBcUIsQ0FBQztNQUN6RixNQUFNRyxzQkFBc0IsR0FBR3RPLHVEQUFHLENBQUN3QixLQUFLLEdBQUc2RixRQUFRLENBQUM2RyxTQUFTLENBQUMsRUFBRUUsb0JBQW9CLENBQUM7TUFDckYsTUFBTUcsT0FBTyxHQUFHLENBQUNoTCxLQUFLLENBQUNiLGNBQWMsQ0FBQ21LLEtBQUs7TUFDM0MsSUFBSTJCLGVBQWUsR0FBR0gsdUJBQXVCO01BQzdDLElBQUlJLGNBQWMsR0FBR0gsc0JBQXNCO01BQzNDLElBQUksQ0FBQ1IscUJBQXFCLEdBQUd2SyxLQUFLLENBQUNiLGNBQWMsQ0FBQ21LLEtBQUssS0FBSyxJQUFJLElBQUlpQixxQkFBcUIsQ0FBQ1YsT0FBTyxDQUFDN0wsQ0FBQyxFQUFFO1FBQ25Ha04sY0FBYyxHQUFHTCxvQkFBb0I7TUFDdkM7TUFDQSxJQUFJLENBQUNMLHNCQUFzQixHQUFHeEssS0FBSyxDQUFDYixjQUFjLENBQUNtSyxLQUFLLEtBQUssSUFBSSxJQUFJa0Isc0JBQXNCLENBQUNYLE9BQU8sQ0FBQzFMLENBQUMsRUFBRTtRQUNyRzhNLGVBQWUsR0FBR0wscUJBQXFCO01BQ3pDO01BQ0EsSUFBSUksT0FBTyxJQUFJLENBQUM5SCxTQUFTLEVBQUU7UUFDekIsTUFBTWlJLElBQUksR0FBR2pPLHVEQUFHLENBQUM0RyxRQUFRLENBQUNyQyxJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBQ2xDLE1BQU0ySixJQUFJLEdBQUdsTyx1REFBRyxDQUFDNEcsUUFBUSxDQUFDcEMsS0FBSyxFQUFFLENBQUMsQ0FBQztRQUNuQyxNQUFNMkosSUFBSSxHQUFHbk8sdURBQUcsQ0FBQzRHLFFBQVEsQ0FBQ3ZDLEdBQUcsRUFBRSxDQUFDLENBQUM7UUFDakMsTUFBTStKLElBQUksR0FBR3BPLHVEQUFHLENBQUM0RyxRQUFRLENBQUN0QyxNQUFNLEVBQUUsQ0FBQyxDQUFDO1FBQ3BDLElBQUlPLE9BQU8sRUFBRTtVQUNYbUosY0FBYyxHQUFHak4sS0FBSyxHQUFHLENBQUMsSUFBSWtOLElBQUksS0FBSyxDQUFDLElBQUlDLElBQUksS0FBSyxDQUFDLEdBQUdELElBQUksR0FBR0MsSUFBSSxHQUFHbE8sdURBQUcsQ0FBQzRHLFFBQVEsQ0FBQ3JDLElBQUksRUFBRXFDLFFBQVEsQ0FBQ3BDLEtBQUssQ0FBQyxDQUFDO1FBQzVHLENBQUMsTUFBTTtVQUNMdUosZUFBZSxHQUFHN00sTUFBTSxHQUFHLENBQUMsSUFBSWlOLElBQUksS0FBSyxDQUFDLElBQUlDLElBQUksS0FBSyxDQUFDLEdBQUdELElBQUksR0FBR0MsSUFBSSxHQUFHcE8sdURBQUcsQ0FBQzRHLFFBQVEsQ0FBQ3ZDLEdBQUcsRUFBRXVDLFFBQVEsQ0FBQ3RDLE1BQU0sQ0FBQyxDQUFDO1FBQzlHO01BQ0Y7TUFDQSxNQUFNaUosS0FBSyxDQUFDO1FBQ1YsR0FBR3pLLEtBQUs7UUFDUmtMLGNBQWM7UUFDZEQ7TUFDRixDQUFDLENBQUM7TUFDRixNQUFNTSxjQUFjLEdBQUcsTUFBTTVNLFFBQVEsQ0FBQ21ELGFBQWEsQ0FBQ2hDLFFBQVEsQ0FBQ3JDLFFBQVEsQ0FBQztNQUN0RSxJQUFJUSxLQUFLLEtBQUtzTixjQUFjLENBQUN0TixLQUFLLElBQUlHLE1BQU0sS0FBS21OLGNBQWMsQ0FBQ25OLE1BQU0sRUFBRTtRQUN0RSxPQUFPO1VBQ0x3QixLQUFLLEVBQUU7WUFDTFosS0FBSyxFQUFFO1VBQ1Q7UUFDRixDQUFDO01BQ0g7TUFDQSxPQUFPLENBQUMsQ0FBQztJQUNYO0VBQ0YsQ0FBQztBQUNILENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxAZmxvYXRpbmctdWlcXGNvcmVcXGRpc3RcXGZsb2F0aW5nLXVpLmNvcmUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldFNpZGVBeGlzLCBnZXRBbGlnbm1lbnRBeGlzLCBnZXRBeGlzTGVuZ3RoLCBnZXRTaWRlLCBnZXRBbGlnbm1lbnQsIGV2YWx1YXRlLCBnZXRQYWRkaW5nT2JqZWN0LCByZWN0VG9DbGllbnRSZWN0LCBtaW4sIGNsYW1wLCBwbGFjZW1lbnRzLCBnZXRBbGlnbm1lbnRTaWRlcywgZ2V0T3Bwb3NpdGVBbGlnbm1lbnRQbGFjZW1lbnQsIGdldE9wcG9zaXRlUGxhY2VtZW50LCBnZXRFeHBhbmRlZFBsYWNlbWVudHMsIGdldE9wcG9zaXRlQXhpc1BsYWNlbWVudHMsIHNpZGVzLCBtYXgsIGdldE9wcG9zaXRlQXhpcyB9IGZyb20gJ0BmbG9hdGluZy11aS91dGlscyc7XG5leHBvcnQgeyByZWN0VG9DbGllbnRSZWN0IH0gZnJvbSAnQGZsb2F0aW5nLXVpL3V0aWxzJztcblxuZnVuY3Rpb24gY29tcHV0ZUNvb3Jkc0Zyb21QbGFjZW1lbnQoX3JlZiwgcGxhY2VtZW50LCBydGwpIHtcbiAgbGV0IHtcbiAgICByZWZlcmVuY2UsXG4gICAgZmxvYXRpbmdcbiAgfSA9IF9yZWY7XG4gIGNvbnN0IHNpZGVBeGlzID0gZ2V0U2lkZUF4aXMocGxhY2VtZW50KTtcbiAgY29uc3QgYWxpZ25tZW50QXhpcyA9IGdldEFsaWdubWVudEF4aXMocGxhY2VtZW50KTtcbiAgY29uc3QgYWxpZ25MZW5ndGggPSBnZXRBeGlzTGVuZ3RoKGFsaWdubWVudEF4aXMpO1xuICBjb25zdCBzaWRlID0gZ2V0U2lkZShwbGFjZW1lbnQpO1xuICBjb25zdCBpc1ZlcnRpY2FsID0gc2lkZUF4aXMgPT09ICd5JztcbiAgY29uc3QgY29tbW9uWCA9IHJlZmVyZW5jZS54ICsgcmVmZXJlbmNlLndpZHRoIC8gMiAtIGZsb2F0aW5nLndpZHRoIC8gMjtcbiAgY29uc3QgY29tbW9uWSA9IHJlZmVyZW5jZS55ICsgcmVmZXJlbmNlLmhlaWdodCAvIDIgLSBmbG9hdGluZy5oZWlnaHQgLyAyO1xuICBjb25zdCBjb21tb25BbGlnbiA9IHJlZmVyZW5jZVthbGlnbkxlbmd0aF0gLyAyIC0gZmxvYXRpbmdbYWxpZ25MZW5ndGhdIC8gMjtcbiAgbGV0IGNvb3JkcztcbiAgc3dpdGNoIChzaWRlKSB7XG4gICAgY2FzZSAndG9wJzpcbiAgICAgIGNvb3JkcyA9IHtcbiAgICAgICAgeDogY29tbW9uWCxcbiAgICAgICAgeTogcmVmZXJlbmNlLnkgLSBmbG9hdGluZy5oZWlnaHRcbiAgICAgIH07XG4gICAgICBicmVhaztcbiAgICBjYXNlICdib3R0b20nOlxuICAgICAgY29vcmRzID0ge1xuICAgICAgICB4OiBjb21tb25YLFxuICAgICAgICB5OiByZWZlcmVuY2UueSArIHJlZmVyZW5jZS5oZWlnaHRcbiAgICAgIH07XG4gICAgICBicmVhaztcbiAgICBjYXNlICdyaWdodCc6XG4gICAgICBjb29yZHMgPSB7XG4gICAgICAgIHg6IHJlZmVyZW5jZS54ICsgcmVmZXJlbmNlLndpZHRoLFxuICAgICAgICB5OiBjb21tb25ZXG4gICAgICB9O1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSAnbGVmdCc6XG4gICAgICBjb29yZHMgPSB7XG4gICAgICAgIHg6IHJlZmVyZW5jZS54IC0gZmxvYXRpbmcud2lkdGgsXG4gICAgICAgIHk6IGNvbW1vbllcbiAgICAgIH07XG4gICAgICBicmVhaztcbiAgICBkZWZhdWx0OlxuICAgICAgY29vcmRzID0ge1xuICAgICAgICB4OiByZWZlcmVuY2UueCxcbiAgICAgICAgeTogcmVmZXJlbmNlLnlcbiAgICAgIH07XG4gIH1cbiAgc3dpdGNoIChnZXRBbGlnbm1lbnQocGxhY2VtZW50KSkge1xuICAgIGNhc2UgJ3N0YXJ0JzpcbiAgICAgIGNvb3Jkc1thbGlnbm1lbnRBeGlzXSAtPSBjb21tb25BbGlnbiAqIChydGwgJiYgaXNWZXJ0aWNhbCA/IC0xIDogMSk7XG4gICAgICBicmVhaztcbiAgICBjYXNlICdlbmQnOlxuICAgICAgY29vcmRzW2FsaWdubWVudEF4aXNdICs9IGNvbW1vbkFsaWduICogKHJ0bCAmJiBpc1ZlcnRpY2FsID8gLTEgOiAxKTtcbiAgICAgIGJyZWFrO1xuICB9XG4gIHJldHVybiBjb29yZHM7XG59XG5cbi8qKlxuICogQ29tcHV0ZXMgdGhlIGB4YCBhbmQgYHlgIGNvb3JkaW5hdGVzIHRoYXQgd2lsbCBwbGFjZSB0aGUgZmxvYXRpbmcgZWxlbWVudFxuICogbmV4dCB0byBhIGdpdmVuIHJlZmVyZW5jZSBlbGVtZW50LlxuICpcbiAqIFRoaXMgZXhwb3J0IGRvZXMgbm90IGhhdmUgYW55IGBwbGF0Zm9ybWAgaW50ZXJmYWNlIGxvZ2ljLiBZb3Ugd2lsbCBuZWVkIHRvXG4gKiB3cml0ZSBvbmUgZm9yIHRoZSBwbGF0Zm9ybSB5b3UgYXJlIHVzaW5nIEZsb2F0aW5nIFVJIHdpdGguXG4gKi9cbmNvbnN0IGNvbXB1dGVQb3NpdGlvbiA9IGFzeW5jIChyZWZlcmVuY2UsIGZsb2F0aW5nLCBjb25maWcpID0+IHtcbiAgY29uc3Qge1xuICAgIHBsYWNlbWVudCA9ICdib3R0b20nLFxuICAgIHN0cmF0ZWd5ID0gJ2Fic29sdXRlJyxcbiAgICBtaWRkbGV3YXJlID0gW10sXG4gICAgcGxhdGZvcm1cbiAgfSA9IGNvbmZpZztcbiAgY29uc3QgdmFsaWRNaWRkbGV3YXJlID0gbWlkZGxld2FyZS5maWx0ZXIoQm9vbGVhbik7XG4gIGNvbnN0IHJ0bCA9IGF3YWl0IChwbGF0Zm9ybS5pc1JUTCA9PSBudWxsID8gdm9pZCAwIDogcGxhdGZvcm0uaXNSVEwoZmxvYXRpbmcpKTtcbiAgbGV0IHJlY3RzID0gYXdhaXQgcGxhdGZvcm0uZ2V0RWxlbWVudFJlY3RzKHtcbiAgICByZWZlcmVuY2UsXG4gICAgZmxvYXRpbmcsXG4gICAgc3RyYXRlZ3lcbiAgfSk7XG4gIGxldCB7XG4gICAgeCxcbiAgICB5XG4gIH0gPSBjb21wdXRlQ29vcmRzRnJvbVBsYWNlbWVudChyZWN0cywgcGxhY2VtZW50LCBydGwpO1xuICBsZXQgc3RhdGVmdWxQbGFjZW1lbnQgPSBwbGFjZW1lbnQ7XG4gIGxldCBtaWRkbGV3YXJlRGF0YSA9IHt9O1xuICBsZXQgcmVzZXRDb3VudCA9IDA7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgdmFsaWRNaWRkbGV3YXJlLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3Qge1xuICAgICAgbmFtZSxcbiAgICAgIGZuXG4gICAgfSA9IHZhbGlkTWlkZGxld2FyZVtpXTtcbiAgICBjb25zdCB7XG4gICAgICB4OiBuZXh0WCxcbiAgICAgIHk6IG5leHRZLFxuICAgICAgZGF0YSxcbiAgICAgIHJlc2V0XG4gICAgfSA9IGF3YWl0IGZuKHtcbiAgICAgIHgsXG4gICAgICB5LFxuICAgICAgaW5pdGlhbFBsYWNlbWVudDogcGxhY2VtZW50LFxuICAgICAgcGxhY2VtZW50OiBzdGF0ZWZ1bFBsYWNlbWVudCxcbiAgICAgIHN0cmF0ZWd5LFxuICAgICAgbWlkZGxld2FyZURhdGEsXG4gICAgICByZWN0cyxcbiAgICAgIHBsYXRmb3JtLFxuICAgICAgZWxlbWVudHM6IHtcbiAgICAgICAgcmVmZXJlbmNlLFxuICAgICAgICBmbG9hdGluZ1xuICAgICAgfVxuICAgIH0pO1xuICAgIHggPSBuZXh0WCAhPSBudWxsID8gbmV4dFggOiB4O1xuICAgIHkgPSBuZXh0WSAhPSBudWxsID8gbmV4dFkgOiB5O1xuICAgIG1pZGRsZXdhcmVEYXRhID0ge1xuICAgICAgLi4ubWlkZGxld2FyZURhdGEsXG4gICAgICBbbmFtZV06IHtcbiAgICAgICAgLi4ubWlkZGxld2FyZURhdGFbbmFtZV0sXG4gICAgICAgIC4uLmRhdGFcbiAgICAgIH1cbiAgICB9O1xuICAgIGlmIChyZXNldCAmJiByZXNldENvdW50IDw9IDUwKSB7XG4gICAgICByZXNldENvdW50Kys7XG4gICAgICBpZiAodHlwZW9mIHJlc2V0ID09PSAnb2JqZWN0Jykge1xuICAgICAgICBpZiAocmVzZXQucGxhY2VtZW50KSB7XG4gICAgICAgICAgc3RhdGVmdWxQbGFjZW1lbnQgPSByZXNldC5wbGFjZW1lbnQ7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHJlc2V0LnJlY3RzKSB7XG4gICAgICAgICAgcmVjdHMgPSByZXNldC5yZWN0cyA9PT0gdHJ1ZSA/IGF3YWl0IHBsYXRmb3JtLmdldEVsZW1lbnRSZWN0cyh7XG4gICAgICAgICAgICByZWZlcmVuY2UsXG4gICAgICAgICAgICBmbG9hdGluZyxcbiAgICAgICAgICAgIHN0cmF0ZWd5XG4gICAgICAgICAgfSkgOiByZXNldC5yZWN0cztcbiAgICAgICAgfVxuICAgICAgICAoe1xuICAgICAgICAgIHgsXG4gICAgICAgICAgeVxuICAgICAgICB9ID0gY29tcHV0ZUNvb3Jkc0Zyb21QbGFjZW1lbnQocmVjdHMsIHN0YXRlZnVsUGxhY2VtZW50LCBydGwpKTtcbiAgICAgIH1cbiAgICAgIGkgPSAtMTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHtcbiAgICB4LFxuICAgIHksXG4gICAgcGxhY2VtZW50OiBzdGF0ZWZ1bFBsYWNlbWVudCxcbiAgICBzdHJhdGVneSxcbiAgICBtaWRkbGV3YXJlRGF0YVxuICB9O1xufTtcblxuLyoqXG4gKiBSZXNvbHZlcyB3aXRoIGFuIG9iamVjdCBvZiBvdmVyZmxvdyBzaWRlIG9mZnNldHMgdGhhdCBkZXRlcm1pbmUgaG93IG11Y2ggdGhlXG4gKiBlbGVtZW50IGlzIG92ZXJmbG93aW5nIGEgZ2l2ZW4gY2xpcHBpbmcgYm91bmRhcnkgb24gZWFjaCBzaWRlLlxuICogLSBwb3NpdGl2ZSA9IG92ZXJmbG93aW5nIHRoZSBib3VuZGFyeSBieSB0aGF0IG51bWJlciBvZiBwaXhlbHNcbiAqIC0gbmVnYXRpdmUgPSBob3cgbWFueSBwaXhlbHMgbGVmdCBiZWZvcmUgaXQgd2lsbCBvdmVyZmxvd1xuICogLSAwID0gbGllcyBmbHVzaCB3aXRoIHRoZSBib3VuZGFyeVxuICogQHNlZSBodHRwczovL2Zsb2F0aW5nLXVpLmNvbS9kb2NzL2RldGVjdE92ZXJmbG93XG4gKi9cbmFzeW5jIGZ1bmN0aW9uIGRldGVjdE92ZXJmbG93KHN0YXRlLCBvcHRpb25zKSB7XG4gIHZhciBfYXdhaXQkcGxhdGZvcm0kaXNFbGU7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgY29uc3Qge1xuICAgIHgsXG4gICAgeSxcbiAgICBwbGF0Zm9ybSxcbiAgICByZWN0cyxcbiAgICBlbGVtZW50cyxcbiAgICBzdHJhdGVneVxuICB9ID0gc3RhdGU7XG4gIGNvbnN0IHtcbiAgICBib3VuZGFyeSA9ICdjbGlwcGluZ0FuY2VzdG9ycycsXG4gICAgcm9vdEJvdW5kYXJ5ID0gJ3ZpZXdwb3J0JyxcbiAgICBlbGVtZW50Q29udGV4dCA9ICdmbG9hdGluZycsXG4gICAgYWx0Qm91bmRhcnkgPSBmYWxzZSxcbiAgICBwYWRkaW5nID0gMFxuICB9ID0gZXZhbHVhdGUob3B0aW9ucywgc3RhdGUpO1xuICBjb25zdCBwYWRkaW5nT2JqZWN0ID0gZ2V0UGFkZGluZ09iamVjdChwYWRkaW5nKTtcbiAgY29uc3QgYWx0Q29udGV4dCA9IGVsZW1lbnRDb250ZXh0ID09PSAnZmxvYXRpbmcnID8gJ3JlZmVyZW5jZScgOiAnZmxvYXRpbmcnO1xuICBjb25zdCBlbGVtZW50ID0gZWxlbWVudHNbYWx0Qm91bmRhcnkgPyBhbHRDb250ZXh0IDogZWxlbWVudENvbnRleHRdO1xuICBjb25zdCBjbGlwcGluZ0NsaWVudFJlY3QgPSByZWN0VG9DbGllbnRSZWN0KGF3YWl0IHBsYXRmb3JtLmdldENsaXBwaW5nUmVjdCh7XG4gICAgZWxlbWVudDogKChfYXdhaXQkcGxhdGZvcm0kaXNFbGUgPSBhd2FpdCAocGxhdGZvcm0uaXNFbGVtZW50ID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5pc0VsZW1lbnQoZWxlbWVudCkpKSAhPSBudWxsID8gX2F3YWl0JHBsYXRmb3JtJGlzRWxlIDogdHJ1ZSkgPyBlbGVtZW50IDogZWxlbWVudC5jb250ZXh0RWxlbWVudCB8fCAoYXdhaXQgKHBsYXRmb3JtLmdldERvY3VtZW50RWxlbWVudCA9PSBudWxsID8gdm9pZCAwIDogcGxhdGZvcm0uZ2V0RG9jdW1lbnRFbGVtZW50KGVsZW1lbnRzLmZsb2F0aW5nKSkpLFxuICAgIGJvdW5kYXJ5LFxuICAgIHJvb3RCb3VuZGFyeSxcbiAgICBzdHJhdGVneVxuICB9KSk7XG4gIGNvbnN0IHJlY3QgPSBlbGVtZW50Q29udGV4dCA9PT0gJ2Zsb2F0aW5nJyA/IHtcbiAgICB4LFxuICAgIHksXG4gICAgd2lkdGg6IHJlY3RzLmZsb2F0aW5nLndpZHRoLFxuICAgIGhlaWdodDogcmVjdHMuZmxvYXRpbmcuaGVpZ2h0XG4gIH0gOiByZWN0cy5yZWZlcmVuY2U7XG4gIGNvbnN0IG9mZnNldFBhcmVudCA9IGF3YWl0IChwbGF0Zm9ybS5nZXRPZmZzZXRQYXJlbnQgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmdldE9mZnNldFBhcmVudChlbGVtZW50cy5mbG9hdGluZykpO1xuICBjb25zdCBvZmZzZXRTY2FsZSA9IChhd2FpdCAocGxhdGZvcm0uaXNFbGVtZW50ID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5pc0VsZW1lbnQob2Zmc2V0UGFyZW50KSkpID8gKGF3YWl0IChwbGF0Zm9ybS5nZXRTY2FsZSA9PSBudWxsID8gdm9pZCAwIDogcGxhdGZvcm0uZ2V0U2NhbGUob2Zmc2V0UGFyZW50KSkpIHx8IHtcbiAgICB4OiAxLFxuICAgIHk6IDFcbiAgfSA6IHtcbiAgICB4OiAxLFxuICAgIHk6IDFcbiAgfTtcbiAgY29uc3QgZWxlbWVudENsaWVudFJlY3QgPSByZWN0VG9DbGllbnRSZWN0KHBsYXRmb3JtLmNvbnZlcnRPZmZzZXRQYXJlbnRSZWxhdGl2ZVJlY3RUb1ZpZXdwb3J0UmVsYXRpdmVSZWN0ID8gYXdhaXQgcGxhdGZvcm0uY29udmVydE9mZnNldFBhcmVudFJlbGF0aXZlUmVjdFRvVmlld3BvcnRSZWxhdGl2ZVJlY3Qoe1xuICAgIGVsZW1lbnRzLFxuICAgIHJlY3QsXG4gICAgb2Zmc2V0UGFyZW50LFxuICAgIHN0cmF0ZWd5XG4gIH0pIDogcmVjdCk7XG4gIHJldHVybiB7XG4gICAgdG9wOiAoY2xpcHBpbmdDbGllbnRSZWN0LnRvcCAtIGVsZW1lbnRDbGllbnRSZWN0LnRvcCArIHBhZGRpbmdPYmplY3QudG9wKSAvIG9mZnNldFNjYWxlLnksXG4gICAgYm90dG9tOiAoZWxlbWVudENsaWVudFJlY3QuYm90dG9tIC0gY2xpcHBpbmdDbGllbnRSZWN0LmJvdHRvbSArIHBhZGRpbmdPYmplY3QuYm90dG9tKSAvIG9mZnNldFNjYWxlLnksXG4gICAgbGVmdDogKGNsaXBwaW5nQ2xpZW50UmVjdC5sZWZ0IC0gZWxlbWVudENsaWVudFJlY3QubGVmdCArIHBhZGRpbmdPYmplY3QubGVmdCkgLyBvZmZzZXRTY2FsZS54LFxuICAgIHJpZ2h0OiAoZWxlbWVudENsaWVudFJlY3QucmlnaHQgLSBjbGlwcGluZ0NsaWVudFJlY3QucmlnaHQgKyBwYWRkaW5nT2JqZWN0LnJpZ2h0KSAvIG9mZnNldFNjYWxlLnhcbiAgfTtcbn1cblxuLyoqXG4gKiBQcm92aWRlcyBkYXRhIHRvIHBvc2l0aW9uIGFuIGlubmVyIGVsZW1lbnQgb2YgdGhlIGZsb2F0aW5nIGVsZW1lbnQgc28gdGhhdCBpdFxuICogYXBwZWFycyBjZW50ZXJlZCB0byB0aGUgcmVmZXJlbmNlIGVsZW1lbnQuXG4gKiBAc2VlIGh0dHBzOi8vZmxvYXRpbmctdWkuY29tL2RvY3MvYXJyb3dcbiAqL1xuY29uc3QgYXJyb3cgPSBvcHRpb25zID0+ICh7XG4gIG5hbWU6ICdhcnJvdycsXG4gIG9wdGlvbnMsXG4gIGFzeW5jIGZuKHN0YXRlKSB7XG4gICAgY29uc3Qge1xuICAgICAgeCxcbiAgICAgIHksXG4gICAgICBwbGFjZW1lbnQsXG4gICAgICByZWN0cyxcbiAgICAgIHBsYXRmb3JtLFxuICAgICAgZWxlbWVudHMsXG4gICAgICBtaWRkbGV3YXJlRGF0YVxuICAgIH0gPSBzdGF0ZTtcbiAgICAvLyBTaW5jZSBgZWxlbWVudGAgaXMgcmVxdWlyZWQsIHdlIGRvbid0IFBhcnRpYWw8PiB0aGUgdHlwZS5cbiAgICBjb25zdCB7XG4gICAgICBlbGVtZW50LFxuICAgICAgcGFkZGluZyA9IDBcbiAgICB9ID0gZXZhbHVhdGUob3B0aW9ucywgc3RhdGUpIHx8IHt9O1xuICAgIGlmIChlbGVtZW50ID09IG51bGwpIHtcbiAgICAgIHJldHVybiB7fTtcbiAgICB9XG4gICAgY29uc3QgcGFkZGluZ09iamVjdCA9IGdldFBhZGRpbmdPYmplY3QocGFkZGluZyk7XG4gICAgY29uc3QgY29vcmRzID0ge1xuICAgICAgeCxcbiAgICAgIHlcbiAgICB9O1xuICAgIGNvbnN0IGF4aXMgPSBnZXRBbGlnbm1lbnRBeGlzKHBsYWNlbWVudCk7XG4gICAgY29uc3QgbGVuZ3RoID0gZ2V0QXhpc0xlbmd0aChheGlzKTtcbiAgICBjb25zdCBhcnJvd0RpbWVuc2lvbnMgPSBhd2FpdCBwbGF0Zm9ybS5nZXREaW1lbnNpb25zKGVsZW1lbnQpO1xuICAgIGNvbnN0IGlzWUF4aXMgPSBheGlzID09PSAneSc7XG4gICAgY29uc3QgbWluUHJvcCA9IGlzWUF4aXMgPyAndG9wJyA6ICdsZWZ0JztcbiAgICBjb25zdCBtYXhQcm9wID0gaXNZQXhpcyA/ICdib3R0b20nIDogJ3JpZ2h0JztcbiAgICBjb25zdCBjbGllbnRQcm9wID0gaXNZQXhpcyA/ICdjbGllbnRIZWlnaHQnIDogJ2NsaWVudFdpZHRoJztcbiAgICBjb25zdCBlbmREaWZmID0gcmVjdHMucmVmZXJlbmNlW2xlbmd0aF0gKyByZWN0cy5yZWZlcmVuY2VbYXhpc10gLSBjb29yZHNbYXhpc10gLSByZWN0cy5mbG9hdGluZ1tsZW5ndGhdO1xuICAgIGNvbnN0IHN0YXJ0RGlmZiA9IGNvb3Jkc1theGlzXSAtIHJlY3RzLnJlZmVyZW5jZVtheGlzXTtcbiAgICBjb25zdCBhcnJvd09mZnNldFBhcmVudCA9IGF3YWl0IChwbGF0Zm9ybS5nZXRPZmZzZXRQYXJlbnQgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmdldE9mZnNldFBhcmVudChlbGVtZW50KSk7XG4gICAgbGV0IGNsaWVudFNpemUgPSBhcnJvd09mZnNldFBhcmVudCA/IGFycm93T2Zmc2V0UGFyZW50W2NsaWVudFByb3BdIDogMDtcblxuICAgIC8vIERPTSBwbGF0Zm9ybSBjYW4gcmV0dXJuIGB3aW5kb3dgIGFzIHRoZSBgb2Zmc2V0UGFyZW50YC5cbiAgICBpZiAoIWNsaWVudFNpemUgfHwgIShhd2FpdCAocGxhdGZvcm0uaXNFbGVtZW50ID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5pc0VsZW1lbnQoYXJyb3dPZmZzZXRQYXJlbnQpKSkpIHtcbiAgICAgIGNsaWVudFNpemUgPSBlbGVtZW50cy5mbG9hdGluZ1tjbGllbnRQcm9wXSB8fCByZWN0cy5mbG9hdGluZ1tsZW5ndGhdO1xuICAgIH1cbiAgICBjb25zdCBjZW50ZXJUb1JlZmVyZW5jZSA9IGVuZERpZmYgLyAyIC0gc3RhcnREaWZmIC8gMjtcblxuICAgIC8vIElmIHRoZSBwYWRkaW5nIGlzIGxhcmdlIGVub3VnaCB0aGF0IGl0IGNhdXNlcyB0aGUgYXJyb3cgdG8gbm8gbG9uZ2VyIGJlXG4gICAgLy8gY2VudGVyZWQsIG1vZGlmeSB0aGUgcGFkZGluZyBzbyB0aGF0IGl0IGlzIGNlbnRlcmVkLlxuICAgIGNvbnN0IGxhcmdlc3RQb3NzaWJsZVBhZGRpbmcgPSBjbGllbnRTaXplIC8gMiAtIGFycm93RGltZW5zaW9uc1tsZW5ndGhdIC8gMiAtIDE7XG4gICAgY29uc3QgbWluUGFkZGluZyA9IG1pbihwYWRkaW5nT2JqZWN0W21pblByb3BdLCBsYXJnZXN0UG9zc2libGVQYWRkaW5nKTtcbiAgICBjb25zdCBtYXhQYWRkaW5nID0gbWluKHBhZGRpbmdPYmplY3RbbWF4UHJvcF0sIGxhcmdlc3RQb3NzaWJsZVBhZGRpbmcpO1xuXG4gICAgLy8gTWFrZSBzdXJlIHRoZSBhcnJvdyBkb2Vzbid0IG92ZXJmbG93IHRoZSBmbG9hdGluZyBlbGVtZW50IGlmIHRoZSBjZW50ZXJcbiAgICAvLyBwb2ludCBpcyBvdXRzaWRlIHRoZSBmbG9hdGluZyBlbGVtZW50J3MgYm91bmRzLlxuICAgIGNvbnN0IG1pbiQxID0gbWluUGFkZGluZztcbiAgICBjb25zdCBtYXggPSBjbGllbnRTaXplIC0gYXJyb3dEaW1lbnNpb25zW2xlbmd0aF0gLSBtYXhQYWRkaW5nO1xuICAgIGNvbnN0IGNlbnRlciA9IGNsaWVudFNpemUgLyAyIC0gYXJyb3dEaW1lbnNpb25zW2xlbmd0aF0gLyAyICsgY2VudGVyVG9SZWZlcmVuY2U7XG4gICAgY29uc3Qgb2Zmc2V0ID0gY2xhbXAobWluJDEsIGNlbnRlciwgbWF4KTtcblxuICAgIC8vIElmIHRoZSByZWZlcmVuY2UgaXMgc21hbGwgZW5vdWdoIHRoYXQgdGhlIGFycm93J3MgcGFkZGluZyBjYXVzZXMgaXQgdG9cbiAgICAvLyB0byBwb2ludCB0byBub3RoaW5nIGZvciBhbiBhbGlnbmVkIHBsYWNlbWVudCwgYWRqdXN0IHRoZSBvZmZzZXQgb2YgdGhlXG4gICAgLy8gZmxvYXRpbmcgZWxlbWVudCBpdHNlbGYuIFRvIGVuc3VyZSBgc2hpZnQoKWAgY29udGludWVzIHRvIHRha2UgYWN0aW9uLFxuICAgIC8vIGEgc2luZ2xlIHJlc2V0IGlzIHBlcmZvcm1lZCB3aGVuIHRoaXMgaXMgdHJ1ZS5cbiAgICBjb25zdCBzaG91bGRBZGRPZmZzZXQgPSAhbWlkZGxld2FyZURhdGEuYXJyb3cgJiYgZ2V0QWxpZ25tZW50KHBsYWNlbWVudCkgIT0gbnVsbCAmJiBjZW50ZXIgIT09IG9mZnNldCAmJiByZWN0cy5yZWZlcmVuY2VbbGVuZ3RoXSAvIDIgLSAoY2VudGVyIDwgbWluJDEgPyBtaW5QYWRkaW5nIDogbWF4UGFkZGluZykgLSBhcnJvd0RpbWVuc2lvbnNbbGVuZ3RoXSAvIDIgPCAwO1xuICAgIGNvbnN0IGFsaWdubWVudE9mZnNldCA9IHNob3VsZEFkZE9mZnNldCA/IGNlbnRlciA8IG1pbiQxID8gY2VudGVyIC0gbWluJDEgOiBjZW50ZXIgLSBtYXggOiAwO1xuICAgIHJldHVybiB7XG4gICAgICBbYXhpc106IGNvb3Jkc1theGlzXSArIGFsaWdubWVudE9mZnNldCxcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgW2F4aXNdOiBvZmZzZXQsXG4gICAgICAgIGNlbnRlck9mZnNldDogY2VudGVyIC0gb2Zmc2V0IC0gYWxpZ25tZW50T2Zmc2V0LFxuICAgICAgICAuLi4oc2hvdWxkQWRkT2Zmc2V0ICYmIHtcbiAgICAgICAgICBhbGlnbm1lbnRPZmZzZXRcbiAgICAgICAgfSlcbiAgICAgIH0sXG4gICAgICByZXNldDogc2hvdWxkQWRkT2Zmc2V0XG4gICAgfTtcbiAgfVxufSk7XG5cbmZ1bmN0aW9uIGdldFBsYWNlbWVudExpc3QoYWxpZ25tZW50LCBhdXRvQWxpZ25tZW50LCBhbGxvd2VkUGxhY2VtZW50cykge1xuICBjb25zdCBhbGxvd2VkUGxhY2VtZW50c1NvcnRlZEJ5QWxpZ25tZW50ID0gYWxpZ25tZW50ID8gWy4uLmFsbG93ZWRQbGFjZW1lbnRzLmZpbHRlcihwbGFjZW1lbnQgPT4gZ2V0QWxpZ25tZW50KHBsYWNlbWVudCkgPT09IGFsaWdubWVudCksIC4uLmFsbG93ZWRQbGFjZW1lbnRzLmZpbHRlcihwbGFjZW1lbnQgPT4gZ2V0QWxpZ25tZW50KHBsYWNlbWVudCkgIT09IGFsaWdubWVudCldIDogYWxsb3dlZFBsYWNlbWVudHMuZmlsdGVyKHBsYWNlbWVudCA9PiBnZXRTaWRlKHBsYWNlbWVudCkgPT09IHBsYWNlbWVudCk7XG4gIHJldHVybiBhbGxvd2VkUGxhY2VtZW50c1NvcnRlZEJ5QWxpZ25tZW50LmZpbHRlcihwbGFjZW1lbnQgPT4ge1xuICAgIGlmIChhbGlnbm1lbnQpIHtcbiAgICAgIHJldHVybiBnZXRBbGlnbm1lbnQocGxhY2VtZW50KSA9PT0gYWxpZ25tZW50IHx8IChhdXRvQWxpZ25tZW50ID8gZ2V0T3Bwb3NpdGVBbGlnbm1lbnRQbGFjZW1lbnQocGxhY2VtZW50KSAhPT0gcGxhY2VtZW50IDogZmFsc2UpO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSk7XG59XG4vKipcbiAqIE9wdGltaXplcyB0aGUgdmlzaWJpbGl0eSBvZiB0aGUgZmxvYXRpbmcgZWxlbWVudCBieSBjaG9vc2luZyB0aGUgcGxhY2VtZW50XG4gKiB0aGF0IGhhcyB0aGUgbW9zdCBzcGFjZSBhdmFpbGFibGUgYXV0b21hdGljYWxseSwgd2l0aG91dCBuZWVkaW5nIHRvIHNwZWNpZnkgYVxuICogcHJlZmVycmVkIHBsYWNlbWVudC4gQWx0ZXJuYXRpdmUgdG8gYGZsaXBgLlxuICogQHNlZSBodHRwczovL2Zsb2F0aW5nLXVpLmNvbS9kb2NzL2F1dG9QbGFjZW1lbnRcbiAqL1xuY29uc3QgYXV0b1BsYWNlbWVudCA9IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBuYW1lOiAnYXV0b1BsYWNlbWVudCcsXG4gICAgb3B0aW9ucyxcbiAgICBhc3luYyBmbihzdGF0ZSkge1xuICAgICAgdmFyIF9taWRkbGV3YXJlRGF0YSRhdXRvUCwgX21pZGRsZXdhcmVEYXRhJGF1dG9QMiwgX3BsYWNlbWVudHNUaGF0Rml0T25FO1xuICAgICAgY29uc3Qge1xuICAgICAgICByZWN0cyxcbiAgICAgICAgbWlkZGxld2FyZURhdGEsXG4gICAgICAgIHBsYWNlbWVudCxcbiAgICAgICAgcGxhdGZvcm0sXG4gICAgICAgIGVsZW1lbnRzXG4gICAgICB9ID0gc3RhdGU7XG4gICAgICBjb25zdCB7XG4gICAgICAgIGNyb3NzQXhpcyA9IGZhbHNlLFxuICAgICAgICBhbGlnbm1lbnQsXG4gICAgICAgIGFsbG93ZWRQbGFjZW1lbnRzID0gcGxhY2VtZW50cyxcbiAgICAgICAgYXV0b0FsaWdubWVudCA9IHRydWUsXG4gICAgICAgIC4uLmRldGVjdE92ZXJmbG93T3B0aW9uc1xuICAgICAgfSA9IGV2YWx1YXRlKG9wdGlvbnMsIHN0YXRlKTtcbiAgICAgIGNvbnN0IHBsYWNlbWVudHMkMSA9IGFsaWdubWVudCAhPT0gdW5kZWZpbmVkIHx8IGFsbG93ZWRQbGFjZW1lbnRzID09PSBwbGFjZW1lbnRzID8gZ2V0UGxhY2VtZW50TGlzdChhbGlnbm1lbnQgfHwgbnVsbCwgYXV0b0FsaWdubWVudCwgYWxsb3dlZFBsYWNlbWVudHMpIDogYWxsb3dlZFBsYWNlbWVudHM7XG4gICAgICBjb25zdCBvdmVyZmxvdyA9IGF3YWl0IGRldGVjdE92ZXJmbG93KHN0YXRlLCBkZXRlY3RPdmVyZmxvd09wdGlvbnMpO1xuICAgICAgY29uc3QgY3VycmVudEluZGV4ID0gKChfbWlkZGxld2FyZURhdGEkYXV0b1AgPSBtaWRkbGV3YXJlRGF0YS5hdXRvUGxhY2VtZW50KSA9PSBudWxsID8gdm9pZCAwIDogX21pZGRsZXdhcmVEYXRhJGF1dG9QLmluZGV4KSB8fCAwO1xuICAgICAgY29uc3QgY3VycmVudFBsYWNlbWVudCA9IHBsYWNlbWVudHMkMVtjdXJyZW50SW5kZXhdO1xuICAgICAgaWYgKGN1cnJlbnRQbGFjZW1lbnQgPT0gbnVsbCkge1xuICAgICAgICByZXR1cm4ge307XG4gICAgICB9XG4gICAgICBjb25zdCBhbGlnbm1lbnRTaWRlcyA9IGdldEFsaWdubWVudFNpZGVzKGN1cnJlbnRQbGFjZW1lbnQsIHJlY3RzLCBhd2FpdCAocGxhdGZvcm0uaXNSVEwgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmlzUlRMKGVsZW1lbnRzLmZsb2F0aW5nKSkpO1xuXG4gICAgICAvLyBNYWtlIGBjb21wdXRlQ29vcmRzYCBzdGFydCBmcm9tIHRoZSByaWdodCBwbGFjZS5cbiAgICAgIGlmIChwbGFjZW1lbnQgIT09IGN1cnJlbnRQbGFjZW1lbnQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICByZXNldDoge1xuICAgICAgICAgICAgcGxhY2VtZW50OiBwbGFjZW1lbnRzJDFbMF1cbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICBjb25zdCBjdXJyZW50T3ZlcmZsb3dzID0gW292ZXJmbG93W2dldFNpZGUoY3VycmVudFBsYWNlbWVudCldLCBvdmVyZmxvd1thbGlnbm1lbnRTaWRlc1swXV0sIG92ZXJmbG93W2FsaWdubWVudFNpZGVzWzFdXV07XG4gICAgICBjb25zdCBhbGxPdmVyZmxvd3MgPSBbLi4uKCgoX21pZGRsZXdhcmVEYXRhJGF1dG9QMiA9IG1pZGRsZXdhcmVEYXRhLmF1dG9QbGFjZW1lbnQpID09IG51bGwgPyB2b2lkIDAgOiBfbWlkZGxld2FyZURhdGEkYXV0b1AyLm92ZXJmbG93cykgfHwgW10pLCB7XG4gICAgICAgIHBsYWNlbWVudDogY3VycmVudFBsYWNlbWVudCxcbiAgICAgICAgb3ZlcmZsb3dzOiBjdXJyZW50T3ZlcmZsb3dzXG4gICAgICB9XTtcbiAgICAgIGNvbnN0IG5leHRQbGFjZW1lbnQgPSBwbGFjZW1lbnRzJDFbY3VycmVudEluZGV4ICsgMV07XG5cbiAgICAgIC8vIFRoZXJlIGFyZSBtb3JlIHBsYWNlbWVudHMgdG8gY2hlY2suXG4gICAgICBpZiAobmV4dFBsYWNlbWVudCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgIGluZGV4OiBjdXJyZW50SW5kZXggKyAxLFxuICAgICAgICAgICAgb3ZlcmZsb3dzOiBhbGxPdmVyZmxvd3NcbiAgICAgICAgICB9LFxuICAgICAgICAgIHJlc2V0OiB7XG4gICAgICAgICAgICBwbGFjZW1lbnQ6IG5leHRQbGFjZW1lbnRcbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICBjb25zdCBwbGFjZW1lbnRzU29ydGVkQnlNb3N0U3BhY2UgPSBhbGxPdmVyZmxvd3MubWFwKGQgPT4ge1xuICAgICAgICBjb25zdCBhbGlnbm1lbnQgPSBnZXRBbGlnbm1lbnQoZC5wbGFjZW1lbnQpO1xuICAgICAgICByZXR1cm4gW2QucGxhY2VtZW50LCBhbGlnbm1lbnQgJiYgY3Jvc3NBeGlzID9cbiAgICAgICAgLy8gQ2hlY2sgYWxvbmcgdGhlIG1haW5BeGlzIGFuZCBtYWluIGNyb3NzQXhpcyBzaWRlLlxuICAgICAgICBkLm92ZXJmbG93cy5zbGljZSgwLCAyKS5yZWR1Y2UoKGFjYywgdikgPT4gYWNjICsgdiwgMCkgOlxuICAgICAgICAvLyBDaGVjayBvbmx5IHRoZSBtYWluQXhpcy5cbiAgICAgICAgZC5vdmVyZmxvd3NbMF0sIGQub3ZlcmZsb3dzXTtcbiAgICAgIH0pLnNvcnQoKGEsIGIpID0+IGFbMV0gLSBiWzFdKTtcbiAgICAgIGNvbnN0IHBsYWNlbWVudHNUaGF0Rml0T25FYWNoU2lkZSA9IHBsYWNlbWVudHNTb3J0ZWRCeU1vc3RTcGFjZS5maWx0ZXIoZCA9PiBkWzJdLnNsaWNlKDAsXG4gICAgICAvLyBBbGlnbmVkIHBsYWNlbWVudHMgc2hvdWxkIG5vdCBjaGVjayB0aGVpciBvcHBvc2l0ZSBjcm9zc0F4aXNcbiAgICAgIC8vIHNpZGUuXG4gICAgICBnZXRBbGlnbm1lbnQoZFswXSkgPyAyIDogMykuZXZlcnkodiA9PiB2IDw9IDApKTtcbiAgICAgIGNvbnN0IHJlc2V0UGxhY2VtZW50ID0gKChfcGxhY2VtZW50c1RoYXRGaXRPbkUgPSBwbGFjZW1lbnRzVGhhdEZpdE9uRWFjaFNpZGVbMF0pID09IG51bGwgPyB2b2lkIDAgOiBfcGxhY2VtZW50c1RoYXRGaXRPbkVbMF0pIHx8IHBsYWNlbWVudHNTb3J0ZWRCeU1vc3RTcGFjZVswXVswXTtcbiAgICAgIGlmIChyZXNldFBsYWNlbWVudCAhPT0gcGxhY2VtZW50KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgaW5kZXg6IGN1cnJlbnRJbmRleCArIDEsXG4gICAgICAgICAgICBvdmVyZmxvd3M6IGFsbE92ZXJmbG93c1xuICAgICAgICAgIH0sXG4gICAgICAgICAgcmVzZXQ6IHtcbiAgICAgICAgICAgIHBsYWNlbWVudDogcmVzZXRQbGFjZW1lbnRcbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICByZXR1cm4ge307XG4gICAgfVxuICB9O1xufTtcblxuLyoqXG4gKiBPcHRpbWl6ZXMgdGhlIHZpc2liaWxpdHkgb2YgdGhlIGZsb2F0aW5nIGVsZW1lbnQgYnkgZmxpcHBpbmcgdGhlIGBwbGFjZW1lbnRgXG4gKiBpbiBvcmRlciB0byBrZWVwIGl0IGluIHZpZXcgd2hlbiB0aGUgcHJlZmVycmVkIHBsYWNlbWVudChzKSB3aWxsIG92ZXJmbG93IHRoZVxuICogY2xpcHBpbmcgYm91bmRhcnkuIEFsdGVybmF0aXZlIHRvIGBhdXRvUGxhY2VtZW50YC5cbiAqIEBzZWUgaHR0cHM6Ly9mbG9hdGluZy11aS5jb20vZG9jcy9mbGlwXG4gKi9cbmNvbnN0IGZsaXAgPSBmdW5jdGlvbiAob3B0aW9ucykge1xuICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9XG4gIHJldHVybiB7XG4gICAgbmFtZTogJ2ZsaXAnLFxuICAgIG9wdGlvbnMsXG4gICAgYXN5bmMgZm4oc3RhdGUpIHtcbiAgICAgIHZhciBfbWlkZGxld2FyZURhdGEkYXJyb3csIF9taWRkbGV3YXJlRGF0YSRmbGlwO1xuICAgICAgY29uc3Qge1xuICAgICAgICBwbGFjZW1lbnQsXG4gICAgICAgIG1pZGRsZXdhcmVEYXRhLFxuICAgICAgICByZWN0cyxcbiAgICAgICAgaW5pdGlhbFBsYWNlbWVudCxcbiAgICAgICAgcGxhdGZvcm0sXG4gICAgICAgIGVsZW1lbnRzXG4gICAgICB9ID0gc3RhdGU7XG4gICAgICBjb25zdCB7XG4gICAgICAgIG1haW5BeGlzOiBjaGVja01haW5BeGlzID0gdHJ1ZSxcbiAgICAgICAgY3Jvc3NBeGlzOiBjaGVja0Nyb3NzQXhpcyA9IHRydWUsXG4gICAgICAgIGZhbGxiYWNrUGxhY2VtZW50czogc3BlY2lmaWVkRmFsbGJhY2tQbGFjZW1lbnRzLFxuICAgICAgICBmYWxsYmFja1N0cmF0ZWd5ID0gJ2Jlc3RGaXQnLFxuICAgICAgICBmYWxsYmFja0F4aXNTaWRlRGlyZWN0aW9uID0gJ25vbmUnLFxuICAgICAgICBmbGlwQWxpZ25tZW50ID0gdHJ1ZSxcbiAgICAgICAgLi4uZGV0ZWN0T3ZlcmZsb3dPcHRpb25zXG4gICAgICB9ID0gZXZhbHVhdGUob3B0aW9ucywgc3RhdGUpO1xuXG4gICAgICAvLyBJZiBhIHJlc2V0IGJ5IHRoZSBhcnJvdyB3YXMgY2F1c2VkIGR1ZSB0byBhbiBhbGlnbm1lbnQgb2Zmc2V0IGJlaW5nXG4gICAgICAvLyBhZGRlZCwgd2Ugc2hvdWxkIHNraXAgYW55IGxvZ2ljIG5vdyBzaW5jZSBgZmxpcCgpYCBoYXMgYWxyZWFkeSBkb25lIGl0c1xuICAgICAgLy8gd29yay5cbiAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9mbG9hdGluZy11aS9mbG9hdGluZy11aS9pc3N1ZXMvMjU0OSNpc3N1ZWNvbW1lbnQtMTcxOTYwMTY0M1xuICAgICAgaWYgKChfbWlkZGxld2FyZURhdGEkYXJyb3cgPSBtaWRkbGV3YXJlRGF0YS5hcnJvdykgIT0gbnVsbCAmJiBfbWlkZGxld2FyZURhdGEkYXJyb3cuYWxpZ25tZW50T2Zmc2V0KSB7XG4gICAgICAgIHJldHVybiB7fTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IHNpZGUgPSBnZXRTaWRlKHBsYWNlbWVudCk7XG4gICAgICBjb25zdCBpbml0aWFsU2lkZUF4aXMgPSBnZXRTaWRlQXhpcyhpbml0aWFsUGxhY2VtZW50KTtcbiAgICAgIGNvbnN0IGlzQmFzZVBsYWNlbWVudCA9IGdldFNpZGUoaW5pdGlhbFBsYWNlbWVudCkgPT09IGluaXRpYWxQbGFjZW1lbnQ7XG4gICAgICBjb25zdCBydGwgPSBhd2FpdCAocGxhdGZvcm0uaXNSVEwgPT0gbnVsbCA/IHZvaWQgMCA6IHBsYXRmb3JtLmlzUlRMKGVsZW1lbnRzLmZsb2F0aW5nKSk7XG4gICAgICBjb25zdCBmYWxsYmFja1BsYWNlbWVudHMgPSBzcGVjaWZpZWRGYWxsYmFja1BsYWNlbWVudHMgfHwgKGlzQmFzZVBsYWNlbWVudCB8fCAhZmxpcEFsaWdubWVudCA/IFtnZXRPcHBvc2l0ZVBsYWNlbWVudChpbml0aWFsUGxhY2VtZW50KV0gOiBnZXRFeHBhbmRlZFBsYWNlbWVudHMoaW5pdGlhbFBsYWNlbWVudCkpO1xuICAgICAgY29uc3QgaGFzRmFsbGJhY2tBeGlzU2lkZURpcmVjdGlvbiA9IGZhbGxiYWNrQXhpc1NpZGVEaXJlY3Rpb24gIT09ICdub25lJztcbiAgICAgIGlmICghc3BlY2lmaWVkRmFsbGJhY2tQbGFjZW1lbnRzICYmIGhhc0ZhbGxiYWNrQXhpc1NpZGVEaXJlY3Rpb24pIHtcbiAgICAgICAgZmFsbGJhY2tQbGFjZW1lbnRzLnB1c2goLi4uZ2V0T3Bwb3NpdGVBeGlzUGxhY2VtZW50cyhpbml0aWFsUGxhY2VtZW50LCBmbGlwQWxpZ25tZW50LCBmYWxsYmFja0F4aXNTaWRlRGlyZWN0aW9uLCBydGwpKTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IHBsYWNlbWVudHMgPSBbaW5pdGlhbFBsYWNlbWVudCwgLi4uZmFsbGJhY2tQbGFjZW1lbnRzXTtcbiAgICAgIGNvbnN0IG92ZXJmbG93ID0gYXdhaXQgZGV0ZWN0T3ZlcmZsb3coc3RhdGUsIGRldGVjdE92ZXJmbG93T3B0aW9ucyk7XG4gICAgICBjb25zdCBvdmVyZmxvd3MgPSBbXTtcbiAgICAgIGxldCBvdmVyZmxvd3NEYXRhID0gKChfbWlkZGxld2FyZURhdGEkZmxpcCA9IG1pZGRsZXdhcmVEYXRhLmZsaXApID09IG51bGwgPyB2b2lkIDAgOiBfbWlkZGxld2FyZURhdGEkZmxpcC5vdmVyZmxvd3MpIHx8IFtdO1xuICAgICAgaWYgKGNoZWNrTWFpbkF4aXMpIHtcbiAgICAgICAgb3ZlcmZsb3dzLnB1c2gob3ZlcmZsb3dbc2lkZV0pO1xuICAgICAgfVxuICAgICAgaWYgKGNoZWNrQ3Jvc3NBeGlzKSB7XG4gICAgICAgIGNvbnN0IHNpZGVzID0gZ2V0QWxpZ25tZW50U2lkZXMocGxhY2VtZW50LCByZWN0cywgcnRsKTtcbiAgICAgICAgb3ZlcmZsb3dzLnB1c2gob3ZlcmZsb3dbc2lkZXNbMF1dLCBvdmVyZmxvd1tzaWRlc1sxXV0pO1xuICAgICAgfVxuICAgICAgb3ZlcmZsb3dzRGF0YSA9IFsuLi5vdmVyZmxvd3NEYXRhLCB7XG4gICAgICAgIHBsYWNlbWVudCxcbiAgICAgICAgb3ZlcmZsb3dzXG4gICAgICB9XTtcblxuICAgICAgLy8gT25lIG9yIG1vcmUgc2lkZXMgaXMgb3ZlcmZsb3dpbmcuXG4gICAgICBpZiAoIW92ZXJmbG93cy5ldmVyeShzaWRlID0+IHNpZGUgPD0gMCkpIHtcbiAgICAgICAgdmFyIF9taWRkbGV3YXJlRGF0YSRmbGlwMiwgX292ZXJmbG93c0RhdGEkZmlsdGVyO1xuICAgICAgICBjb25zdCBuZXh0SW5kZXggPSAoKChfbWlkZGxld2FyZURhdGEkZmxpcDIgPSBtaWRkbGV3YXJlRGF0YS5mbGlwKSA9PSBudWxsID8gdm9pZCAwIDogX21pZGRsZXdhcmVEYXRhJGZsaXAyLmluZGV4KSB8fCAwKSArIDE7XG4gICAgICAgIGNvbnN0IG5leHRQbGFjZW1lbnQgPSBwbGFjZW1lbnRzW25leHRJbmRleF07XG4gICAgICAgIGlmIChuZXh0UGxhY2VtZW50KSB7XG4gICAgICAgICAgdmFyIF9vdmVyZmxvd3NEYXRhJDtcbiAgICAgICAgICBjb25zdCBpZ25vcmVDcm9zc0F4aXNPdmVyZmxvdyA9IGNoZWNrQ3Jvc3NBeGlzID09PSAnYWxpZ25tZW50JyA/IGluaXRpYWxTaWRlQXhpcyAhPT0gZ2V0U2lkZUF4aXMobmV4dFBsYWNlbWVudCkgOiBmYWxzZTtcbiAgICAgICAgICBjb25zdCBoYXNJbml0aWFsTWFpbkF4aXNPdmVyZmxvdyA9ICgoX292ZXJmbG93c0RhdGEkID0gb3ZlcmZsb3dzRGF0YVswXSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9vdmVyZmxvd3NEYXRhJC5vdmVyZmxvd3NbMF0pID4gMDtcbiAgICAgICAgICBpZiAoIWlnbm9yZUNyb3NzQXhpc092ZXJmbG93IHx8IGhhc0luaXRpYWxNYWluQXhpc092ZXJmbG93KSB7XG4gICAgICAgICAgICAvLyBUcnkgbmV4dCBwbGFjZW1lbnQgYW5kIHJlLXJ1biB0aGUgbGlmZWN5Y2xlLlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICAgIGluZGV4OiBuZXh0SW5kZXgsXG4gICAgICAgICAgICAgICAgb3ZlcmZsb3dzOiBvdmVyZmxvd3NEYXRhXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHJlc2V0OiB7XG4gICAgICAgICAgICAgICAgcGxhY2VtZW50OiBuZXh0UGxhY2VtZW50XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gRmlyc3QsIGZpbmQgdGhlIGNhbmRpZGF0ZXMgdGhhdCBmaXQgb24gdGhlIG1haW5BeGlzIHNpZGUgb2Ygb3ZlcmZsb3csXG4gICAgICAgIC8vIHRoZW4gZmluZCB0aGUgcGxhY2VtZW50IHRoYXQgZml0cyB0aGUgYmVzdCBvbiB0aGUgbWFpbiBjcm9zc0F4aXMgc2lkZS5cbiAgICAgICAgbGV0IHJlc2V0UGxhY2VtZW50ID0gKF9vdmVyZmxvd3NEYXRhJGZpbHRlciA9IG92ZXJmbG93c0RhdGEuZmlsdGVyKGQgPT4gZC5vdmVyZmxvd3NbMF0gPD0gMCkuc29ydCgoYSwgYikgPT4gYS5vdmVyZmxvd3NbMV0gLSBiLm92ZXJmbG93c1sxXSlbMF0pID09IG51bGwgPyB2b2lkIDAgOiBfb3ZlcmZsb3dzRGF0YSRmaWx0ZXIucGxhY2VtZW50O1xuXG4gICAgICAgIC8vIE90aGVyd2lzZSBmYWxsYmFjay5cbiAgICAgICAgaWYgKCFyZXNldFBsYWNlbWVudCkge1xuICAgICAgICAgIHN3aXRjaCAoZmFsbGJhY2tTdHJhdGVneSkge1xuICAgICAgICAgICAgY2FzZSAnYmVzdEZpdCc6XG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB2YXIgX292ZXJmbG93c0RhdGEkZmlsdGVyMjtcbiAgICAgICAgICAgICAgICBjb25zdCBwbGFjZW1lbnQgPSAoX292ZXJmbG93c0RhdGEkZmlsdGVyMiA9IG92ZXJmbG93c0RhdGEuZmlsdGVyKGQgPT4ge1xuICAgICAgICAgICAgICAgICAgaWYgKGhhc0ZhbGxiYWNrQXhpc1NpZGVEaXJlY3Rpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY3VycmVudFNpZGVBeGlzID0gZ2V0U2lkZUF4aXMoZC5wbGFjZW1lbnQpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY3VycmVudFNpZGVBeGlzID09PSBpbml0aWFsU2lkZUF4aXMgfHxcbiAgICAgICAgICAgICAgICAgICAgLy8gQ3JlYXRlIGEgYmlhcyB0byB0aGUgYHlgIHNpZGUgYXhpcyBkdWUgdG8gaG9yaXpvbnRhbFxuICAgICAgICAgICAgICAgICAgICAvLyByZWFkaW5nIGRpcmVjdGlvbnMgZmF2b3JpbmcgZ3JlYXRlciB3aWR0aC5cbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFNpZGVBeGlzID09PSAneSc7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9KS5tYXAoZCA9PiBbZC5wbGFjZW1lbnQsIGQub3ZlcmZsb3dzLmZpbHRlcihvdmVyZmxvdyA9PiBvdmVyZmxvdyA+IDApLnJlZHVjZSgoYWNjLCBvdmVyZmxvdykgPT4gYWNjICsgb3ZlcmZsb3csIDApXSkuc29ydCgoYSwgYikgPT4gYVsxXSAtIGJbMV0pWzBdKSA9PSBudWxsID8gdm9pZCAwIDogX292ZXJmbG93c0RhdGEkZmlsdGVyMlswXTtcbiAgICAgICAgICAgICAgICBpZiAocGxhY2VtZW50KSB7XG4gICAgICAgICAgICAgICAgICByZXNldFBsYWNlbWVudCA9IHBsYWNlbWVudDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhc2UgJ2luaXRpYWxQbGFjZW1lbnQnOlxuICAgICAgICAgICAgICByZXNldFBsYWNlbWVudCA9IGluaXRpYWxQbGFjZW1lbnQ7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAocGxhY2VtZW50ICE9PSByZXNldFBsYWNlbWVudCkge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICByZXNldDoge1xuICAgICAgICAgICAgICBwbGFjZW1lbnQ6IHJlc2V0UGxhY2VtZW50XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgfTtcbn07XG5cbmZ1bmN0aW9uIGdldFNpZGVPZmZzZXRzKG92ZXJmbG93LCByZWN0KSB7XG4gIHJldHVybiB7XG4gICAgdG9wOiBvdmVyZmxvdy50b3AgLSByZWN0LmhlaWdodCxcbiAgICByaWdodDogb3ZlcmZsb3cucmlnaHQgLSByZWN0LndpZHRoLFxuICAgIGJvdHRvbTogb3ZlcmZsb3cuYm90dG9tIC0gcmVjdC5oZWlnaHQsXG4gICAgbGVmdDogb3ZlcmZsb3cubGVmdCAtIHJlY3Qud2lkdGhcbiAgfTtcbn1cbmZ1bmN0aW9uIGlzQW55U2lkZUZ1bGx5Q2xpcHBlZChvdmVyZmxvdykge1xuICByZXR1cm4gc2lkZXMuc29tZShzaWRlID0+IG92ZXJmbG93W3NpZGVdID49IDApO1xufVxuLyoqXG4gKiBQcm92aWRlcyBkYXRhIHRvIGhpZGUgdGhlIGZsb2F0aW5nIGVsZW1lbnQgaW4gYXBwbGljYWJsZSBzaXR1YXRpb25zLCBzdWNoIGFzXG4gKiB3aGVuIGl0IGlzIG5vdCBpbiB0aGUgc2FtZSBjbGlwcGluZyBjb250ZXh0IGFzIHRoZSByZWZlcmVuY2UgZWxlbWVudC5cbiAqIEBzZWUgaHR0cHM6Ly9mbG9hdGluZy11aS5jb20vZG9jcy9oaWRlXG4gKi9cbmNvbnN0IGhpZGUgPSBmdW5jdGlvbiAob3B0aW9ucykge1xuICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9XG4gIHJldHVybiB7XG4gICAgbmFtZTogJ2hpZGUnLFxuICAgIG9wdGlvbnMsXG4gICAgYXN5bmMgZm4oc3RhdGUpIHtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgcmVjdHNcbiAgICAgIH0gPSBzdGF0ZTtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgc3RyYXRlZ3kgPSAncmVmZXJlbmNlSGlkZGVuJyxcbiAgICAgICAgLi4uZGV0ZWN0T3ZlcmZsb3dPcHRpb25zXG4gICAgICB9ID0gZXZhbHVhdGUob3B0aW9ucywgc3RhdGUpO1xuICAgICAgc3dpdGNoIChzdHJhdGVneSkge1xuICAgICAgICBjYXNlICdyZWZlcmVuY2VIaWRkZW4nOlxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGNvbnN0IG92ZXJmbG93ID0gYXdhaXQgZGV0ZWN0T3ZlcmZsb3coc3RhdGUsIHtcbiAgICAgICAgICAgICAgLi4uZGV0ZWN0T3ZlcmZsb3dPcHRpb25zLFxuICAgICAgICAgICAgICBlbGVtZW50Q29udGV4dDogJ3JlZmVyZW5jZSdcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY29uc3Qgb2Zmc2V0cyA9IGdldFNpZGVPZmZzZXRzKG92ZXJmbG93LCByZWN0cy5yZWZlcmVuY2UpO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICAgIHJlZmVyZW5jZUhpZGRlbk9mZnNldHM6IG9mZnNldHMsXG4gICAgICAgICAgICAgICAgcmVmZXJlbmNlSGlkZGVuOiBpc0FueVNpZGVGdWxseUNsaXBwZWQob2Zmc2V0cylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICB9XG4gICAgICAgIGNhc2UgJ2VzY2FwZWQnOlxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGNvbnN0IG92ZXJmbG93ID0gYXdhaXQgZGV0ZWN0T3ZlcmZsb3coc3RhdGUsIHtcbiAgICAgICAgICAgICAgLi4uZGV0ZWN0T3ZlcmZsb3dPcHRpb25zLFxuICAgICAgICAgICAgICBhbHRCb3VuZGFyeTogdHJ1ZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zdCBvZmZzZXRzID0gZ2V0U2lkZU9mZnNldHMob3ZlcmZsb3csIHJlY3RzLmZsb2F0aW5nKTtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgICAgICBlc2NhcGVkT2Zmc2V0czogb2Zmc2V0cyxcbiAgICAgICAgICAgICAgICBlc2NhcGVkOiBpc0FueVNpZGVGdWxseUNsaXBwZWQob2Zmc2V0cylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICB9XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAge1xuICAgICAgICAgICAgcmV0dXJuIHt9O1xuICAgICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH07XG59O1xuXG5mdW5jdGlvbiBnZXRCb3VuZGluZ1JlY3QocmVjdHMpIHtcbiAgY29uc3QgbWluWCA9IG1pbiguLi5yZWN0cy5tYXAocmVjdCA9PiByZWN0LmxlZnQpKTtcbiAgY29uc3QgbWluWSA9IG1pbiguLi5yZWN0cy5tYXAocmVjdCA9PiByZWN0LnRvcCkpO1xuICBjb25zdCBtYXhYID0gbWF4KC4uLnJlY3RzLm1hcChyZWN0ID0+IHJlY3QucmlnaHQpKTtcbiAgY29uc3QgbWF4WSA9IG1heCguLi5yZWN0cy5tYXAocmVjdCA9PiByZWN0LmJvdHRvbSkpO1xuICByZXR1cm4ge1xuICAgIHg6IG1pblgsXG4gICAgeTogbWluWSxcbiAgICB3aWR0aDogbWF4WCAtIG1pblgsXG4gICAgaGVpZ2h0OiBtYXhZIC0gbWluWVxuICB9O1xufVxuZnVuY3Rpb24gZ2V0UmVjdHNCeUxpbmUocmVjdHMpIHtcbiAgY29uc3Qgc29ydGVkUmVjdHMgPSByZWN0cy5zbGljZSgpLnNvcnQoKGEsIGIpID0+IGEueSAtIGIueSk7XG4gIGNvbnN0IGdyb3VwcyA9IFtdO1xuICBsZXQgcHJldlJlY3QgPSBudWxsO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHNvcnRlZFJlY3RzLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgcmVjdCA9IHNvcnRlZFJlY3RzW2ldO1xuICAgIGlmICghcHJldlJlY3QgfHwgcmVjdC55IC0gcHJldlJlY3QueSA+IHByZXZSZWN0LmhlaWdodCAvIDIpIHtcbiAgICAgIGdyb3Vwcy5wdXNoKFtyZWN0XSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGdyb3Vwc1tncm91cHMubGVuZ3RoIC0gMV0ucHVzaChyZWN0KTtcbiAgICB9XG4gICAgcHJldlJlY3QgPSByZWN0O1xuICB9XG4gIHJldHVybiBncm91cHMubWFwKHJlY3QgPT4gcmVjdFRvQ2xpZW50UmVjdChnZXRCb3VuZGluZ1JlY3QocmVjdCkpKTtcbn1cbi8qKlxuICogUHJvdmlkZXMgaW1wcm92ZWQgcG9zaXRpb25pbmcgZm9yIGlubGluZSByZWZlcmVuY2UgZWxlbWVudHMgdGhhdCBjYW4gc3BhblxuICogb3ZlciBtdWx0aXBsZSBsaW5lcywgc3VjaCBhcyBoeXBlcmxpbmtzIG9yIHJhbmdlIHNlbGVjdGlvbnMuXG4gKiBAc2VlIGh0dHBzOi8vZmxvYXRpbmctdWkuY29tL2RvY3MvaW5saW5lXG4gKi9cbmNvbnN0IGlubGluZSA9IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBuYW1lOiAnaW5saW5lJyxcbiAgICBvcHRpb25zLFxuICAgIGFzeW5jIGZuKHN0YXRlKSB7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHBsYWNlbWVudCxcbiAgICAgICAgZWxlbWVudHMsXG4gICAgICAgIHJlY3RzLFxuICAgICAgICBwbGF0Zm9ybSxcbiAgICAgICAgc3RyYXRlZ3lcbiAgICAgIH0gPSBzdGF0ZTtcbiAgICAgIC8vIEEgTW91c2VFdmVudCdzIGNsaWVudHtYLFl9IGNvb3JkcyBjYW4gYmUgdXAgdG8gMiBwaXhlbHMgb2ZmIGFcbiAgICAgIC8vIENsaWVudFJlY3QncyBib3VuZHMsIGRlc3BpdGUgdGhlIGV2ZW50IGxpc3RlbmVyIGJlaW5nIHRyaWdnZXJlZC4gQVxuICAgICAgLy8gcGFkZGluZyBvZiAyIHNlZW1zIHRvIGhhbmRsZSB0aGlzIGlzc3VlLlxuICAgICAgY29uc3Qge1xuICAgICAgICBwYWRkaW5nID0gMixcbiAgICAgICAgeCxcbiAgICAgICAgeVxuICAgICAgfSA9IGV2YWx1YXRlKG9wdGlvbnMsIHN0YXRlKTtcbiAgICAgIGNvbnN0IG5hdGl2ZUNsaWVudFJlY3RzID0gQXJyYXkuZnJvbSgoYXdhaXQgKHBsYXRmb3JtLmdldENsaWVudFJlY3RzID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5nZXRDbGllbnRSZWN0cyhlbGVtZW50cy5yZWZlcmVuY2UpKSkgfHwgW10pO1xuICAgICAgY29uc3QgY2xpZW50UmVjdHMgPSBnZXRSZWN0c0J5TGluZShuYXRpdmVDbGllbnRSZWN0cyk7XG4gICAgICBjb25zdCBmYWxsYmFjayA9IHJlY3RUb0NsaWVudFJlY3QoZ2V0Qm91bmRpbmdSZWN0KG5hdGl2ZUNsaWVudFJlY3RzKSk7XG4gICAgICBjb25zdCBwYWRkaW5nT2JqZWN0ID0gZ2V0UGFkZGluZ09iamVjdChwYWRkaW5nKTtcbiAgICAgIGZ1bmN0aW9uIGdldEJvdW5kaW5nQ2xpZW50UmVjdCgpIHtcbiAgICAgICAgLy8gVGhlcmUgYXJlIHR3byByZWN0cyBhbmQgdGhleSBhcmUgZGlzam9pbmVkLlxuICAgICAgICBpZiAoY2xpZW50UmVjdHMubGVuZ3RoID09PSAyICYmIGNsaWVudFJlY3RzWzBdLmxlZnQgPiBjbGllbnRSZWN0c1sxXS5yaWdodCAmJiB4ICE9IG51bGwgJiYgeSAhPSBudWxsKSB7XG4gICAgICAgICAgLy8gRmluZCB0aGUgZmlyc3QgcmVjdCBpbiB3aGljaCB0aGUgcG9pbnQgaXMgZnVsbHkgaW5zaWRlLlxuICAgICAgICAgIHJldHVybiBjbGllbnRSZWN0cy5maW5kKHJlY3QgPT4geCA+IHJlY3QubGVmdCAtIHBhZGRpbmdPYmplY3QubGVmdCAmJiB4IDwgcmVjdC5yaWdodCArIHBhZGRpbmdPYmplY3QucmlnaHQgJiYgeSA+IHJlY3QudG9wIC0gcGFkZGluZ09iamVjdC50b3AgJiYgeSA8IHJlY3QuYm90dG9tICsgcGFkZGluZ09iamVjdC5ib3R0b20pIHx8IGZhbGxiYWNrO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gVGhlcmUgYXJlIDIgb3IgbW9yZSBjb25uZWN0ZWQgcmVjdHMuXG4gICAgICAgIGlmIChjbGllbnRSZWN0cy5sZW5ndGggPj0gMikge1xuICAgICAgICAgIGlmIChnZXRTaWRlQXhpcyhwbGFjZW1lbnQpID09PSAneScpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpcnN0UmVjdCA9IGNsaWVudFJlY3RzWzBdO1xuICAgICAgICAgICAgY29uc3QgbGFzdFJlY3QgPSBjbGllbnRSZWN0c1tjbGllbnRSZWN0cy5sZW5ndGggLSAxXTtcbiAgICAgICAgICAgIGNvbnN0IGlzVG9wID0gZ2V0U2lkZShwbGFjZW1lbnQpID09PSAndG9wJztcbiAgICAgICAgICAgIGNvbnN0IHRvcCA9IGZpcnN0UmVjdC50b3A7XG4gICAgICAgICAgICBjb25zdCBib3R0b20gPSBsYXN0UmVjdC5ib3R0b207XG4gICAgICAgICAgICBjb25zdCBsZWZ0ID0gaXNUb3AgPyBmaXJzdFJlY3QubGVmdCA6IGxhc3RSZWN0LmxlZnQ7XG4gICAgICAgICAgICBjb25zdCByaWdodCA9IGlzVG9wID8gZmlyc3RSZWN0LnJpZ2h0IDogbGFzdFJlY3QucmlnaHQ7XG4gICAgICAgICAgICBjb25zdCB3aWR0aCA9IHJpZ2h0IC0gbGVmdDtcbiAgICAgICAgICAgIGNvbnN0IGhlaWdodCA9IGJvdHRvbSAtIHRvcDtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgIHRvcCxcbiAgICAgICAgICAgICAgYm90dG9tLFxuICAgICAgICAgICAgICBsZWZ0LFxuICAgICAgICAgICAgICByaWdodCxcbiAgICAgICAgICAgICAgd2lkdGgsXG4gICAgICAgICAgICAgIGhlaWdodCxcbiAgICAgICAgICAgICAgeDogbGVmdCxcbiAgICAgICAgICAgICAgeTogdG9wXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH1cbiAgICAgICAgICBjb25zdCBpc0xlZnRTaWRlID0gZ2V0U2lkZShwbGFjZW1lbnQpID09PSAnbGVmdCc7XG4gICAgICAgICAgY29uc3QgbWF4UmlnaHQgPSBtYXgoLi4uY2xpZW50UmVjdHMubWFwKHJlY3QgPT4gcmVjdC5yaWdodCkpO1xuICAgICAgICAgIGNvbnN0IG1pbkxlZnQgPSBtaW4oLi4uY2xpZW50UmVjdHMubWFwKHJlY3QgPT4gcmVjdC5sZWZ0KSk7XG4gICAgICAgICAgY29uc3QgbWVhc3VyZVJlY3RzID0gY2xpZW50UmVjdHMuZmlsdGVyKHJlY3QgPT4gaXNMZWZ0U2lkZSA/IHJlY3QubGVmdCA9PT0gbWluTGVmdCA6IHJlY3QucmlnaHQgPT09IG1heFJpZ2h0KTtcbiAgICAgICAgICBjb25zdCB0b3AgPSBtZWFzdXJlUmVjdHNbMF0udG9wO1xuICAgICAgICAgIGNvbnN0IGJvdHRvbSA9IG1lYXN1cmVSZWN0c1ttZWFzdXJlUmVjdHMubGVuZ3RoIC0gMV0uYm90dG9tO1xuICAgICAgICAgIGNvbnN0IGxlZnQgPSBtaW5MZWZ0O1xuICAgICAgICAgIGNvbnN0IHJpZ2h0ID0gbWF4UmlnaHQ7XG4gICAgICAgICAgY29uc3Qgd2lkdGggPSByaWdodCAtIGxlZnQ7XG4gICAgICAgICAgY29uc3QgaGVpZ2h0ID0gYm90dG9tIC0gdG9wO1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0b3AsXG4gICAgICAgICAgICBib3R0b20sXG4gICAgICAgICAgICBsZWZ0LFxuICAgICAgICAgICAgcmlnaHQsXG4gICAgICAgICAgICB3aWR0aCxcbiAgICAgICAgICAgIGhlaWdodCxcbiAgICAgICAgICAgIHg6IGxlZnQsXG4gICAgICAgICAgICB5OiB0b3BcbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBmYWxsYmFjaztcbiAgICAgIH1cbiAgICAgIGNvbnN0IHJlc2V0UmVjdHMgPSBhd2FpdCBwbGF0Zm9ybS5nZXRFbGVtZW50UmVjdHMoe1xuICAgICAgICByZWZlcmVuY2U6IHtcbiAgICAgICAgICBnZXRCb3VuZGluZ0NsaWVudFJlY3RcbiAgICAgICAgfSxcbiAgICAgICAgZmxvYXRpbmc6IGVsZW1lbnRzLmZsb2F0aW5nLFxuICAgICAgICBzdHJhdGVneVxuICAgICAgfSk7XG4gICAgICBpZiAocmVjdHMucmVmZXJlbmNlLnggIT09IHJlc2V0UmVjdHMucmVmZXJlbmNlLnggfHwgcmVjdHMucmVmZXJlbmNlLnkgIT09IHJlc2V0UmVjdHMucmVmZXJlbmNlLnkgfHwgcmVjdHMucmVmZXJlbmNlLndpZHRoICE9PSByZXNldFJlY3RzLnJlZmVyZW5jZS53aWR0aCB8fCByZWN0cy5yZWZlcmVuY2UuaGVpZ2h0ICE9PSByZXNldFJlY3RzLnJlZmVyZW5jZS5oZWlnaHQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICByZXNldDoge1xuICAgICAgICAgICAgcmVjdHM6IHJlc2V0UmVjdHNcbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgICByZXR1cm4ge307XG4gICAgfVxuICB9O1xufTtcblxuLy8gRm9yIHR5cGUgYmFja3dhcmRzLWNvbXBhdGliaWxpdHksIHRoZSBgT2Zmc2V0T3B0aW9uc2AgdHlwZSB3YXMgYWxzb1xuLy8gRGVyaXZhYmxlLlxuXG5hc3luYyBmdW5jdGlvbiBjb252ZXJ0VmFsdWVUb0Nvb3JkcyhzdGF0ZSwgb3B0aW9ucykge1xuICBjb25zdCB7XG4gICAgcGxhY2VtZW50LFxuICAgIHBsYXRmb3JtLFxuICAgIGVsZW1lbnRzXG4gIH0gPSBzdGF0ZTtcbiAgY29uc3QgcnRsID0gYXdhaXQgKHBsYXRmb3JtLmlzUlRMID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5pc1JUTChlbGVtZW50cy5mbG9hdGluZykpO1xuICBjb25zdCBzaWRlID0gZ2V0U2lkZShwbGFjZW1lbnQpO1xuICBjb25zdCBhbGlnbm1lbnQgPSBnZXRBbGlnbm1lbnQocGxhY2VtZW50KTtcbiAgY29uc3QgaXNWZXJ0aWNhbCA9IGdldFNpZGVBeGlzKHBsYWNlbWVudCkgPT09ICd5JztcbiAgY29uc3QgbWFpbkF4aXNNdWx0aSA9IFsnbGVmdCcsICd0b3AnXS5pbmNsdWRlcyhzaWRlKSA/IC0xIDogMTtcbiAgY29uc3QgY3Jvc3NBeGlzTXVsdGkgPSBydGwgJiYgaXNWZXJ0aWNhbCA/IC0xIDogMTtcbiAgY29uc3QgcmF3VmFsdWUgPSBldmFsdWF0ZShvcHRpb25zLCBzdGF0ZSk7XG5cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHByZWZlci1jb25zdFxuICBsZXQge1xuICAgIG1haW5BeGlzLFxuICAgIGNyb3NzQXhpcyxcbiAgICBhbGlnbm1lbnRBeGlzXG4gIH0gPSB0eXBlb2YgcmF3VmFsdWUgPT09ICdudW1iZXInID8ge1xuICAgIG1haW5BeGlzOiByYXdWYWx1ZSxcbiAgICBjcm9zc0F4aXM6IDAsXG4gICAgYWxpZ25tZW50QXhpczogbnVsbFxuICB9IDoge1xuICAgIG1haW5BeGlzOiByYXdWYWx1ZS5tYWluQXhpcyB8fCAwLFxuICAgIGNyb3NzQXhpczogcmF3VmFsdWUuY3Jvc3NBeGlzIHx8IDAsXG4gICAgYWxpZ25tZW50QXhpczogcmF3VmFsdWUuYWxpZ25tZW50QXhpc1xuICB9O1xuICBpZiAoYWxpZ25tZW50ICYmIHR5cGVvZiBhbGlnbm1lbnRBeGlzID09PSAnbnVtYmVyJykge1xuICAgIGNyb3NzQXhpcyA9IGFsaWdubWVudCA9PT0gJ2VuZCcgPyBhbGlnbm1lbnRBeGlzICogLTEgOiBhbGlnbm1lbnRBeGlzO1xuICB9XG4gIHJldHVybiBpc1ZlcnRpY2FsID8ge1xuICAgIHg6IGNyb3NzQXhpcyAqIGNyb3NzQXhpc011bHRpLFxuICAgIHk6IG1haW5BeGlzICogbWFpbkF4aXNNdWx0aVxuICB9IDoge1xuICAgIHg6IG1haW5BeGlzICogbWFpbkF4aXNNdWx0aSxcbiAgICB5OiBjcm9zc0F4aXMgKiBjcm9zc0F4aXNNdWx0aVxuICB9O1xufVxuXG4vKipcbiAqIE1vZGlmaWVzIHRoZSBwbGFjZW1lbnQgYnkgdHJhbnNsYXRpbmcgdGhlIGZsb2F0aW5nIGVsZW1lbnQgYWxvbmcgdGhlXG4gKiBzcGVjaWZpZWQgYXhlcy5cbiAqIEEgbnVtYmVyIChzaG9ydGhhbmQgZm9yIGBtYWluQXhpc2Agb3IgZGlzdGFuY2UpLCBvciBhbiBheGVzIGNvbmZpZ3VyYXRpb25cbiAqIG9iamVjdCBtYXkgYmUgcGFzc2VkLlxuICogQHNlZSBodHRwczovL2Zsb2F0aW5nLXVpLmNvbS9kb2NzL29mZnNldFxuICovXG5jb25zdCBvZmZzZXQgPSBmdW5jdGlvbiAob3B0aW9ucykge1xuICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgb3B0aW9ucyA9IDA7XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBuYW1lOiAnb2Zmc2V0JyxcbiAgICBvcHRpb25zLFxuICAgIGFzeW5jIGZuKHN0YXRlKSB7XG4gICAgICB2YXIgX21pZGRsZXdhcmVEYXRhJG9mZnNlLCBfbWlkZGxld2FyZURhdGEkYXJyb3c7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHgsXG4gICAgICAgIHksXG4gICAgICAgIHBsYWNlbWVudCxcbiAgICAgICAgbWlkZGxld2FyZURhdGFcbiAgICAgIH0gPSBzdGF0ZTtcbiAgICAgIGNvbnN0IGRpZmZDb29yZHMgPSBhd2FpdCBjb252ZXJ0VmFsdWVUb0Nvb3JkcyhzdGF0ZSwgb3B0aW9ucyk7XG5cbiAgICAgIC8vIElmIHRoZSBwbGFjZW1lbnQgaXMgdGhlIHNhbWUgYW5kIHRoZSBhcnJvdyBjYXVzZWQgYW4gYWxpZ25tZW50IG9mZnNldFxuICAgICAgLy8gdGhlbiB3ZSBkb24ndCBuZWVkIHRvIGNoYW5nZSB0aGUgcG9zaXRpb25pbmcgY29vcmRpbmF0ZXMuXG4gICAgICBpZiAocGxhY2VtZW50ID09PSAoKF9taWRkbGV3YXJlRGF0YSRvZmZzZSA9IG1pZGRsZXdhcmVEYXRhLm9mZnNldCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9taWRkbGV3YXJlRGF0YSRvZmZzZS5wbGFjZW1lbnQpICYmIChfbWlkZGxld2FyZURhdGEkYXJyb3cgPSBtaWRkbGV3YXJlRGF0YS5hcnJvdykgIT0gbnVsbCAmJiBfbWlkZGxld2FyZURhdGEkYXJyb3cuYWxpZ25tZW50T2Zmc2V0KSB7XG4gICAgICAgIHJldHVybiB7fTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHg6IHggKyBkaWZmQ29vcmRzLngsXG4gICAgICAgIHk6IHkgKyBkaWZmQ29vcmRzLnksXG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAuLi5kaWZmQ29vcmRzLFxuICAgICAgICAgIHBsYWNlbWVudFxuICAgICAgICB9XG4gICAgICB9O1xuICAgIH1cbiAgfTtcbn07XG5cbi8qKlxuICogT3B0aW1pemVzIHRoZSB2aXNpYmlsaXR5IG9mIHRoZSBmbG9hdGluZyBlbGVtZW50IGJ5IHNoaWZ0aW5nIGl0IGluIG9yZGVyIHRvXG4gKiBrZWVwIGl0IGluIHZpZXcgd2hlbiBpdCB3aWxsIG92ZXJmbG93IHRoZSBjbGlwcGluZyBib3VuZGFyeS5cbiAqIEBzZWUgaHR0cHM6Ly9mbG9hdGluZy11aS5jb20vZG9jcy9zaGlmdFxuICovXG5jb25zdCBzaGlmdCA9IGZ1bmN0aW9uIChvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICBvcHRpb25zID0ge307XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBuYW1lOiAnc2hpZnQnLFxuICAgIG9wdGlvbnMsXG4gICAgYXN5bmMgZm4oc3RhdGUpIHtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgeCxcbiAgICAgICAgeSxcbiAgICAgICAgcGxhY2VtZW50XG4gICAgICB9ID0gc3RhdGU7XG4gICAgICBjb25zdCB7XG4gICAgICAgIG1haW5BeGlzOiBjaGVja01haW5BeGlzID0gdHJ1ZSxcbiAgICAgICAgY3Jvc3NBeGlzOiBjaGVja0Nyb3NzQXhpcyA9IGZhbHNlLFxuICAgICAgICBsaW1pdGVyID0ge1xuICAgICAgICAgIGZuOiBfcmVmID0+IHtcbiAgICAgICAgICAgIGxldCB7XG4gICAgICAgICAgICAgIHgsXG4gICAgICAgICAgICAgIHlcbiAgICAgICAgICAgIH0gPSBfcmVmO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgeCxcbiAgICAgICAgICAgICAgeVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIC4uLmRldGVjdE92ZXJmbG93T3B0aW9uc1xuICAgICAgfSA9IGV2YWx1YXRlKG9wdGlvbnMsIHN0YXRlKTtcbiAgICAgIGNvbnN0IGNvb3JkcyA9IHtcbiAgICAgICAgeCxcbiAgICAgICAgeVxuICAgICAgfTtcbiAgICAgIGNvbnN0IG92ZXJmbG93ID0gYXdhaXQgZGV0ZWN0T3ZlcmZsb3coc3RhdGUsIGRldGVjdE92ZXJmbG93T3B0aW9ucyk7XG4gICAgICBjb25zdCBjcm9zc0F4aXMgPSBnZXRTaWRlQXhpcyhnZXRTaWRlKHBsYWNlbWVudCkpO1xuICAgICAgY29uc3QgbWFpbkF4aXMgPSBnZXRPcHBvc2l0ZUF4aXMoY3Jvc3NBeGlzKTtcbiAgICAgIGxldCBtYWluQXhpc0Nvb3JkID0gY29vcmRzW21haW5BeGlzXTtcbiAgICAgIGxldCBjcm9zc0F4aXNDb29yZCA9IGNvb3Jkc1tjcm9zc0F4aXNdO1xuICAgICAgaWYgKGNoZWNrTWFpbkF4aXMpIHtcbiAgICAgICAgY29uc3QgbWluU2lkZSA9IG1haW5BeGlzID09PSAneScgPyAndG9wJyA6ICdsZWZ0JztcbiAgICAgICAgY29uc3QgbWF4U2lkZSA9IG1haW5BeGlzID09PSAneScgPyAnYm90dG9tJyA6ICdyaWdodCc7XG4gICAgICAgIGNvbnN0IG1pbiA9IG1haW5BeGlzQ29vcmQgKyBvdmVyZmxvd1ttaW5TaWRlXTtcbiAgICAgICAgY29uc3QgbWF4ID0gbWFpbkF4aXNDb29yZCAtIG92ZXJmbG93W21heFNpZGVdO1xuICAgICAgICBtYWluQXhpc0Nvb3JkID0gY2xhbXAobWluLCBtYWluQXhpc0Nvb3JkLCBtYXgpO1xuICAgICAgfVxuICAgICAgaWYgKGNoZWNrQ3Jvc3NBeGlzKSB7XG4gICAgICAgIGNvbnN0IG1pblNpZGUgPSBjcm9zc0F4aXMgPT09ICd5JyA/ICd0b3AnIDogJ2xlZnQnO1xuICAgICAgICBjb25zdCBtYXhTaWRlID0gY3Jvc3NBeGlzID09PSAneScgPyAnYm90dG9tJyA6ICdyaWdodCc7XG4gICAgICAgIGNvbnN0IG1pbiA9IGNyb3NzQXhpc0Nvb3JkICsgb3ZlcmZsb3dbbWluU2lkZV07XG4gICAgICAgIGNvbnN0IG1heCA9IGNyb3NzQXhpc0Nvb3JkIC0gb3ZlcmZsb3dbbWF4U2lkZV07XG4gICAgICAgIGNyb3NzQXhpc0Nvb3JkID0gY2xhbXAobWluLCBjcm9zc0F4aXNDb29yZCwgbWF4KTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGxpbWl0ZWRDb29yZHMgPSBsaW1pdGVyLmZuKHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIFttYWluQXhpc106IG1haW5BeGlzQ29vcmQsXG4gICAgICAgIFtjcm9zc0F4aXNdOiBjcm9zc0F4aXNDb29yZFxuICAgICAgfSk7XG4gICAgICByZXR1cm4ge1xuICAgICAgICAuLi5saW1pdGVkQ29vcmRzLFxuICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgeDogbGltaXRlZENvb3Jkcy54IC0geCxcbiAgICAgICAgICB5OiBsaW1pdGVkQ29vcmRzLnkgLSB5LFxuICAgICAgICAgIGVuYWJsZWQ6IHtcbiAgICAgICAgICAgIFttYWluQXhpc106IGNoZWNrTWFpbkF4aXMsXG4gICAgICAgICAgICBbY3Jvc3NBeGlzXTogY2hlY2tDcm9zc0F4aXNcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfVxuICB9O1xufTtcbi8qKlxuICogQnVpbHQtaW4gYGxpbWl0ZXJgIHRoYXQgd2lsbCBzdG9wIGBzaGlmdCgpYCBhdCBhIGNlcnRhaW4gcG9pbnQuXG4gKi9cbmNvbnN0IGxpbWl0U2hpZnQgPSBmdW5jdGlvbiAob3B0aW9ucykge1xuICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9XG4gIHJldHVybiB7XG4gICAgb3B0aW9ucyxcbiAgICBmbihzdGF0ZSkge1xuICAgICAgY29uc3Qge1xuICAgICAgICB4LFxuICAgICAgICB5LFxuICAgICAgICBwbGFjZW1lbnQsXG4gICAgICAgIHJlY3RzLFxuICAgICAgICBtaWRkbGV3YXJlRGF0YVxuICAgICAgfSA9IHN0YXRlO1xuICAgICAgY29uc3Qge1xuICAgICAgICBvZmZzZXQgPSAwLFxuICAgICAgICBtYWluQXhpczogY2hlY2tNYWluQXhpcyA9IHRydWUsXG4gICAgICAgIGNyb3NzQXhpczogY2hlY2tDcm9zc0F4aXMgPSB0cnVlXG4gICAgICB9ID0gZXZhbHVhdGUob3B0aW9ucywgc3RhdGUpO1xuICAgICAgY29uc3QgY29vcmRzID0ge1xuICAgICAgICB4LFxuICAgICAgICB5XG4gICAgICB9O1xuICAgICAgY29uc3QgY3Jvc3NBeGlzID0gZ2V0U2lkZUF4aXMocGxhY2VtZW50KTtcbiAgICAgIGNvbnN0IG1haW5BeGlzID0gZ2V0T3Bwb3NpdGVBeGlzKGNyb3NzQXhpcyk7XG4gICAgICBsZXQgbWFpbkF4aXNDb29yZCA9IGNvb3Jkc1ttYWluQXhpc107XG4gICAgICBsZXQgY3Jvc3NBeGlzQ29vcmQgPSBjb29yZHNbY3Jvc3NBeGlzXTtcbiAgICAgIGNvbnN0IHJhd09mZnNldCA9IGV2YWx1YXRlKG9mZnNldCwgc3RhdGUpO1xuICAgICAgY29uc3QgY29tcHV0ZWRPZmZzZXQgPSB0eXBlb2YgcmF3T2Zmc2V0ID09PSAnbnVtYmVyJyA/IHtcbiAgICAgICAgbWFpbkF4aXM6IHJhd09mZnNldCxcbiAgICAgICAgY3Jvc3NBeGlzOiAwXG4gICAgICB9IDoge1xuICAgICAgICBtYWluQXhpczogMCxcbiAgICAgICAgY3Jvc3NBeGlzOiAwLFxuICAgICAgICAuLi5yYXdPZmZzZXRcbiAgICAgIH07XG4gICAgICBpZiAoY2hlY2tNYWluQXhpcykge1xuICAgICAgICBjb25zdCBsZW4gPSBtYWluQXhpcyA9PT0gJ3knID8gJ2hlaWdodCcgOiAnd2lkdGgnO1xuICAgICAgICBjb25zdCBsaW1pdE1pbiA9IHJlY3RzLnJlZmVyZW5jZVttYWluQXhpc10gLSByZWN0cy5mbG9hdGluZ1tsZW5dICsgY29tcHV0ZWRPZmZzZXQubWFpbkF4aXM7XG4gICAgICAgIGNvbnN0IGxpbWl0TWF4ID0gcmVjdHMucmVmZXJlbmNlW21haW5BeGlzXSArIHJlY3RzLnJlZmVyZW5jZVtsZW5dIC0gY29tcHV0ZWRPZmZzZXQubWFpbkF4aXM7XG4gICAgICAgIGlmIChtYWluQXhpc0Nvb3JkIDwgbGltaXRNaW4pIHtcbiAgICAgICAgICBtYWluQXhpc0Nvb3JkID0gbGltaXRNaW47XG4gICAgICAgIH0gZWxzZSBpZiAobWFpbkF4aXNDb29yZCA+IGxpbWl0TWF4KSB7XG4gICAgICAgICAgbWFpbkF4aXNDb29yZCA9IGxpbWl0TWF4O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAoY2hlY2tDcm9zc0F4aXMpIHtcbiAgICAgICAgdmFyIF9taWRkbGV3YXJlRGF0YSRvZmZzZSwgX21pZGRsZXdhcmVEYXRhJG9mZnNlMjtcbiAgICAgICAgY29uc3QgbGVuID0gbWFpbkF4aXMgPT09ICd5JyA/ICd3aWR0aCcgOiAnaGVpZ2h0JztcbiAgICAgICAgY29uc3QgaXNPcmlnaW5TaWRlID0gWyd0b3AnLCAnbGVmdCddLmluY2x1ZGVzKGdldFNpZGUocGxhY2VtZW50KSk7XG4gICAgICAgIGNvbnN0IGxpbWl0TWluID0gcmVjdHMucmVmZXJlbmNlW2Nyb3NzQXhpc10gLSByZWN0cy5mbG9hdGluZ1tsZW5dICsgKGlzT3JpZ2luU2lkZSA/ICgoX21pZGRsZXdhcmVEYXRhJG9mZnNlID0gbWlkZGxld2FyZURhdGEub2Zmc2V0KSA9PSBudWxsID8gdm9pZCAwIDogX21pZGRsZXdhcmVEYXRhJG9mZnNlW2Nyb3NzQXhpc10pIHx8IDAgOiAwKSArIChpc09yaWdpblNpZGUgPyAwIDogY29tcHV0ZWRPZmZzZXQuY3Jvc3NBeGlzKTtcbiAgICAgICAgY29uc3QgbGltaXRNYXggPSByZWN0cy5yZWZlcmVuY2VbY3Jvc3NBeGlzXSArIHJlY3RzLnJlZmVyZW5jZVtsZW5dICsgKGlzT3JpZ2luU2lkZSA/IDAgOiAoKF9taWRkbGV3YXJlRGF0YSRvZmZzZTIgPSBtaWRkbGV3YXJlRGF0YS5vZmZzZXQpID09IG51bGwgPyB2b2lkIDAgOiBfbWlkZGxld2FyZURhdGEkb2Zmc2UyW2Nyb3NzQXhpc10pIHx8IDApIC0gKGlzT3JpZ2luU2lkZSA/IGNvbXB1dGVkT2Zmc2V0LmNyb3NzQXhpcyA6IDApO1xuICAgICAgICBpZiAoY3Jvc3NBeGlzQ29vcmQgPCBsaW1pdE1pbikge1xuICAgICAgICAgIGNyb3NzQXhpc0Nvb3JkID0gbGltaXRNaW47XG4gICAgICAgIH0gZWxzZSBpZiAoY3Jvc3NBeGlzQ29vcmQgPiBsaW1pdE1heCkge1xuICAgICAgICAgIGNyb3NzQXhpc0Nvb3JkID0gbGltaXRNYXg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybiB7XG4gICAgICAgIFttYWluQXhpc106IG1haW5BeGlzQ29vcmQsXG4gICAgICAgIFtjcm9zc0F4aXNdOiBjcm9zc0F4aXNDb29yZFxuICAgICAgfTtcbiAgICB9XG4gIH07XG59O1xuXG4vKipcbiAqIFByb3ZpZGVzIGRhdGEgdGhhdCBhbGxvd3MgeW91IHRvIGNoYW5nZSB0aGUgc2l6ZSBvZiB0aGUgZmxvYXRpbmcgZWxlbWVudCDigJRcbiAqIGZvciBpbnN0YW5jZSwgcHJldmVudCBpdCBmcm9tIG92ZXJmbG93aW5nIHRoZSBjbGlwcGluZyBib3VuZGFyeSBvciBtYXRjaCB0aGVcbiAqIHdpZHRoIG9mIHRoZSByZWZlcmVuY2UgZWxlbWVudC5cbiAqIEBzZWUgaHR0cHM6Ly9mbG9hdGluZy11aS5jb20vZG9jcy9zaXplXG4gKi9cbmNvbnN0IHNpemUgPSBmdW5jdGlvbiAob3B0aW9ucykge1xuICBpZiAob3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9XG4gIHJldHVybiB7XG4gICAgbmFtZTogJ3NpemUnLFxuICAgIG9wdGlvbnMsXG4gICAgYXN5bmMgZm4oc3RhdGUpIHtcbiAgICAgIHZhciBfc3RhdGUkbWlkZGxld2FyZURhdGEsIF9zdGF0ZSRtaWRkbGV3YXJlRGF0YTI7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHBsYWNlbWVudCxcbiAgICAgICAgcmVjdHMsXG4gICAgICAgIHBsYXRmb3JtLFxuICAgICAgICBlbGVtZW50c1xuICAgICAgfSA9IHN0YXRlO1xuICAgICAgY29uc3Qge1xuICAgICAgICBhcHBseSA9ICgpID0+IHt9LFxuICAgICAgICAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnNcbiAgICAgIH0gPSBldmFsdWF0ZShvcHRpb25zLCBzdGF0ZSk7XG4gICAgICBjb25zdCBvdmVyZmxvdyA9IGF3YWl0IGRldGVjdE92ZXJmbG93KHN0YXRlLCBkZXRlY3RPdmVyZmxvd09wdGlvbnMpO1xuICAgICAgY29uc3Qgc2lkZSA9IGdldFNpZGUocGxhY2VtZW50KTtcbiAgICAgIGNvbnN0IGFsaWdubWVudCA9IGdldEFsaWdubWVudChwbGFjZW1lbnQpO1xuICAgICAgY29uc3QgaXNZQXhpcyA9IGdldFNpZGVBeGlzKHBsYWNlbWVudCkgPT09ICd5JztcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgd2lkdGgsXG4gICAgICAgIGhlaWdodFxuICAgICAgfSA9IHJlY3RzLmZsb2F0aW5nO1xuICAgICAgbGV0IGhlaWdodFNpZGU7XG4gICAgICBsZXQgd2lkdGhTaWRlO1xuICAgICAgaWYgKHNpZGUgPT09ICd0b3AnIHx8IHNpZGUgPT09ICdib3R0b20nKSB7XG4gICAgICAgIGhlaWdodFNpZGUgPSBzaWRlO1xuICAgICAgICB3aWR0aFNpZGUgPSBhbGlnbm1lbnQgPT09ICgoYXdhaXQgKHBsYXRmb3JtLmlzUlRMID09IG51bGwgPyB2b2lkIDAgOiBwbGF0Zm9ybS5pc1JUTChlbGVtZW50cy5mbG9hdGluZykpKSA/ICdzdGFydCcgOiAnZW5kJykgPyAnbGVmdCcgOiAncmlnaHQnO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgd2lkdGhTaWRlID0gc2lkZTtcbiAgICAgICAgaGVpZ2h0U2lkZSA9IGFsaWdubWVudCA9PT0gJ2VuZCcgPyAndG9wJyA6ICdib3R0b20nO1xuICAgICAgfVxuICAgICAgY29uc3QgbWF4aW11bUNsaXBwaW5nSGVpZ2h0ID0gaGVpZ2h0IC0gb3ZlcmZsb3cudG9wIC0gb3ZlcmZsb3cuYm90dG9tO1xuICAgICAgY29uc3QgbWF4aW11bUNsaXBwaW5nV2lkdGggPSB3aWR0aCAtIG92ZXJmbG93LmxlZnQgLSBvdmVyZmxvdy5yaWdodDtcbiAgICAgIGNvbnN0IG92ZXJmbG93QXZhaWxhYmxlSGVpZ2h0ID0gbWluKGhlaWdodCAtIG92ZXJmbG93W2hlaWdodFNpZGVdLCBtYXhpbXVtQ2xpcHBpbmdIZWlnaHQpO1xuICAgICAgY29uc3Qgb3ZlcmZsb3dBdmFpbGFibGVXaWR0aCA9IG1pbih3aWR0aCAtIG92ZXJmbG93W3dpZHRoU2lkZV0sIG1heGltdW1DbGlwcGluZ1dpZHRoKTtcbiAgICAgIGNvbnN0IG5vU2hpZnQgPSAhc3RhdGUubWlkZGxld2FyZURhdGEuc2hpZnQ7XG4gICAgICBsZXQgYXZhaWxhYmxlSGVpZ2h0ID0gb3ZlcmZsb3dBdmFpbGFibGVIZWlnaHQ7XG4gICAgICBsZXQgYXZhaWxhYmxlV2lkdGggPSBvdmVyZmxvd0F2YWlsYWJsZVdpZHRoO1xuICAgICAgaWYgKChfc3RhdGUkbWlkZGxld2FyZURhdGEgPSBzdGF0ZS5taWRkbGV3YXJlRGF0YS5zaGlmdCkgIT0gbnVsbCAmJiBfc3RhdGUkbWlkZGxld2FyZURhdGEuZW5hYmxlZC54KSB7XG4gICAgICAgIGF2YWlsYWJsZVdpZHRoID0gbWF4aW11bUNsaXBwaW5nV2lkdGg7XG4gICAgICB9XG4gICAgICBpZiAoKF9zdGF0ZSRtaWRkbGV3YXJlRGF0YTIgPSBzdGF0ZS5taWRkbGV3YXJlRGF0YS5zaGlmdCkgIT0gbnVsbCAmJiBfc3RhdGUkbWlkZGxld2FyZURhdGEyLmVuYWJsZWQueSkge1xuICAgICAgICBhdmFpbGFibGVIZWlnaHQgPSBtYXhpbXVtQ2xpcHBpbmdIZWlnaHQ7XG4gICAgICB9XG4gICAgICBpZiAobm9TaGlmdCAmJiAhYWxpZ25tZW50KSB7XG4gICAgICAgIGNvbnN0IHhNaW4gPSBtYXgob3ZlcmZsb3cubGVmdCwgMCk7XG4gICAgICAgIGNvbnN0IHhNYXggPSBtYXgob3ZlcmZsb3cucmlnaHQsIDApO1xuICAgICAgICBjb25zdCB5TWluID0gbWF4KG92ZXJmbG93LnRvcCwgMCk7XG4gICAgICAgIGNvbnN0IHlNYXggPSBtYXgob3ZlcmZsb3cuYm90dG9tLCAwKTtcbiAgICAgICAgaWYgKGlzWUF4aXMpIHtcbiAgICAgICAgICBhdmFpbGFibGVXaWR0aCA9IHdpZHRoIC0gMiAqICh4TWluICE9PSAwIHx8IHhNYXggIT09IDAgPyB4TWluICsgeE1heCA6IG1heChvdmVyZmxvdy5sZWZ0LCBvdmVyZmxvdy5yaWdodCkpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGF2YWlsYWJsZUhlaWdodCA9IGhlaWdodCAtIDIgKiAoeU1pbiAhPT0gMCB8fCB5TWF4ICE9PSAwID8geU1pbiArIHlNYXggOiBtYXgob3ZlcmZsb3cudG9wLCBvdmVyZmxvdy5ib3R0b20pKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgYXdhaXQgYXBwbHkoe1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgYXZhaWxhYmxlV2lkdGgsXG4gICAgICAgIGF2YWlsYWJsZUhlaWdodFxuICAgICAgfSk7XG4gICAgICBjb25zdCBuZXh0RGltZW5zaW9ucyA9IGF3YWl0IHBsYXRmb3JtLmdldERpbWVuc2lvbnMoZWxlbWVudHMuZmxvYXRpbmcpO1xuICAgICAgaWYgKHdpZHRoICE9PSBuZXh0RGltZW5zaW9ucy53aWR0aCB8fCBoZWlnaHQgIT09IG5leHREaW1lbnNpb25zLmhlaWdodCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHJlc2V0OiB7XG4gICAgICAgICAgICByZWN0czogdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB7fTtcbiAgICB9XG4gIH07XG59O1xuXG5leHBvcnQgeyBhcnJvdywgYXV0b1BsYWNlbWVudCwgY29tcHV0ZVBvc2l0aW9uLCBkZXRlY3RPdmVyZmxvdywgZmxpcCwgaGlkZSwgaW5saW5lLCBsaW1pdFNoaWZ0LCBvZmZzZXQsIHNoaWZ0LCBzaXplIH07XG4iXSwibmFtZXMiOlsiZ2V0U2lkZUF4aXMiLCJnZXRBbGlnbm1lbnRBeGlzIiwiZ2V0QXhpc0xlbmd0aCIsImdldFNpZGUiLCJnZXRBbGlnbm1lbnQiLCJldmFsdWF0ZSIsImdldFBhZGRpbmdPYmplY3QiLCJyZWN0VG9DbGllbnRSZWN0IiwibWluIiwiY2xhbXAiLCJwbGFjZW1lbnRzIiwiZ2V0QWxpZ25tZW50U2lkZXMiLCJnZXRPcHBvc2l0ZUFsaWdubWVudFBsYWNlbWVudCIsImdldE9wcG9zaXRlUGxhY2VtZW50IiwiZ2V0RXhwYW5kZWRQbGFjZW1lbnRzIiwiZ2V0T3Bwb3NpdGVBeGlzUGxhY2VtZW50cyIsInNpZGVzIiwibWF4IiwiZ2V0T3Bwb3NpdGVBeGlzIiwiY29tcHV0ZUNvb3Jkc0Zyb21QbGFjZW1lbnQiLCJfcmVmIiwicGxhY2VtZW50IiwicnRsIiwicmVmZXJlbmNlIiwiZmxvYXRpbmciLCJzaWRlQXhpcyIsImFsaWdubWVudEF4aXMiLCJhbGlnbkxlbmd0aCIsInNpZGUiLCJpc1ZlcnRpY2FsIiwiY29tbW9uWCIsIngiLCJ3aWR0aCIsImNvbW1vblkiLCJ5IiwiaGVpZ2h0IiwiY29tbW9uQWxpZ24iLCJjb29yZHMiLCJjb21wdXRlUG9zaXRpb24iLCJjb25maWciLCJzdHJhdGVneSIsIm1pZGRsZXdhcmUiLCJwbGF0Zm9ybSIsInZhbGlkTWlkZGxld2FyZSIsImZpbHRlciIsIkJvb2xlYW4iLCJpc1JUTCIsInJlY3RzIiwiZ2V0RWxlbWVudFJlY3RzIiwic3RhdGVmdWxQbGFjZW1lbnQiLCJtaWRkbGV3YXJlRGF0YSIsInJlc2V0Q291bnQiLCJpIiwibGVuZ3RoIiwibmFtZSIsImZuIiwibmV4dFgiLCJuZXh0WSIsImRhdGEiLCJyZXNldCIsImluaXRpYWxQbGFjZW1lbnQiLCJlbGVtZW50cyIsImRldGVjdE92ZXJmbG93Iiwic3RhdGUiLCJvcHRpb25zIiwiX2F3YWl0JHBsYXRmb3JtJGlzRWxlIiwiYm91bmRhcnkiLCJyb290Qm91bmRhcnkiLCJlbGVtZW50Q29udGV4dCIsImFsdEJvdW5kYXJ5IiwicGFkZGluZyIsInBhZGRpbmdPYmplY3QiLCJhbHRDb250ZXh0IiwiZWxlbWVudCIsImNsaXBwaW5nQ2xpZW50UmVjdCIsImdldENsaXBwaW5nUmVjdCIsImlzRWxlbWVudCIsImNvbnRleHRFbGVtZW50IiwiZ2V0RG9jdW1lbnRFbGVtZW50IiwicmVjdCIsIm9mZnNldFBhcmVudCIsImdldE9mZnNldFBhcmVudCIsIm9mZnNldFNjYWxlIiwiZ2V0U2NhbGUiLCJlbGVtZW50Q2xpZW50UmVjdCIsImNvbnZlcnRPZmZzZXRQYXJlbnRSZWxhdGl2ZVJlY3RUb1ZpZXdwb3J0UmVsYXRpdmVSZWN0IiwidG9wIiwiYm90dG9tIiwibGVmdCIsInJpZ2h0IiwiYXJyb3ciLCJheGlzIiwiYXJyb3dEaW1lbnNpb25zIiwiZ2V0RGltZW5zaW9ucyIsImlzWUF4aXMiLCJtaW5Qcm9wIiwibWF4UHJvcCIsImNsaWVudFByb3AiLCJlbmREaWZmIiwic3RhcnREaWZmIiwiYXJyb3dPZmZzZXRQYXJlbnQiLCJjbGllbnRTaXplIiwiY2VudGVyVG9SZWZlcmVuY2UiLCJsYXJnZXN0UG9zc2libGVQYWRkaW5nIiwibWluUGFkZGluZyIsIm1heFBhZGRpbmciLCJtaW4kMSIsImNlbnRlciIsIm9mZnNldCIsInNob3VsZEFkZE9mZnNldCIsImFsaWdubWVudE9mZnNldCIsImNlbnRlck9mZnNldCIsImdldFBsYWNlbWVudExpc3QiLCJhbGlnbm1lbnQiLCJhdXRvQWxpZ25tZW50IiwiYWxsb3dlZFBsYWNlbWVudHMiLCJhbGxvd2VkUGxhY2VtZW50c1NvcnRlZEJ5QWxpZ25tZW50IiwiYXV0b1BsYWNlbWVudCIsIl9taWRkbGV3YXJlRGF0YSRhdXRvUCIsIl9taWRkbGV3YXJlRGF0YSRhdXRvUDIiLCJfcGxhY2VtZW50c1RoYXRGaXRPbkUiLCJjcm9zc0F4aXMiLCJkZXRlY3RPdmVyZmxvd09wdGlvbnMiLCJwbGFjZW1lbnRzJDEiLCJ1bmRlZmluZWQiLCJvdmVyZmxvdyIsImN1cnJlbnRJbmRleCIsImluZGV4IiwiY3VycmVudFBsYWNlbWVudCIsImFsaWdubWVudFNpZGVzIiwiY3VycmVudE92ZXJmbG93cyIsImFsbE92ZXJmbG93cyIsIm92ZXJmbG93cyIsIm5leHRQbGFjZW1lbnQiLCJwbGFjZW1lbnRzU29ydGVkQnlNb3N0U3BhY2UiLCJtYXAiLCJkIiwic2xpY2UiLCJyZWR1Y2UiLCJhY2MiLCJ2Iiwic29ydCIsImEiLCJiIiwicGxhY2VtZW50c1RoYXRGaXRPbkVhY2hTaWRlIiwiZXZlcnkiLCJyZXNldFBsYWNlbWVudCIsImZsaXAiLCJfbWlkZGxld2FyZURhdGEkYXJyb3ciLCJfbWlkZGxld2FyZURhdGEkZmxpcCIsIm1haW5BeGlzIiwiY2hlY2tNYWluQXhpcyIsImNoZWNrQ3Jvc3NBeGlzIiwiZmFsbGJhY2tQbGFjZW1lbnRzIiwic3BlY2lmaWVkRmFsbGJhY2tQbGFjZW1lbnRzIiwiZmFsbGJhY2tTdHJhdGVneSIsImZhbGxiYWNrQXhpc1NpZGVEaXJlY3Rpb24iLCJmbGlwQWxpZ25tZW50IiwiaW5pdGlhbFNpZGVBeGlzIiwiaXNCYXNlUGxhY2VtZW50IiwiaGFzRmFsbGJhY2tBeGlzU2lkZURpcmVjdGlvbiIsInB1c2giLCJvdmVyZmxvd3NEYXRhIiwiX21pZGRsZXdhcmVEYXRhJGZsaXAyIiwiX292ZXJmbG93c0RhdGEkZmlsdGVyIiwibmV4dEluZGV4IiwiX292ZXJmbG93c0RhdGEkIiwiaWdub3JlQ3Jvc3NBeGlzT3ZlcmZsb3ciLCJoYXNJbml0aWFsTWFpbkF4aXNPdmVyZmxvdyIsIl9vdmVyZmxvd3NEYXRhJGZpbHRlcjIiLCJjdXJyZW50U2lkZUF4aXMiLCJnZXRTaWRlT2Zmc2V0cyIsImlzQW55U2lkZUZ1bGx5Q2xpcHBlZCIsInNvbWUiLCJoaWRlIiwib2Zmc2V0cyIsInJlZmVyZW5jZUhpZGRlbk9mZnNldHMiLCJyZWZlcmVuY2VIaWRkZW4iLCJlc2NhcGVkT2Zmc2V0cyIsImVzY2FwZWQiLCJnZXRCb3VuZGluZ1JlY3QiLCJtaW5YIiwibWluWSIsIm1heFgiLCJtYXhZIiwiZ2V0UmVjdHNCeUxpbmUiLCJzb3J0ZWRSZWN0cyIsImdyb3VwcyIsInByZXZSZWN0IiwiaW5saW5lIiwibmF0aXZlQ2xpZW50UmVjdHMiLCJBcnJheSIsImZyb20iLCJnZXRDbGllbnRSZWN0cyIsImNsaWVudFJlY3RzIiwiZmFsbGJhY2siLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJmaW5kIiwiZmlyc3RSZWN0IiwibGFzdFJlY3QiLCJpc1RvcCIsImlzTGVmdFNpZGUiLCJtYXhSaWdodCIsIm1pbkxlZnQiLCJtZWFzdXJlUmVjdHMiLCJyZXNldFJlY3RzIiwiY29udmVydFZhbHVlVG9Db29yZHMiLCJtYWluQXhpc011bHRpIiwiaW5jbHVkZXMiLCJjcm9zc0F4aXNNdWx0aSIsInJhd1ZhbHVlIiwiX21pZGRsZXdhcmVEYXRhJG9mZnNlIiwiZGlmZkNvb3JkcyIsInNoaWZ0IiwibGltaXRlciIsIm1haW5BeGlzQ29vcmQiLCJjcm9zc0F4aXNDb29yZCIsIm1pblNpZGUiLCJtYXhTaWRlIiwibGltaXRlZENvb3JkcyIsImVuYWJsZWQiLCJsaW1pdFNoaWZ0IiwicmF3T2Zmc2V0IiwiY29tcHV0ZWRPZmZzZXQiLCJsZW4iLCJsaW1pdE1pbiIsImxpbWl0TWF4IiwiX21pZGRsZXdhcmVEYXRhJG9mZnNlMiIsImlzT3JpZ2luU2lkZSIsInNpemUiLCJfc3RhdGUkbWlkZGxld2FyZURhdGEiLCJfc3RhdGUkbWlkZGxld2FyZURhdGEyIiwiYXBwbHkiLCJoZWlnaHRTaWRlIiwid2lkdGhTaWRlIiwibWF4aW11bUNsaXBwaW5nSGVpZ2h0IiwibWF4aW11bUNsaXBwaW5nV2lkdGgiLCJvdmVyZmxvd0F2YWlsYWJsZUhlaWdodCIsIm92ZXJmbG93QXZhaWxhYmxlV2lkdGgiLCJub1NoaWZ0IiwiYXZhaWxhYmxlSGVpZ2h0IiwiYXZhaWxhYmxlV2lkdGgiLCJ4TWluIiwieE1heCIsInlNaW4iLCJ5TWF4IiwibmV4dERpbWVuc2lvbnMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   autoUpdate: () => (/* binding */ autoUpdate),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   platform: () => (/* binding */ platform),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n/* harmony import */ var _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @floating-ui/core */ \"(ssr)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs\");\n/* harmony import */ var _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils/dom */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\");\n\n\n\n\nfunction getCssDimensions(element) {\n  const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(width) !== offsetWidth || (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(domElement)) {\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.width) : rect.width) / width;\n  let y = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\nfunction getVisualOffsets(element) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(domElement);\n    const offsetWin = offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent) ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(currentIFrame);\n      currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n    }\n  }\n  return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n  const topLayer = elements ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== 'body' || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n      scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n    }\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  const scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element);\n  const body = element.ownerDocument.body;\n  const width = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(body).direction === 'rtl') {\n    x += (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) ? getScale(element) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element));\n  } else if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n  if (parentNode === stopNode || !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(parentNode) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(parentNode)) {\n    return false;\n  }\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(element, [], false).filter(el => (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(el) && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'fixed';\n  let currentNode = elementIsFixed ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(currentNode) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(currentNode)) {\n    const computedStyle = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentNode);\n    const currentNodeIsContaining = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.top, accRect.top);\n    accRect.right = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.right, accRect.right);\n    accRect.bottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.bottom, accRect.bottom);\n    accRect.left = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n  const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== 'body' || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n      scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction isStaticPositioned(element) {\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'static';\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n  if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element)) {\n    return win;\n  }\n  if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n    let svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n    while (svgOffsetParent && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(svgOffsetParent)) {\n      if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTableElement)(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(offsetParent) && isStaticPositioned(offsetParent) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(offsetParent)) {\n    return win;\n  }\n  return offsetParent || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getContainingBlock)(element) || win;\n}\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\nfunction isRTL(element) {\n  return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement,\n  isRTL\n};\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(top);\n    const insetRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientWidth - (left + width));\n    const insetBottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientHeight - (top + height));\n    const insetLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(0, (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(referenceEl) : []), ...(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.detectOverflow;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.offset;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.autoPlacement;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.shift;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.flip;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.size;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.hide;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.arrow;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.inline;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.limitShift;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return (0,_floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.computePosition)(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   autoUpdate: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.autoUpdate),\n/* harmony export */   computePosition: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.computePosition),\n/* harmony export */   detectOverflow: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   platform: () => (/* reexport safe */ _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.platform),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   useFloating: () => (/* binding */ useFloating)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _floating_ui_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @floating-ui/dom */ \"(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n\n\n\n\n\nvar index = typeof document !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_2__.useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\nfunction getDPR(element) {\n  if (true) {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\nfunction useLatestRef(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_2__.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = react__WEBPACK_IMPORTED_MODULE_2__.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = react__WEBPACK_IMPORTED_MODULE_2__.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = react__WEBPACK_IMPORTED_MODULE_2__.useState(null);\n  const [_floating, _setFloating] = react__WEBPACK_IMPORTED_MODULE_2__.useState(null);\n  const setReference = react__WEBPACK_IMPORTED_MODULE_2__.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = react__WEBPACK_IMPORTED_MODULE_2__.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  const floatingRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  const dataRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = react__WEBPACK_IMPORTED_MODULE_2__.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    (0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.computePosition)(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        react_dom__WEBPACK_IMPORTED_MODULE_3__.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = react__WEBPACK_IMPORTED_MODULE_2__.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return react__WEBPACK_IMPORTED_MODULE_2__.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return (0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.arrow)({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return (0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.arrow)({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.offset)(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.shift)(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.limitShift)(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.flip)(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.size)(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.autoPlacement)(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.hide)(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...(0,_floating_ui_dom__WEBPACK_IMPORTED_MODULE_0__.inline)(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getComputedStyle: () => (/* binding */ getComputedStyle),\n/* harmony export */   getContainingBlock: () => (/* binding */ getContainingBlock),\n/* harmony export */   getDocumentElement: () => (/* binding */ getDocumentElement),\n/* harmony export */   getFrameElement: () => (/* binding */ getFrameElement),\n/* harmony export */   getNearestOverflowAncestor: () => (/* binding */ getNearestOverflowAncestor),\n/* harmony export */   getNodeName: () => (/* binding */ getNodeName),\n/* harmony export */   getNodeScroll: () => (/* binding */ getNodeScroll),\n/* harmony export */   getOverflowAncestors: () => (/* binding */ getOverflowAncestors),\n/* harmony export */   getParentNode: () => (/* binding */ getParentNode),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   isContainingBlock: () => (/* binding */ isContainingBlock),\n/* harmony export */   isElement: () => (/* binding */ isElement),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isLastTraversableNode: () => (/* binding */ isLastTraversableNode),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isOverflowElement: () => (/* binding */ isOverflowElement),\n/* harmony export */   isShadowRoot: () => (/* binding */ isShadowRoot),\n/* harmony export */   isTableElement: () => (/* binding */ isTableElement),\n/* harmony export */   isTopLayer: () => (/* binding */ isTopLayer),\n/* harmony export */   isWebKit: () => (/* binding */ isWebKit)\n/* harmony export */ });\nfunction hasWindow() {\n  return false;\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alignments: () => (/* binding */ alignments),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   createCoords: () => (/* binding */ createCoords),\n/* harmony export */   evaluate: () => (/* binding */ evaluate),\n/* harmony export */   expandPaddingObject: () => (/* binding */ expandPaddingObject),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   getAlignment: () => (/* binding */ getAlignment),\n/* harmony export */   getAlignmentAxis: () => (/* binding */ getAlignmentAxis),\n/* harmony export */   getAlignmentSides: () => (/* binding */ getAlignmentSides),\n/* harmony export */   getAxisLength: () => (/* binding */ getAxisLength),\n/* harmony export */   getExpandedPlacements: () => (/* binding */ getExpandedPlacements),\n/* harmony export */   getOppositeAlignmentPlacement: () => (/* binding */ getOppositeAlignmentPlacement),\n/* harmony export */   getOppositeAxis: () => (/* binding */ getOppositeAxis),\n/* harmony export */   getOppositeAxisPlacements: () => (/* binding */ getOppositeAxisPlacements),\n/* harmony export */   getOppositePlacement: () => (/* binding */ getOppositePlacement),\n/* harmony export */   getPaddingObject: () => (/* binding */ getPaddingObject),\n/* harmony export */   getSide: () => (/* binding */ getSide),\n/* harmony export */   getSideAxis: () => (/* binding */ getSideAxis),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   placements: () => (/* binding */ placements),\n/* harmony export */   rectToClientRect: () => (/* binding */ rectToClientRect),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   sides: () => (/* binding */ sides)\n/* harmony export */ });\n/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\n");

/***/ })

};
;