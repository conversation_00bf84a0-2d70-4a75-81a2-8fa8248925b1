'use client';

import { env, features, isDev, isProd, isTest } from '@/lib/config';

/**
 * Environment information component
 * Displays current environment and feature flags
 * Only shown in development mode
 */
export function EnvInfo() {
  if (!isDev) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 p-4 bg-gray-800 text-white rounded-lg shadow-lg text-xs z-50 max-w-xs opacity-80 hover:opacity-100 transition-opacity">
      <h3 className="font-bold mb-2">Environment Info</h3>
      <div className="grid grid-cols-2 gap-1">
        <span>Environment:</span>
        <span className="font-mono">{env.NODE_ENV}</span>
        
        <span>App Name:</span>
        <span className="font-mono">{env.NEXT_PUBLIC_APP_NAME}</span>
        
        <span>App URL:</span>
        <span className="font-mono truncate">{env.NEXT_PUBLIC_APP_URL}</span>
      </div>
      
      <h4 className="font-bold mt-3 mb-1">Feature Flags</h4>
      <div className="grid grid-cols-2 gap-1">
        <span>Module Conversion:</span>
        <span className={features.moduleConversion ? 'text-green-400' : 'text-red-400'}>
          {features.moduleConversion ? 'Enabled' : 'Disabled'}
        </span>
        
        <span>Reporting:</span>
        <span className={features.reporting ? 'text-green-400' : 'text-red-400'}>
          {features.reporting ? 'Enabled' : 'Disabled'}
        </span>
        
        <span>Email Notifications:</span>
        <span className={features.emailNotifications ? 'text-green-400' : 'text-red-400'}>
          {features.emailNotifications ? 'Enabled' : 'Disabled'}
        </span>
      </div>
    </div>
  );
}
