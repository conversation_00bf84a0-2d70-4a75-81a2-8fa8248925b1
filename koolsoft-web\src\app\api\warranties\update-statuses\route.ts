import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getWarrantyRepository } from '@/lib/repositories';

/**
 * POST /api/warranties/update-statuses
 * Update statuses of warranties based on their warranty dates
 *
 * This endpoint finds all ACTIVE warranties with warranty dates in the past
 * and updates their status to EXPIRED.
 *
 * @requires Authentication and ADMIN or MANAGER role
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      console.log('Update Warranty Statuses API: Starting status update process');
      
      const warrantyRepository = getWarrantyRepository();
      
      // Update expired warranty statuses
      const updatedCount = await warrantyRepository.updateExpiredStatuses();
      
      console.log(`Update Warranty Statuses API: Updated ${updatedCount} warranties to EXPIRED status`);
      
      return NextResponse.json({
        success: true,
        message: `Successfully updated ${updatedCount} warranties to EXPIRED status`,
        updatedCount,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Update Warranty Statuses API: Error updating warranty statuses:', error);
      return NextResponse.json(
        { 
          error: 'Failed to update warranty statuses',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }
  }
);
