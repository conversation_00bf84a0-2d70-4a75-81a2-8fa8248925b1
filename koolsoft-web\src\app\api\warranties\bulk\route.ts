import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getWarrantyRepository } from '@/lib/repositories';
import { z } from 'zod';

/**
 * Bulk Operations for Warranties
 * 
 * POST /api/warranties/bulk
 * 
 * Performs bulk operations on warranties (status updates, exports, etc.)
 */
async function bulkHandler(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const bulkActionSchema = z.object({
      warrantyIds: z.array(z.string()).min(1, 'At least one warranty ID is required'),
      action: z.enum(['UPDATE_STATUS', 'DELETE', 'EXPORT', 'UPDATE_EXPIRY_STATUS']),
      status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED']).optional(),
      exportFormat: z.enum(['CSV', 'EXCEL', 'PDF']).optional(),
    }).refine(data => {
      // If action is UPDATE_STATUS, status is required
      if (data.action === 'UPDATE_STATUS') {
        return !!data.status;
      }
      // If action is EXPORT, exportFormat is required
      if (data.action === 'EXPORT') {
        return !!data.exportFormat;
      }
      return true;
    }, {
      message: "Status is required for UPDATE_STATUS action, exportFormat is required for EXPORT action",
    });

    const { warrantyIds, action, status, exportFormat } = bulkActionSchema.parse(body);

    const warrantyRepository = getWarrantyRepository();
    let result: any = {};

    // Perform the bulk action
    switch (action) {
      case 'UPDATE_STATUS':
        if (!status) {
          return NextResponse.json(
            { error: 'Status is required for UPDATE_STATUS action' },
            { status: 400 }
          );
        }

        const updateResult = await warrantyRepository.updateManyStatuses(warrantyIds, status);
        result = {
          action: 'UPDATE_STATUS',
          affected: updateResult.count,
          status: status,
          message: `Updated ${updateResult.count} warranties to ${status} status`
        };
        break;

      case 'UPDATE_EXPIRY_STATUS':
        // Update warranties that have expired based on their warranty dates
        const expiredCount = await warrantyRepository.updateExpiredStatuses();
        result = {
          action: 'UPDATE_EXPIRY_STATUS',
          affected: expiredCount,
          message: `Updated ${expiredCount} warranties to EXPIRED status based on warranty dates`
        };
        break;

      case 'DELETE':
        const deleteResult = await warrantyRepository.deleteMany(warrantyIds);
        result = {
          action: 'DELETE',
          affected: deleteResult.count,
          message: `Deleted ${deleteResult.count} warranties`
        };
        break;

      case 'EXPORT':
        if (!exportFormat) {
          return NextResponse.json(
            { error: 'Export format is required for EXPORT action' },
            { status: 400 }
          );
        }

        // Get warranties for export
        const warranties = await warrantyRepository.findByIds(warrantyIds);
        
        // Generate export based on format
        switch (exportFormat) {
          case 'CSV':
            return generateCSVExport(warranties);
          case 'EXCEL':
            return generateExcelExport(warranties);
          case 'PDF':
            return generatePDFExport(warranties);
          default:
            return NextResponse.json(
              { error: 'Unsupported export format' },
              { status: 400 }
            );
        }

      default:
        return NextResponse.json(
          { error: 'Unsupported bulk action' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error performing bulk action:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to perform bulk action' },
      { status: 500 }
    );
  }
}

/**
 * Generate CSV export for selected warranties
 */
function generateCSVExport(warranties: any[]) {
  const headers = [
    'BSL No',
    'Customer Name',
    'Customer City',
    'BSL Amount',
    'Install Date',
    'Warranty Date',
    'Warning Date',
    'Number of Machines',
    'Status',
    'Created At'
  ];

  const csvRows = warranties.map(warranty => [
    warranty.bslNo || '',
    warranty.customer?.name || '',
    warranty.customer?.city || '',
    warranty.bslAmount?.toString() || '0',
    warranty.installDate ? new Date(warranty.installDate).toISOString().split('T')[0] : '',
    warranty.warrantyDate ? new Date(warranty.warrantyDate).toISOString().split('T')[0] : '',
    warranty.warningDate ? new Date(warranty.warningDate).toISOString().split('T')[0] : '',
    warranty.numberOfMachines?.toString() || '0',
    warranty.status || '',
    warranty.createdAt ? new Date(warranty.createdAt).toISOString().split('T')[0] : ''
  ]);

  // Escape CSV values
  const escapeCsvValue = (value: string) => {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  };

  const csvHeader = headers.map(escapeCsvValue).join(',');
  const csvData = csvRows.map(row => 
    row.map(cell => escapeCsvValue(cell.toString())).join(',')
  ).join('\n');

  const csv = [csvHeader, csvData].join('\n');

  // Generate filename
  const now = new Date().toISOString().split('T')[0];
  const filename = `bulk_warranties_${now}.csv`;

  return new NextResponse(csv, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * Generate Excel export (placeholder)
 */
function generateExcelExport(warranties: any[]) {
  // For now, return CSV with Excel MIME type
  const csvResponse = generateCSVExport(warranties);
  
  const now = new Date().toISOString().split('T')[0];
  const filename = `bulk_warranties_${now}.xlsx`;

  return new NextResponse(csvResponse.body, {
    headers: {
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * Generate PDF export (placeholder)
 */
function generatePDFExport(warranties: any[]) {
  // For now, return a simple text response
  const textContent = warranties.map(warranty => 
    `BSL No: ${warranty.bslNo}\nCustomer: ${warranty.customer?.name}\nStatus: ${warranty.status}\n\n`
  ).join('');

  const now = new Date().toISOString().split('T')[0];
  const filename = `bulk_warranties_${now}.pdf`;

  return new NextResponse(textContent, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

// Wrap the handler with role protection
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  bulkHandler
);
