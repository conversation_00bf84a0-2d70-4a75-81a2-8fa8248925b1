import { NextRequest, NextResponse } from 'next/server';
import { getEmailTemplateRepository } from '@/lib/repositories';
import { z } from 'zod';

/**
 * Email template preview schema
 */
const previewEmailTemplateSchema = z.object({
  templateId: z.string().uuid().optional(),
  templateName: z.string().min(2).max(100).optional(),
  data: z.record(z.any()),
});

/**
 * POST /api/email/preview
 * Preview an email template with test data without sending it
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = previewEmailTemplateSchema.parse(body);

    // Ensure either templateId or templateName is provided
    if (!validatedData.templateId && !validatedData.templateName) {
      return NextResponse.json(
        { error: 'Either templateId or templateName must be provided' },
        { status: 400 }
      );
    }

    const emailTemplateRepository = getEmailTemplateRepository();

    // Get template by ID or name
    let template;
    if (validatedData.templateId) {
      template = await emailTemplateRepository.findById(validatedData.templateId);
    } else if (validatedData.templateName) {
      template = await emailTemplateRepository.findByName(validatedData.templateName);
    }

    if (!template) {
      return NextResponse.json(
        { error: 'Email template not found' },
        { status: 404 }
      );
    }

    // Replace placeholders in template
    // Use bodyHtml if available, otherwise fall back to body
    let html = template.bodyHtml || template.body || '';
    let subject = template.subject || '';

    for (const [key, value] of Object.entries(validatedData.data)) {
      const placeholder = `{{${key}}}`;
      html = html.replace(new RegExp(placeholder, 'g'), String(value));
      subject = subject.replace(new RegExp(placeholder, 'g'), String(value));
    }

    // Use variables from template if available, otherwise extract them
    let allVariables = template.variables || [];

    if (!allVariables || allVariables.length === 0) {
      // Extract variables from template
      const variableRegex = /{{([^{}]+)}}/g;
      const bodyContent = template.bodyHtml || template.body || '';
      const bodyVariables = [...bodyContent.matchAll(variableRegex)].map(match => match[1]);
      const subjectVariables = [...template.subject.matchAll(variableRegex)].map(match => match[1]);
      allVariables = [...new Set([...bodyVariables, ...subjectVariables])];
    }

    return NextResponse.json({
      preview: {
        subject,
        html,
      },
      template: {
        id: template.id,
        name: template.name,
        subject: template.subject,
        body: template.body || '',
        bodyHtml: template.bodyHtml || template.body || '',
        bodyText: template.bodyText || '',
        description: template.description,
        variables: allVariables,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error previewing email template:', error);
    return NextResponse.json(
      { error: 'Failed to preview email template' },
      { status: 500 }
    );
  }
}
