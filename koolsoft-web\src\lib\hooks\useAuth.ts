'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

/**
 * Custom hook for authentication
 *
 * This hook provides:
 * - Authentication state (loading, authenticated, user data)
 * - Login and logout functions
 * - Role-based access control helpers
 */
export function useAuth() {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Log session data for debugging
  console.log('useAuth Hook:', {
    status,
    sessionExists: !!session,
    userExists: !!session?.user,
    userRole: session?.user?.role,
    isRefreshing
  });

  // Force refresh the session if it's taking too long to load
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (status === 'loading' && !isRefreshing) {
      // If still loading after 2 seconds, try to refresh the session
      timeoutId = setTimeout(() => {
        console.log('useAuth: Session loading timeout, forcing refresh');
        setIsRefreshing(true);
        update() // Force refresh the session
          .then(() => {
            console.log('useAuth: Session refreshed successfully');
          })
          .catch(err => {
            console.error('useAuth: Error refreshing session', err);
          })
          .finally(() => {
            setIsRefreshing(false);
          });
      }, 2000);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [status, isRefreshing, update]);

  // Check if the user is authenticated
  const isAuthenticated = status === 'authenticated' && !!session?.user;

  // Check if the authentication state is loading
  const isLoading = status === 'loading' || isRefreshing;

  // Get the user's role (with fallback to empty string to avoid null errors)
  const userRole = session?.user?.role || '';

  // Log authentication state
  console.log('useAuth State:', {
    isAuthenticated,
    isLoading,
    userRole,
    status
  });

  /**
   * Login function
   * @param email User email
   * @param password User password
   * @param callbackUrl URL to redirect to after successful login
   * @returns Promise resolving to login success status
   */
  const login = async (email: string, password: string, callbackUrl?: string) => {
    try {
      setError(null);

      const result = await signIn('credentials', {
        redirect: false,
        email,
        password,
      });

      if (result?.error) {
        setError('Invalid email or password');
        return false;
      }

      if (result?.ok) {
        router.push(callbackUrl || '/dashboard');
        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      setError('An error occurred during login');
      return false;
    }
  };

  /**
   * Logout function
   * @param callbackUrl URL to redirect to after logout
   */
  const logout = async (callbackUrl?: string) => {
    await signOut({ redirect: false });
    router.push(callbackUrl || '/auth/login');
  };

  /**
   * Check if the user has a specific role
   * @param role Role to check
   * @returns Boolean indicating if the user has the role
   */
  const hasRole = (role: string | string[]) => {
    if (!userRole) return false;

    if (Array.isArray(role)) {
      return role.includes(userRole);
    }

    return userRole === role;
  };

  /**
   * Check if the user is an admin
   * @returns Boolean indicating if the user is an admin
   */
  const isAdmin = () => userRole === 'ADMIN';

  /**
   * Check if the user is a manager
   * @returns Boolean indicating if the user is a manager
   */
  const isManager = () => userRole === 'MANAGER';

  /**
   * Check if the user is an executive
   * @returns Boolean indicating if the user is an executive
   */
  const isExecutive = () => userRole === 'EXECUTIVE';

  return {
    user: session?.user || null,
    isAuthenticated,
    isLoading,
    userRole,
    login,
    logout,
    error,
    hasRole,
    isAdmin,
    isManager,
    isExecutive,
  };
}
