/**
 * Base Repository Interface
 * 
 * This interface defines the common operations that all repositories should implement.
 * It provides a consistent API for interacting with different domain entities.
 */

export interface BaseRepository<T, ID, CreateInput, UpdateInput> {
  /**
   * Find all entities with optional pagination
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of entities
   */
  findAll(skip?: number, take?: number): Promise<T[]>;

  /**
   * Find entities by a filter condition
   * @param filter Filter condition
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of entities
   */
  findBy(filter: any, skip?: number, take?: number): Promise<T[]>;

  /**
   * Find a single entity by its ID
   * @param id Entity ID
   * @returns Promise resolving to the entity or null if not found
   */
  findById(id: ID): Promise<T | null>;

  /**
   * Find a single entity by a unique condition
   * @param where Unique condition
   * @returns Promise resolving to the entity or null if not found
   */
  findOne(where: any): Promise<T | null>;

  /**
   * Count entities matching a filter condition
   * @param filter Filter condition
   * @returns Promise resolving to the count
   */
  count(filter?: any): Promise<number>;

  /**
   * Create a new entity
   * @param data Entity data
   * @returns Promise resolving to the created entity
   */
  create(data: CreateInput): Promise<T>;

  /**
   * Create multiple entities
   * @param data Array of entity data
   * @returns Promise resolving to the created entities
   */
  createMany(data: CreateInput[]): Promise<T[]>;

  /**
   * Update an entity by its ID
   * @param id Entity ID
   * @param data Update data
   * @returns Promise resolving to the updated entity
   */
  update(id: ID, data: UpdateInput): Promise<T>;

  /**
   * Update multiple entities by a filter condition
   * @param filter Filter condition
   * @param data Update data
   * @returns Promise resolving to the number of updated entities
   */
  updateMany(filter: any, data: UpdateInput): Promise<number>;

  /**
   * Delete an entity by its ID
   * @param id Entity ID
   * @returns Promise resolving to the deleted entity
   */
  delete(id: ID): Promise<T>;

  /**
   * Delete multiple entities by a filter condition
   * @param filter Filter condition
   * @returns Promise resolving to the number of deleted entities
   */
  deleteMany(filter: any): Promise<number>;

  /**
   * Execute a function within a transaction
   * @param fn Function to execute within the transaction
   * @returns Promise resolving to the result of the function
   */
  transaction<R>(fn: (repo: BaseRepository<T, ID, CreateInput, UpdateInput>) => Promise<R>): Promise<R>;
}
