'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AMCList } from '@/components/amc/amc-list';
import { ArrowLeft } from 'lucide-react';
import { showErrorToast } from '@/lib/toast';
import { AMCContract } from '@/lib/hooks/useAMCContracts';

/**
 * Expiring AMC Contracts Page
 *
 * This page displays a list of AMC contracts that are expiring soon.
 */
export default function ExpiringAMCPage() {
  const router = useRouter();
  const [contracts, setContracts] = useState<AMCContract[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [pagination, setPagination] = useState({
    skip: 0,
    take: 10,
    total: 0,
  });
  const [days, setDays] = useState(30);

  // Fetch expiring contracts
  const fetchExpiringContracts = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/amc/contracts/expiring?days=${days}&skip=${pagination.skip}&take=${pagination.take}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch expiring AMC contracts');
      }

      const data = await response.json();
      setContracts(data.data || []);
      setPagination(prev => ({
        ...prev,
        total: data.pagination?.total || 0,
      }));
    } catch (error) {
      console.error('Error fetching expiring AMC contracts:', error);
      showErrorToast('Error', 'Failed to fetch expiring AMC contracts');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle pagination change
  const handlePaginationChange = (skip: number, take: number) => {
    setPagination(prev => ({
      ...prev,
      skip,
      take,
    }));
  };

  // Handle delete
  const handleDelete = async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/amc/contracts/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete AMC contract');
      }

      // Refresh the contracts list
      await fetchExpiringContracts();
      return true;
    } catch (error) {
      console.error('Error deleting AMC contract:', error);
      showErrorToast('Error', 'Failed to delete AMC contract');
      return false;
    }
  };

  // Fetch contracts when dependencies change
  useEffect(() => {
    fetchExpiringContracts();
  }, [pagination.skip, pagination.take, days]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle>Expiring AMC Contracts</CardTitle>
            <CardDescription className="text-gray-100">
              AMC contracts expiring in the next {days} days
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button asChild variant="secondary">
              <Link href="/amc">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to AMC List
              </Link>
            </Button>
            <select
              className="h-9 rounded-md px-3 bg-white text-black"
              value={days}
              onChange={(e) => setDays(parseInt(e.target.value))}
            >
              <option value="7">Next 7 days</option>
              <option value="15">Next 15 days</option>
              <option value="30">Next 30 days</option>
              <option value="60">Next 60 days</option>
              <option value="90">Next 90 days</option>
            </select>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <AMCList
            contracts={contracts}
            isLoading={isLoading}
            pagination={pagination}
            onPaginationChange={handlePaginationChange}
            onRefresh={fetchExpiringContracts}
            onDelete={handleDelete}
          />
        </CardContent>
      </Card>
    </div>
  );
}
