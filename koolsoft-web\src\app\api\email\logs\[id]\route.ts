import { NextRequest, NextResponse } from 'next/server';
import { getEmailLogRepository } from '@/lib/repositories';

/**
 * GET /api/email/logs/[id]
 * Get a specific email log by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const emailLogRepository = getEmailLogRepository();

    // Get email log
    const log = await emailLogRepository.findById(id);

    if (!log) {
      return NextResponse.json(
        { error: 'Email log not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(log);
  } catch (error) {
    console.error('Error fetching email log:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email log' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/email/logs/[id]
 * Delete a specific email log
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const emailLogRepository = getEmailLogRepository();

    // Check if email log exists
    const existingLog = await emailLogRepository.findById(id);

    if (!existingLog) {
      return NextResponse.json(
        { error: 'Email log not found' },
        { status: 404 }
      );
    }

    // Delete email log
    await emailLogRepository.delete(id);

    return NextResponse.json(
      { message: 'Email log deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting email log:', error);
    return NextResponse.json(
      { error: 'Failed to delete email log' },
      { status: 500 }
    );
  }
}
