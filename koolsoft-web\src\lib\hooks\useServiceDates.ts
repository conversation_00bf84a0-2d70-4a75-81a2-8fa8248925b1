import { useState, useCallback } from 'react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

export interface ServiceDate {
  id: string;
  amcContractId: string;
  serviceDate: Date;
  serviceFlag?: string;
  completedDate?: Date;
  serviceNumber?: number;
  serviceId?: string;
  originalAmcId?: number;
  originalServiceDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ServiceDateStatistics {
  total: number;
  completed: number;
  pending: number;
  overdue: number;
  upcoming: number;
  completionPercentage: number;
}

export interface CreateServiceDateData {
  amcContractId: string;
  scheduledDate: Date;
  status?: 'SCHEDULED' | 'COMPLETED' | 'MISSED';
  technicianId?: string;
  remarks?: string;
  serviceNumber?: number;
}

export interface UpdateServiceDateData {
  scheduledDate?: Date;
  completedDate?: Date;
  status?: 'SCHEDULED' | 'COMPLETED' | 'MISSED';
  technicianId?: string;
  remarks?: string;
  serviceNumber?: number;
}

export interface ServiceDateQueryParams {
  contractId?: string;
  startDate?: Date;
  endDate?: Date;
  status?: 'SCHEDULED' | 'COMPLETED' | 'MISSED';
  skip?: number;
  take?: number;
  orderBy?: 'serviceDate' | 'completedDate' | 'createdAt';
  orderDirection?: 'asc' | 'desc';
}

export function useServiceDates() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchServiceDates = useCallback(async (params?: ServiceDateQueryParams) => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();

      if (params?.contractId) searchParams.append('contractId', params.contractId);
      if (params?.startDate) searchParams.append('startDate', params.startDate.toISOString());
      if (params?.endDate) searchParams.append('endDate', params.endDate.toISOString());
      if (params?.status) searchParams.append('status', params.status);
      if (params?.skip !== undefined) searchParams.append('skip', params.skip.toString());
      if (params?.take !== undefined) searchParams.append('take', params.take.toString());
      if (params?.orderBy) searchParams.append('orderBy', params.orderBy);
      if (params?.orderDirection) searchParams.append('orderDirection', params.orderDirection);

      const response = await fetch(`/api/amc/service-dates?${searchParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch service dates');
      }

      const data = await response.json();
      return {
        serviceDates: data.serviceDates.map((sd: any) => ({
          ...sd,
          serviceDate: new Date(sd.serviceDate),
          completedDate: sd.completedDate ? new Date(sd.completedDate) : undefined,
          originalServiceDate: sd.originalServiceDate ? new Date(sd.originalServiceDate) : undefined,
          createdAt: new Date(sd.createdAt),
          updatedAt: new Date(sd.updatedAt),
        })) as ServiceDate[],
        meta: data.meta,
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch service dates';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchContractServiceDates = useCallback(async (
    contractId: string,
    params?: {
      skip?: number;
      take?: number;
      orderBy?: 'serviceDate' | 'completedDate' | 'createdAt';
      orderDirection?: 'asc' | 'desc';
      includeStatistics?: boolean;
    }
  ) => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();

      if (params?.skip !== undefined) searchParams.append('skip', params.skip.toString());
      if (params?.take !== undefined) searchParams.append('take', params.take.toString());
      if (params?.orderBy) searchParams.append('orderBy', params.orderBy);
      if (params?.orderDirection) searchParams.append('orderDirection', params.orderDirection);
      if (params?.includeStatistics) searchParams.append('includeStatistics', 'true');

      const response = await fetch(`/api/amc/contracts/${contractId}/service-dates?${searchParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch contract service dates');
      }

      const data = await response.json();
      return {
        serviceDates: data.serviceDates.map((sd: any) => ({
          ...sd,
          serviceDate: new Date(sd.serviceDate),
          completedDate: sd.completedDate ? new Date(sd.completedDate) : undefined,
          originalServiceDate: sd.originalServiceDate ? new Date(sd.originalServiceDate) : undefined,
          createdAt: new Date(sd.createdAt),
          updatedAt: new Date(sd.updatedAt),
        })) as ServiceDate[],
        meta: data.meta,
        statistics: data.statistics as ServiceDateStatistics | undefined,
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch contract service dates';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const createServiceDate = useCallback(async (data: CreateServiceDateData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/amc/service-dates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create service date');
      }

      const result = await response.json();
      showSuccessToast('Success', 'Service date created successfully');
      
      return {
        ...result.serviceDate,
        serviceDate: new Date(result.serviceDate.serviceDate),
        completedDate: result.serviceDate.completedDate ? new Date(result.serviceDate.completedDate) : undefined,
        createdAt: new Date(result.serviceDate.createdAt),
        updatedAt: new Date(result.serviceDate.updatedAt),
      } as ServiceDate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create service date';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateServiceDate = useCallback(async (id: string, data: UpdateServiceDateData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/amc/service-dates/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update service date');
      }

      const result = await response.json();
      showSuccessToast('Success', 'Service date updated successfully');
      
      return {
        ...result.serviceDate,
        serviceDate: new Date(result.serviceDate.serviceDate),
        completedDate: result.serviceDate.completedDate ? new Date(result.serviceDate.completedDate) : undefined,
        createdAt: new Date(result.serviceDate.createdAt),
        updatedAt: new Date(result.serviceDate.updatedAt),
      } as ServiceDate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update service date';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteServiceDate = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/amc/service-dates/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete service date');
      }

      showSuccessToast('Success', 'Service date deleted successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete service date';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const completeServiceDate = useCallback(async (
    id: string,
    data?: {
      completedDate?: Date;
      remarks?: string;
      technicianId?: string;
    }
  ) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/amc/service-dates/${id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data || {}),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to complete service date');
      }

      const result = await response.json();
      showSuccessToast('Success', 'Service date marked as completed');
      
      return {
        ...result.serviceDate,
        serviceDate: new Date(result.serviceDate.serviceDate),
        completedDate: result.serviceDate.completedDate ? new Date(result.serviceDate.completedDate) : undefined,
        createdAt: new Date(result.serviceDate.createdAt),
        updatedAt: new Date(result.serviceDate.updatedAt),
      } as ServiceDate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to complete service date';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    fetchServiceDates,
    fetchContractServiceDates,
    createServiceDate,
    updateServiceDate,
    deleteServiceDate,
    completeServiceDate,
  };
}
