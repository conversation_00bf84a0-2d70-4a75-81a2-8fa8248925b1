import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Service Schedule Repository
 *
 * This repository handles database operations for the Service Schedule entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class ServiceScheduleRepository extends PrismaRepository<
  Prisma.service_schedulesGetPayload<{}>,
  string,
  Prisma.service_schedulesCreateInput,
  Prisma.service_schedulesUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('service_schedules');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): ServiceScheduleRepository {
    return new ServiceScheduleRepository(tx);
  }

  /**
   * Find service schedules with related data
   * @param id Schedule ID
   * @returns Promise resolving to a service schedule with relations
   */
  async findWithRelations(id: string): Promise<any> {
    return this.model.findUnique({
      where: { id },
      include: {
        serviceReport: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                city: true,
                phone: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                phone: true,
              },
            },
          },
        },
        technician: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
      },
    });
  }

  /**
   * Find service schedules by service report ID
   * @param serviceReportId Service Report ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service schedules
   */
  async findByServiceReportId(serviceReportId: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { serviceReportId },
      skip,
      take,
      orderBy: { scheduledDate: 'asc' },
      include: {
        serviceReport: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                city: true,
              },
            },
          },
        },
        technician: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  /**
   * Find service schedules by status
   * @param status Schedule status
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service schedules
   */
  async findByStatus(status: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { status },
      skip,
      take,
      orderBy: { scheduledDate: 'asc' },
      include: {
        serviceReport: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                city: true,
              },
            },
          },
        },
        technician: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  /**
   * Find service schedules by technician ID
   * @param technicianId Technician ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service schedules
   */
  async findByTechnicianId(technicianId: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { technicianId },
      skip,
      take,
      orderBy: { scheduledDate: 'asc' },
      include: {
        serviceReport: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                city: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Find service schedules with filtering and pagination
   * @param filter Filter object
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Order by clause
   * @returns Promise resolving to an array of service schedules
   */
  async findWithFilter(
    filter: any = {},
    skip: number = 0,
    take: number = 50,
    orderBy?: any
  ): Promise<any[]> {
    try {
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { scheduledDate: 'asc' },
        include: {
          serviceReport: {
            include: {
              customer: {
                select: {
                  id: true,
                  name: true,
                  city: true,
                  phone: true,
                },
              },
              executive: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                },
              },
            },
          },
          technician: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
        },
      });

      return result;
    } catch (error) {
      console.error('ServiceScheduleRepository.findWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Count service schedules with filtering
   * @param filter Filter object
   * @returns Promise resolving to the count
   */
  async countWithFilter(filter: any = {}): Promise<number> {
    try {
      return await this.model.count({
        where: filter,
      });
    } catch (error) {
      console.error('ServiceScheduleRepository.countWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Find upcoming schedules (scheduled for today or future)
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of upcoming schedules
   */
  async findUpcoming(skip?: number, take?: number): Promise<any[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return this.model.findMany({
      where: {
        scheduledDate: {
          gte: today,
        },
        status: {
          in: ['SCHEDULED', 'IN_PROGRESS'],
        },
      },
      skip,
      take,
      orderBy: { scheduledDate: 'asc' },
      include: {
        serviceReport: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                city: true,
              },
            },
          },
        },
        technician: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  /**
   * Find overdue schedules (scheduled for past dates but not completed)
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of overdue schedules
   */
  async findOverdue(skip?: number, take?: number): Promise<any[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return this.model.findMany({
      where: {
        scheduledDate: {
          lt: today,
        },
        status: {
          in: ['SCHEDULED', 'IN_PROGRESS'],
        },
      },
      skip,
      take,
      orderBy: { scheduledDate: 'desc' },
      include: {
        serviceReport: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                city: true,
              },
            },
          },
        },
        technician: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }
}
