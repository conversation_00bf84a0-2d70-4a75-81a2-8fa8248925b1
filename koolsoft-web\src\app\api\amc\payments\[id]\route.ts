import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCPaymentRepository } from '@/lib/repositories';
import { updatePaymentSchema } from '@/lib/validations/amc-contract.schema';
import { z } from 'zod';

/**
 * GET /api/amc/payments/[id]
 * Get a specific payment by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcPaymentRepository = getAMCPaymentRepository();
      const payment = await amcPaymentRepository.findById(id);

      if (!payment) {
        return NextResponse.json(
          { error: 'Payment not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(payment);
    } catch (error) {
      console.error('Error fetching payment:', error);
      return NextResponse.json(
        { error: 'Failed to fetch payment' },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/amc/payments/[id]
 * Update a payment
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updatePaymentSchema.parse(body);

      const amcPaymentRepository = getAMCPaymentRepository();

      // Check if payment exists
      const existingPayment = await amcPaymentRepository.findById(id);
      if (!existingPayment) {
        return NextResponse.json(
          { error: 'Payment not found' },
          { status: 404 }
        );
      }

      // Check if receipt number is unique (if being updated)
      if (validatedData.receiptNo && validatedData.receiptNo !== existingPayment.receiptNo) {
        const isUnique = await amcPaymentRepository.isReceiptNumberUnique(validatedData.receiptNo, id);
        if (!isUnique) {
          return NextResponse.json(
            { error: 'Receipt number already exists' },
            { status: 400 }
          );
        }
      }

      // Update payment
      const updatedPayment = await amcPaymentRepository.update(id, validatedData);

      // Update contract paid amount if amount changed
      if (validatedData.amount !== undefined) {
        await amcPaymentRepository.updateContractPaidAmount(existingPayment.amcContractId);
      }

      return NextResponse.json(updatedPayment);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.errors },
          { status: 400 }
        );
      }

      console.error('Error updating payment:', error);
      
      if (error instanceof Error) {
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to update payment' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/amc/payments/[id]
 * Delete a payment
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcPaymentRepository = getAMCPaymentRepository();

      // Check if payment exists
      const existingPayment = await amcPaymentRepository.findById(id);
      if (!existingPayment) {
        return NextResponse.json(
          { error: 'Payment not found' },
          { status: 404 }
        );
      }

      // Delete payment
      await amcPaymentRepository.delete(id);

      // Update contract paid amount
      await amcPaymentRepository.updateContractPaidAmount(existingPayment.amcContractId);

      return NextResponse.json(
        { message: 'Payment deleted successfully' },
        { status: 200 }
      );
    } catch (error) {
      console.error('Error deleting payment:', error);
      
      if (error instanceof Error) {
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to delete payment' },
        { status: 500 }
      );
    }
  }
);
