import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

/**
 * Individual Service Center Reference Data API Handler
 * 
 * GET /api/reference/serviceCenters/[id] - Get a specific service center
 * PATCH /api/reference/serviceCenters/[id] - Update a service center (Admin/Manager only)
 * DELETE /api/reference/serviceCenters/[id] - Delete a service center (Admin only)
 */
export default async function handler(req, res) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { id } = req.query;
    if (!id) {
      return res.status(400).json({ error: 'Service center ID is required' });
    }

    switch (req.method) {
      case 'GET':
        return await handleGet(req, res, id);
      case 'PATCH':
        return await handlePatch(req, res, session, id);
      case 'DELETE':
        return await handleDelete(req, res, session, id);
      default:
        res.setHeader('Allow', ['GET', 'PATCH', 'DELETE']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Service Center Reference API Error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  } finally {
    await prisma.$disconnect();
  }
}

async function handleGet(req, res, id) {
  try {
    const serviceCenter = await prisma.serviceCenter.findUnique({
      where: { id }
    });

    if (!serviceCenter) {
      return res.status(404).json({ error: 'Service center not found' });
    }

    return res.status(200).json(serviceCenter);

  } catch (error) {
    console.error('Error fetching service center:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch service center',
      details: error.message 
    });
  }
}

async function handlePatch(req, res, session, id) {
  try {
    // Check if user is admin or manager
    if (!['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return res.status(403).json({ error: 'Admin or Manager access required' });
    }

    const {
      name,
      vendor,
      address,
      city,
      state,
      pincode,
      phone,
      email,
      contactPerson,
      active
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Service center name is required' });
    }

    // Check if service center exists
    const existingServiceCenter = await prisma.serviceCenter.findUnique({
      where: { id }
    });

    if (!existingServiceCenter) {
      return res.status(404).json({ error: 'Service center not found' });
    }

    // Check for duplicate name (excluding current record)
    const duplicateServiceCenter = await prisma.serviceCenter.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive'
        },
        id: {
          not: id
        }
      }
    });

    if (duplicateServiceCenter) {
      return res.status(400).json({ 
        error: 'A service center with this name already exists' 
      });
    }

    // Update service center
    const updatedServiceCenter = await prisma.serviceCenter.update({
      where: { id },
      data: {
        name,
        vendor,
        address,
        city,
        state,
        pincode,
        phone,
        email,
        contactPerson,
        active: active !== undefined ? active : existingServiceCenter.active
      }
    });

    return res.status(200).json({
      message: 'Service center updated successfully',
      data: updatedServiceCenter
    });

  } catch (error) {
    console.error('Error updating service center:', error);
    return res.status(500).json({ 
      error: 'Failed to update service center',
      details: error.message 
    });
  }
}

async function handleDelete(req, res, session, id) {
  try {
    // Check if user is admin
    if (session.user.role !== 'ADMIN') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Check if service center exists
    const existingServiceCenter = await prisma.serviceCenter.findUnique({
      where: { id }
    });

    if (!existingServiceCenter) {
      return res.status(404).json({ error: 'Service center not found' });
    }

    // Soft delete by setting active to false
    const deletedServiceCenter = await prisma.serviceCenter.update({
      where: { id },
      data: { active: false }
    });

    return res.status(200).json({
      message: 'Service center deleted successfully',
      data: deletedServiceCenter
    });

  } catch (error) {
    console.error('Error deleting service center:', error);
    return res.status(500).json({ 
      error: 'Failed to delete service center',
      details: error.message 
    });
  }
}
