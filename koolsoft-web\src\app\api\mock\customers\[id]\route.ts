import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/mock/customers/[id]
 * Get a mock customer by ID for testing
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Mock customer data
    const customer = {
      id,
      name: 'Test Customer',
      address: '123 Test Street',
      city: 'Test City',
      state: 'Test State',
      pinCode: '123456',
      phone: '************',
      fax: '************',
      email: '<EMAIL>',
      mobile: '************',
      website: 'https://example.com',
      phone1: '************',
      phone2: '************',
      phone3: '************',
      location: 'Test Location',
      birthDate: new Date('1990-01-01').toISOString(),
      birthYear: '1990',
      anniversaryDate: new Date('2010-01-01').toISOString(),
      anniversaryYear: '2010',
      segmentId: 1,
      designation: 'Test Designation',
      visitCardPath: '/test/path',
      originalId: 12345,
      isActive: true,
      createdAt: new Date('2023-01-01').toISOString(),
      updatedAt: new Date('2023-06-01').toISOString(),
      contacts: [
        {
          id: '1',
          name: 'Contact Person 1',
          designation: 'Manager',
          phone: '************',
          email: '<EMAIL>',
          isPrimary: true
        },
        {
          id: '2',
          name: 'Contact Person 2',
          designation: 'Assistant',
          phone: '************',
          email: '<EMAIL>',
          isPrimary: false
        }
      ],
      visitCards: [
        {
          id: '1',
          customerId: id,
          filePath: '/test/visitcard1.pdf',
          uploadDate: new Date('2023-01-15').toISOString(),
          notes: 'First visit card',
          createdAt: new Date('2023-01-15').toISOString(),
          updatedAt: new Date('2023-01-15').toISOString()
        },
        {
          id: '2',
          customerId: id,
          filePath: '/test/visitcard2.pdf',
          uploadDate: new Date('2023-03-20').toISOString(),
          notes: 'Second visit card',
          createdAt: new Date('2023-03-20').toISOString(),
          updatedAt: new Date('2023-03-20').toISOString()
        }
      ],
      amcContracts: [
        {
          id: '1',
          customerId: id,
          contractNumber: 'AMC-2023-001',
          startDate: new Date('2023-01-01').toISOString(),
          endDate: new Date('2023-12-31').toISOString(),
          status: 'ACTIVE',
          amount: 10000,
          machines: [
            {
              id: '1',
              serialNumber: 'SN-001',
              model: 'Model A',
              location: 'Main Office'
            },
            {
              id: '2',
              serialNumber: 'SN-002',
              model: 'Model B',
              location: 'Branch Office'
            }
          ],
          serviceDates: [
            {
              id: '1',
              date: new Date('2023-03-15').toISOString(),
              status: 'COMPLETED'
            },
            {
              id: '2',
              date: new Date('2023-06-15').toISOString(),
              status: 'SCHEDULED'
            }
          ],
          payments: [
            {
              id: '1',
              amount: 5000,
              date: new Date('2023-01-15').toISOString(),
              method: 'BANK_TRANSFER'
            }
          ],
          divisions: ['Division A', 'Division B']
        }
      ],
      warranties: [
        {
          id: '1',
          customerId: id,
          warrantyNumber: 'WTY-2023-001',
          startDate: new Date('2023-01-01').toISOString(),
          endDate: new Date('2024-01-01').toISOString(),
          status: 'ACTIVE',
          machines: [
            {
              id: '3',
              serialNumber: 'SN-003',
              model: 'Model C',
              location: 'Main Office'
            }
          ]
        }
      ]
    };

    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error fetching mock customer:', error);
    return NextResponse.json(
      { error: 'Failed to fetch mock customer' },
      { status: 500 }
    );
  }
}
