// Script to run the user migration
const { execSync } = require('child_process');
const path = require('path');

console.log('Starting user migration process...');

try {
  // Run the migration script
  console.log('Running migration script...');
  execSync('node ./scripts/migrate-legacy-users.js', {
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });

  console.log('Migration completed successfully!');
} catch (error) {
  console.error('Error running migration:', error.message);
  process.exit(1);
}
