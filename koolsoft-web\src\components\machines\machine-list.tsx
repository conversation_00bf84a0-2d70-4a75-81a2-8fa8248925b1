'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Trash2, Search, Plus, Settings } from 'lucide-react';
import { Machine } from '@/lib/hooks/useMachines';

interface MachineListProps {
  machines: Machine[];
  isLoading?: boolean;
  onEdit: (machine: Machine) => void;
  onDelete: (machineId: string) => Promise<void>;
  onAdd?: () => void;
  showAddButton?: boolean;
  title?: string;
}

export function MachineList({
  machines,
  isLoading = false,
  onEdit,
  onDelete,
  onAdd,
  showAddButton = true,
  title = 'Machines',
}: MachineListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Filter machines based on search term
  const filteredMachines = machines.filter((machine) =>
    machine.serialNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    machine.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    machine.brand?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    machine.product?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    machine.model?.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = async (machineId: string) => {
    try {
      setDeletingId(machineId);
      await onDelete(machineId);
    } catch (error) {
      console.error('Error deleting machine:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge variant="default">Active</Badge>;
      case 'INACTIVE':
        return <Badge variant="secondary">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="text-muted-foreground">Loading machines...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
        <div>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {title}
          </CardTitle>
          <p className="text-sm text-gray-100 mt-1">
            {machines.length} machine{machines.length !== 1 ? 's' : ''} total
          </p>
        </div>
        {showAddButton && onAdd && (
          <Button variant="secondary" onClick={onAdd}>
            <Plus className="h-4 w-4 mr-2" />
            Add Machine
          </Button>
        )}
      </CardHeader>
      <CardContent className="p-6">
        {/* Search */}
        <div className="flex items-center space-x-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search machines by serial number, location, brand, product, or model..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        {/* Machines Table */}
        {filteredMachines.length === 0 ? (
          <div className="text-center py-8">
            <Settings className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              {searchTerm ? 'No machines found' : 'No machines added yet'}
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              {searchTerm
                ? 'Try adjusting your search criteria'
                : 'Add machines to this AMC contract to get started'}
            </p>
            {showAddButton && onAdd && !searchTerm && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Machine
              </Button>
            )}
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Serial Number</TableHead>
                  <TableHead>Brand</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Model</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Tonnage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMachines.map((machine) => (
                  <TableRow key={machine.id}>
                    <TableCell className="font-medium">
                      {machine.serialNumber || 'N/A'}
                    </TableCell>
                    <TableCell>{machine.brand?.name || 'N/A'}</TableCell>
                    <TableCell>{machine.product?.name || 'N/A'}</TableCell>
                    <TableCell>{machine.model?.name || 'N/A'}</TableCell>
                    <TableCell>{machine.location || 'N/A'}</TableCell>
                    <TableCell>
                      {machine.tonnage ? `${machine.tonnage} TR` : 'N/A'}
                    </TableCell>
                    <TableCell>{getStatusBadge(machine.status)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(machine)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle className="text-black">
                                Delete Machine
                              </AlertDialogTitle>
                              <AlertDialogDescription className="text-black">
                                Are you sure you want to delete this machine? This action
                                cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDelete(machine.id)}
                                disabled={deletingId === machine.id}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                {deletingId === machine.id ? 'Deleting...' : 'Delete'}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
