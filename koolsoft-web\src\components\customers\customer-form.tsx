'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Plus, Trash2, X } from 'lucide-react';

// Define the customer form schema
const customerFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be at most 100 characters'),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pinCode: z.string().optional(),
  phone: z.string().optional(),
  phone1: z.string().optional().nullable(),
  phone2: z.string().optional().nullable(),
  phone3: z.string().optional().nullable(),
  fax: z.string().optional(),
  email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
  mobile: z.string().optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  location: z.string().optional(),
  isActive: z.boolean().default(true),
  contacts: z.array(
    z.object({
      id: z.string().optional(),
      name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be at most 100 characters'),
      designation: z.string().optional(),
      phone: z.string().optional(),
      email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
      isPrimary: z.boolean().default(false),
    })
  ).optional(),
})
// Transform the form data to match the API schema
.transform(data => {
  // Convert empty strings to undefined for optional fields
  const transformedData = { ...data };

  // Handle email and website fields
  if (transformedData.email === '') transformedData.email = undefined;
  if (transformedData.website === '') transformedData.website = undefined;

  // Handle contacts
  if (transformedData.contacts) {
    transformedData.contacts = transformedData.contacts.map(contact => {
      const transformedContact = { ...contact };
      if (transformedContact.email === '') transformedContact.email = undefined;
      return transformedContact;
    });
  }

  return transformedData;
});

// Define the form values type
type CustomerFormValues = z.infer<typeof customerFormSchema>;

// Define the component props
interface CustomerFormProps {
  customer?: any; // Optional customer data for editing
  onSuccess?: () => void; // Optional callback for success
}

/**
 * Customer Form Component
 *
 * This component provides a form for adding or editing customers.
 * It supports both creating new customers and editing existing ones.
 */
export function CustomerForm({ customer, onSuccess }: CustomerFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!customer;

  // Initialize form with react-hook-form
  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerFormSchema),
    defaultValues: {
      name: customer?.name || '',
      address: customer?.address || '',
      city: customer?.city || '',
      state: customer?.state || '',
      pinCode: customer?.pinCode || '',
      phone: customer?.phone || '',
      phone1: customer?.phone1 || '',
      phone2: customer?.phone2 || '',
      phone3: customer?.phone3 || '',
      fax: customer?.fax || '',
      email: customer?.email || '',
      mobile: customer?.mobile || '',
      website: customer?.website || '',
      location: customer?.location || '',
      isActive: customer?.isActive ?? true,
      contacts: customer?.contacts?.length
        ? customer.contacts.map((contact: any) => ({
            id: contact.id || undefined,
            name: contact.name || '',
            designation: contact.designation || '',
            phone: contact.phone || '',
            email: contact.email || '',
            isPrimary: contact.isPrimary ?? false,
          }))
        : [{ name: '', designation: '', phone: '', email: '', isPrimary: false }],
    },
  });

  // Set up field array for contacts
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'contacts',
  });

  // Handle form submission
  const onSubmit = async (values: CustomerFormValues) => {
    setIsSubmitting(true);

    try {
      const url = isEditMode ? `/api/customers/${customer.id}` : '/api/customers';
      const method = isEditMode ? 'PUT' : 'POST';

      // Convert form values to API format
      const apiData = {
        name: values.name,
        address: values.address || undefined,
        city: values.city || undefined,
        state: values.state || undefined,
        pinCode: values.pinCode || undefined,
        phone: values.phone || undefined,
        phone1: values.phone1 || undefined,
        phone2: values.phone2 || undefined,
        phone3: values.phone3 || undefined,
        fax: values.fax || undefined,
        email: values.email === '' ? undefined : values.email,
        mobile: values.mobile || undefined,
        website: values.website === '' ? undefined : values.website,
        location: values.location || undefined,
        isActive: values.isActive,
      };

      // Only include contacts if there are valid contacts
      if (values.contacts && values.contacts.length > 0) {
        const validContacts = values.contacts.filter(contact => contact.name.trim() !== '');
        if (validContacts.length > 0) {
          (apiData as any).contacts = validContacts.map(contact => ({
            id: contact.id || undefined,
            name: contact.name,
            designation: contact.designation || undefined,
            phone: contact.phone || undefined,
            email: contact.email === '' ? undefined : contact.email,
            isPrimary: contact.isPrimary ?? false
          }));
        }
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
          console.error('API Error Response:', errorData);
        } catch (e) {
          console.error('Failed to parse error response:', e);
          throw new Error(`Failed to ${isEditMode ? 'update' : 'create'} customer: Server returned ${response.status}`);
        }

        if (errorData.message) {
          throw new Error(errorData.message);
        } else if (errorData.details && Array.isArray(errorData.details)) {
          // Format Zod validation errors
          const errorMessages = errorData.details.map((err: any) =>
            `${err.path.join('.')}: ${err.message}`
          ).join(', ');
          throw new Error(`Validation error: ${errorMessages}`);
        }

        throw new Error(errorData.error || `Failed to ${isEditMode ? 'update' : 'create'} customer`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: `Customer ${isEditMode ? 'updated' : 'created'} successfully.`,
      });

      if (onSuccess) {
        onSuccess();
      } else {
        // Navigate to the customer details page
        router.push(`/customers/${data.id}`);
        router.refresh();
      }
    } catch (error: any) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} customer:`, error);
      toast({
        title: 'Error',
        description: error.message || `Failed to ${isEditMode ? 'update' : 'create'} customer. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add a new empty contact
  const addContact = () => {
    append({
      name: '',
      designation: '',
      phone: '',
      email: '',
      isPrimary: false
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Card>
          <CardHeader className="pb-3 flex flex-row items-center justify-between">
            <div>
              <CardTitle>{isEditMode ? 'Edit Customer' : 'Add New Customer'}</CardTitle>
              <CardDescription>
                {isEditMode
                  ? 'Update customer information and contact details.'
                  : 'Enter customer information and contact details.'}
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditMode ? 'Update Customer' : 'Create Customer'}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="Customer name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="Primary phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="mobile"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile</FormLabel>
                    <FormControl>
                      <Input placeholder="Mobile number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone1"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Phone 1</FormLabel>
                    <FormControl>
                      <Input placeholder="Additional phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone2"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Phone 2</FormLabel>
                    <FormControl>
                      <Input placeholder="Additional phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone3"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Phone 3</FormLabel>
                    <FormControl>
                      <Input placeholder="Additional phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Customer address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input placeholder="City" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="state"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>State</FormLabel>
                    <FormControl>
                      <Input placeholder="State" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pinCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>PIN Code</FormLabel>
                    <FormControl>
                      <Input placeholder="PIN code" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Location" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input placeholder="https://example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fax"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fax</FormLabel>
                    <FormControl>
                      <Input placeholder="Fax number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Mark this customer as active
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Contact Persons</CardTitle>
              <CardDescription>
                Add contact persons associated with this customer
              </CardDescription>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addContact}
              className="ml-auto"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Contact
            </Button>
          </CardHeader>
          <CardContent>
            {fields.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No contacts added. Click "Add Contact" to add a contact person.
              </div>
            ) : (
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="border rounded-md p-4 relative">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => remove(index)}
                      className="absolute top-2 right-2 h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`contacts.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name*</FormLabel>
                            <FormControl>
                              <Input placeholder="Contact name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`contacts.${index}.designation`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Designation</FormLabel>
                            <FormControl>
                              <Input placeholder="Designation" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`contacts.${index}.phone`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <Input placeholder="Phone number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`contacts.${index}.email`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input placeholder="Email" type="email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`contacts.${index}.isPrimary`}
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Primary Contact</FormLabel>
                              <FormDescription>
                                Mark as primary contact person
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>


      </form>
    </Form>
  );
}
