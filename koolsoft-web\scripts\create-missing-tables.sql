-- Create spare_types table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.spare_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    code VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    original_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create measurement_types table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.measurement_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    code VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    original_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data into spare_types
INSERT INTO public.spare_types (name, description, code, active)
VALUES 
    ('Electrical Component', 'Parts related to electrical systems', 'ELEC', TRUE),
    ('Mechanical Component', 'Parts related to mechanical systems', 'MECH', TRUE),
    ('Plumbing Component', 'Parts related to plumbing systems', 'PLMB', TRUE),
    ('HVAC Component', 'Parts related to heating and cooling systems', 'HVAC', TRUE),
    ('Electronic Component', 'Parts related to electronic systems', 'ELTN', TRUE),
    ('Structural Component', 'Parts related to structural elements', 'STRC', TRUE),
    ('Consumable', 'Parts that are regularly consumed or replaced', 'CONS', TRUE),
    ('Fastener', 'Screws, bolts, nuts, and other fastening components', 'FAST', TRUE),
    ('Gasket', 'Sealing components', 'GASK', TRUE),
    ('Filter', 'Filtering components', 'FLTR', TRUE);

-- Insert sample data into measurement_types
INSERT INTO public.measurement_types (name, description, code, active)
VALUES 
    ('Length', 'Measurement of physical distance', 'LEN', TRUE),
    ('Weight', 'Measurement of mass', 'WGT', TRUE),
    ('Volume', 'Measurement of three-dimensional space', 'VOL', TRUE),
    ('Temperature', 'Measurement of heat', 'TEMP', TRUE),
    ('Pressure', 'Measurement of force per unit area', 'PRES', TRUE),
    ('Flow Rate', 'Measurement of volume per unit time', 'FLOW', TRUE),
    ('Electrical Current', 'Measurement of electrical flow', 'CURR', TRUE),
    ('Voltage', 'Measurement of electrical potential', 'VOLT', TRUE),
    ('Resistance', 'Measurement of electrical resistance', 'OHMS', TRUE),
    ('Time', 'Measurement of duration', 'TIME', TRUE);
