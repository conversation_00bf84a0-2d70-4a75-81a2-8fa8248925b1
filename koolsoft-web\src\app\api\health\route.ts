import { NextResponse } from 'next/server';

/**
 * Health check endpoint to verify the API is working
 * @route GET /api/health
 * @returns {object} 200 - Success response with status and timestamp
 */
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '0.1.0',
  });
}
