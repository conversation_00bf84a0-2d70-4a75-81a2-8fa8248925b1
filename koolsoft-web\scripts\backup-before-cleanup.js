const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function createBackup() {
  try {
    console.log('Creating backup before duplicate cleanup...\n');

    const backupDir = path.join(__dirname, 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `backup-${timestamp}.json`);

    // Backup customers
    console.log('Backing up customers...');
    const customers = await prisma.customer.findMany({
      include: {
        contacts: true,
        amcContracts: {
          include: {
            payments: true
          }
        }
      }
    });

    // Backup AMC payments
    console.log('Backing up AMC payments...');
    const amcPayments = await prisma.amc_payments.findMany();

    const backup = {
      timestamp: new Date().toISOString(),
      customers: customers,
      amcPayments: amcPayments,
      counts: {
        customers: customers.length,
        amcPayments: amcPayments.length
      }
    };

    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    console.log(`✅ Backup created: ${backupFile}`);
    console.log(`Backed up ${customers.length} customers and ${amcPayments.length} AMC payments\n`);

    return backupFile;

  } catch (error) {
    console.error('Error creating backup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createBackup();
