"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/service/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/service/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./src/app/service/dashboard/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServiceDashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\nfunction ServiceDashboardPage() {\n    _s();\n    _s1();\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [period, setPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('MONTH');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceDashboardPage.useEffect\": ()=>{\n            loadStatistics();\n        }\n    }[\"ServiceDashboardPage.useEffect\"], [\n        period\n    ]);\n    const loadStatistics = async ()=>{\n        try {\n            const response = await fetch(\"/api/service/statistics?period=\".concat(period), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setStatistics(data.statistics);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load service statistics');\n            }\n        } catch (error) {\n            console.error('Error loading service statistics:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load service statistics');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        const colors = {\n            OPEN: 'text-orange-600',\n            IN_PROGRESS: 'text-blue-600',\n            COMPLETED: 'text-green-600',\n            CANCELLED: 'text-red-600',\n            PENDING: 'text-yellow-600'\n        };\n        return colors[status] || 'text-gray-600';\n    };\n    const getComplaintTypeColor = (type)=>{\n        const colors = {\n            REPAIR: 'text-red-600',\n            MAINTENANCE: 'text-blue-600',\n            INSTALLATION: 'text-green-600',\n            INSPECTION: 'text-yellow-600',\n            WARRANTY: 'text-purple-600',\n            OTHER: 'text-gray-600'\n        };\n        return colors[type] || 'text-gray-600';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Service Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                className: \"text-gray-100\",\n                                children: \"Loading service metrics and analytics...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-200 rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 40\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n            lineNumber: 57,\n            columnNumber: 12\n        }, this);\n    }\n    if (!statistics) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                \"Service Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            className: \"text-gray-100\",\n                            children: \"Failed to load service statistics.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dashboard, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Service Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                    value: period,\n                                    onValueChange: setPeriod,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                            className: \"w-[180px] bg-white text-primary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                placeholder: \"Select period\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                    value: \"WEEK\",\n                                                    children: \"This Week\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                    value: \"MONTH\",\n                                                    children: \"This Month\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                    value: \"QUARTER\",\n                                                    children: \"This Quarter\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                    value: \"YEAR\",\n                                                    children: \"This Year\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            className: \"text-gray-100\",\n                            children: [\n                                \"Service metrics and analytics for \",\n                                statistics.period.name.toLowerCase(),\n                                \" period.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Total Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.totalReports\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Open Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.openReports\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.completedReports\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Completion Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    statistics.overview.completionRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                statistics.period.name,\n                                \" Performance\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: [\n                                                \"Reports This \",\n                                                statistics.period.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: statistics.period.totalReports\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: [\n                                                \"Completed This \",\n                                                statistics.period.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: statistics.period.completedReports\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: [\n                                                statistics.period.name,\n                                                \" Completion Rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: [\n                                                statistics.period.completionRate.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Avg Resolution Time\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    statistics.overview.averageResolutionTime.toFixed(1),\n                                                    \" days\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Service Details\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.totalDetails\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Part Replacement Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    statistics.overview.partReplacementRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Unique Machines\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.uniqueSerialNumbers\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"bg-primary text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Status Breakdown\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: statistics.breakdowns.status.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium \".concat(getStatusColor(item.status)),\n                                                    children: item.status.replace('_', ' ')\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: item.count\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.status, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 57\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"bg-primary text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Complaint Type Breakdown\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: statistics.breakdowns.complaintType.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium \".concat(getComplaintTypeColor(item.complaintType)),\n                                                    children: item.complaintType\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: item.count\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.complaintType, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 64\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"bg-primary text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Most Common Problems\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: statistics.insights.mostCommonProblems.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium truncate flex-1 mr-2\",\n                                                    title: item.problem,\n                                                    children: item.problem\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-sm\",\n                                                    children: item.count\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 88\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"bg-primary text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Most Replaced Parts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: statistics.insights.mostReplacedParts.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium truncate flex-1 mr-2\",\n                                                    title: item.part,\n                                                    children: item.part\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-sm\",\n                                                    children: item.count\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 87\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 10\n    }, this);\n}\n_s(ServiceDashboardPage, \"cHLAL4XywB85jmaKy41PmFldicw=\");\n_c1 = ServiceDashboardPage;\n_s1(ServiceDashboardPage, \"cHLAL4XywB85jmaKy41PmFldicw=\");\n_c = ServiceDashboardPage;\nvar _c;\n$RefreshReg$(_c, \"ServiceDashboardPage\");\nvar _c1;\n$RefreshReg$(_c1, \"ServiceDashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/service/dashboard/page.tsx\n"));

/***/ })

});