'use client';

import { useState, useCallback } from 'react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

export interface Division {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  originalId?: number;
}

export interface AMCDivision {
  id?: string;
  divisionId: string;
  name?: string;
  description?: string;
  percentage: number;
  isPrimary: boolean;
}

export interface DivisionAssignmentResponse {
  contract: {
    id: string;
    customer: any;
  };
  divisions: AMCDivision[];
  allDivisions: Division[];
  statistics: {
    totalDivisions: number;
    availableDivisions: number;
    totalPercentage: number;
    isComplete: boolean;
    primaryDivision: string | null;
  };
}

export function useAMCDivisions(contractId?: string) {
  const [divisions, setDivisions] = useState<AMCDivision[]>([]);
  const [allDivisions, setAllDivisions] = useState<Division[]>([]);
  const [statistics, setStatistics] = useState<DivisionAssignmentResponse['statistics'] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDivisions = useCallback(async () => {
    if (!contractId) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/amc/contracts/${contractId}/divisions`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch divisions');
      }

      const data: DivisionAssignmentResponse = await response.json();
      setDivisions(data.divisions);
      setAllDivisions(data.allDivisions);
      setStatistics(data.statistics);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching divisions:', err);
    } finally {
      setIsLoading(false);
    }
  }, [contractId]);

  const updateDivisions = useCallback(async (newDivisions: AMCDivision[]) => {
    if (!contractId) return false;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/amc/contracts/${contractId}/divisions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ divisions: newDivisions }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update divisions');
      }

      const data = await response.json();
      setDivisions(data.divisions);
      showSuccessToast('Divisions updated successfully');
      
      // Refresh statistics
      await fetchDivisions();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      showErrorToast('Failed to update divisions', errorMessage);
      console.error('Error updating divisions:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [contractId, fetchDivisions]);

  const addDivision = useCallback((division: Omit<AMCDivision, 'id'>) => {
    const newDivision: AMCDivision = {
      ...division,
      id: Date.now().toString(), // Temporary ID for form management
    };
    setDivisions(prev => [...prev, newDivision]);
  }, []);

  const updateDivision = useCallback((divisionId: string, updates: Partial<AMCDivision>) => {
    setDivisions(prev => prev.map(div => 
      div.id === divisionId ? { ...div, ...updates } : div
    ));
  }, []);

  const removeDivision = useCallback((divisionId: string) => {
    setDivisions(prev => prev.filter(div => div.id !== divisionId));
  }, []);

  const validateDivisions = useCallback((divisionsToValidate: AMCDivision[]) => {
    const errors: string[] = [];

    // Check if percentages sum to 100
    const totalPercentage = divisionsToValidate.reduce((sum, div) => sum + div.percentage, 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      errors.push(`Division percentages must sum to 100% (current: ${totalPercentage.toFixed(2)}%)`);
    }

    // Check if only one primary division
    const primaryCount = divisionsToValidate.filter(div => div.isPrimary).length;
    if (primaryCount > 1) {
      errors.push('Only one division can be marked as primary');
    }

    // Check for duplicate divisions
    const divisionIds = divisionsToValidate.map(div => div.divisionId);
    const uniqueDivisionIds = new Set(divisionIds);
    if (divisionIds.length !== uniqueDivisionIds.size) {
      errors.push('Duplicate divisions are not allowed');
    }

    // Check for valid percentages
    const invalidPercentages = divisionsToValidate.filter(div => 
      div.percentage < 0 || div.percentage > 100
    );
    if (invalidPercentages.length > 0) {
      errors.push('All percentages must be between 0 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, []);

  const setPrimaryDivision = useCallback((divisionId: string) => {
    setDivisions(prev => prev.map(div => ({
      ...div,
      isPrimary: div.id === divisionId,
    })));
  }, []);

  const calculateRemainingPercentage = useCallback((excludeDivisionId?: string) => {
    const total = divisions
      .filter(div => div.id !== excludeDivisionId)
      .reduce((sum, div) => sum + div.percentage, 0);
    return Math.max(0, 100 - total);
  }, [divisions]);

  return {
    divisions,
    allDivisions,
    statistics,
    isLoading,
    error,
    fetchDivisions,
    updateDivisions,
    addDivision,
    updateDivision,
    removeDivision,
    validateDivisions,
    setPrimaryDivision,
    calculateRemainingPercentage,
  };
}

export function useAllDivisions() {
  const [divisions, setDivisions] = useState<Division[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDivisions = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/reference-data?type=divisions', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch divisions');
      }

      const result = await response.json();
      setDivisions(result || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching divisions:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    divisions,
    isLoading,
    error,
    fetchDivisions,
  };
}
