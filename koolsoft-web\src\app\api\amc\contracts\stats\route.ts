import { NextRequest, NextResponse } from 'next/server';
import { getAMCContractRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';

/**
 * GET /api/amc/contracts/stats
 * Get statistics for AMC contracts
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const amcContractRepository = getAMCContractRepository();
      const prisma = amcContractRepository.prisma;
      
      // Get current date
      const today = new Date();
      
      // Get total number of contracts
      const totalContracts = await prisma.amc_contracts.count();
      
      // Get active contracts
      const activeContracts = await prisma.amc_contracts.count({
        where: {
          status: 'ACTIVE',
          endDate: {
            gte: today,
          },
        },
      });
      
      // Get expired contracts
      const expiredContracts = await prisma.amc_contracts.count({
        where: {
          status: 'EXPIRED',
        },
      });
      
      // Get contracts expiring in the next 30 days
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + 30);
      
      const expiringContracts = await prisma.amc_contracts.count({
        where: {
          status: 'ACTIVE',
          endDate: {
            gte: today,
            lte: futureDate,
          },
        },
      });
      
      // Get renewed contracts
      const renewedContracts = await prisma.amc_contracts.count({
        where: {
          status: 'RENEWED',
        },
      });
      
      // Get total value of active contracts
      const activeContractsValue = await prisma.amc_contracts.aggregate({
        where: {
          status: 'ACTIVE',
          endDate: {
            gte: today,
          },
        },
        _sum: {
          amount: true,
        },
      });
      
      // Get contracts by month (for the current year)
      const currentYear = today.getFullYear();
      const startOfYear = new Date(currentYear, 0, 1);
      const endOfYear = new Date(currentYear, 11, 31);
      
      const contractsByMonth = await prisma.amc_contracts.groupBy({
        by: ['startDate'],
        where: {
          startDate: {
            gte: startOfYear,
            lte: endOfYear,
          },
        },
        _count: {
          id: true,
        },
      });
      
      // Process contracts by month
      const monthlyData = Array(12).fill(0);
      contractsByMonth.forEach((item) => {
        const month = new Date(item.startDate).getMonth();
        monthlyData[month] += item._count.id;
      });
      
      return NextResponse.json({
        totalContracts,
        activeContracts,
        expiredContracts,
        expiringContracts,
        renewedContracts,
        activeContractsValue: activeContractsValue._sum.amount || 0,
        contractsByMonth: monthlyData,
      });
    } catch (error) {
      console.error('Error fetching AMC contract statistics:', error);
      return NextResponse.json(
        { error: 'Failed to fetch AMC contract statistics' },
        { status: 500 }
      );
    }
  }
);
