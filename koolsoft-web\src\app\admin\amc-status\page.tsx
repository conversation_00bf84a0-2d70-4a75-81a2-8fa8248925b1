'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AMCStatusManagement } from '@/components/amc/amc-status-management';
import { 
  ArrowLeft, 
  RefreshCw, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Calendar,
  TrendingUp,
  Activity,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { showErrorToast } from '@/lib/toast';

interface ContractStats {
  total: number;
  active: number;
  expired: number;
  expiring: number;
}

/**
 * AMC Status Management Page
 * 
 * This admin page provides comprehensive contract status management capabilities
 * including manual status updates and contract statistics.
 */
export default function AMCStatusPage() {
  const [contractStats, setContractStats] = useState<ContractStats>({
    total: 0,
    active: 0,
    expired: 0,
    expiring: 0
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Fetch contract statistics
  useEffect(() => {
    const fetchContractStats = async () => {
      try {
        setIsLoadingStats(true);

        const response = await fetch('/api/amc/contracts?take=1000', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch contract statistics');
        }

        const data = await response.json();
        const contracts = data.contracts || [];

        // Calculate statistics
        const stats = {
          total: contracts.length,
          active: contracts.filter((c: any) => c.status === 'ACTIVE').length,
          expired: contracts.filter((c: any) => c.status === 'EXPIRED').length,
          expiring: contracts.filter((c: any) => {
            if (c.status !== 'ACTIVE') return false;
            const endDate = new Date(c.endDate);
            const today = new Date();
            const daysUntilExpiry = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
          }).length
        };

        setContractStats(stats);
      } catch (error) {
        console.error('Error fetching contract statistics:', error);
        showErrorToast('Error', 'Failed to fetch contract statistics');
      } finally {
        setIsLoadingStats(false);
      }
    };

    fetchContractStats();
  }, []);

  // Handle status update completion
  const handleStatusUpdate = () => {
    // Refresh statistics after status update
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>AMC Contract Status Management</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Monitor and manage AMC contract statuses across the system
            </CardDescription>
          </div>
          <Button asChild variant="secondary">
            <Link href="/admin">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Admin
            </Link>
          </Button>
        </CardHeader>
      </Card>

      {/* Contract Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-black">Total Contracts</p>
                <p className="text-2xl font-bold text-black">
                  {isLoadingStats ? '...' : contractStats.total}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-black">Active Contracts</p>
                <p className="text-2xl font-bold text-black">
                  {isLoadingStats ? '...' : contractStats.active}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-black">Expiring Soon</p>
                <p className="text-2xl font-bold text-black">
                  {isLoadingStats ? '...' : contractStats.expiring}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-black">Expired Contracts</p>
                <p className="text-2xl font-bold text-black">
                  {isLoadingStats ? '...' : contractStats.expired}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Management Interface */}
      <AMCStatusManagement onStatusUpdate={handleStatusUpdate} />

      {/* Quick Actions */}
      <Card>
        <CardHeader className="pb-3 bg-secondary text-black">
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Quick Actions</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/amc?status=ACTIVE">
                <div className="text-center">
                  <CheckCircle className="h-6 w-6 mx-auto mb-2 text-green-600" />
                  <p className="font-medium text-black">View Active Contracts</p>
                  <p className="text-sm text-black">Manage currently active AMC contracts</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/amc/expiring">
                <div className="text-center">
                  <Clock className="h-6 w-6 mx-auto mb-2 text-yellow-600" />
                  <p className="font-medium text-black">Expiring Contracts</p>
                  <p className="text-sm text-black">Review contracts expiring soon</p>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4">
              <Link href="/amc?status=EXPIRED">
                <div className="text-center">
                  <AlertCircle className="h-6 w-6 mx-auto mb-2 text-red-600" />
                  <p className="font-medium text-black">Expired Contracts</p>
                  <p className="text-sm text-black">Handle expired contracts</p>
                </div>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card>
        <CardHeader className="pb-3 bg-secondary text-black">
          <CardTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5" />
            <span>System Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-black">
                <strong>Automated Status Updates:</strong> The system automatically checks for expired contracts 
                and updates their status. Use the manual update feature above to trigger immediate status checks.
              </AlertDescription>
            </Alert>
            
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription className="text-black">
                <strong>Audit Trail:</strong> All status changes are logged in the activity logs with timestamps 
                and user information for compliance and audit purposes.
              </AlertDescription>
            </Alert>
            
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription className="text-black">
                <strong>Expiring Soon:</strong> Contracts are considered "expiring soon" when they have 30 days 
                or less remaining in their term. These contracts may need renewal attention.
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
