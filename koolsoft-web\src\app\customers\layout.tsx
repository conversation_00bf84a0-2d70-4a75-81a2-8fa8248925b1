'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { Users } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * Customers Layout Component
 *
 * This component provides a consistent layout for all customer-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function CustomersLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title based on the pathname
  let pageTitle = 'Customers';
  if (pathname !== '/customers') {
    if (pathname.includes('/edit')) {
      pageTitle = 'Edit Customer';
    } else if (pathname.includes('/new')) {
      pageTitle = 'New Customer';
    } else {
      // For customer detail pages, we'll use a generic title
      // The actual customer name will be shown in the page component
      pageTitle = 'Customer Details';
    }
  }

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Customers', href: '/customers', icon: <Users className="h-4 w-4" /> }
  ];

  // Add additional breadcrumb for subpages
  if (pathname !== '/customers') {
    // For customer detail pages, we'll set the label in the page component
    // since we need to fetch the customer name
    breadcrumbs.push({ label: pageTitle, current: true });
  }

  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
