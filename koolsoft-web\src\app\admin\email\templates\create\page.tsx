'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Save, ArrowLeft } from 'lucide-react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

/**
 * Create Email Template Page
 *
 * This page allows administrators to create new email templates.
 */
export default function CreateEmailTemplatePage() {
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    bodyHtml: '',
    bodyText: '',
    description: '',
    variables: [] as string[],
    isActive: true,
  });
  const router = useRouter();

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Extract variables from template content
  const extractVariables = () => {
    const variableRegex = /{{([^{}]+)}}/g;
    const bodyVariables = [...formData.bodyHtml.matchAll(variableRegex)].map(match => match[1]);
    const subjectVariables = [...formData.subject.matchAll(variableRegex)].map(match => match[1]);
    const allVariables = [...new Set([...bodyVariables, ...subjectVariables])];

    return allVariables;
  };

  // Create template
  const createTemplate = async () => {
    try {
      setSaving(true);

      // Extract variables before saving
      const variables = extractVariables();

      // Ensure bodyText is provided and valid
      let bodyText = formData.bodyText;
      if (!bodyText || bodyText.length < 10) {
        // Create a simple text version from bodyHtml if bodyText is not valid
        bodyText = formData.bodyHtml.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
        // Ensure it's at least 10 characters
        if (bodyText.length < 10) {
          bodyText = bodyText.padEnd(10, ' ');
        }
      }

      const templateData = {
        ...formData,
        bodyText,
        variables,
      };

      const response = await fetch('/api/email/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData),
      });

      if (!response.ok) {
        let errorMessage = `Server returned ${response.status} ${response.statusText}`;

        try {
          const errorData = await response.json();
          console.error('Error creating template:', errorData);

          if (errorData && errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData && errorData.details) {
            // Handle Zod validation errors
            errorMessage = `Validation error: ${JSON.stringify(errorData.details)}`;
          }
        } catch (parseError) {
          // If we can't parse the response as JSON, use the text content
          const errorText = await response.text();
          console.error('Error response text:', errorText);
          errorMessage = errorText || errorMessage;
        }

        showErrorToast(errorMessage);
        return;
      }

      const createdTemplate = await response.json();
      showSuccessToast('Template created successfully');

      // Navigate to templates list
      router.push('/admin/email/templates');
    } catch (error) {
      console.error('Error creating template:', error);
      showErrorToast(`Failed to create template: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  // Go back to templates list
  const goBack = () => {
    router.push('/admin/email/templates');
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-black">Create Email Template</h1>
        <Button variant="outline" onClick={goBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Templates
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-black">Template Details</CardTitle>
            <CardDescription>
              Create a new email template
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-black">Template Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="text-black"
                    placeholder="welcome-email"
                  />
                  <p className="text-xs text-gray-500">
                    Use a unique identifier for this template
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="subject" className="text-black">Subject</Label>
                  <Input
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="text-black"
                    placeholder="Welcome to KoolSoft"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-black">Description</Label>
                <Input
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="text-black"
                  placeholder="Welcome email for new users"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bodyHtml" className="text-black">HTML Content</Label>
                <Textarea
                  id="bodyHtml"
                  name="bodyHtml"
                  value={formData.bodyHtml}
                  onChange={handleInputChange}
                  className="text-black min-h-[200px] font-mono"
                  rows={10}
                  placeholder={`<div style="font-family: Arial, sans-serif;">
  <h1>Welcome, \${'{'}name{'}'}!</h1>
  <p>Thank you for joining KoolSoft.</p>
</div>`}
                />
                <p className="text-sm text-gray-500">
                  Use {'{{'}<span>variableName</span>{'}}'} syntax for dynamic content
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bodyText" className="text-black">Plain Text Content</Label>
                <Textarea
                  id="bodyText"
                  name="bodyText"
                  value={formData.bodyText}
                  onChange={handleInputChange}
                  className="text-black min-h-[150px] font-mono"
                  rows={6}
                  placeholder={`Welcome, \${'{'}name{'}'} !

Thank you for joining KoolSoft.`}
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="isActive" className="text-black">Active</Label>
              </div>

              <div className="pt-4">
                <Button
                  onClick={createTemplate}
                  disabled={saving || !formData.name || !formData.subject || !formData.bodyHtml}
                  className="w-full md:w-auto"
                >
                  {saving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  <Save className="h-4 w-4 mr-2" />
                  Create Template
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
