import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * AMC Machine Repository
 * 
 * This repository handles database operations for the AMC Machine entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class AMCMachineRepository extends PrismaRepository<
  Prisma.amc_machinesGetPayload<{}>,
  string,
  Prisma.amc_machinesCreateInput,
  Prisma.amc_machinesUpdateInput
> {
  constructor() {
    super('amc_machines');
  }

  /**
   * Find AMC machines by contract ID
   * @param amcContractId AMC contract ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of AMC machines
   */
  async findByContractId(amcContractId: string, skip?: number, take?: number): Promise<Prisma.amc_machinesGetPayload<{}>[]> {
    return this.model.findMany({
      where: { amcContractId },
      skip,
      take,
    });
  }

  /**
   * Find AMC machine by serial number
   * @param serialNumber Machine serial number
   * @returns Promise resolving to the AMC machine or null if not found
   */
  async findBySerialNumber(serialNumber: string): Promise<Prisma.amc_machinesGetPayload<{}> | null> {
    return this.model.findFirst({
      where: { serialNumber },
    });
  }

  /**
   * Find AMC machine by original ID
   * @param originalAmcId Original AMC ID from legacy table
   * @param originalAssetNo Original asset number from legacy table
   * @returns Promise resolving to the AMC machine or null if not found
   */
  async findByOriginalId(originalAmcId: number, originalAssetNo: number): Promise<Prisma.amc_machinesGetPayload<{}> | null> {
    return this.model.findFirst({
      where: {
        originalAmcId,
        originalAssetNo,
      },
    });
  }

  /**
   * Find AMC machine with all related data
   * @param id AMC machine ID
   * @returns Promise resolving to the AMC machine with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        amcContract: true,
        product: true,
        model: true,
        brand: true,
        components: true,
      },
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.amc_machinesGetPayload<{}>,
    string,
    Prisma.amc_machinesCreateInput,
    Prisma.amc_machinesUpdateInput
  > {
    const repo = new AMCMachineRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
