'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Calendar,
  TrendingUp,
  Activity
} from 'lucide-react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

interface StatusUpdateResult {
  updatedCount: number;
  totalChecked: number;
  updatedContracts: Array<{
    id: string;
    contractNumber?: string;
    customerName: string;
    endDate: string;
    previousStatus: string;
    newStatus: string;
  }>;
}

interface AMCStatusManagementProps {
  onStatusUpdate?: (result: StatusUpdateResult) => void;
}

/**
 * AMC Status Management Component
 * 
 * This component provides an interface for administrators to manually trigger
 * contract status updates and view the results of the update process.
 */
export function AMCStatusManagement({ onStatusUpdate }: AMCStatusManagementProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdateResult, setLastUpdateResult] = useState<StatusUpdateResult | null>(null);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);

  // Handle manual status update
  const handleUpdateStatuses = async () => {
    try {
      setIsUpdating(true);

      const response = await fetch('/api/amc/contracts/update-statuses', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update contract statuses');
      }

      const result = await response.json();
      
      setLastUpdateResult(result);
      setLastUpdateTime(new Date());
      
      if (result.updatedCount > 0) {
        showSuccessToast(
          'Status Update Complete',
          `Successfully updated ${result.updatedCount} contract(s) to EXPIRED status`
        );
      } else {
        showSuccessToast(
          'Status Check Complete',
          'All contracts are up to date - no status changes required'
        );
      }

      onStatusUpdate?.(result);
    } catch (error) {
      console.error('Error updating contract statuses:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update contract statuses';
      showErrorToast('Update Failed', errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Status Update Control */}
      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Contract Status Management</span>
          </CardTitle>
          <CardDescription className="text-gray-100">
            Manually trigger contract status updates to mark expired contracts
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <p className="text-black font-medium">Manual Status Update</p>
              <p className="text-sm text-black">
                Check all active contracts and update those that have passed their end date to EXPIRED status.
              </p>
              {lastUpdateTime && (
                <p className="text-xs text-black">
                  Last update: {lastUpdateTime.toLocaleString()}
                </p>
              )}
            </div>
            <Button 
              onClick={handleUpdateStatuses}
              disabled={isUpdating}
              className="ml-4"
            >
              {isUpdating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Update Statuses
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Update Results */}
      {lastUpdateResult && (
        <Card>
          <CardHeader className="pb-3 bg-secondary text-black">
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Last Update Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Summary Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-black">Contracts Checked</p>
                      <p className="text-lg font-semibold text-black">{lastUpdateResult.totalChecked}</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-sm font-medium text-black">Updated to EXPIRED</p>
                      <p className="text-lg font-semibold text-black">{lastUpdateResult.updatedCount}</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5 text-yellow-600" />
                    <div>
                      <p className="text-sm font-medium text-black">Update Time</p>
                      <p className="text-sm font-semibold text-black">
                        {lastUpdateTime?.toLocaleTimeString() || 'N/A'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Updated Contracts List */}
              {lastUpdateResult.updatedContracts.length > 0 ? (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-black">Updated Contracts:</h4>
                  <div className="space-y-2">
                    {lastUpdateResult.updatedContracts.map((contract, index) => (
                      <div key={contract.id} className="border rounded-lg p-3 bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-black">
                              {contract.contractNumber || `Contract #${contract.id.slice(0, 8)}`}
                            </p>
                            <p className="text-xs text-black">{contract.customerName}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-black">
                              Ended: {new Date(contract.endDate).toLocaleDateString()}
                            </p>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                                {contract.previousStatus}
                              </Badge>
                              <span className="text-xs text-black">→</span>
                              <Badge className="bg-red-100 text-red-800 text-xs">
                                {contract.newStatus}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription className="text-black">
                    No contracts required status updates. All contracts are current.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Information Card */}
      <Card>
        <CardHeader className="pb-3 bg-secondary text-black">
          <CardTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5" />
            <span>How Status Updates Work</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-3 text-sm text-black">
            <p>
              <strong>Automatic Detection:</strong> The system identifies contracts with end dates in the past that still have ACTIVE status.
            </p>
            <p>
              <strong>Status Change:</strong> These contracts are automatically updated from ACTIVE to EXPIRED status.
            </p>
            <p>
              <strong>Activity Logging:</strong> All status changes are logged with timestamps and user information for audit purposes.
            </p>
            <p>
              <strong>Safe Operation:</strong> Only contracts that have genuinely expired are affected. Active contracts within their term remain unchanged.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
