import {
  formatDate,
  truncateString,
  formatCurrency,
  capitalizeWords,
  generateRandomString,
  isValidEmail,
  formatPhoneNumber,
  toKebabCase
} from '@/lib/utils';

describe('Utils', () => {
  describe('formatDate', () => {
    it('should format a date string correctly', () => {
      expect(formatDate('2023-01-15')).toMatch(/Jan 15, 2023/);
    });

    it('should format a Date object correctly', () => {
      const date = new Date(2023, 0, 15); // Jan 15, 2023
      expect(formatDate(date)).toMatch(/Jan 15, 2023/);
    });

    it('should return empty string for null or undefined', () => {
      expect(formatDate(null)).toBe('');
      expect(formatDate(undefined)).toBe('');
    });

    it('should return empty string for invalid date', () => {
      expect(formatDate('invalid-date')).toBe('');
    });
  });

  describe('truncateString', () => {
    it('should truncate a string longer than the specified length', () => {
      const longString = 'This is a very long string that needs to be truncated';
      expect(truncateString(longString, 10)).toBe('This is a ...');
    });

    it('should not truncate a string shorter than the specified length', () => {
      const shortString = 'Short';
      expect(truncateString(shortString, 10)).toBe(shortString);
    });

    it('should use default length of 50 if not specified', () => {
      const longString = 'a'.repeat(60);
      expect(truncateString(longString)).toBe('a'.repeat(50) + '...');
    });

    it('should return empty string for falsy values', () => {
      expect(truncateString('')).toBe('');
    });
  });

  describe('formatCurrency', () => {
    it('should format a number as INR currency by default', () => {
      expect(formatCurrency(1000)).toMatch(/₹1,000.00/);
    });

    it('should format a string number as currency', () => {
      expect(formatCurrency('2000', 'USD')).toMatch(/\$2,000.00/);
    });

    it('should return empty string for null or undefined values', () => {
      expect(formatCurrency(null as any)).toBe('');
      expect(formatCurrency(undefined as any)).toBe('');
    });

    it('should return empty string for NaN values', () => {
      expect(formatCurrency('not-a-number' as any)).toBe('');
    });
  });

  describe('capitalizeWords', () => {
    it('should capitalize the first letter of each word', () => {
      expect(capitalizeWords('hello world')).toBe('Hello World');
    });

    it('should convert rest of the word to lowercase', () => {
      expect(capitalizeWords('hELLO wORLD')).toBe('Hello World');
    });

    it('should return empty string for falsy values', () => {
      expect(capitalizeWords('')).toBe('');
    });
  });

  describe('generateRandomString', () => {
    it('should generate a string of the specified length', () => {
      expect(generateRandomString(10)).toHaveLength(10);
    });

    it('should generate a string of default length 8 if not specified', () => {
      expect(generateRandomString()).toHaveLength(8);
    });

    it('should generate different strings on multiple calls', () => {
      const str1 = generateRandomString();
      const str2 = generateRandomString();
      expect(str1).not.toBe(str2);
    });
  });

  describe('isValidEmail', () => {
    it('should return true for valid email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should return false for invalid email addresses', () => {
      expect(isValidEmail('not-an-email')).toBe(false);
      expect(isValidEmail('missing@domain')).toBe(false);
      expect(isValidEmail('@domain.com')).toBe(false);
      expect(isValidEmail('user@.com')).toBe(false);
    });
  });

  describe('formatPhoneNumber', () => {
    it('should format a 10-digit phone number correctly', () => {
      expect(formatPhoneNumber('1234567890')).toBe('(*************');
    });

    it('should clean non-numeric characters before formatting', () => {
      expect(formatPhoneNumber('(*************')).toBe('(*************');
      expect(formatPhoneNumber('************')).toBe('(*************');
    });

    it('should return the original string if not a 10-digit number', () => {
      expect(formatPhoneNumber('12345')).toBe('12345');
      expect(formatPhoneNumber('1234567890123')).toBe('1234567890123');
    });

    it('should return empty string for falsy values', () => {
      expect(formatPhoneNumber('')).toBe('');
    });
  });

  describe('toKebabCase', () => {
    it('should convert camelCase to kebab-case', () => {
      expect(toKebabCase('camelCaseString')).toBe('camel-case-string');
    });

    it('should convert spaces to hyphens', () => {
      expect(toKebabCase('string with spaces')).toBe('string-with-spaces');
    });

    it('should convert to lowercase', () => {
      expect(toKebabCase('MixedCASE')).toBe('mixed-case');
    });

    it('should return empty string for falsy values', () => {
      expect(toKebabCase('')).toBe('');
    });
  });
});