# KoolSoft Modernization Project - Data Flow Diagrams

This document provides data flow diagrams for key processes in the KoolSoft application to guide Augment AI implementation.

## 1. Authentication Flow

```
┌─────────┐     ┌─────────────┐     ┌──────────┐     ┌─────────┐
│  Login  │────▶│ Credentials │────▶│ NextAuth │────▶│ Success │
│  Form   │     │ Provider    │     │ Handler  │     │ Redirect│
└─────────┘     └─────────────┘     └──────────┘     └─────────┘
                       │                  │
                       │                  ▼
                       │            ┌──────────┐
                       └───────────▶│  Error   │
                                    │ Handling │
                                    └──────────┘
```

**Process Description:**
1. User enters credentials in the login form
2. Credentials are sent to NextAuth.js Credentials Provider
3. NextAuth handler validates credentials against the database
4. On success, user is redirected to dashboard
5. On failure, error is displayed to the user

## 2. AMC Creation Flow

```
┌─────────┐     ┌─────────────┐     ┌──────────┐     ┌─────────┐
│ Customer│────▶│ Contract    │────▶│ Machine  │────▶│ Payment │
│ Selection│    │ Details     │     │ Details  │     │ Details │
└─────────┘     └─────────────┘     └──────────┘     └─────────┘
                                                          │
┌─────────┐     ┌─────────────┐     ┌──────────┐         │
│ Success │◀────│ Database    │◀────│ Review & │◀────────┘
│ Message │     │ Transaction │     │ Confirm  │
└─────────┘     └─────────────┘     └──────────┘
```

**Process Description:**
1. User selects a customer from the database or creates a new one
2. User enters contract details (dates, amount, terms)
3. User adds machines covered under the AMC
4. User enters payment details and schedule
5. User reviews all information and confirms
6. System creates AMC record in a database transaction
7. Success message is displayed with AMC details

## 3. Module Conversion Flow

```
┌─────────┐     ┌─────────────┐     ┌──────────┐     ┌─────────┐
│ Source  │────▶│ Conversion  │────▶│ Target   │────▶│ History │
│ Module  │     │ Parameters  │     │ Module   │     │ Card    │
└─────────┘     └─────────────┘     └──────────┘     │ Update  │
                                                     └─────────┘
                                                          │
┌─────────┐     ┌─────────────┐                          │
│ Email   │◀────│ Conversion  │◀─────────────────────────┘
│ Notify  │     │ Complete    │
└─────────┘     └─────────────┘
```

**Process Description:**
1. User selects source module (In-Warranty, AMC, etc.)
2. User configures conversion parameters
3. System creates target module with data from source
4. System updates history card to track the conversion
5. System marks conversion as complete
6. Email notification is sent to relevant stakeholders

## 4. Reporting Data Flow

```
┌─────────┐     ┌─────────────┐     ┌──────────┐     ┌─────────┐
│ Report  │────▶│ Parameter   │────▶│ Data     │────▶│ Report  │
│ Selection│    │ Input       │     │ Fetching │     │ Rendering│
└─────────┘     └─────────────┘     └──────────┘     └─────────┘
                                                          │
┌─────────┐     ┌─────────────┐     ┌──────────┐         │
│ Email   │◀────│ Export      │◀────│ User     │◀────────┘
│ Delivery│     │ Generation  │     │ Actions  │
└─────────┘     └─────────────┘     └──────────┘
```

**Process Description:**
1. User selects a report type from the dashboard
2. User enters report parameters (date range, filters, etc.)
3. System fetches data based on parameters
4. System renders the report with tables and charts
5. User can view, export (PDF/Excel), or email the report
6. System generates export files or sends email as requested

## 5. Service Management Flow

```
┌─────────┐     ┌─────────────┐     ┌──────────┐     ┌─────────┐
│ Service │────▶│ Technician  │────▶│ Service  │────▶│ Customer│
│ Request │     │ Assignment  │     │ Execution│     │ Approval│
└─────────┘     └─────────────┘     └──────────┘     └─────────┘
                                                          │
┌─────────┐     ┌─────────────┐     ┌──────────┐         │
│ Invoice │◀────│ Report      │◀────│ Service  │◀────────┘
│ Generation    │ Generation  │     │ Closure  │
└─────────┘     └─────────────┘     └──────────┘
```

**Process Description:**
1. Service request is created (from customer call or scheduled maintenance)
2. Technician is assigned based on availability and expertise
3. Technician performs service and records details
4. Customer approves the service completion
5. Service is marked as closed
6. Service report is generated
7. Invoice is generated if applicable (for out-of-warranty services)

## 6. Customer Management Flow

```
┌─────────┐     ┌─────────────┐     ┌──────────┐
│ Customer│────▶│ Customer    │────▶│ Related  │
│ Creation│     │ Details     │     │ Records  │
└─────────┘     └─────────────┘     └──────────┘
                       │                  │
                       ▼                  ▼
                ┌─────────────┐     ┌──────────┐
                │ Contact     │     │ History  │
                │ Management  │     │ Tracking │
                └─────────────┘     └──────────┘
```

**Process Description:**
1. New customer is created with basic information
2. Detailed customer information is added (address, contacts, etc.)
3. Related records are linked (AMCs, warranties, services)
4. Contact information is managed (multiple contacts per customer)
5. Customer history is tracked for all interactions

## 7. Email Notification Flow

```
┌─────────┐     ┌─────────────┐     ┌──────────┐     ┌─────────┐
│ Trigger │────▶│ Template    │────▶│ Content  │────▶│ Delivery│
│ Event   │     │ Selection   │     │ Generation    │ Queue   │
└─────────┘     └─────────────┘     └──────────┘     └─────────┘
                                                          │
┌─────────┐     ┌─────────────┐                          │
│ Delivery│◀────│ Retry       │◀─────────────────────────┘
│ Status  │     │ Mechanism   │
└─────────┘     └─────────────┘
```

**Process Description:**
1. System event triggers email notification (AMC expiry, service scheduled, etc.)
2. Appropriate email template is selected
3. Email content is generated with dynamic data
4. Email is queued for delivery
5. Delivery is attempted with retry mechanism for failures
6. Delivery status is recorded for tracking
