import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getServiceReportRepository } from '@/lib/repositories';
import { 
  serviceReportQuerySchema, 
  createServiceReportSchema 
} from '@/lib/validations/service.schema';

/**
 * GET /api/service
 * Get service reports with optional filtering and pagination
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const queryParams = Object.fromEntries(searchParams.entries());

      // Validate query parameters
      const validatedQuery = serviceReportQuerySchema.parse(queryParams);

      const serviceReportRepository = getServiceReportRepository();

      // Build filter object
      const filter: any = {};

      if (validatedQuery.customerId) {
        filter.customerId = validatedQuery.customerId;
      }

      if (validatedQuery.executiveId) {
        filter.executiveId = validatedQuery.executiveId;
      }

      if (validatedQuery.status) {
        filter.status = validatedQuery.status;
      }

      if (validatedQuery.complaintType) {
        filter.complaintType = validatedQuery.complaintType;
      }

      // Date range filters
      if (validatedQuery.reportDateFrom || validatedQuery.reportDateTo) {
        filter.reportDate = {};
        if (validatedQuery.reportDateFrom) {
          filter.reportDate.gte = validatedQuery.reportDateFrom;
        }
        if (validatedQuery.reportDateTo) {
          filter.reportDate.lte = validatedQuery.reportDateTo;
        }
      }

      if (validatedQuery.visitDateFrom || validatedQuery.visitDateTo) {
        filter.visitDate = {};
        if (validatedQuery.visitDateFrom) {
          filter.visitDate.gte = validatedQuery.visitDateFrom;
        }
        if (validatedQuery.visitDateTo) {
          filter.visitDate.lte = validatedQuery.visitDateTo;
        }
      }

      if (validatedQuery.completionDateFrom || validatedQuery.completionDateTo) {
        filter.completionDate = {};
        if (validatedQuery.completionDateFrom) {
          filter.completionDate.gte = validatedQuery.completionDateFrom;
        }
        if (validatedQuery.completionDateTo) {
          filter.completionDate.lte = validatedQuery.completionDateTo;
        }
      }

      // Search filter
      if (validatedQuery.search) {
        filter.OR = [
          {
            customer: {
              name: {
                contains: validatedQuery.search,
                mode: 'insensitive',
              },
            },
          },
          {
            natureOfService: {
              contains: validatedQuery.search,
              mode: 'insensitive',
            },
          },
          {
            actionTaken: {
              contains: validatedQuery.search,
              mode: 'insensitive',
            },
          },
          {
            remarks: {
              contains: validatedQuery.search,
              mode: 'insensitive',
            },
          },
        ];
      }

      // Build order by
      const orderBy: any = {};
      if (validatedQuery.sortBy === 'customer') {
        orderBy.customer = { name: validatedQuery.sortOrder };
      } else if (validatedQuery.sortBy === 'executive') {
        orderBy.executive = { name: validatedQuery.sortOrder };
      } else {
        orderBy[validatedQuery.sortBy] = validatedQuery.sortOrder;
      }

      // Calculate pagination
      const skip = (validatedQuery.page - 1) * validatedQuery.limit;
      const take = validatedQuery.limit;

      // Get service reports and total count
      const [serviceReports, total] = await Promise.all([
        serviceReportRepository.findWithFilter(filter, skip, take, orderBy),
        serviceReportRepository.countWithFilter(filter),
      ]);

      // Calculate pagination info
      const totalPages = Math.ceil(total / validatedQuery.limit);
      const hasNextPage = validatedQuery.page < totalPages;
      const hasPreviousPage = validatedQuery.page > 1;

      return NextResponse.json({
        serviceReports,
        pagination: {
          page: validatedQuery.page,
          limit: validatedQuery.limit,
          total,
          totalPages,
          hasNextPage,
          hasPreviousPage,
        },
      });
    } catch (error) {
      console.error('Error fetching service reports:', error);
      
      if (error instanceof Error && error.message.includes('Invalid filter field')) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch service reports' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/service
 * Create a new service report
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createServiceReportSchema.parse(body);

      const serviceReportRepository = getServiceReportRepository();

      // Extract details from the validated data
      const { details, ...serviceReportData } = validatedData;

      // Create the service report with details
      const serviceReport = await serviceReportRepository.createWithDetails(
        serviceReportData,
        details
      );

      return NextResponse.json(
        { 
          message: 'Service report created successfully',
          serviceReport 
        },
        { status: 201 }
      );
    } catch (error) {
      console.error('Error creating service report:', error);

      if (error instanceof Error) {
        // Handle validation errors
        if (error.message.includes('validation')) {
          return NextResponse.json(
            { error: 'Validation failed', details: error.message },
            { status: 400 }
          );
        }

        // Handle database constraint errors
        if (error.message.includes('foreign key constraint')) {
          return NextResponse.json(
            { error: 'Invalid customer or executive ID' },
            { status: 400 }
          );
        }

        // Handle unique constraint errors
        if (error.message.includes('unique constraint')) {
          return NextResponse.json(
            { error: 'Service report with this information already exists' },
            { status: 409 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Failed to create service report' },
        { status: 500 }
      );
    }
  }
);
