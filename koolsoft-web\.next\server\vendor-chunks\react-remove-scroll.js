"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-remove-scroll";
exports.ids = ["vendor-chunks/react-remove-scroll"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-remove-scroll/dist/es2015/Combination.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _UI__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UI */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/UI.js\");\n/* harmony import */ var _sidecar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sidecar */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/sidecar.js\");\n\n\n\n\nvar ReactRemoveScroll = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function (props, ref) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_UI__WEBPACK_IMPORTED_MODULE_2__.RemoveScroll, (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({}, props, {\n    ref: ref,\n    sideCar: _sidecar__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n  }));\n});\nReactRemoveScroll.classNames = _UI__WEBPACK_IMPORTED_MODULE_2__.RemoveScroll.classNames;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactRemoveScroll);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcmVtb3ZlLXNjcm9sbC9kaXN0L2VzMjAxNS9Db21iaW5hdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBaUM7QUFDRjtBQUNLO0FBQ0o7QUFDaEMsSUFBSUksaUJBQWlCLEdBQUdILDZDQUFnQixDQUFDLFVBQVVLLEtBQUssRUFBRUMsR0FBRyxFQUFFO0VBQUUsT0FBUU4sZ0RBQW1CLENBQUNDLDZDQUFZLEVBQUVGLCtDQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUVNLEtBQUssRUFBRTtJQUFFQyxHQUFHLEVBQUVBLEdBQUc7SUFBRUUsT0FBTyxFQUFFTixnREFBT0E7RUFBQyxDQUFDLENBQUMsQ0FBQztBQUFHLENBQUMsQ0FBQztBQUNwS0MsaUJBQWlCLENBQUNNLFVBQVUsR0FBR1IsNkNBQVksQ0FBQ1EsVUFBVTtBQUN0RCxpRUFBZU4saUJBQWlCIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xccmVhY3QtcmVtb3ZlLXNjcm9sbFxcZGlzdFxcZXMyMDE1XFxDb21iaW5hdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBfX2Fzc2lnbiB9IGZyb20gXCJ0c2xpYlwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUmVtb3ZlU2Nyb2xsIH0gZnJvbSAnLi9VSSc7XG5pbXBvcnQgU2lkZUNhciBmcm9tICcuL3NpZGVjYXInO1xudmFyIFJlYWN0UmVtb3ZlU2Nyb2xsID0gUmVhY3QuZm9yd2FyZFJlZihmdW5jdGlvbiAocHJvcHMsIHJlZikgeyByZXR1cm4gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoUmVtb3ZlU2Nyb2xsLCBfX2Fzc2lnbih7fSwgcHJvcHMsIHsgcmVmOiByZWYsIHNpZGVDYXI6IFNpZGVDYXIgfSkpKTsgfSk7XG5SZWFjdFJlbW92ZVNjcm9sbC5jbGFzc05hbWVzID0gUmVtb3ZlU2Nyb2xsLmNsYXNzTmFtZXM7XG5leHBvcnQgZGVmYXVsdCBSZWFjdFJlbW92ZVNjcm9sbDtcbiJdLCJuYW1lcyI6WyJfX2Fzc2lnbiIsIlJlYWN0IiwiUmVtb3ZlU2Nyb2xsIiwiU2lkZUNhciIsIlJlYWN0UmVtb3ZlU2Nyb2xsIiwiZm9yd2FyZFJlZiIsInByb3BzIiwicmVmIiwiY3JlYXRlRWxlbWVudCIsInNpZGVDYXIiLCJjbGFzc05hbWVzIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-remove-scroll/dist/es2015/SideEffect.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScrollSideCar: () => (/* binding */ RemoveScrollSideCar),\n/* harmony export */   getDeltaXY: () => (/* binding */ getDeltaXY),\n/* harmony export */   getTouchXY: () => (/* binding */ getTouchXY)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_remove_scroll_bar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-remove-scroll-bar */ \"(ssr)/./node_modules/react-remove-scroll-bar/dist/es2015/index.js\");\n/* harmony import */ var react_style_singleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-style-singleton */ \"(ssr)/./node_modules/react-style-singleton/dist/es2015/index.js\");\n/* harmony import */ var _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./aggresiveCapture */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js\");\n/* harmony import */ var _handleScroll__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./handleScroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js\");\n\n\n\n\n\n\nvar getTouchXY = function (event) {\n  return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nvar getDeltaXY = function (event) {\n  return [event.deltaX, event.deltaY];\n};\nvar extractRef = function (ref) {\n  return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) {\n  return x[0] === y[0] && x[1] === y[1];\n};\nvar generateStyle = function (id) {\n  return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\");\n};\nvar idCounter = 0;\nvar lockStack = [];\nfunction RemoveScrollSideCar(props) {\n  var shouldPreventQueue = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n  var touchStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef([0, 0]);\n  var activeAxis = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  var id = react__WEBPACK_IMPORTED_MODULE_0__.useState(idCounter++)[0];\n  var Style = react__WEBPACK_IMPORTED_MODULE_0__.useState(react_style_singleton__WEBPACK_IMPORTED_MODULE_2__.styleSingleton)[0];\n  var lastProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    lastProps.current = props;\n  }, [props]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    if (props.inert) {\n      document.body.classList.add(\"block-interactivity-\".concat(id));\n      var allow_1 = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__spreadArray)([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n      allow_1.forEach(function (el) {\n        return el.classList.add(\"allow-interactivity-\".concat(id));\n      });\n      return function () {\n        document.body.classList.remove(\"block-interactivity-\".concat(id));\n        allow_1.forEach(function (el) {\n          return el.classList.remove(\"allow-interactivity-\".concat(id));\n        });\n      };\n    }\n    return;\n  }, [props.inert, props.lockRef.current, props.shards]);\n  var shouldCancelEvent = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (event, parent) {\n    if ('touches' in event && event.touches.length === 2 || event.type === 'wheel' && event.ctrlKey) {\n      return !lastProps.current.allowPinchZoom;\n    }\n    var touch = getTouchXY(event);\n    var touchStart = touchStartRef.current;\n    var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n    var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n    var currentAxis;\n    var target = event.target;\n    var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n    // allow horizontal touch move on Range inputs. They will not cause any scroll\n    if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n      return false;\n    }\n    var canBeScrolledInMainDirection = (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.locationCouldBeScrolled)(moveDirection, target);\n    if (!canBeScrolledInMainDirection) {\n      return true;\n    }\n    if (canBeScrolledInMainDirection) {\n      currentAxis = moveDirection;\n    } else {\n      currentAxis = moveDirection === 'v' ? 'h' : 'v';\n      canBeScrolledInMainDirection = (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.locationCouldBeScrolled)(moveDirection, target);\n      // other axis might be not scrollable\n    }\n\n    if (!canBeScrolledInMainDirection) {\n      return false;\n    }\n    if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n      activeAxis.current = currentAxis;\n    }\n    if (!currentAxis) {\n      return true;\n    }\n    var cancelingAxis = activeAxis.current || currentAxis;\n    return (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.handleScroll)(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n  }, []);\n  var shouldPrevent = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (_event) {\n    var event = _event;\n    if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n      // not the last active\n      return;\n    }\n    var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n    var sourceEvent = shouldPreventQueue.current.filter(function (e) {\n      return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta);\n    })[0];\n    // self event, and should be canceled\n    if (sourceEvent && sourceEvent.should) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      return;\n    }\n    // outside or shard event\n    if (!sourceEvent) {\n      var shardNodes = (lastProps.current.shards || []).map(extractRef).filter(Boolean).filter(function (node) {\n        return node.contains(event.target);\n      });\n      var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n      if (shouldStop) {\n        if (event.cancelable) {\n          event.preventDefault();\n        }\n      }\n    }\n  }, []);\n  var shouldCancel = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (name, delta, target, should) {\n    var event = {\n      name: name,\n      delta: delta,\n      target: target,\n      should: should,\n      shadowParent: getOutermostShadowParent(target)\n    };\n    shouldPreventQueue.current.push(event);\n    setTimeout(function () {\n      shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) {\n        return e !== event;\n      });\n    }, 1);\n  }, []);\n  var scrollTouchStart = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (event) {\n    touchStartRef.current = getTouchXY(event);\n    activeAxis.current = undefined;\n  }, []);\n  var scrollWheel = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (event) {\n    shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n  }, []);\n  var scrollTouchMove = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (event) {\n    shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    lockStack.push(Style);\n    props.setCallbacks({\n      onScrollCapture: scrollWheel,\n      onWheelCapture: scrollWheel,\n      onTouchMoveCapture: scrollTouchMove\n    });\n    document.addEventListener('wheel', shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n    document.addEventListener('touchmove', shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n    document.addEventListener('touchstart', scrollTouchStart, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n    return function () {\n      lockStack = lockStack.filter(function (inst) {\n        return inst !== Style;\n      });\n      document.removeEventListener('wheel', shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n      document.removeEventListener('touchmove', shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n      document.removeEventListener('touchstart', scrollTouchStart, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n    };\n  }, []);\n  var removeScrollBar = props.removeScrollBar,\n    inert = props.inert;\n  return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, inert ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(Style, {\n    styles: generateStyle(id)\n  }) : null, removeScrollBar ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_remove_scroll_bar__WEBPACK_IMPORTED_MODULE_1__.RemoveScrollBar, {\n    gapMode: props.gapMode\n  }) : null);\n}\nfunction getOutermostShadowParent(node) {\n  var shadowParent = null;\n  while (node !== null) {\n    if (node instanceof ShadowRoot) {\n      shadowParent = node.host;\n      node = node.host;\n    }\n    node = node.parentNode;\n  }\n  return shadowParent;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-remove-scroll/dist/es2015/UI.js":
/*!************************************************************!*\
  !*** ./node_modules/react-remove-scroll/dist/es2015/UI.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScroll: () => (/* binding */ RemoveScroll)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-remove-scroll-bar/constants */ \"(ssr)/./node_modules/react-remove-scroll-bar/dist/es2015/constants.js\");\n/* harmony import */ var use_callback_ref__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-callback-ref */ \"(ssr)/./node_modules/use-callback-ref/dist/es2015/useMergeRef.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./medium */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/medium.js\");\n\n\n\n\n\nvar nothing = function () {\n  return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function (props, parentRef) {\n  var ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  var _a = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n      onScrollCapture: nothing,\n      onWheelCapture: nothing,\n      onTouchMoveCapture: nothing\n    }),\n    callbacks = _a[0],\n    setCallbacks = _a[1];\n  var forwardProps = props.forwardProps,\n    children = props.children,\n    className = props.className,\n    removeScrollBar = props.removeScrollBar,\n    enabled = props.enabled,\n    shards = props.shards,\n    sideCar = props.sideCar,\n    noIsolation = props.noIsolation,\n    inert = props.inert,\n    allowPinchZoom = props.allowPinchZoom,\n    _b = props.as,\n    Container = _b === void 0 ? 'div' : _b,\n    gapMode = props.gapMode,\n    rest = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n  var SideCar = sideCar;\n  var containerRef = (0,use_callback_ref__WEBPACK_IMPORTED_MODULE_3__.useMergeRefs)([ref, parentRef]);\n  var containerProps = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, rest), callbacks);\n  return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, enabled && react__WEBPACK_IMPORTED_MODULE_0__.createElement(SideCar, {\n    sideCar: _medium__WEBPACK_IMPORTED_MODULE_4__.effectCar,\n    removeScrollBar: removeScrollBar,\n    shards: shards,\n    noIsolation: noIsolation,\n    inert: inert,\n    setCallbacks: setCallbacks,\n    allowPinchZoom: !!allowPinchZoom,\n    lockRef: ref,\n    gapMode: gapMode\n  }), forwardProps ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children), (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, containerProps), {\n    ref: containerRef\n  })) : react__WEBPACK_IMPORTED_MODULE_0__.createElement(Container, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, containerProps, {\n    className: className,\n    ref: containerRef\n  }), children));\n});\nRemoveScroll.defaultProps = {\n  enabled: true,\n  removeScrollBar: true,\n  inert: false\n};\nRemoveScroll.classNames = {\n  fullWidth: react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__.fullWidthClassName,\n  zeroRight: react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__.zeroRightClassName\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-remove-scroll/dist/es2015/UI.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nonPassive: () => (/* binding */ nonPassive)\n/* harmony export */ });\nvar passiveSupported = false;\nif (false) { var options; }\nvar nonPassive = passiveSupported ? {\n  passive: false\n} : false;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcmVtb3ZlLXNjcm9sbC9kaXN0L2VzMjAxNS9hZ2dyZXNpdmVDYXB0dXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxJQUFJQSxnQkFBZ0IsR0FBRyxLQUFLO0FBQzVCLElBQUksT0FBK0IsZ0JBZ0JsQztBQUNNLElBQUlTLFVBQVUsR0FBR1QsZ0JBQWdCLEdBQUc7RUFBRVUsT0FBTyxFQUFFO0FBQU0sQ0FBQyxHQUFHLEtBQUsiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxyZWFjdC1yZW1vdmUtc2Nyb2xsXFxkaXN0XFxlczIwMTVcXGFnZ3Jlc2l2ZUNhcHR1cmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHBhc3NpdmVTdXBwb3J0ZWQgPSBmYWxzZTtcbmlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgIHRyeSB7XG4gICAgICAgIHZhciBvcHRpb25zID0gT2JqZWN0LmRlZmluZVByb3BlcnR5KHt9LCAncGFzc2l2ZScsIHtcbiAgICAgICAgICAgIGdldDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgIHBhc3NpdmVTdXBwb3J0ZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Rlc3QnLCBvcHRpb25zLCBvcHRpb25zKTtcbiAgICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigndGVzdCcsIG9wdGlvbnMsIG9wdGlvbnMpO1xuICAgIH1cbiAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgIHBhc3NpdmVTdXBwb3J0ZWQgPSBmYWxzZTtcbiAgICB9XG59XG5leHBvcnQgdmFyIG5vblBhc3NpdmUgPSBwYXNzaXZlU3VwcG9ydGVkID8geyBwYXNzaXZlOiBmYWxzZSB9IDogZmFsc2U7XG4iXSwibmFtZXMiOlsicGFzc2l2ZVN1cHBvcnRlZCIsIm9wdGlvbnMiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImdldCIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZXJyIiwibm9uUGFzc2l2ZSIsInBhc3NpdmUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-remove-scroll/dist/es2015/handleScroll.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleScroll: () => (/* binding */ handleScroll),\n/* harmony export */   locationCouldBeScrolled: () => (/* binding */ locationCouldBeScrolled)\n/* harmony export */ });\nvar alwaysContainsScroll = function (node) {\n  // textarea will always _contain_ scroll inside self. It only can be hidden\n  return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n  if (!(node instanceof Element)) {\n    return false;\n  }\n  var styles = window.getComputedStyle(node);\n  return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n    // contains scroll inside self\n    !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible')\n  );\n};\nvar elementCouldBeVScrolled = function (node) {\n  return elementCanBeScrolled(node, 'overflowY');\n};\nvar elementCouldBeHScrolled = function (node) {\n  return elementCanBeScrolled(node, 'overflowX');\n};\nvar locationCouldBeScrolled = function (axis, node) {\n  var ownerDocument = node.ownerDocument;\n  var current = node;\n  do {\n    // Skip over shadow root\n    if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n      current = current.host;\n    }\n    var isScrollable = elementCouldBeScrolled(axis, current);\n    if (isScrollable) {\n      var _a = getScrollVariables(axis, current),\n        scrollHeight = _a[1],\n        clientHeight = _a[2];\n      if (scrollHeight > clientHeight) {\n        return true;\n      }\n    }\n    current = current.parentNode;\n  } while (current && current !== ownerDocument.body);\n  return false;\n};\nvar getVScrollVariables = function (_a) {\n  var scrollTop = _a.scrollTop,\n    scrollHeight = _a.scrollHeight,\n    clientHeight = _a.clientHeight;\n  return [scrollTop, scrollHeight, clientHeight];\n};\nvar getHScrollVariables = function (_a) {\n  var scrollLeft = _a.scrollLeft,\n    scrollWidth = _a.scrollWidth,\n    clientWidth = _a.clientWidth;\n  return [scrollLeft, scrollWidth, clientWidth];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n  return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n  return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n  /**\n   * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n   * and then increasingly negative as you scroll towards the end of the content.\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n   */\n  return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nvar handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n  var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n  var delta = directionFactor * sourceDelta;\n  // find scrollable target\n  var target = event.target;\n  var targetInLock = endTarget.contains(target);\n  var shouldCancelScroll = false;\n  var isDeltaPositive = delta > 0;\n  var availableScroll = 0;\n  var availableScrollTop = 0;\n  do {\n    var _a = getScrollVariables(axis, target),\n      position = _a[0],\n      scroll_1 = _a[1],\n      capacity = _a[2];\n    var elementScroll = scroll_1 - capacity - directionFactor * position;\n    if (position || elementScroll) {\n      if (elementCouldBeScrolled(axis, target)) {\n        availableScroll += elementScroll;\n        availableScrollTop += position;\n      }\n    }\n    if (target instanceof ShadowRoot) {\n      target = target.host;\n    } else {\n      target = target.parentNode;\n    }\n  } while (\n  // portaled content\n  !targetInLock && target !== document.body ||\n  // self content\n  targetInLock && (endTarget.contains(target) || endTarget === target));\n  // handle epsilon around 0 (non standard zoom levels)\n  if (isDeltaPositive && (noOverscroll && Math.abs(availableScroll) < 1 || !noOverscroll && delta > availableScroll)) {\n    shouldCancelScroll = true;\n  } else if (!isDeltaPositive && (noOverscroll && Math.abs(availableScrollTop) < 1 || !noOverscroll && -delta > availableScrollTop)) {\n    shouldCancelScroll = true;\n  }\n  return shouldCancelScroll;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-remove-scroll/dist/es2015/medium.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-remove-scroll/dist/es2015/medium.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   effectCar: () => (/* binding */ effectCar)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(ssr)/./node_modules/use-sidecar/dist/es2015/medium.js\");\n\nvar effectCar = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createSidecarMedium)();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcmVtb3ZlLXNjcm9sbC9kaXN0L2VzMjAxNS9tZWRpdW0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0Q7QUFDM0MsSUFBSUMsU0FBUyxHQUFHRCxnRUFBbUIsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xccmVhY3QtcmVtb3ZlLXNjcm9sbFxcZGlzdFxcZXMyMDE1XFxtZWRpdW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2lkZWNhck1lZGl1bSB9IGZyb20gJ3VzZS1zaWRlY2FyJztcbmV4cG9ydCB2YXIgZWZmZWN0Q2FyID0gY3JlYXRlU2lkZWNhck1lZGl1bSgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZVNpZGVjYXJNZWRpdW0iLCJlZmZlY3RDYXIiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-remove-scroll/dist/es2015/medium.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-remove-scroll/dist/es2015/sidecar.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-remove-scroll/dist/es2015/sidecar.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(ssr)/./node_modules/use-sidecar/dist/es2015/exports.js\");\n/* harmony import */ var _SideEffect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SideEffect */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./medium */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/medium.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.exportSidecar)(_medium__WEBPACK_IMPORTED_MODULE_1__.effectCar, _SideEffect__WEBPACK_IMPORTED_MODULE_2__.RemoveScrollSideCar));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtcmVtb3ZlLXNjcm9sbC9kaXN0L2VzMjAxNS9zaWRlY2FyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEM7QUFDTztBQUNkO0FBQ3JDLGlFQUFlQSwwREFBYSxDQUFDRSw4Q0FBUyxFQUFFRCw0REFBbUIsQ0FBQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXHJlYWN0LXJlbW92ZS1zY3JvbGxcXGRpc3RcXGVzMjAxNVxcc2lkZWNhci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBleHBvcnRTaWRlY2FyIH0gZnJvbSAndXNlLXNpZGVjYXInO1xuaW1wb3J0IHsgUmVtb3ZlU2Nyb2xsU2lkZUNhciB9IGZyb20gJy4vU2lkZUVmZmVjdCc7XG5pbXBvcnQgeyBlZmZlY3RDYXIgfSBmcm9tICcuL21lZGl1bSc7XG5leHBvcnQgZGVmYXVsdCBleHBvcnRTaWRlY2FyKGVmZmVjdENhciwgUmVtb3ZlU2Nyb2xsU2lkZUNhcik7XG4iXSwibmFtZXMiOlsiZXhwb3J0U2lkZWNhciIsIlJlbW92ZVNjcm9sbFNpZGVDYXIiLCJlZmZlY3RDYXIiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-remove-scroll/dist/es2015/sidecar.js\n");

/***/ })

};
;