'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// import { Progress } from '@/components/ui/progress'; // TODO: Create Progress component
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Shield, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  Calendar,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';

/**
 * Warranty Status Dashboard Page
 * 
 * This page provides a comprehensive overview of warranty status across
 * all products, with visual indicators and key metrics.
 */
export default function WarrantyStatusPage() {
  const [statusData, setStatusData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mock data for development
  useEffect(() => {
    loadStatusData();
  }, []);

  const loadStatusData = async () => {
    try {
      setIsLoading(true);

      // Fetch warranty summary data
      const [warrantyRes, expiringRes, componentRes] = await Promise.all([
        fetch('/api/warranties?take=1000', { credentials: 'include' }),
        fetch('/api/warranties/expiring?days=30', { credentials: 'include' }),
        fetch('/api/warranties/components?take=1000', { credentials: 'include' })
      ]);

      if (!warrantyRes.ok) {
        console.error('Warranty API error:', warrantyRes.status, warrantyRes.statusText);
        if (warrantyRes.status === 401) {
          throw new Error('Authentication required. Please log in.');
        }
        throw new Error(`Failed to fetch warranties: ${warrantyRes.statusText}`);
      }

      if (!expiringRes.ok) {
        console.error('Expiring warranties API error:', expiringRes.status, expiringRes.statusText);
        if (expiringRes.status === 401) {
          throw new Error('Authentication required. Please log in.');
        }
        throw new Error(`Failed to fetch expiring warranties: ${expiringRes.statusText}`);
      }

      if (!componentRes.ok) {
        console.error('Components API error:', componentRes.status, componentRes.statusText);
        if (componentRes.status === 401) {
          throw new Error('Authentication required. Please log in.');
        }
        throw new Error(`Failed to fetch components: ${componentRes.statusText}`);
      }

      const [warrantyData, expiringData, componentData] = await Promise.all([
        warrantyRes.json(),
        expiringRes.json(),
        componentRes.json()
      ]);

      // Calculate summary statistics
      const warranties = warrantyData.warranties || [];
      const expiringWarranties = expiringData.warranties || [];
      const components = componentData.components || [];

      const summary = {
        total: warranties.length,
        active: warranties.filter((w: any) => w.status === 'ACTIVE').length,
        expiring: expiringWarranties.length,
        expired: warranties.filter((w: any) => w.status === 'EXPIRED').length,
        pending: warranties.filter((w: any) => w.status === 'PENDING').length
      };

      // Calculate component status
      const today = new Date();
      const componentStatus = {
        total: components.length,
        active: components.filter((c: any) => {
          const warrantyDate = c.warrantyDate ? new Date(c.warrantyDate) : null;
          return warrantyDate && warrantyDate > today;
        }).length,
        expiring: components.filter((c: any) => {
          const warrantyDate = c.warrantyDate ? new Date(c.warrantyDate) : null;
          if (!warrantyDate) return false;
          const daysUntil = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
          return daysUntil > 0 && daysUntil <= 30;
        }).length,
        expired: components.filter((c: any) => {
          const warrantyDate = c.warrantyDate ? new Date(c.warrantyDate) : null;
          return warrantyDate && warrantyDate <= today;
        }).length
      };

      // Calculate expiring breakdown
      const expiringBreakdown = {
        next7Days: expiringWarranties.filter((w: any) => {
          const warrantyDate = new Date(w.warrantyDate);
          const daysUntil = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
          return daysUntil <= 7;
        }).length,
        next30Days: expiringWarranties.length,
        next90Days: warranties.filter((w: any) => {
          if (!w.warrantyDate) return false;
          const warrantyDate = new Date(w.warrantyDate);
          const daysUntil = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
          return daysUntil > 0 && daysUntil <= 90;
        }).length
      };

      setStatusData({
        summary,
        trends: {
          activeChange: 0, // Would need historical data to calculate
          expiringChange: 0,
          expiredChange: 0
        },
        expiringBreakdown,
        componentStatus,
        recentAlerts: [] // Would come from a separate alerts API
      });
      setError(null);
    } catch (err) {
      console.error('Error loading status data:', err);
      setError('Failed to load warranty status data');
      setStatusData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadStatusData();
    setIsRefreshing(false);
  };

  const getStatusPercentage = (count: number, total: number) => {
    return total > 0 ? Math.round((count / total) * 100) : 0;
  };

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <div className="h-4 w-4" />;
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'critical':
        return <Badge variant="destructive">Critical</Badge>;
      case 'high':
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800">High</Badge>;
      case 'medium':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      default:
        return <Badge variant="secondary">Low</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <Skeleton className="h-12 w-12 mb-4" />
                    <Skeleton className="h-8 w-16 mb-2" />
                    <Skeleton className="h-4 w-24" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Warranty Status Dashboard</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Monitor warranty status and expiration alerts across all products
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="secondary" 
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="secondary" asChild>
              <Link href="/warranties/alerts">
                <AlertTriangle className="h-4 w-4 mr-2" />
                View Alerts
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {error && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-black">{error}</AlertDescription>
            </Alert>
          )}

          {statusData && (
            <>
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Warranties</p>
                        <p className="text-2xl font-bold text-black">{statusData.summary.total}</p>
                      </div>
                      <Shield className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Active</p>
                        <p className="text-2xl font-bold text-black">{statusData.summary.active}</p>
                        <div className="flex items-center space-x-1 mt-1">
                          {getTrendIcon(statusData.trends.activeChange)}
                          <span className="text-sm text-gray-500">
                            {Math.abs(statusData.trends.activeChange)} this month
                          </span>
                        </div>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
                        <p className="text-2xl font-bold text-black">{statusData.summary.expiring}</p>
                        <div className="flex items-center space-x-1 mt-1">
                          {getTrendIcon(statusData.trends.expiringChange)}
                          <span className="text-sm text-gray-500">
                            {Math.abs(statusData.trends.expiringChange)} this month
                          </span>
                        </div>
                      </div>
                      <AlertTriangle className="h-8 w-8 text-yellow-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Expired</p>
                        <p className="text-2xl font-bold text-black">{statusData.summary.expired}</p>
                        <div className="flex items-center space-x-1 mt-1">
                          {getTrendIcon(statusData.trends.expiredChange)}
                          <span className="text-sm text-gray-500">
                            {Math.abs(statusData.trends.expiredChange)} this month
                          </span>
                        </div>
                      </div>
                      <XCircle className="h-8 w-8 text-red-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Status Distribution */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-black">Warranty Status Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-black">Active</span>
                          <span className="text-sm text-gray-500">
                            {getStatusPercentage(statusData.summary.active, statusData.summary.total)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: `${getStatusPercentage(statusData.summary.active, statusData.summary.total)}%` }}
                          ></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-black">Expiring Soon</span>
                          <span className="text-sm text-gray-500">
                            {getStatusPercentage(statusData.summary.expiring, statusData.summary.total)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-yellow-600 h-2 rounded-full"
                            style={{ width: `${getStatusPercentage(statusData.summary.expiring, statusData.summary.total)}%` }}
                          ></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-black">Expired</span>
                          <span className="text-sm text-gray-500">
                            {getStatusPercentage(statusData.summary.expired, statusData.summary.total)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-red-600 h-2 rounded-full"
                            style={{ width: `${getStatusPercentage(statusData.summary.expired, statusData.summary.total)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-black">Expiration Timeline</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-red-600" />
                          <span className="text-sm font-medium text-black">Next 7 Days</span>
                        </div>
                        <Badge variant="destructive">{statusData.expiringBreakdown.next7Days}</Badge>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-yellow-600" />
                          <span className="text-sm font-medium text-black">Next 30 Days</span>
                        </div>
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          {statusData.expiringBreakdown.next30Days}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium text-black">Next 90 Days</span>
                        </div>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {statusData.expiringBreakdown.next90Days}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Alerts */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-black">Recent Alerts</CardTitle>
                  <CardDescription className="text-black">
                    Latest warranty expiration notifications and alerts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {statusData.recentAlerts.length === 0 ? (
                    <div className="text-center py-8">
                      <AlertTriangle className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                      <p className="text-gray-500">No recent alerts</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {statusData.recentAlerts.map((alert: any) => (
                        <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            {alert.type === 'expiring' ? (
                              <AlertTriangle className="h-5 w-5 text-yellow-600" />
                            ) : (
                              <XCircle className="h-5 w-5 text-red-600" />
                            )}
                            <div>
                              <p className="text-sm font-medium text-black">{alert.message}</p>
                              <p className="text-xs text-gray-500">{alert.date}</p>
                            </div>
                          </div>
                          {getPriorityBadge(alert.priority)}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
