import { NextRequest, NextResponse } from 'next/server';
import { getWarrantyComponentRepository } from '@/lib/repositories';
import { warrantyComponentSchema, warrantyComponentFilterSchema } from '@/lib/validations/warranty.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { z } from 'zod';

/**
 * GET /api/warranties/components
 * Get warranty components with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const queryParams = {
        machineId: searchParams.get('machineId') || undefined,
        componentNo: searchParams.get('componentNo') ? parseInt(searchParams.get('componentNo')!) : undefined,
        serialNumber: searchParams.get('serialNumber') || undefined,
        warrantyDateFrom: searchParams.get('warrantyDateFrom') ? new Date(searchParams.get('warrantyDateFrom')!) : undefined,
        warrantyDateTo: searchParams.get('warrantyDateTo') ? new Date(searchParams.get('warrantyDateTo')!) : undefined,
        search: searchParams.get('search') || undefined,
        skip: parseInt(searchParams.get('skip') || '0'),
        take: parseInt(searchParams.get('take') || '10'),
        sortBy: searchParams.get('sortBy') || 'componentNo',
        sortOrder: searchParams.get('sortOrder') || 'asc',
      };

      try {
        const validatedParams = warrantyComponentFilterSchema.parse(queryParams);
        
        const warrantyComponentRepository = getWarrantyComponentRepository();
        
        // Build filter object
        const filter: Record<string, any> = {};
        
        if (validatedParams.machineId) {
          filter.machineId = validatedParams.machineId;
        }
        
        if (validatedParams.componentNo) {
          filter.componentNo = validatedParams.componentNo;
        }
        
        if (validatedParams.serialNumber) {
          filter.serialNumber = {
            contains: validatedParams.serialNumber,
            mode: 'insensitive',
          };
        }
        
        if (validatedParams.warrantyDateFrom || validatedParams.warrantyDateTo) {
          filter.warrantyDate = {};
          if (validatedParams.warrantyDateFrom) {
            filter.warrantyDate.gte = validatedParams.warrantyDateFrom;
          }
          if (validatedParams.warrantyDateTo) {
            filter.warrantyDate.lte = validatedParams.warrantyDateTo;
          }
        }
        
        if (validatedParams.search) {
          filter.OR = [
            {
              serialNumber: {
                contains: validatedParams.search,
                mode: 'insensitive',
              },
            },
            {
              componentNo: {
                equals: isNaN(parseInt(validatedParams.search)) ? undefined : parseInt(validatedParams.search),
              },
            },
          ].filter(condition => condition.componentNo !== undefined || condition.serialNumber);
        }
        
        // Build order by object
        const orderBy: Record<string, any> = {};
        orderBy[validatedParams.sortBy] = validatedParams.sortOrder;
        
        // Get warranty components with filter
        const components = await warrantyComponentRepository.findWithFilter(
          filter,
          validatedParams.skip,
          validatedParams.take,
          orderBy
        );
        
        // Get total count for pagination
        const totalCount = await warrantyComponentRepository.countWithFilter(filter);
        
        return NextResponse.json({
          components,
          pagination: {
            skip: validatedParams.skip,
            take: validatedParams.take,
            total: totalCount,
            hasMore: validatedParams.skip + validatedParams.take < totalCount,
          },
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Invalid query parameters', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error fetching warranty components:', error);
      return NextResponse.json(
        { error: 'Failed to fetch warranty components' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/warranties/components
 * Create a new warranty component
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = warrantyComponentSchema.parse(body);

        const warrantyComponentRepository = getWarrantyComponentRepository();

        // Create warranty component with validation
        const component = await warrantyComponentRepository.createWithValidation(validatedData);

        return NextResponse.json(component, { status: 201 });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error creating warranty component:', error);
      
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          return NextResponse.json(
            { error: 'A component with this serial number already exists in warranty' },
            { status: 409 }
          );
        }
        if (error.code === 'P2003') {
          return NextResponse.json(
            { error: 'Referenced warranty machine does not exist' },
            { status: 400 }
          );
        }
      }
      
      if (error instanceof Error && error.message.includes('already exists')) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to create warranty component' },
        { status: 500 }
      );
    }
  }
);
