'use client';

import { useState, useEffect, useCallback } from 'react';
import { showErrorToast } from '@/lib/toast';

// Define the AMC contract type
export interface AMCContract {
  id: string;
  customerId: string;
  customer?: {
    id: string;
    name: string;
    city?: string;
    phone?: string;
  };
  contactPersonId?: string;
  contactPerson?: {
    id: string;
    name: string;
    phone?: string;
  };
  executiveId?: string;
  executive?: {
    id: string;
    name: string;
  };
  natureOfService?: string;
  startDate: Date | string;
  endDate: Date | string;
  warningDate?: Date | string;
  amount: number;
  bslDebit?: number;
  previousAmount?: number;
  amcPeriod?: number;
  yearOfCommencement?: number;
  numberOfMachines?: number;
  numberOfServices?: number;
  renewalFlag?: boolean;
  blstrFlag?: string;
  paidAmount?: number;
  balanceAmount?: number;
  totalTonnage?: number;
  status: 'ACTIVE' | 'EXPIRED' | 'PENDING' | 'CANCELLED' | 'RENEWED';
  contractNumber?: string;
  remarks?: string;
  originalId?: number;
  originalAmcId?: number;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  machines?: any[];
  serviceDates?: any[];
  payments?: any[];
  divisions?: any[];
}

// Define the pagination state
interface PaginationState {
  skip: number;
  take: number;
  total: number;
}

// Define the filter state
interface AMCContractFilters {
  customerId?: string;
  executiveId?: string;
  status?: string;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
  search?: string;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

interface UseAMCContractsReturn {
  contracts: AMCContract[];
  isLoading: boolean;
  error: Error | null;
  pagination: PaginationState;
  filters: AMCContractFilters;
  setFilters: (filters: AMCContractFilters) => void;
  setPagination: (pagination: Partial<PaginationState>) => void;
  refreshContracts: () => Promise<void>;
  deleteContract: (id: string) => Promise<boolean>;
}

/**
 * Custom hook for fetching and managing AMC contract data
 */
export function useAMCContracts(): UseAMCContractsReturn {
  const [contracts, setContracts] = useState<AMCContract[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [pagination, setPaginationState] = useState<PaginationState>({
    skip: 0,
    take: 10,
    total: 0,
  });
  const [filters, setFiltersState] = useState<AMCContractFilters>({
    sortField: 'startDate',
    sortOrder: 'desc',
  });

  // Function to fetch AMC contracts
  const fetchContracts = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Prepare search parameters for the API
      const searchParams = {
        skip: pagination.skip,
        take: pagination.take,
        sortField: filters.sortField || 'startDate',
        sortOrder: filters.sortOrder || 'desc',
        query: filters.search,
        customerId: filters.customerId,
        executiveId: filters.executiveId,
        status: filters.status,
        startDateFrom: filters.startDateFrom,
        startDateTo: filters.startDateTo,
        endDateFrom: filters.endDateFrom,
        endDateTo: filters.endDateTo,
      };

      // Use the search API endpoint with POST method
      const response = await fetch('/api/amc/contracts/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchParams),
        credentials: 'include', // Include credentials for authentication
      });

      if (!response.ok) {
        // Try to get more detailed error information
        let errorMessage = 'Failed to fetch AMC contracts';
        try {
          const errorData = await response.json();
          if (errorData && errorData.error) {
            errorMessage = `Failed to fetch AMC contracts: ${errorData.error}`;
          }
        } catch (jsonError) {
          // If we can't parse the error response as JSON, use the status text
          errorMessage = `Failed to fetch AMC contracts: ${response.status} ${response.statusText}`;
        }
        console.error('API Error:', errorMessage, 'Status:', response.status);
        throw new Error(errorMessage);
      }

      const data = await response.json();
      setContracts(data.data || []);
      setPaginationState(prev => ({
        ...prev,
        total: data.pagination?.total || 0,
      }));
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      console.error('Error fetching AMC contracts:', error.message, err);

      // Show error toast for better user feedback
      showErrorToast('Error', error.message);
    } finally {
      setIsLoading(false);
    }
  }, [pagination.skip, pagination.take, filters]);

  // Function to delete an AMC contract
  const deleteContract = async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/amc/contracts/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete AMC contract');
      }

      // Refresh the contracts list
      await fetchContracts();
      return true;
    } catch (err) {
      showErrorToast('Error', err instanceof Error ? err.message : 'Failed to delete AMC contract');
      console.error('Error deleting AMC contract:', err);
      return false;
    }
  };

  // Set filters with callback
  const setFilters = useCallback((newFilters: AMCContractFilters) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
    // Reset pagination to first page when filters change
    setPaginationState(prev => ({ ...prev, skip: 0 }));
  }, []);

  // Set pagination with callback
  const setPagination = useCallback((newPagination: Partial<PaginationState>) => {
    setPaginationState(prev => ({ ...prev, ...newPagination }));
  }, []);

  // Fetch contracts when dependencies change
  useEffect(() => {
    fetchContracts();
  }, [fetchContracts]);

  return {
    contracts,
    isLoading,
    error,
    pagination,
    filters,
    setFilters,
    setPagination,
    refreshContracts: fetchContracts,
    deleteContract,
  };
}
