# Activity Logging System

This document describes the activity logging system implemented in the KoolSoft application. The system tracks user actions and system events for auditing and monitoring purposes.

## Overview

The activity logging system consists of:

1. A database table for storing activity logs
2. A repository for database operations
3. A service for logging activities
4. Middleware for automatically logging API requests
5. Integration with NextAuth.js for authentication events
6. A user interface for viewing and filtering logs

## Database Schema

The activity logs are stored in the `activity_logs` table with the following schema:

| Field       | Type      | Description                                   |
|-------------|-----------|-----------------------------------------------|
| id          | UUID      | Primary key                                   |
| userId      | UUID      | Foreign key to the user who performed the action (optional) |
| action      | String    | The action performed (e.g., "login", "create_user") |
| entityType  | String    | The type of entity affected (e.g., "user", "customer") |
| entityId    | String    | The ID of the entity affected                 |
| details     | JSON      | Additional details about the action           |
| ipAddress   | String    | The IP address of the user                    |
| userAgent   | String    | The user agent of the user                    |
| createdAt   | DateTime  | When the action was performed                 |

## Setup

To set up the activity logging system:

1. Create the activity logs table:

```bash
npm run db:create-activity-logs
```

This will:
- Update the Prisma schema with the activity logs model
- Generate the Prisma client
- Push the changes to the database

## Usage

### Logging Activities

The activity logging system provides several ways to log activities:

#### 1. Using the Activity Log Service

```typescript
import { getActivityLogService } from '@/lib/services/activity-log.service';

// Get the activity log service
const activityLogService = getActivityLogService();

// Log a user action
await activityLogService.logAction(
  'create_customer',
  userId,
  'customer',
  customerId,
  { name: customerName, email: customerEmail }
);
```

#### 2. Using the Activity Logger Middleware

```typescript
import { withActivityLogging } from '@/lib/middleware/activity-logger.middleware';

// Wrap an API route handler with activity logging
export const GET = withActivityLogging(
  async (req: NextRequest) => {
    // Handler implementation
    return NextResponse.json({ data: 'example' });
  },
  {
    action: 'view_customers',
    entityType: 'customer',
    getEntityId: (req) => {
      const url = new URL(req.url);
      return url.searchParams.get('id');
    },
  }
);
```

#### 3. Authentication Events

Authentication events (login, logout, etc.) are automatically logged by the NextAuth.js configuration in `lib/auth.ts`.

### Viewing Activity Logs

Activity logs can be viewed in the admin dashboard at `/admin/activity-logs`. This page provides:

- Filtering by user, action, entity type, and date range
- Pagination for large result sets
- Export functionality for logs

## Common Actions

The system logs the following common actions:

| Action            | Description                                |
|-------------------|--------------------------------------------|
| login             | User login                                 |
| logout            | User logout                                |
| failed_login      | Failed login attempt                       |
| create_user       | User creation                              |
| update_user       | User update                                |
| delete_user       | User deletion                              |
| create_customer   | Customer creation                          |
| update_customer   | Customer update                            |
| delete_customer   | Customer deletion                          |
| create_amc_contract | AMC contract creation                    |
| update_amc_contract | AMC contract update                      |
| delete_amc_contract | AMC contract deletion                    |
| send_email        | Email sending                              |
| export_data       | Data export                                |
| import_data       | Data import                                |

## API Endpoints

### GET /api/admin/activity-logs

Fetch activity logs with filtering and pagination.

Query parameters:
- `skip`: Number of records to skip
- `take`: Maximum number of records to return
- `userId`: Filter by user ID
- `action`: Filter by action
- `entityType`: Filter by entity type
- `entityId`: Filter by entity ID
- `startDate`: Filter by start date
- `endDate`: Filter by end date
- `search`: Search term

### GET /api/admin/activity-logs/export

Export activity logs to CSV.

Query parameters:
- Same as above, plus:
- `format`: Export format (csv)

## Best Practices

1. **Log Critical Operations**: Always log operations that modify data or affect system state.
2. **Include Relevant Details**: Include enough details to understand what happened, but avoid sensitive information.
3. **Use Consistent Action Names**: Use consistent naming for actions (e.g., `create_user`, `update_user`).
4. **Include Entity Information**: Always include the entity type and ID when logging operations on entities.
5. **Handle Errors**: Log failed operations with error details.

## Performance Considerations

The activity logging system is designed to have minimal impact on performance:

1. Logging is performed asynchronously where possible
2. Database operations use efficient indexes
3. The UI uses pagination to handle large volumes of logs

## Retention Policy

By default, activity logs are retained indefinitely. To implement a retention policy:

1. Create a scheduled job to clean up old logs
2. Configure the retention period based on your requirements
3. Consider archiving logs before deletion for long-term storage
