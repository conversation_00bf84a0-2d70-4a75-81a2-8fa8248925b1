'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, RefreshCw, ShieldAlert } from 'lucide-react';
import Link from 'next/link';
import { showErrorToast, showSuccessToast } from '@/lib/toast';
import { Skeleton } from '@/components/ui/skeleton';
import { AMCEditForm } from '@/components/amc/amc-edit-form';
import { useAuth } from '@/lib/hooks/useAuth';

interface AMCContract {
  id: string;
  customerId: string;
  customer: {
    id: string;
    name: string;
    email?: string;
  };
  contactPersonId?: string;
  executiveId?: string;
  natureOfService?: string;
  startDate: string;
  endDate: string;
  warningDate?: string;
  amount: number;
  numberOfServices?: number;
  numberOfMachines?: number;
  totalTonnage?: number;
  status: string;
  contractNumber?: string;
  remarks?: string;
  machines?: any[];
  divisions?: any[];
  serviceDates?: any[];
  payments?: any[];
}

/**
 * AMC Contract Edit Page
 * 
 * This page provides a form for editing existing AMC contracts.
 * It pre-populates the form with existing contract data and follows
 * the established UI standards and multi-step workflow.
 */
export default function AMCContractEditPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [contract, setContract] = useState<AMCContract | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const contractId = params?.id as string;

  // Check if user has permission to edit contracts
  const canEdit = user && ['ADMIN', 'MANAGER', 'EXECUTIVE'].includes(user.role);

  // Redirect if not authorized
  useEffect(() => {
    if (!authLoading && !canEdit) {
      showErrorToast('Access Denied', 'You do not have permission to edit contracts');
      router.push(`/amc/contracts/${contractId}`);
    }
  }, [authLoading, canEdit, router, contractId]);

  // Fetch contract details
  useEffect(() => {
    const fetchContract = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/amc/contracts/${contractId}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('AMC contract not found');
          }
          throw new Error('Failed to fetch contract details');
        }

        const data = await response.json();
        setContract(data);
      } catch (error) {
        console.error('Error fetching contract:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch contract details';
        setError(errorMessage);
        showErrorToast('Error', errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (contractId) {
      fetchContract();
    }
  }, [contractId]);

  // Handle successful contract update
  const handleUpdateSuccess = (updatedContract: any) => {
    showSuccessToast('Success', 'AMC contract updated successfully');
    router.push(`/amc/contracts/${contractId}`);
  };

  // Handle form cancellation
  const handleCancel = () => {
    router.push(`/amc/contracts/${contractId}`);
  };

  // Show loading while checking auth
  if (authLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-48 bg-white/20" />
                <Skeleton className="h-4 w-64 bg-white/20 mt-2" />
              </div>
              <Skeleton className="h-10 w-24 bg-white/20" />
            </div>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Show access denied if user doesn't have permission
  if (!canEdit) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-destructive text-white">
            <CardTitle className="flex items-center space-x-2">
              <ShieldAlert className="h-5 w-5" />
              <span>Access Denied</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center py-8 space-y-4">
              <p className="text-black">You do not have permission to edit AMC contracts.</p>
              <Button asChild variant="outline">
                <Link href={`/amc/contracts/${contractId}`}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Contract
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-48 bg-white/20" />
                <Skeleton className="h-4 w-64 bg-white/20 mt-2" />
              </div>
              <Skeleton className="h-10 w-24 bg-white/20" />
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-destructive text-white">
            <CardTitle>Error Loading Contract</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center py-8 space-y-4">
              <p className="text-black">{error || 'Contract not found'}</p>
              <div className="flex justify-center space-x-4">
                <Button asChild variant="outline">
                  <Link href="/amc">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to AMC List
                  </Link>
                </Button>
                <Button onClick={() => window.location.reload()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Edit className="h-5 w-5" />
              <span>Edit AMC Contract</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Modify contract details for {contract.customer.name}
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button asChild variant="secondary">
              <Link href={`/amc/contracts/${contractId}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Contract
              </Link>
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Edit Form */}
      <AMCEditForm
        contract={contract}
        onSuccess={handleUpdateSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
