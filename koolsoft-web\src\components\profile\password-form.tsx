'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Password form validation schema
const passwordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Confirm password must be at least 8 characters'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

// Type for password form data
type PasswordFormData = z.infer<typeof passwordSchema>;

/**
 * Password Form Component
 *
 * This component allows users to change their password.
 */
export function PasswordForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Initialize form with react-hook-form
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: PasswordFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      const response = await fetch('/api/users/me/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: data.currentPassword,
          newPassword: data.newPassword,
          confirmPassword: data.confirmPassword,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || 'Failed to change password');
        return;
      }

      setSuccess(true);
      reset();

      // Show success message for 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error changing password:', error);
      setError('An error occurred while changing your password');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium leading-6 text-black">Change Password</h3>
        <p className="mt-1 text-sm text-black">
          Update your password to keep your account secure.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="currentPassword" className="text-black">
              Current Password
            </Label>
            <div className="mt-1">
              <Input
                type="password"
                id="currentPassword"
                autoComplete="current-password"
                className="text-black"
                {...register('currentPassword')}
              />
              {errors.currentPassword && (
                <p className="mt-1 text-sm text-[#ef4444]">{errors.currentPassword.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="newPassword" className="text-black">
              New Password
            </Label>
            <div className="mt-1">
              <Input
                type="password"
                id="newPassword"
                autoComplete="new-password"
                className="text-black"
                {...register('newPassword')}
              />
              {errors.newPassword && (
                <p className="mt-1 text-sm text-[#ef4444]">{errors.newPassword.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="confirmPassword" className="text-black">
              Confirm New Password
            </Label>
            <div className="mt-1">
              <Input
                type="password"
                id="confirmPassword"
                autoComplete="new-password"
                className="text-black"
                {...register('confirmPassword')}
              />
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-[#ef4444]">{errors.confirmPassword.message}</p>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <Alert className="bg-[#f3f4f6] border-[#d1d5db] text-black">
            <AlertTitle className="text-black font-medium">Password Requirements</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5 space-y-1 text-black">
                <li>At least 8 characters long</li>
                <li>Include a mix of letters, numbers, and special characters</li>
                <li>Avoid using easily guessable information</li>
              </ul>
            </AlertDescription>
          </Alert>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="bg-green-50 text-green-800 border-green-200">
              <AlertDescription className="text-green-800">
                Password changed successfully!
              </AlertDescription>
            </Alert>
          )}
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-[#0F52BA] text-white hover:bg-[#0F52BA]/90"
          >
            {isSubmitting ? 'Changing...' : 'Change Password'}
          </Button>
        </div>
      </form>
    </div>
  );
}
