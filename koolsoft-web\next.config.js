/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Add webpack configuration to handle problematic packages
  webpack: (config, { isServer }) => {
    // Ignore all HTML files in node_modules
    config.module.rules.push({
      test: /\.html$/,
      include: /node_modules/,
      use: 'null-loader',
    });

    // Handle Node.js native modules
    if (!isServer) {
      // For client-side builds, provide empty modules for Node.js built-ins
      config.resolve.fallback = {
        ...config.resolve.fallback,
        // Node.js core modules
        'child_process': false,
        'fs': false,
        'path': false,
        'os': false,
        'crypto': false,
        'stream': false,
        'http': false,
        'https': false,
        'zlib': false,
        'net': false,
        'dns': false,
        'tls': false,
        'assert': false,
        'util': false,
        'url': false,
        'querystring': false,
        'buffer': false,
        'constants': false,

        // Problematic packages
        '@mapbox/node-pre-gyp': false,
        'node-pre-gyp': false,
        'better-sqlite3': false,
        'sqlite3': false,
        'bcrypt': false
      };

      // Explicitly mark bcrypt as external
      config.externals = [...(config.externals || []),
        'bcrypt',
        '@mapbox/node-pre-gyp'
      ];
    }

    // For server-side builds, properly externalize native modules
    if (isServer) {
      const nodeModules = [
        'bcrypt',
        '@mapbox/node-pre-gyp',
        'better-sqlite3',
        'sqlite3'
      ];

      // Mark these modules as external for server-side
      config.externals = [
        ...config.externals,
        ...nodeModules
      ];
    }

    return config;
  },
}

module.exports = nextConfig;