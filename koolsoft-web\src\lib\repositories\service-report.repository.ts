import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Service Report Repository
 *
 * This repository handles database operations for the Service Report entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class ServiceReportRepository extends PrismaRepository<
  Prisma.service_reportsGetPayload<{}>,
  string,
  Prisma.service_reportsCreateInput,
  Prisma.service_reportsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('service_reports');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): ServiceReportRepository {
    return new ServiceReportRepository(tx);
  }

  /**
   * Find service reports with related data
   * @param id Service report ID
   * @returns Promise resolving to the service report with relations or null
   */
  async findWithRelations(id: string): Promise<any> {
    return this.model.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
            phone: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
        details: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });
  }

  /**
   * Find service reports by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service reports
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { reportDate: 'desc' },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
          },
        },
        details: true,
      },
    });
  }

  /**
   * Find service reports by status
   * @param status Service report status
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service reports
   */
  async findByStatus(status: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { status },
      skip,
      take,
      orderBy: { reportDate: 'desc' },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  /**
   * Find service reports by executive ID
   * @param executiveId Executive ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service reports
   */
  async findByExecutiveId(executiveId: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { executiveId },
      skip,
      take,
      orderBy: { reportDate: 'desc' },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
          },
        },
        details: true,
      },
    });
  }

  /**
   * Find service reports with filtering and pagination
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Order by criteria
   * @returns Promise resolving to filtered service reports
   */
  async findWithFilter(
    filter: any = {},
    skip?: number,
    take?: number,
    orderBy?: any
  ): Promise<any[]> {
    try {
      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);
      
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { reportDate: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
          details: {
            select: {
              id: true,
              machineType: true,
              serialNumber: true,
              problem: true,
              solution: true,
              partReplaced: true,
            },
          },
        },
      });

      return result;
    } catch (error) {
      console.error('ServiceReportRepository.findWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Count service reports with filter
   * @param filter Filter criteria
   * @returns Promise resolving to the count
   */
  async countWithFilter(filter: any = {}): Promise<number> {
    try {
      this.validateFilter(filter);
      return this.model.count({
        where: filter,
      });
    } catch (error) {
      console.error('ServiceReportRepository.countWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Get service report statistics
   * @returns Promise resolving to service report statistics
   */
  async getStatistics(): Promise<any> {
    const [
      totalReports,
      openReports,
      completedReports,
      pendingReports,
      recentReports,
    ] = await Promise.all([
      this.model.count(),
      this.model.count({ where: { status: 'OPEN' } }),
      this.model.count({ where: { status: 'COMPLETED' } }),
      this.model.count({ where: { status: 'PENDING' } }),
      this.model.count({
        where: {
          reportDate: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ]);

    return {
      totalReports,
      openReports,
      completedReports,
      pendingReports,
      recentReports,
      completionRate: totalReports > 0 ? (completedReports / totalReports) * 100 : 0,
    };
  }

  /**
   * Create service report with details and history card integration
   * @param serviceReportData Service report data
   * @param details Service details data
   * @returns Promise resolving to the created service report
   */
  async createWithDetails(serviceReportData: any, details: any[] = []): Promise<any> {
    return this.prisma.$transaction(async (tx) => {
      // Create the service report
      const serviceReport = await tx.service_reports.create({
        data: serviceReportData,
      });

      // Create service details if provided
      if (details && details.length > 0) {
        await tx.service_details.createMany({
          data: details.map((detail: any) => ({
            ...detail,
            serviceReportId: serviceReport.id,
          })),
        });
      }

      // Create or update history card for service tracking
      await this.createOrUpdateHistoryCard(tx, serviceReport, details);

      // Return the service report with related data
      return tx.service_reports.findUnique({
        where: { id: serviceReport.id },
        include: {
          customer: true,
          executive: true,
          details: true,
        },
      });
    });
  }

  /**
   * Create or update history card for service report
   * @param tx Transaction client
   * @param serviceReport Service report data
   * @param details Service details data
   */
  private async createOrUpdateHistoryCard(tx: any, serviceReport: any, details: any[] = []): Promise<void> {
    try {
      // Check if a history card already exists for this customer
      let historyCard = await tx.history_cards.findFirst({
        where: {
          customerId: serviceReport.customerId,
          source: 'SERVICE',
        },
        orderBy: { createdAt: 'desc' },
      });

      // If no history card exists, create one
      if (!historyCard) {
        // Get the next card number
        const lastCard = await tx.history_cards.findFirst({
          orderBy: { cardNo: 'desc' },
        });
        const nextCardNo = lastCard ? (lastCard.cardNo || 0) + 1 : 1;

        historyCard = await tx.history_cards.create({
          data: {
            customerId: serviceReport.customerId,
            cardNo: nextCardNo,
            source: 'SERVICE',
          },
        });
      }

      // Create history sections for the service report
      const serviceContent = this.formatServiceReportForHistory(serviceReport, details);

      await tx.history_sections.create({
        data: {
          historyCardId: historyCard.id,
          sectionCode: `SVC-${serviceReport.id.slice(-8)}`,
          content: serviceContent,
        },
      });

      // Create repair entries if applicable
      if (serviceReport.complaintType === 'REPAIR' && details.length > 0) {
        for (const detail of details) {
          await tx.history_repairs.create({
            data: {
              historyCardId: historyCard.id,
              repairDate: serviceReport.reportDate,
              problem: detail.problem,
              solution: detail.solution,
              partReplaced: detail.partReplaced || null,
              machineType: detail.machineType,
              serialNumber: detail.serialNumber,
            },
          });
        }
      }

      // Create maintenance entries if applicable
      if (serviceReport.complaintType === 'MAINTENANCE' && details.length > 0) {
        for (const detail of details) {
          await tx.history_maintenance.create({
            data: {
              historyCardId: historyCard.id,
              maintenanceDate: serviceReport.reportDate,
              maintenanceType: serviceReport.natureOfService,
              description: detail.solution,
              machineType: detail.machineType,
              serialNumber: detail.serialNumber,
            },
          });
        }
      }

      // Create complaint entries for all service types
      await tx.history_complaints.create({
        data: {
          historyCardId: historyCard.id,
          complaintDate: serviceReport.reportDate,
          complaintType: serviceReport.complaintType,
          description: serviceReport.natureOfService,
          actionTaken: serviceReport.actionTaken || '',
          status: serviceReport.status,
          executiveId: serviceReport.executiveId,
        },
      });

    } catch (error) {
      console.error('Error creating/updating history card for service report:', error);
      // Don't throw error to avoid breaking the main transaction
      // History card creation is supplementary
    }
  }

  /**
   * Format service report data for history card content
   * @param serviceReport Service report data
   * @param details Service details data
   * @returns Formatted content string
   */
  private formatServiceReportForHistory(serviceReport: any, details: any[] = []): string {
    const lines = [
      `Service Report: ${serviceReport.id}`,
      `Date: ${new Date(serviceReport.reportDate).toLocaleDateString()}`,
      `Type: ${serviceReport.complaintType}`,
      `Nature: ${serviceReport.natureOfService}`,
      `Status: ${serviceReport.status}`,
    ];

    if (serviceReport.visitDate) {
      lines.push(`Visit Date: ${new Date(serviceReport.visitDate).toLocaleDateString()}`);
    }

    if (serviceReport.completionDate) {
      lines.push(`Completion Date: ${new Date(serviceReport.completionDate).toLocaleDateString()}`);
    }

    if (serviceReport.actionTaken) {
      lines.push(`Action Taken: ${serviceReport.actionTaken}`);
    }

    if (details.length > 0) {
      lines.push('');
      lines.push('Service Details:');
      details.forEach((detail, index) => {
        lines.push(`${index + 1}. Machine: ${detail.machineType} (${detail.serialNumber})`);
        lines.push(`   Problem: ${detail.problem}`);
        lines.push(`   Solution: ${detail.solution}`);
        if (detail.partReplaced) {
          lines.push(`   Part Replaced: ${detail.partReplaced}`);
        }
      });
    }

    if (serviceReport.remarks) {
      lines.push('');
      lines.push(`Remarks: ${serviceReport.remarks}`);
    }

    return lines.join('\n');
  }

  /**
   * Update service report status
   * @param id Service report ID
   * @param status New status
   * @param completionDate Completion date (optional)
   * @returns Promise resolving to the updated service report
   */
  async updateStatus(id: string, status: string, completionDate?: Date): Promise<any> {
    const updateData: any = { status };
    if (status === 'COMPLETED' && completionDate) {
      updateData.completionDate = completionDate;
    }

    return this.model.update({
      where: { id },
      data: updateData,
      include: {
        customer: true,
        executive: true,
        details: true,
      },
    });
  }

  /**
   * Validate filter object to prevent injection attacks
   * @param filter Filter object to validate
   */
  private validateFilter(filter: any): void {
    const allowedFields = [
      'id',
      'customerId',
      'executiveId',
      'status',
      'reportDate',
      'visitDate',
      'completionDate',
      'natureOfService',
      'complaintType',
      'originalId',
    ];

    for (const key in filter) {
      if (!allowedFields.includes(key)) {
        throw new Error(`Invalid filter field: ${key}`);
      }
    }
  }
}
