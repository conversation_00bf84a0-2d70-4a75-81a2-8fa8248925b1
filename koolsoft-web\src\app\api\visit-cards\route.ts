import { NextRequest, NextResponse } from 'next/server';
import { getVisitCardRepository } from '@/lib/repositories';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * Visit card creation schema
 */
const createVisitCardSchema = z.object({
  customerId: z.string().uuid(),
  filePath: z.string(),
  notes: z.string().optional(),
});

/**
 * GET /api/visit-cards
 * Get all visit cards with optional pagination and filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    console.log('Session in GET /api/visit-cards:', session);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');
    const customerId = searchParams.get('customerId');
    const userId = searchParams.get('userId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const search = searchParams.get('search');

    const visitCardRepository = getVisitCardRepository();

    let visitCards = [];
    let total = 0;

    if (customerId) {
      // Get visit cards for a specific customer
      visitCards = await visitCardRepository.findByCustomerId(customerId, skip, take);
      total = await visitCardRepository.count({ customerId });
    } else if (userId) {
      // Get visit cards for a specific user
      visitCards = await visitCardRepository.findByUserId(userId, skip, take);
      total = await visitCardRepository.count({ userId });
    } else if (status) {
      // Get visit cards by status
      visitCards = await visitCardRepository.findByStatus(status, skip, take);
      total = await visitCardRepository.count({ status });
    } else if (startDate && endDate) {
      // Get visit cards by date range
      const start = new Date(startDate);
      const end = new Date(endDate);
      visitCards = await visitCardRepository.findByDateRange(start, end, skip, take);
      total = await visitCardRepository.count({
        visitDate: {
          gte: start,
          lte: end,
        },
      });
    } else if (search) {
      // Search visit cards by notes or file path
      visitCards = await visitCardRepository.search(search, skip, take);
      total = await visitCardRepository.countSearch(search);
    } else {
      // Get all visit cards
      visitCards = await visitCardRepository.findAll(skip, take);
      total = await visitCardRepository.count();
    }

    return NextResponse.json({
      visitCards,
      meta: {
        total,
        skip,
        take,
      },
    });
  } catch (error) {
    console.error('Error fetching visit cards:', error);
    return NextResponse.json(
      { error: 'Failed to fetch visit cards' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/visit-cards
 * Create a new visit card
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    console.log('Session in POST /api/visit-cards:', session);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate request body
    const validatedData = createVisitCardSchema.parse(body);

    const visitCardRepository = getVisitCardRepository();

    // Create visit card
    const visitCard = await visitCardRepository.create(validatedData);

    return NextResponse.json(visitCard, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating visit card:', error);
    return NextResponse.json(
      { error: 'Failed to create visit card' },
      { status: 500 }
    );
  }
}
