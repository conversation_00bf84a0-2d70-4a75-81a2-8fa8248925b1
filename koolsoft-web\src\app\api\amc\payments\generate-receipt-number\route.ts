import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCPaymentRepository } from '@/lib/repositories';

/**
 * GET /api/amc/payments/generate-receipt-number
 * Generate the next available receipt number
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const amcPaymentRepository = getAMCPaymentRepository();
      const receiptNumber = await amcPaymentRepository.generateReceiptNumber();

      return NextResponse.json({ receiptNumber });
    } catch (error) {
      console.error('Error generating receipt number:', error);
      return NextResponse.json(
        { error: 'Failed to generate receipt number' },
        { status: 500 }
      );
    }
  }
);
