'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import {
  Home,
  Database,
  RefreshCw,
  Search,
  Plus,
  Edit,
  Trash,
  Loader2
} from 'lucide-react';

// Define the form schema for reference data items
const referenceItemFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be at most 100 characters'),
  description: z.string().max(500, 'Description must be at most 500 characters').optional()
});

type ReferenceItemFormValues = z.infer<typeof referenceItemFormSchema>;

// Define category titles and descriptions
const categoryInfo: Record<string, { title: string; description: string }> = {
  territories: {
    title: 'Territories',
    description: 'Geographic regions for business operations'
  },
  segments: {
    title: 'Segments',
    description: 'Customer segmentation categories'
  },
  competitors: {
    title: 'Competitors',
    description: 'Competing businesses in the market'
  },
  serviceVisitType: {
    title: 'Service Visit Types',
    description: 'Types of service visits conducted'
  },
  complaintType: {
    title: 'Complaint Types',
    description: 'Categories of customer complaints'
  },
  complaintNatureType: {
    title: 'Complaint Nature Types',
    description: 'Nature or characteristics of complaints'
  },
  failureType: {
    title: 'Failure Types',
    description: 'Categories of equipment failures'
  },
  spareType: {
    title: 'Spare Types',
    description: 'Categories of spare parts'
  },
  measurementType: {
    title: 'Measurement Types',
    description: 'Types of measurements used'
  },
  priorityTypes: {
    title: 'Priority Types',
    description: 'Levels of priority for tasks and issues'
  },
  priorityType: {
    title: 'Priority Types',
    description: 'Levels of priority for tasks and issues'
  },
  enquiryTypes: {
    title: 'Enquiry Types',
    description: 'Categories of customer enquiries'
  },
  enquiryType: {
    title: 'Enquiry Types',
    description: 'Categories of customer enquiries'
  },
  deductionTypes: {
    title: 'Deduction Types',
    description: 'Types of deductions applied'
  },
  deductionType: {
    title: 'Deduction Types',
    description: 'Types of deductions applied'
  },
  debitDivisions: {
    title: 'Debit Divisions',
    description: 'Divisions for debit categorization'
  },
  debitDivision: {
    title: 'Debit Divisions',
    description: 'Divisions for debit categorization'
  },
  accountDivisions: {
    title: 'Account Divisions',
    description: 'Divisions for account categorization'
  },
  accountDivision: {
    title: 'Account Divisions',
    description: 'Divisions for account categorization'
  },
  spareParts: {
    title: 'Spare Parts',
    description: 'Replacement parts for equipment'
  },
  sparePart: {
    title: 'Spare Parts',
    description: 'Replacement parts for equipment'
  },
  taxRates: {
    title: 'Tax Rates',
    description: 'Different tax rates applied to products'
  },
  taxRate: {
    title: 'Tax Rates',
    description: 'Different tax rates applied to products'
  },
  transitDamageTypes: {
    title: 'Transit Damage Types',
    description: 'Types of damage that can occur during transit'
  },
  transitDamageType: {
    title: 'Transit Damage Types',
    description: 'Types of damage that can occur during transit'
  },
  userGroups: {
    title: 'User Groups',
    description: 'Groups of users with similar permissions'
  },
  userGroup: {
    title: 'User Groups',
    description: 'Groups of users with similar permissions'
  },
  uspTypes: {
    title: 'USP Types',
    description: 'Unique selling proposition categories'
  },
  uspType: {
    title: 'USP Types',
    description: 'Unique selling proposition categories'
  },
  visitTypes: {
    title: 'Visit Types',
    description: 'Types of customer visits'
  },
  visitType: {
    title: 'Visit Types',
    description: 'Types of customer visits'
  },
  divisions: {
    title: 'Divisions',
    description: 'Business divisions or departments'
  },
  brands: {
    title: 'Brands',
    description: 'Product brands and manufacturers'
  },
  serviceCenters: {
    title: 'Service Centers',
    description: 'Authorized service centers and vendors'
  }
};

/**
 * Reference Data Category Page
 *
 * This page displays and manages reference data items for a specific category.
 */
export default function ReferenceDataCategoryPage() {
  const router = useRouter();
  const params = useParams();
  const type = params.type as string;

  // State for reference data items
  const [referenceItems, setReferenceItems] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [editItem, setEditItem] = useState<any | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for duplicate name handling
  const [forceUpdate, setForceUpdate] = useState(false);
  const [duplicateNameFound, setDuplicateNameFound] = useState(false);
  const [duplicateItemName, setDuplicateItemName] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Search state
  const [searchTerm, setSearchTerm] = useState('');

  // Get category info
  const categoryTitle = categoryInfo[type]?.title || type;
  const categoryDescription = categoryInfo[type]?.description || 'Reference data items';

  // Initialize form with react-hook-form
  const form = useForm<ReferenceItemFormValues>({
    resolver: zodResolver(referenceItemFormSchema),
    defaultValues: {
      name: '',
      description: ''
    }
  });

  // Fetch reference data items
  const fetchReferenceItems = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const skip = (currentPage - 1) * pageSize;
      const queryParams = new URLSearchParams({
        skip: skip.toString(),
        take: pageSize.toString()
      });

      // Add search term if provided
      if (searchTerm) {
        queryParams.append('search', searchTerm);
      }

      // Fetch data from API
      const response = await fetch(`/api/reference/${type}?${queryParams.toString()}`, {
        credentials: 'include' // Include credentials for authentication
      });

      if (!response.ok) {
        // Try to get detailed error message from response
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          // If we can't parse the response, just throw the original error
          throw new Error(`Failed to fetch ${categoryTitle.toLowerCase()}`);
        }

        // Check if this is a known error (like table doesn't exist)
        if (errorData && errorData.error) {
          if (errorData.error.includes('does not exist') ||
              errorData.error.includes('not found in Prisma schema')) {
            // Handle non-existent table gracefully
            setReferenceItems([]);
            setTotalItems(0);
            setTotalPages(0);
            setError(`The ${categoryTitle.toLowerCase()} reference data is not available yet.`);
            return;
          }
        }

        throw new Error(errorData?.error || `Failed to fetch ${categoryTitle.toLowerCase()}`);
      }

      const data = await response.json();

      // Update state with fetched data
      setReferenceItems(data.items || []);
      setTotalItems(data.meta?.total || 0);
      setTotalPages(Math.ceil((data.meta?.total || 0) / pageSize));

      // If we got empty data but no error, show a message
      if ((data.items || []).length === 0 && data.meta?.total === 0) {
        setError(`No ${categoryTitle.toLowerCase()} found. Try adding a new item.`);
      }
    } catch (error: any) {
      console.error(`Error fetching ${categoryTitle.toLowerCase()}:`, error);
      setError(error.message || `Failed to load ${categoryTitle.toLowerCase()}`);
      toast({
        title: 'Error',
        description: `Failed to load ${categoryTitle.toLowerCase()}. Please try again.`,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on initial load and when filters or pagination changes
  useEffect(() => {
    fetchReferenceItems();
  }, [currentPage, pageSize, searchTerm, type]);

  // Reset form when dialog opens or edit item changes
  useEffect(() => {
    if (isDialogOpen) {
      if (editItem) {
        form.reset({
          name: editItem.name || '',
          description: editItem.description || ''
        });
      } else {
        form.reset({
          name: '',
          description: ''
        });
      }
      // Reset duplicate name state
      setDuplicateNameFound(false);
      setDuplicateItemName('');
      setForceUpdate(false);
    }
  }, [isDialogOpen, editItem, form]);

  // Handle search
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page when searching
    fetchReferenceItems();
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle force update
  const handleForceUpdate = () => {
    setForceUpdate(true);
    setDuplicateNameFound(false);

    // Re-submit the form with the current values
    const currentValues = form.getValues();
    onSubmit(currentValues);
  };

  // Check if a name already exists
  const checkNameExists = async (name: string): Promise<boolean> => {
    try {
      // Build query parameters with search term for exact match
      const queryParams = new URLSearchParams({
        search: name,
        take: '10',
        exactMatch: 'true'
      });

      // Fetch data from API
      const response = await fetch(`/api/reference/${type}?${queryParams.toString()}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to check for existing ${categoryTitle.toLowerCase()}`);
      }

      const data = await response.json();

      // If we're editing an item, we need to exclude the current item from the check
      if (editItem) {
        return data.items.some((item: any) =>
          item.name.toLowerCase() === name.toLowerCase() && item.id !== editItem.id
        );
      }

      // For new items, check if any item with the same name exists (exact match only)
      return data.items.some((item: any) => item.name.toLowerCase() === name.toLowerCase());
    } catch (error) {
      console.error('Error checking for existing name:', error);
      return false; // Assume name doesn't exist if check fails
    }
  };

  // Handle form submission
  const onSubmit = async (data: ReferenceItemFormValues) => {
    setIsSubmitting(true);

    try {
      // Check if name already exists (skip if force update is enabled)
      if (!forceUpdate) {
        const nameExists = await checkNameExists(data.name);

        if (nameExists) {
          // Store the duplicate name for the confirmation dialog
          setDuplicateNameFound(true);
          setDuplicateItemName(data.name);
          setIsSubmitting(false);
          return; // Stop here and wait for user confirmation
        }
      }

      // Reset force update flag
      if (forceUpdate) {
        setForceUpdate(false);
      }

      let response;

      if (editItem) {
        // Update existing item
        response = await fetch(`/api/reference/${type}/${editItem.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data),
          credentials: 'include'
        });
      } else {
        // Create new item
        response = await fetch(`/api/reference/${type}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data),
          credentials: 'include'
        });
      }

      if (!response.ok) {
        // Try to get detailed error message from response
        let errorMessage;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || (editItem ? 'Failed to update item' : 'Failed to create item');

          // Check if this is a duplicate name error from the server
          if (errorMessage.includes('already exists')) {
            // Store the duplicate name for the confirmation dialog
            setDuplicateNameFound(true);
            setDuplicateItemName(data.name);
            setIsSubmitting(false);
            return; // Stop here and wait for user confirmation
          }
        } catch (e) {
          errorMessage = editItem ? 'Failed to update item' : 'Failed to create item';
        }
        throw new Error(errorMessage);
      }

      // Close dialog and refresh data
      setIsDialogOpen(false);
      setDuplicateNameFound(false); // Reset duplicate name flag
      fetchReferenceItems();

      toast({
        title: 'Success',
        description: editItem ? 'Item updated successfully' : 'Item created successfully'
      });
    } catch (error: any) {
      console.error('Error saving reference item:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to save item. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
      if (!duplicateNameFound) {
        setEditItem(null);
      }
    }
  };

  // Handle delete item
  const handleDeleteItem = async () => {
    if (!deleteItemId) return;

    try {
      setIsDeleting(true);

      const response = await fetch(`/api/reference/${type}/${deleteItemId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        // Try to get detailed error message from response
        let errorMessage;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || 'Failed to delete item';
        } catch (e) {
          errorMessage = 'Failed to delete item';
        }
        throw new Error(errorMessage);
      }

      // Refresh the list after deletion
      fetchReferenceItems();

      toast({
        title: 'Success',
        description: 'Item deleted successfully'
      });
    } catch (error: any) {
      console.error('Error deleting reference item:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete item. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsDeleting(false);
      setDeleteItemId(null);
    }
  };

  // Render loading state
  if (isLoading && !referenceItems.length) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-64 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">

      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <div className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>{categoryTitle}</CardTitle>
              <CardDescription className="text-gray-100">
                {categoryDescription}
              </CardDescription>
            </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="secondary" onClick={() => setEditItem(null)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{editItem ? 'Edit' : 'Add'} {categoryTitle.slice(0, -1)}</DialogTitle>
                  <DialogDescription>
                    {editItem
                      ? `Update the details for this ${categoryTitle.toLowerCase().slice(0, -1)}.`
                      : `Add a new ${categoryTitle.toLowerCase().slice(0, -1)} to the system.`}
                  </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name*</FormLabel>
                          <FormControl>
                            <Input
                              placeholder={`Enter ${categoryTitle.toLowerCase().slice(0, -1)} name`}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Input
                              placeholder={`Enter ${categoryTitle.toLowerCase().slice(0, -1)} description`}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Optional description for this item
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <DialogFooter>
                      <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {editItem ? 'Update' : 'Create'}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>

            {/* Duplicate Name Confirmation Dialog */}
            <AlertDialog open={duplicateNameFound} onOpenChange={setDuplicateNameFound}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Duplicate Name Detected</AlertDialogTitle>
                  <AlertDialogDescription>
                    A {categoryTitle.toLowerCase().slice(0, -1)} with the name &quot;{duplicateItemName}&quot; already exists.
                    Do you want to force the update anyway? This might affect existing references.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={() => setDuplicateNameFound(false)}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction onClick={handleForceUpdate}>
                    Force Update
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardHeader>
        <CardContent className="space-y-6 pt-6">
          {/* Search Form */}
          <form onSubmit={handleSearch} className="flex space-x-2">
            <div className="flex-1">
              <Label htmlFor="search" className="sr-only">
                Search
              </Label>
              <Input
                id="search"
                placeholder={`Search ${categoryTitle.toLowerCase()}`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            <Button type="button" variant="outline" onClick={() => {
              setSearchTerm('');
              setCurrentPage(1);
            }}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </form>

          {/* Reference Items Table */}
          {error ? (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          ) : referenceItems.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              No {categoryTitle.toLowerCase()} found. Try adjusting your search or add a new item.
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {referenceItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>{item.description || '-'}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setEditItem(item);
                                  setIsDialogOpen(true);
                                }}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </Button>
                            </DialogTrigger>
                          </Dialog>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600"
                                onClick={() => setDeleteItemId(item.id)}
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                Delete
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This action cannot be undone. This will permanently delete the
                                  {` ${categoryTitle.toLowerCase().slice(0, -1)} `}
                                  and remove it from our servers.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel onClick={() => setDeleteItemId(null)}>
                                  Cancel
                                </AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={handleDeleteItem}
                                  className="bg-red-600 text-white hover:bg-red-700"
                                  disabled={isDeleting}
                                >
                                  {isDeleting ? 'Deleting...' : 'Delete'}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    />
                  </PaginationItem>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNumber = i + 1;
                    return (
                      <PaginationItem key={pageNumber}>
                        <PaginationLink
                          onClick={() => handlePageChange(pageNumber)}
                          isActive={currentPage === pageNumber}
                        >
                          {pageNumber}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  {totalPages > 5 && (
                    <>
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => handlePageChange(totalPages)}
                          isActive={currentPage === totalPages}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
