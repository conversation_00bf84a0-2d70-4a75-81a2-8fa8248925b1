/**
 * <PERSON><PERSON><PERSON> to create service centers table and populate with initial data
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createServiceCentersTable() {
  try {
    console.log('Creating service centers table...');

    // Check if table already exists
    const existingServiceCenters = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'service_centers'
    `;

    if (existingServiceCenters.length > 0) {
      console.log('Service centers table already exists. Checking for data...');
      
      const count = await prisma.serviceCenter.count();
      if (count > 0) {
        console.log(`Service centers table already has ${count} records.`);
        return;
      }
    } else {
      // Create the table using raw SQL
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS "service_centers" (
          "id" TEXT NOT NULL,
          "name" TEXT NOT NULL,
          "vendor" TEXT,
          "address" TEXT,
          "city" TEXT,
          "state" TEXT,
          "pincode" TEXT,
          "phone" TEXT,
          "email" TEXT,
          "contact_person" TEXT,
          "active" BOOLEAN NOT NULL DEFAULT true,
          "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT "service_centers_pkey" PRIMARY KEY ("id")
        )
      `;

      // Create indexes
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "service_centers_name_idx" ON "service_centers"("name")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "service_centers_vendor_idx" ON "service_centers"("vendor")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "service_centers_city_idx" ON "service_centers"("city")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "service_centers_active_idx" ON "service_centers"("active")`;

      console.log('Service centers table created successfully.');
    }

    // Insert initial BLUESTAR service centers data
    console.log('Inserting initial service centers data...');

    const serviceCenters = [
      {
        name: 'Mumbai Central Service Center',
        vendor: 'BLUESTAR',
        address: 'Plot No. 123, Industrial Area, Andheri East',
        city: 'Mumbai',
        state: 'Maharashtra',
        pincode: '400069',
        phone: '+91-22-2821-5678',
        email: '<EMAIL>',
        contactPerson: 'Rajesh Kumar',
        active: true
      },
      {
        name: 'Delhi North Service Center',
        vendor: 'BLUESTAR',
        address: 'A-45, Sector 63, Noida',
        city: 'Delhi',
        state: 'Delhi',
        pincode: '110001',
        phone: '+91-11-4567-8901',
        email: '<EMAIL>',
        contactPerson: 'Amit Sharma',
        active: true
      },
      {
        name: 'Bangalore South Service Center',
        vendor: 'BLUESTAR',
        address: 'No. 78, Electronic City Phase 1',
        city: 'Bangalore',
        state: 'Karnataka',
        pincode: '560100',
        phone: '+91-80-2345-6789',
        email: '<EMAIL>',
        contactPerson: 'Suresh Reddy',
        active: true
      },
      {
        name: 'Chennai East Service Center',
        vendor: 'BLUESTAR',
        address: '56, OMR Road, Thoraipakkam',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600097',
        phone: '+91-44-3456-7890',
        email: '<EMAIL>',
        contactPerson: 'Venkat Raman',
        active: true
      },
      {
        name: 'Kolkata West Service Center',
        vendor: 'BLUESTAR',
        address: 'Plot 234, Salt Lake City, Sector V',
        city: 'Kolkata',
        state: 'West Bengal',
        pincode: '700091',
        phone: '+91-33-4567-8901',
        email: '<EMAIL>',
        contactPerson: 'Debashish Roy',
        active: true
      },
      {
        name: 'Pune Central Service Center',
        vendor: 'BLUESTAR',
        address: 'Survey No. 45, Hinjewadi Phase 2',
        city: 'Pune',
        state: 'Maharashtra',
        pincode: '411057',
        phone: '+91-20-6789-0123',
        email: '<EMAIL>',
        contactPerson: 'Pradeep Patil',
        active: true
      },
      {
        name: 'Hyderabad Service Center',
        vendor: 'BLUESTAR',
        address: 'Plot No. 67, HITEC City, Madhapur',
        city: 'Hyderabad',
        state: 'Telangana',
        pincode: '500081',
        phone: '+91-40-7890-1234',
        email: '<EMAIL>',
        contactPerson: 'Krishna Murthy',
        active: true
      },
      {
        name: 'Ahmedabad Service Center',
        vendor: 'BLUESTAR',
        address: 'Plot 89, SG Highway, Gota',
        city: 'Ahmedabad',
        state: 'Gujarat',
        pincode: '382481',
        phone: '+91-79-8901-2345',
        email: '<EMAIL>',
        contactPerson: 'Kiran Patel',
        active: true
      }
    ];

    // Insert service centers using createMany
    await prisma.serviceCenter.createMany({
      data: serviceCenters,
      skipDuplicates: true
    });

    console.log(`Successfully inserted ${serviceCenters.length} service centers.`);

    // Verify the data
    const count = await prisma.serviceCenter.count();
    console.log(`Total service centers in database: ${count}`);

    const bluestarCount = await prisma.serviceCenter.count({
      where: { vendor: 'BLUESTAR' }
    });
    console.log(`BLUESTAR service centers: ${bluestarCount}`);

  } catch (error) {
    console.error('Error creating service centers table:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  createServiceCentersTable()
    .then(() => {
      console.log('Service centers table creation completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Service centers table creation failed:', error);
      process.exit(1);
    });
}

module.exports = { createServiceCentersTable };
