/**
 * Remove Legacy Repositories Script
 * 
 * This script removes the legacy repositories directory after confirming
 * that no files in the codebase are still importing from it.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readline = require('readline');

const readFileAsync = promisify(fs.readFile);
const readdirAsync = promisify(fs.readdir);
const statAsync = promisify(fs.stat);
const rmdirAsync = promisify(fs.rmdir);
const unlinkAsync = promisify(fs.unlink);

/**
 * Create a readline interface for user input
 * @returns {readline.Interface} - Readline interface
 */
function createInterface() {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

/**
 * Ask a yes/no question
 * @param {string} question - Question to ask
 * @returns {Promise<boolean>} - User's answer
 */
function askQuestion(question) {
  const rl = createInterface();
  
  return new Promise(resolve => {
    rl.question(`${question} (y/n): `, answer => {
      rl.close();
      resolve(answer.toLowerCase() === 'y');
    });
  });
}

/**
 * Recursively scan a directory for files
 * @param {string} dir - Directory to scan
 * @param {Array<string>} fileList - List of files found
 * @returns {Promise<Array<string>>} - List of files found
 */
async function scanDirectory(dir, fileList = []) {
  const files = await readdirAsync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = await statAsync(filePath);
    
    if (stat.isDirectory()) {
      if (!filePath.includes('node_modules') && !filePath.includes('.next')) {
        fileList = await scanDirectory(filePath, fileList);
      }
    } else if (file.endsWith('.js') || file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  }
  
  return fileList;
}

/**
 * Check if a file imports from the legacy repositories
 * @param {string} filePath - Path to the file
 * @returns {Promise<boolean>} - Whether the file imports from legacy repositories
 */
async function checkFileImports(filePath) {
  const content = await readFileAsync(filePath, 'utf8');
  
  // Check for various import patterns
  const importPatterns = [
    "from '../../../repositories'",
    "from '../../repositories'",
    "from '../repositories'",
    "from './repositories'",
    "require('../../../repositories')",
    "require('../../repositories')",
    "require('../repositories')",
    "require('./repositories')"
  ];
  
  return importPatterns.some(pattern => content.includes(pattern));
}

/**
 * Recursively remove a directory
 * @param {string} dir - Directory to remove
 */
async function removeDirectory(dir) {
  const files = await readdirAsync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = await statAsync(filePath);
    
    if (stat.isDirectory()) {
      await removeDirectory(filePath);
    } else {
      await unlinkAsync(filePath);
    }
  }
  
  await rmdirAsync(dir);
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('Checking for files that still import from legacy repositories...');
    
    // Scan the src directory
    const srcDir = path.join(__dirname, '..', 'src');
    const files = await scanDirectory(srcDir);
    
    console.log(`Found ${files.length} files to check.`);
    
    // Check each file for imports from legacy repositories
    const filesWithLegacyImports = [];
    for (const file of files) {
      const hasLegacyImports = await checkFileImports(file);
      if (hasLegacyImports) {
        filesWithLegacyImports.push(file);
      }
    }
    
    if (filesWithLegacyImports.length > 0) {
      console.log('\nThe following files still import from legacy repositories:');
      filesWithLegacyImports.forEach(file => {
        console.log(`- ${path.relative(__dirname, file)}`);
      });
      
      console.log('\nPlease update these files to use the modern repositories before removing the legacy repositories.');
      return;
    }
    
    console.log('\nNo files found that import from legacy repositories.');
    
    // Confirm removal
    const shouldRemove = await askQuestion('Do you want to remove the legacy repositories directory?');
    
    if (!shouldRemove) {
      console.log('Operation cancelled.');
      return;
    }
    
    // Remove the legacy repositories directory
    const legacyReposDir = path.join(__dirname, '..', 'src', 'repositories');
    await removeDirectory(legacyReposDir);
    
    console.log('\nLegacy repositories directory has been removed.');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the script
main();
