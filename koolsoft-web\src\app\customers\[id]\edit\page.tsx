'use client';

import { useState, useEffect } from 'react';
import { CustomerForm } from '@/components/customers/customer-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { ManagerGate } from '@/components/auth/role-gate';
import { Skeleton, SpanSkeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { Home } from 'lucide-react';
import { useParams } from 'next/navigation';
import { toast } from '@/components/ui/use-toast';

/**
 * Edit Customer Page
 *
 * This page allows users with ADMIN or MANAGER roles to edit an existing customer.
 */
export default function EditCustomerPage() {
  const params = useParams();
  const customerId = params.id as string;

  const [customer, setCustomer] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch customer data
  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        const response = await fetch(`/api/customers/${customerId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch customer data');
        }

        const data = await response.json();
        setCustomer(data);
      } catch (error: any) {
        console.error('Error fetching customer:', error);
        setError(error.message || 'Failed to load customer data');
        toast({
          title: 'Error',
          description: 'Failed to load customer data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomer();
  }, [customerId]);

  return (
    <ManagerGate fallback={
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
          <p className="text-gray-500">You do not have permission to access this page</p>
          <div className="mt-4">
            <Link href="/dashboard" className="text-blue-600 hover:text-blue-500">
              Return to Dashboard
            </Link>
          </div>
        </div>
      </div>
    }>
      <div className="space-y-6">
        {isLoading ? (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>
                <Skeleton className="h-9 w-64" />
              </CardTitle>
              <CardDescription>
                <SpanSkeleton className="h-5 w-48" />
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <Skeleton className="h-12 w-full" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        ) : error ? (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Error</CardTitle>
              <CardDescription>
                There was a problem loading the customer data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong className="font-bold">Error: </strong>
                <span className="block sm:inline">{error}</span>
              </div>
            </CardContent>
          </Card>
        ) : customer ? (
          <CustomerForm customer={customer} />
        ) : (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Customer Not Found</CardTitle>
              <CardDescription>
                The requested customer could not be found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative" role="alert">
                <strong className="font-bold">Not Found: </strong>
                <span className="block sm:inline">The requested customer could not be found.</span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </ManagerGate>
  );
}
