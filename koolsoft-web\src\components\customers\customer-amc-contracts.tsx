import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  DollarSign, 
  Package, 
  ChevronRight, 
  Clock 
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';
import Link from 'next/link';

interface CustomerAMCContractsProps {
  amcContracts: any[];
}

/**
 * Customer AMC Contracts Component
 *
 * This component displays the AMC contracts associated with a customer.
 */
export function CustomerAMCContracts({ amcContracts }: CustomerAMCContractsProps) {
  // Function to determine contract status badge
  const getStatusBadge = (status: string, endDate: string) => {
    const now = new Date();
    const end = new Date(endDate);
    
    if (status === 'ACTIVE' && end < now) {
      return <Badge className="bg-yellow-500 text-white">Expired</Badge>;
    }
    
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-green-500 text-white">Active</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-500 text-white">Pending</Badge>;
      case 'EXPIRED':
        return <Badge className="bg-gray-500 text-white">Expired</Badge>;
      case 'CANCELLED':
        return <Badge className="bg-red-500 text-white">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (!amcContracts || amcContracts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>AMC Contracts</CardTitle>
          <CardDescription>
            Annual Maintenance Contracts for this customer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-gray-500">
            No AMC contracts found for this customer.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>AMC Contracts</CardTitle>
        <CardDescription>
          Annual Maintenance Contracts for this customer
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Contract Number</TableHead>
              <TableHead>Period</TableHead>
              <TableHead>Machines</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {amcContracts.map((contract) => (
              <TableRow key={contract.id}>
                <TableCell className="font-medium">
                  {contract.contractNumber || `AMC-${contract.id.substring(0, 8)}`}
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      <span>Start: {formatDate(contract.startDate)}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      <span>End: {formatDate(contract.endDate)}</span>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Package className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{contract.machines?.length || 0} machines</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{formatCurrency(contract.amount)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(contract.status, contract.endDate)}
                </TableCell>
                <TableCell className="text-right">
                  <Button asChild variant="ghost" size="sm">
                    <Link href={`/amc/contracts/${contract.id}`}>
                      View Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
