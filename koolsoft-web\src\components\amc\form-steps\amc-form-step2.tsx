'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CalendarIcon, FileText, ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAMCForm, AMCFormStep } from '@/contexts/amc-form-context';

// Step 2 validation schema
const step2Schema = z.object({
  startDate: z.date({ required_error: 'Start date is required' }),
  endDate: z.date({ required_error: 'End date is required' }),
  warningDate: z.date().optional(),
  amount: z.number({
    required_error: 'Contract amount is required',
    invalid_type_error: 'Amount must be a number',
  }).positive({ message: 'Amount must be greater than 0' }),
  paidAmount: z.number().nonnegative().optional(),
  numberOfMachines: z.number().int().nonnegative().optional(),
  numberOfServices: z.number().int().positive().optional(),
  totalTonnage: z.number().nonnegative().optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED', 'RENEWED']).default('ACTIVE'),
}).refine(
  (data) => {
    // Ensure end date is after start date
    return data.endDate > data.startDate;
  },
  {
    message: 'End date must be after start date',
    path: ['endDate'],
  }
).refine(
  (data) => {
    // If warning date is provided, ensure it's between start and end date
    if (data.warningDate) {
      return data.warningDate >= data.startDate && data.warningDate <= data.endDate;
    }
    return true;
  },
  {
    message: 'Warning date must be between start and end date',
    path: ['warningDate'],
  }
);

type Step2FormValues = z.infer<typeof step2Schema>;

export function AMCFormStep2() {
  const { state, updateFormData, goToNextStep, goToPreviousStep, dispatch } = useAMCForm();

  // Initialize form
  const form = useForm<Step2FormValues>({
    resolver: zodResolver(step2Schema),
    defaultValues: {
      startDate: state.formData.startDate ? new Date(state.formData.startDate) : undefined,
      endDate: state.formData.endDate ? new Date(state.formData.endDate) : undefined,
      warningDate: state.formData.warningDate ? new Date(state.formData.warningDate) : undefined,
      amount: state.formData.amount || undefined,
      paidAmount: state.formData.paidAmount || 0,
      numberOfMachines: state.formData.numberOfMachines || undefined,
      numberOfServices: state.formData.numberOfServices || undefined,
      totalTonnage: state.formData.totalTonnage || undefined,
      status: state.formData.status || 'ACTIVE',
    },
    mode: 'onChange', // Enable validation on change
  });

  // Watch specific form values for validation (avoid watching all fields)
  const startDate = form.watch('startDate');
  const endDate = form.watch('endDate');
  const amount = form.watch('amount');
  const paidAmount = form.watch('paidAmount');

  // Validate step whenever critical form values change
  useEffect(() => {
    const isValid = !!startDate && !!endDate && !!amount && amount > 0;
    dispatch({
      type: 'SET_STEP_VALIDATION',
      payload: { step: AMCFormStep.CONTRACT_DETAILS, isValid },
    });
  }, [startDate, endDate, amount, dispatch]);

  // Calculate balance amount
  const balanceAmount = amount ? amount - (paidAmount || 0) : 0;

  // Watch warning date separately for auto-calculation
  const warningDate = form.watch('warningDate');

  // Auto-calculate warning date (30 days before end date)
  useEffect(() => {
    if (endDate && !warningDate && startDate) {
      const calculatedWarningDate = new Date(endDate);
      calculatedWarningDate.setDate(calculatedWarningDate.getDate() - 30);
      if (calculatedWarningDate >= startDate) {
        form.setValue('warningDate', calculatedWarningDate, { shouldValidate: false });
      }
    }
  }, [endDate, startDate, warningDate]); // Removed 'form' to prevent infinite re-renders

  const onSubmit = (values: Step2FormValues) => {
    // Calculate balance amount
    const formDataWithBalance = {
      ...values,
      balanceAmount: values.amount - (values.paidAmount || 0),
    };
    updateFormData(formDataWithBalance);
    goToNextStep();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Contract Details
        </CardTitle>
        <CardDescription>
          Define contract dates, amount, and terms
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Contract Dates */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Start Date */}
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>
                      Start Date <span className="text-red-500">*</span>
                    </FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick start date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* End Date */}
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>
                      End Date <span className="text-red-500">*</span>
                    </FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick end date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < (startDate || new Date())
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Warning Date */}
              <FormField
                control={form.control}
                name="warningDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Warning Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Auto-calculated</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            !startDate ||
                            !endDate ||
                            date < startDate ||
                            date > endDate
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      Date to send renewal reminder (auto-set to 30 days before end)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Financial Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Contract Amount */}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Contract Amount <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Paid Amount */}
              <FormField
                control={form.control}
                name="paidAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Paid Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Balance Amount (Calculated) */}
              <FormItem>
                <FormLabel>Balance Amount</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    value={balanceAmount.toFixed(2)}
                    disabled
                    className="bg-gray-50"
                  />
                </FormControl>
                <FormDescription>
                  Automatically calculated: Amount - Paid Amount
                </FormDescription>
              </FormItem>
            </div>

            {/* Contract Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Number of Machines */}
              <FormField
                control={form.control}
                name="numberOfMachines"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Number of Machines</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      Will be auto-calculated from machines added
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Number of Services */}
              <FormField
                control={form.control}
                name="numberOfServices"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Services per Year</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="4"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      Frequency of service visits per year
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Total Tonnage */}
              <FormField
                control={form.control}
                name="totalTonnage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Tonnage</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        placeholder="0.0"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      Will be auto-calculated from machines added
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Contract Status */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contract Status</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger className="w-full md:w-64">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                      <SelectItem value="EXPIRED">Expired</SelectItem>
                      <SelectItem value="CANCELLED">Cancelled</SelectItem>
                      <SelectItem value="RENEWED">Renewed</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Navigation */}
            <div className="flex justify-between">
              <Button type="button" variant="outline" onClick={goToPreviousStep}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              <Button
                type="submit"
                disabled={!startDate || !endDate || !amount || amount <= 0}
              >
                Next: Machine Management
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
