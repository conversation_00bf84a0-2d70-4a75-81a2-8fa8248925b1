import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Calendar,
  Download,
  Eye,
  Edit,
  Plus,
  User
} from 'lucide-react';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';

interface CustomerVisitCardsProps {
  visitCards: any[];
  customerId: string;
}

/**
 * Customer Visit Cards Component
 *
 * This component displays the visit cards associated with a customer.
 */
export function CustomerVisitCards({ visitCards, customerId }: CustomerVisitCardsProps) {
  // Handle view visit card file
  const handleViewCardFile = (card: any) => {
    window.open(card.filePath, '_blank');
  };

  // Handle download visit card
  const handleDownloadCard = (card: any) => {
    window.open(card.filePath, '_blank');
  };

  if (!visitCards || visitCards.length === 0) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle>Visit Cards</CardTitle>
            <CardDescription className="text-gray-100">
              Visit cards associated with this customer
            </CardDescription>
          </div>
          <Button asChild variant="secondary">
            <Link href={`/visit-cards/new?customerId=${customerId}`}>
              <Plus className="h-4 w-4 mr-2" />
              Add Visit Card
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-gray-500">
            No visit cards found for this customer. Click the "Add Visit Card" button to create one.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between bg-primary text-white">
        <div>
          <CardTitle>Visit Cards</CardTitle>
          <CardDescription className="text-gray-100">
            Visit cards associated with this customer
          </CardDescription>
        </div>
        <Button asChild variant="secondary">
          <Link href={`/visit-cards/new?customerId=${customerId}`}>
            <Plus className="h-4 w-4 mr-2" />
            Add Visit Card
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>File Name</TableHead>
              <TableHead>Upload Date</TableHead>
              <TableHead>Notes</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {visitCards.map((card) => (
              <TableRow key={card.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{card.filePath.split('/').pop()}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{formatDate(card.uploadDate)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {card.notes || <span className="text-gray-500">No notes</span>}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link href={`/visit-cards/${card.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Link>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link href={`/visit-cards/${card.id}/edit`}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Link>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewCardFile(card)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View File
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
