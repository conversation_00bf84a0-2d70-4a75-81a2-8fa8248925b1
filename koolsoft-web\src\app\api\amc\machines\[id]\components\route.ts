import { NextRequest, NextResponse } from 'next/server';
import { getAMCComponentRepository, getAMCMachineRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';

/**
 * Component query schema for machine
 */
const machineComponentQuerySchema = z.object({
  skip: z.coerce.number().int().nonnegative().default(0),
  take: z.coerce.number().int().positive().max(100).default(50),
  orderBy: z.enum(['createdAt', 'updatedAt', 'serialNumber', 'componentNo', 'warrantyDate']).default('createdAt'),
  orderDirection: z.enum(['asc', 'desc']).default('desc'),
  includeStatistics: z.coerce.boolean().default(false),
});

/**
 * GET /api/amc/machines/[id]/components
 * Get components for a specific AMC machine
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const { searchParams } = new URL(request.url);
      const queryParams = Object.fromEntries(searchParams.entries());

      // Validate query parameters
      const validatedQuery = machineComponentQuerySchema.parse(queryParams);

      const amcComponentRepository = getAMCComponentRepository();
      const amcMachineRepository = getAMCMachineRepository();

      // Check if machine exists
      const machine = await amcMachineRepository.findById(id);
      if (!machine) {
        return NextResponse.json(
          { error: 'AMC machine not found' },
          { status: 404 }
        );
      }

      // Get components for the machine
      const components = await amcComponentRepository.findByMachineId(id, {
        skip: validatedQuery.skip,
        take: validatedQuery.take,
        orderBy: {
          [validatedQuery.orderBy]: validatedQuery.orderDirection,
        },
      });

      // Get total count
      const total = await amcComponentRepository.count({
        machineId: id,
      });

      const response: any = {
        components,
        meta: {
          total,
          skip: validatedQuery.skip,
          take: validatedQuery.take,
          orderBy: validatedQuery.orderBy,
          orderDirection: validatedQuery.orderDirection,
        },
      };

      // Include statistics if requested
      if (validatedQuery.includeStatistics) {
        const statistics = await amcComponentRepository.getStatistics(id);
        response.statistics = statistics;
      }

      return NextResponse.json(response);
    } catch (error) {
      console.error('Error fetching machine components:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid query parameters', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch components' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/amc/machines/[id]/components
 * Delete all components for a specific AMC machine
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcComponentRepository = getAMCComponentRepository();
      const amcMachineRepository = getAMCMachineRepository();

      // Check if machine exists
      const machine = await amcMachineRepository.findById(id);
      if (!machine) {
        return NextResponse.json(
          { error: 'AMC machine not found' },
          { status: 404 }
        );
      }

      // Delete all components for the machine
      const deletedCount = await amcComponentRepository.deleteByMachineId(id);

      return NextResponse.json({
        message: `Successfully deleted ${deletedCount} components`,
        deletedCount,
      });
    } catch (error) {
      console.error('Error deleting machine components:', error);
      return NextResponse.json(
        { error: 'Failed to delete components' },
        { status: 500 }
      );
    }
  }
);
