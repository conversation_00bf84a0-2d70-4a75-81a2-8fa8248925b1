import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { hash } from 'bcrypt';
import { withActivityLogging } from '@/lib/middleware/activity-logger.middleware';

// Define the query parameters schema
const querySchema = z.object({
  skip: z.coerce.number().default(0),
  take: z.coerce.number().default(10),
  role: z.string().optional(),
  activeOnly: z.enum(['true', 'false']).optional(),
  search: z.string().optional(),
});

/**
 * GET /api/admin/users
 *
 * Get a list of users with pagination, filtering, and search
 * Only accessible to admin users
 */
async function handleGetUsers(req: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    if (!session) {
      console.log('API: No session found');
      return NextResponse.json({ error: 'Authentication required. Please log in.' }, { status: 401 });
    }

    if (!session.user) {
      console.log('API: Session exists but no user data found');
      return NextResponse.json({ error: 'Invalid session. Please log in again.' }, { status: 401 });
    }

    console.log('API: User role from session:', session.user.role);

    // Convert role to uppercase for comparison (case-insensitive check)
    const userRole = (session.user.role || '').toUpperCase();

    if (userRole !== 'ADMIN') {
      console.log(`API: Access denied - User role "${userRole}" is not ADMIN`);
      return NextResponse.json({
        error: 'Access denied. This resource requires administrator privileges.'
      }, { status: 403 });
    }

    console.log('API: Access granted - User is ADMIN');

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());

    try {
      const { skip, take, role, activeOnly, search } = querySchema.parse(queryParams);

      // Build the where clause for filtering
      const where: any = {};

      if (role && role !== 'all') {
        where.role = role;
      }

      if (activeOnly === 'true') {
        where.isActive = true;
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Get users with pagination using Prisma client with the modern users table
      try {
        console.log('Executing Prisma query with:', { where, skip, take });

        const [users, total] = await Promise.all([
          prisma.users.findMany({
            where,
            skip: Number(skip),
            take: Number(take),
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              isActive: true,
              phone: true,
              designation: true,
              createdAt: true,
              updatedAt: true,
              originalId: true,
            },
          }),
          prisma.users.count({ where }),
        ]);

        console.log(`Found ${users.length} users out of ${total} total`);

        // Return the results
        return NextResponse.json({
          users,
          meta: {
            total,
            skip,
            take,
          },
        });
      } catch (dbError: any) {
        console.error('Database error:', dbError);
        return NextResponse.json(
          { error: `Database error: ${dbError.message}` },
          { status: 500 }
        );
      }
    } catch (validationError) {
      console.error('Validation error:', validationError);
      return NextResponse.json(
        { error: 'Invalid query parameters' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: `Failed to fetch users: ${error.message}` },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/users
 *
 * Create a new user
 * Only accessible to admin users
 */
async function handleCreateUser(req: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    if (!session) {
      console.log('API: No session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('API: User role from session:', session.user.role);

    // Convert role to uppercase for comparison
    const userRole = session.user.role.toUpperCase();

    if (userRole !== 'ADMIN') {
      console.log(`API: Access denied - User role "${userRole}" is not ADMIN`);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    console.log('API: Access granted - User is ADMIN');

    // Parse request body
    const body = await req.json();

    // Validate request body
    const userSchema = z.object({
      name: z.string().min(2).max(100),
      email: z.string().email(),
      password: z.string().min(8),
      role: z.enum(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']),
      phone: z.string().optional(),
      designation: z.string().optional(),
      isActive: z.boolean().default(true),
    });

    const { name, email, password, role, phone, designation, isActive } = userSchema.parse(body);

    // Check if user with email already exists
    const existingUser = await prisma.users.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Hash the password before storing it
    const hashedPassword = await hash(password, 10);

    // Create the user
    const user = await prisma.users.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role,
        phone,
        designation,
        isActive,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true,
      },
    });

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}

// Export the handlers with activity logging
export const GET = async (req: NextRequest) => {
  try {
    // Call the original handler directly without activity logging for now
    return await handleGetUsers(req);
  } catch (error: any) {
    console.error('Error in GET /api/admin/users:', error);

    // Determine the appropriate status code based on the error
    let statusCode = 500;
    if (error.message?.includes('Unauthorized') || error.message?.includes('Authentication')) {
      statusCode = 401;
    } else if (error.message?.includes('Forbidden') || error.message?.includes('Access denied')) {
      statusCode = 403;
    } else if (error.message?.includes('Invalid') || error.message?.includes('validation')) {
      statusCode = 400;
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch users',
        message: error.message || 'An unexpected error occurred',
        timestamp: new Date().toISOString()
      },
      { status: statusCode }
    );
  }
};

export const POST = async (req: NextRequest) => {
  try {
    // Call the original handler directly without activity logging for now
    return await handleCreateUser(req);
  } catch (error) {
    console.error('Error in POST /api/admin/users:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
};