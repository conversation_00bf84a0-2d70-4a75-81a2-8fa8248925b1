/**
 * <PERSON><PERSON><PERSON> to swap the 'name' and 'address' columns in the customers table
 * This fixes a data mismatch where customer names were stored in the 'address' column
 * and addresses were stored in the 'name' column
 */
const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

async function swapCustomerNameAddress() {
  try {
    console.log('Starting to swap customer name and address columns...');

    // Execute the SQL commands to swap the columns
    await prisma.$executeRaw`ALTER TABLE customers RENAME COLUMN name TO temp_name`;
    console.log('Step 1: Renamed name to temp_name');

    await prisma.$executeRaw`ALTER TABLE customers RENAME COLUMN address TO name`;
    console.log('Step 2: Renamed address to name');

    await prisma.$executeRaw`ALTER TABLE customers RENAME COLUMN temp_name TO address`;
    console.log('Step 3: Renamed temp_name to address');

    console.log('Successfully swapped customer name and address columns');

    // Verify the change by checking a few records
    const customers = await prisma.customer.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        address: true,
      },
    });

    console.log('\nSample customer records after swap:');
    customers.forEach((customer, index) => {
      console.log(`Customer ${index + 1}:`);
      console.log(`  ID: ${customer.id}`);
      console.log(`  Name: ${customer.name}`);
      console.log(`  Address: ${customer.address}`);
      console.log('');
    });

  } catch (error) {
    console.error('Error swapping customer name and address columns:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
swapCustomerNameAddress();
