import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/ui/use-toast';

/**
 * Custom hook for managing customer form data
 * @param customerId Optional customer ID for editing mode
 * @returns Customer form state and handlers
 */
export function useCustomerForm(customerId?: string) {
  const router = useRouter();
  const [customer, setCustomer] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(customerId ? true : false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isEditMode = !!customerId;

  // Fetch customer data if in edit mode
  useEffect(() => {
    if (customerId) {
      const fetchCustomer = async () => {
        setIsLoading(true);
        try {
          const response = await fetch(`/api/customers/${customerId}`);
          
          if (!response.ok) {
            throw new Error('Failed to fetch customer data');
          }
          
          const data = await response.json();
          setCustomer(data);
        } catch (error: any) {
          console.error('Error fetching customer:', error);
          setError(error.message || 'Failed to load customer data');
          toast({
            title: 'Error',
            description: 'Failed to load customer data. Please try again.',
            variant: 'destructive',
          });
        } finally {
          setIsLoading(false);
        }
      };

      fetchCustomer();
    }
  }, [customerId]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const url = isEditMode ? `/api/customers/${customerId}` : '/api/customers';
      const method = isEditMode ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${isEditMode ? 'update' : 'create'} customer`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: `Customer ${isEditMode ? 'updated' : 'created'} successfully.`,
      });

      // Navigate to the customer details page
      router.push(`/customers/${data.id || customerId}`);
      router.refresh();
      
      return true;
    } catch (error: any) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} customer:`, error);
      setError(error.message || `Failed to ${isEditMode ? 'update' : 'create'} customer. Please try again.`);
      toast({
        title: 'Error',
        description: error.message || `Failed to ${isEditMode ? 'update' : 'create'} customer. Please try again.`,
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    customer,
    isLoading,
    isSubmitting,
    error,
    isEditMode,
    handleSubmit,
  };
}
