const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function simpleContactCheck() {
  try {
    console.log('Checking contacts for specific customer...\n');

    const customerId = '82c3c4ac-3f0c-4aee-b0bb-a5330ba490e9';
    
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        contacts: {
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (!customer) {
      console.log('Customer not found');
      return;
    }

    console.log(`Customer: ${customer.name}`);
    console.log(`Total contacts: ${customer.contacts.length}\n`);

    customer.contacts.forEach((contact, index) => {
      console.log(`Contact ${index + 1}:`);
      console.log(`  ID: ${contact.id}`);
      console.log(`  Name: ${contact.name}`);
      console.log(`  Phone: ${contact.phone || 'N/A'}`);
      console.log(`  Email: ${contact.email || 'N/A'}`);
      console.log(`  Primary: ${contact.isPrimary}`);
      console.log(`  Created: ${contact.createdAt}`);
      console.log('');
    });

    // Simple duplicate check
    const names = customer.contacts.map(c => c.name);
    const uniqueNames = [...new Set(names)];
    
    if (names.length !== uniqueNames.length) {
      console.log('🚨 DUPLICATE NAMES DETECTED!');
      const duplicates = names.filter((name, index) => names.indexOf(name) !== index);
      console.log('Duplicate names:', [...new Set(duplicates)]);
    } else {
      console.log('✅ No duplicate names found');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

simpleContactCheck();
