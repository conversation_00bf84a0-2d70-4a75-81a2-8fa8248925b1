'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';

// Forgot password form validation schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

// Type for forgot password form data
type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

/**
 * Forgot Password Page Component
 *
 * This page allows users to request a password reset link.
 */
export default function ForgotPasswordPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Initialize form with react-hook-form
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || 'Failed to send password reset link');
        return;
      }

      setSuccess(true);
    } catch (error) {
      console.error('Forgot password error:', error);
      setError('An error occurred while sending the password reset link');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h1 className="text-center text-3xl font-extrabold text-[#0F52BA]">KoolSoft</h1>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-black">
            Forgot your password?
          </h2>
          <p className="mt-2 text-center text-sm text-black">
            Enter your email address and we'll send you a link to reset your password.
          </p>
        </div>

        {success ? (
          <div className="rounded-md bg-[#0F52BA] p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-white">
                  Password reset link sent!
                </h3>
                <div className="mt-2 text-sm text-white">
                  <p>
                    If an account exists with this email, you will receive a password reset link.
                    Please check your email and follow the instructions to reset your password.
                  </p>
                </div>
                <div className="mt-4">
                  <Link
                    href="/auth/login"
                    className="font-medium text-white underline hover:text-gray-100"
                  >
                    Return to login
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-black">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  type="email"
                  autoComplete="email"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#0F52BA] focus:border-[#0F52BA] sm:text-sm text-black"
                  placeholder="<EMAIL>"
                  {...register('email')}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-[#ef4444]">{errors.email.message}</p>
                )}
              </div>
            </div>

            {error && (
              <div className="rounded-md bg-[#ef4444] p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-white">{error}</h3>
                  </div>
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#0F52BA] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0F52BA] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Sending...' : 'Send reset link'}
              </button>
            </div>

            <div className="flex items-center justify-center">
              <Link
                href="/auth/login"
                className="font-medium text-[#0F52BA] hover:text-blue-700"
              >
                Return to login
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
