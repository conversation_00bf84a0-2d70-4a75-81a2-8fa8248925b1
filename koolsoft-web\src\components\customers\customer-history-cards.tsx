import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Calendar,
  FileText,
  ChevronRight,
  Clock,
  AlertTriangle,
  Shield,
  Package,
  Wrench, // Using Wrench instead of Tool
  Plus
} from 'lucide-react';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';

interface ComplaintData {
  id?: string;
  history_card_id?: string;
  complaint_date?: string | Date;
  description?: string;
  complaint_type?: string;
  resolution?: string;
  resolution_date?: string | Date;
}

interface HistoryCard {
  id: string;
  reference: string;
  date: string | Date;
  type: string;
  description: string;
  status: string;
  notes?: string;
  source?: string;
  complaintData?: ComplaintData;
}

interface CustomerHistoryCardsProps {
  historyCards: HistoryCard[];
  customerId: string;
}

/**
 * Customer History Cards Component
 *
 * This component displays the history cards associated with a customer.
 * History cards track various events like repairs, maintenance, warranty claims, etc.
 *
 * Features:
 * - Displays complaint types in square brackets before descriptions
 * - Calculates status based on resolution_date and resolution text
 * - Formats references as HC-{card_no}
 * - Uses modern database tables (history_cards, history_complaints)
 */
export function CustomerHistoryCards({ historyCards, customerId }: CustomerHistoryCardsProps) {
  // Function to determine history card type badge
  const getTypeBadge = (type: string) => {
    switch (type?.toUpperCase()) {
      case 'REPAIR':
        return <Badge className="bg-yellow-500 text-white">Repair</Badge>;
      case 'MAINTENANCE':
        return <Badge className="bg-blue-500 text-white">Maintenance</Badge>;
      case 'WARRANTY':
        return <Badge className="bg-green-500 text-white">Warranty</Badge>;
      case 'AMC':
        return <Badge className="bg-purple-500 text-white">AMC</Badge>;
      case 'COMPLAINT':
        return <Badge className="bg-red-500 text-white">Complaint</Badge>;
      default:
        return <Badge variant="outline">{type || 'Unknown'}</Badge>;
    }
  };

  // Function to get icon based on history card type
  const getTypeIcon = (type: string) => {
    switch (type?.toUpperCase()) {
      case 'REPAIR':
        return <Wrench className="h-4 w-4 mr-2 text-yellow-500" />;
      case 'MAINTENANCE':
        return <Wrench className="h-4 w-4 mr-2 text-blue-500" />;
      case 'WARRANTY':
        return <Shield className="h-4 w-4 mr-2 text-green-500" />;
      case 'AMC':
        return <Package className="h-4 w-4 mr-2 text-purple-500" />;
      case 'COMPLAINT':
        return <AlertTriangle className="h-4 w-4 mr-2 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 mr-2 text-gray-500" />;
    }
  };

  if (!historyCards || historyCards.length === 0) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>History Cards</CardTitle>
            <CardDescription>
              History of repairs, maintenance, and other events for this customer
            </CardDescription>
          </div>
          <Button asChild variant="default">
            <Link href={`/history/new?customerId=${customerId}`}>
              <Plus className="h-4 w-4 mr-2" />
              Add History Card
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-gray-500">
            No history cards found for this customer.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>History Cards</CardTitle>
          <CardDescription>
            History of repairs, maintenance, and other events for this customer
          </CardDescription>
        </div>
        <Button asChild variant="default">
          <Link href={`/history/new?customerId=${customerId}`}>
            <Plus className="h-4 w-4 mr-2" />
            Add History Card
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Reference</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {historyCards.map((card) => (
              <TableRow key={card.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    {getTypeIcon(card.type)}
                    <span>{card.reference}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{formatDate(card.date)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {getTypeBadge(card.type)}
                </TableCell>
                <TableCell>
                  <span className="line-clamp-2">{card.description || 'No description'}</span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-gray-500" />
                    {card.type === 'COMPLAINT' ? (
                      <Badge className={
                        card.status === 'RESOLVED' ? 'bg-green-500 text-white' :
                        card.status === 'IN PROGRESS' ? 'bg-yellow-500 text-white' :
                        card.status === 'PENDING' ? 'bg-red-500 text-white' :
                        'bg-gray-500 text-white'
                      }>
                        {card.status}
                      </Badge>
                    ) : (
                      <span>{card.status}</span>
                    )}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Button asChild variant="ghost" size="sm">
                    <Link href={`/history/${card.id}`}>
                      View Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
