import { NextRequest, NextResponse } from 'next/server';
import { getEmailLogRepository } from '@/lib/repositories';

/**
 * GET /api/email/logs
 * Get all email logs with optional pagination and filtering
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');
    const customerId = searchParams.get('customerId');
    const userId = searchParams.get('userId');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    const emailLogRepository = getEmailLogRepository();
    
    let logs = [];
    let total = 0;
    
    if (customerId) {
      // Get logs for a specific customer
      logs = await emailLogRepository.findByCustomerId(customerId, skip, take);
      total = await emailLogRepository.count({ customerId });
    } else if (userId) {
      // Get logs for a specific user
      logs = await emailLogRepository.findByUserId(userId, skip, take);
      total = await emailLogRepository.count({ userId });
    } else if (status) {
      // Get logs by status
      logs = await emailLogRepository.findByStatus(status, skip, take);
      total = await emailLogRepository.count({ status });
    } else if (startDate && endDate) {
      // Get logs by date range
      const start = new Date(startDate);
      const end = new Date(endDate);
      logs = await emailLogRepository.findByDateRange(start, end, skip, take);
      total = await emailLogRepository.count({
        sentAt: {
          gte: start,
          lte: end,
        },
      });
    } else {
      // Get all logs
      logs = await emailLogRepository.findAll(skip, take);
      total = await emailLogRepository.count();
    }
    
    return NextResponse.json({
      logs,
      meta: {
        total,
        skip,
        take,
      },
    });
  } catch (error) {
    console.error('Error fetching email logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email logs' },
      { status: 500 }
    );
  }
}
