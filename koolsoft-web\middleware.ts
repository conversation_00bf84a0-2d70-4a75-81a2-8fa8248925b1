import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Define protected paths and required roles
const protectedPaths = [
  // Admin routes - explicitly list all admin routes to avoid path matching issues
  { path: '/admin', roles: ['ADMIN'] },
  { path: '/admin/users', roles: ['ADMIN'] },
  { path: '/admin/activity-logs', roles: ['ADMIN'] },
  { path: '/admin/settings', roles: ['ADMIN'] },
  { path: '/admin/email', roles: ['ADMIN'] },

  // Admin API routes
  { path: '/api/admin', roles: ['ADMIN'] },
  { path: '/api/email/preview', roles: ['ADMIN'] },

  // User routes
  { path: '/dashboard', roles: ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'] },
  { path: '/profile', roles: ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'] },
  { path: '/customers', roles: ['ADMIN', 'MANAGE<PERSON>', 'EXECUTIVE', 'USER'] },
  { path: '/amc', roles: ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'] },
  { path: '/warranty', roles: ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'] },
  { path: '/service', roles: ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'] },
  { path: '/sales', roles: ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'] },
  { path: '/reports', roles: ['ADMIN', 'MANAGER', 'EXECUTIVE'] },
];

// Helper function to check if user has required role
function hasRequiredRole(requiredRoles: string[], userRole?: string | null): boolean {
  if (!userRole) {
    console.log('Role check failed: No user role provided');
    return false;
  }

  const hasRole = requiredRoles.includes(userRole);

  if (!hasRole) {
    console.log(`Role check failed: User role "${userRole}" not in required roles [${requiredRoles.join(', ')}]`);
  }

  return hasRole;
}

/**
 * Middleware for route protection and role-based access control
 *
 * This middleware:
 * - Protects routes that require authentication
 * - Enforces role-based access control
 * - Redirects unauthenticated users to the login page
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Public paths that don't require authentication
  const publicPaths = [
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/auth/error',
    '/api/auth',
    '/api/health',
    '/_next',
    '/favicon.ico',
    '/static',
  ];

  // Check if the path is public
  const isPublicPath = publicPaths.some(path =>
    pathname.startsWith(path) || pathname === '/'
  );

  // If it's a public path, allow access
  if (isPublicPath) {
    return NextResponse.next();
  }

  // Get the JWT token from the request
  const token = await getToken({ req: request });

  // If there's no token, redirect to login for page routes or return 401 for API routes
  if (!token) {
    if (pathname.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const url = new URL('/auth/login', request.url);
    url.searchParams.set('callbackUrl', encodeURI(pathname));
    return NextResponse.redirect(url);
  }

  // Get the user's role from the token
  const userRole = token.role as string;

  console.log(`Middleware: Checking access for path "${pathname}" with user role "${userRole}"`);

  // Check if the current path is protected
  for (const { path, roles } of protectedPaths) {
    if (pathname.startsWith(path)) {
      console.log(`Middleware: Path "${pathname}" matches protected path "${path}" requiring roles [${roles.join(', ')}]`);

      if (!hasRequiredRole(roles, userRole)) {
        console.log(`Middleware: Access denied for user with role "${userRole}" to path "${pathname}"`);

        // For API routes, return 403 Forbidden
        if (pathname.startsWith('/api/')) {
          return new NextResponse(
            JSON.stringify({ error: 'Forbidden: Insufficient permissions' }),
            { status: 403, headers: { 'Content-Type': 'application/json' } }
          );
        }

        // For page routes, redirect to dashboard
        return NextResponse.redirect(new URL('/dashboard', request.url));
      } else {
        console.log(`Middleware: Access granted for user with role "${userRole}" to path "${pathname}"`);
      }
    }
  }

  // Allow access for authenticated and authorized users
  return NextResponse.next();
}

// Configure middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
