// Import only what we need for testing
// We'll mock the actual middleware implementation
import { NextRequest, NextResponse } from 'next/server';
import { getActivityLogService } from '@/lib/services/activity-log.service';

// Mock dependencies
jest.mock('@/lib/services/activity-log.service', () => ({
  getActivityLogService: jest.fn(),
}));

// Mock the withActivityLogging function
const withActivityLoggingMock = jest.fn((handler, options = {}) => {
  return async (req: NextRequest) => {
    // Skip logging if specified
    if (options.skipLogging && options.skipLogging(req)) {
      return handler(req);
    }

    // Call the original handler
    const response = await handler(req);

    // Extract action from options or request
    const action = options.action || `${req.method} ${new URL(req.url).pathname}`;

    // Extract entity ID if provided
    let entityId: string | undefined = undefined;
    if (options.getEntityId) {
      const entityIdResult = options.getEntityId(req);
      if (entityIdResult instanceof Promise) {
        entityId = await entityIdResult;
      } else {
        entityId = entityIdResult;
      }
    }

    // Extract details
    const details = options.getDetails ? options.getDetails(req, response) : {
      method: req.method,
      url: req.url,
      status: response.status,
      duration: 100, // Mock duration
    };

    // Log the request
    const activityLogService = getActivityLogService();
    await activityLogService.logApiRequest(
      req,
      action,
      options.entityType,
      entityId,
      details
    );

    return response;
  };
});

// Mock the ActivityLoggerMiddlewareFactory
const ActivityLoggerMiddlewareFactory = {
  forAMCContractRoutes: jest.fn((handler, options = {}) => {
    return withActivityLoggingMock(handler, {
      ...options,
      entityType: 'amc_contract',
      getEntityId: options.getEntityId || ((req) => {
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        return pathParts[pathParts.length - 1];
      }),
    });
  })
};

describe('AMC Contract Routes Middleware', () => {
  let mockRequest: NextRequest;
  let mockResponse: NextResponse;
  let mockHandler: jest.Mock;
  let mockLogService: any;

  beforeEach(() => {
    // Create mock request
    mockRequest = {
      method: 'GET',
      url: 'https://example.com/api/amc/contracts/123',
      headers: {
        get: jest.fn((name) => {
          if (name === 'x-forwarded-for') return '127.0.0.1';
          if (name === 'user-agent') return 'test-user-agent';
          return null;
        }),
      },
    } as unknown as NextRequest;

    // Create mock response
    mockResponse = {
      status: 200,
    } as unknown as NextResponse;

    // Create mock handler
    mockHandler = jest.fn().mockResolvedValue(mockResponse);

    // Create mock log service
    mockLogService = {
      logApiRequest: jest.fn().mockResolvedValue({ id: 'test-log-id' }),
    };

    // Mock the log service factory
    (getActivityLogService as jest.Mock).mockReturnValue(mockLogService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('forAMCContractRoutes', () => {
    it('should create middleware for AMC contract routes', async () => {
      // Arrange
      const middleware = ActivityLoggerMiddlewareFactory.forAMCContractRoutes(mockHandler);

      // Act
      await middleware(mockRequest);

      // Assert
      expect(mockLogService.logApiRequest).toHaveBeenCalledWith(
        mockRequest,
        'GET /api/amc/contracts/123',
        'amc_contract',
        '123',
        expect.any(Object)
      );
    });

    it('should use custom action if provided', async () => {
      // Arrange
      const middleware = ActivityLoggerMiddlewareFactory.forAMCContractRoutes(mockHandler, {
        action: 'custom_amc_action',
      });

      // Act
      await middleware(mockRequest);

      // Assert
      expect(mockLogService.logApiRequest).toHaveBeenCalledWith(
        mockRequest,
        'custom_amc_action',
        'amc_contract',
        '123',
        expect.any(Object)
      );
    });

    it('should use custom entity ID getter if provided', async () => {
      // Arrange
      const middleware = ActivityLoggerMiddlewareFactory.forAMCContractRoutes(mockHandler, {
        getEntityId: () => 'custom-entity-id',
      });

      // Act
      await middleware(mockRequest);

      // Assert
      expect(mockLogService.logApiRequest).toHaveBeenCalledWith(
        mockRequest,
        'GET /api/amc/contracts/123',
        'amc_contract',
        'custom-entity-id',
        expect.any(Object)
      );
    });

    it('should use custom details if provided', async () => {
      // Arrange
      const customDetails = { custom: 'amc_details' };
      const middleware = ActivityLoggerMiddlewareFactory.forAMCContractRoutes(mockHandler, {
        getDetails: () => customDetails,
      });

      // Act
      await middleware(mockRequest);

      // Assert
      expect(mockLogService.logApiRequest).toHaveBeenCalledWith(
        mockRequest,
        'GET /api/amc/contracts/123',
        'amc_contract',
        '123',
        customDetails
      );
    });

    it('should skip logging if skipLogging returns true', async () => {
      // Arrange
      const middleware = ActivityLoggerMiddlewareFactory.forAMCContractRoutes(mockHandler, {
        skipLogging: () => true,
      });

      // Act
      await middleware(mockRequest);

      // Assert
      expect(mockHandler).toHaveBeenCalledWith(mockRequest);
      expect(mockLogService.logApiRequest).not.toHaveBeenCalled();
    });
  });
});