import { NextRequest, NextResponse } from 'next/server';
import { getUserRepository } from '@/lib/repositories';
import { z } from 'zod';
import { hash } from 'bcrypt';
import { prisma } from '@/lib/prisma';
import { getActivityLogService } from '@/lib/services/activity-log.service';

/**
 * Reset password schema with stronger validation
 */
const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
});

/**
 * POST /api/auth/reset-password
 * Reset password using a token
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = resetPasswordSchema.parse(body);

    // Find token in database
    const passwordReset = await prisma.passwordResetToken.findFirst({
      where: {
        token: validatedData.token,
        expires: {
          gt: new Date(),
        },
      },
      include: {
        user: true,
      },
    });

    // If token not found or expired, return error
    if (!passwordReset) {
      // Log failed attempt
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAuthEvent(
          'password_reset_failed',
          undefined,
          {
            reason: 'invalid_token',
            token: validatedData.token.substring(0, 8) + '...',
          }
        );
      } catch (logError) {
        console.error('Error logging failed password reset:', logError);
      }

      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 400 }
      );
    }

    // Get salt rounds from environment or use default
    const saltRounds = process.env.PASSWORD_SALT_ROUNDS
      ? parseInt(process.env.PASSWORD_SALT_ROUNDS)
      : 10;

    // Hash new password
    const hashedPassword = await hash(validatedData.password, saltRounds);

    // Update user password
    const userRepository = getUserRepository();
    await userRepository.update(passwordReset.userId, {
      password: hashedPassword,
    });

    // Delete token
    await prisma.passwordResetToken.delete({
      where: {
        id: passwordReset.id,
      },
    });

    // Log successful password reset
    try {
      const activityLogService = getActivityLogService();
      await activityLogService.logAuthEvent(
        'password_reset_success',
        passwordReset.userId,
        {
          userId: passwordReset.userId,
        }
      );
    } catch (logError) {
      console.error('Error logging successful password reset:', logError);
    }

    return NextResponse.json({
      message: 'Password reset successful',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error resetting password:', error);
    return NextResponse.json(
      { error: 'Failed to reset password' },
      { status: 500 }
    );
  }
}
