# AMC Contract Status Management

This document outlines the implementation of automatic status updates for AMC contracts in the KoolSoft application.

## Overview

AMC (Annual Maintenance Contract) contracts in the KoolSoft system have a status field that can be one of the following values:
- `ACTIVE`: Contract is currently active
- `EXPIRED`: Contract has ended (end date is in the past)
- `PENDING`: Contract is created but not yet active
- `CANCELLED`: Contract has been cancelled
- `RENEWED`: Contract has been renewed

The system now includes functionality to automatically update the status of contracts from `ACTIVE` to `EXPIRED` when their end date passes.

## Implementation Details

### 1. API Route Filtering

The `/api/amc/contracts` route has been updated to consider end dates when filtering by status. When the status is 'ACTIVE', it also checks that the end date is in the future:

```typescript
// If status is ACTIVE, also check that end date is in the future
if (status === 'ACTIVE') {
  filterParams.endDate = {
    ...filterParams.endDate,
    gte: new Date()
  };
}
```

This ensures that contracts with end dates in the past don't appear in the Active tab, even if their status is still set to 'ACTIVE' in the database.

### 2. Status Update Utility

A utility function `updateContractStatuses()` has been implemented in `src/lib/jobs/update-contract-status.ts` that:
- Finds all ACTIVE contracts with end dates in the past
- Updates their status to EXPIRED
- Creates activity logs for the updated contracts

```typescript
export async function updateContractStatuses() {
  const prisma = new PrismaClient();
  const today = new Date();
  
  try {
    // Find all ACTIVE contracts with end dates in the past
    const expiredContracts = await prisma.amc_contracts.findMany({
      where: {
        status: 'ACTIVE',
        endDate: {
          lt: today,
        },
      },
      select: {
        id: true,
        customerId: true,
        startDate: true,
        endDate: true,
        originalId: true,
      }
    });
    
    // Update their status to EXPIRED
    if (expiredContracts.length > 0) {
      const updateResult = await prisma.amc_contracts.updateMany({
        where: {
          id: {
            in: expiredContracts.map(contract => contract.id),
          },
        },
        data: {
          status: 'EXPIRED',
        },
      });
      
      // Create activity logs for the updated contracts
      const activityLogs = expiredContracts.map(contract => ({
        entityType: 'AMC_CONTRACT',
        entityId: contract.id,
        action: 'STATUS_UPDATED',
        details: JSON.stringify({
          previousStatus: 'ACTIVE',
          newStatus: 'EXPIRED',
          reason: 'End date passed',
          endDate: contract.endDate,
        }),
        userId: null, // System-generated update
      }));
      
      if (activityLogs.length > 0) {
        await prisma.activity_logs.createMany({
          data: activityLogs,
        });
      }
    }
    
    return {
      success: true,
      updatedCount: expiredContracts.length,
    };
  } catch (error) {
    console.error('Error updating contract statuses:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  } finally {
    await prisma.$disconnect();
  }
}
```

### 3. API Endpoint for Status Updates

An API endpoint `/api/amc/contracts/update-statuses` has been created that:
- Calls the `updateContractStatuses()` function
- Returns the number of contracts updated
- Requires ADMIN or MANAGER role

```typescript
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      const result = await updateContractStatuses();
      
      if (result.success) {
        return NextResponse.json({
          message: `Successfully updated ${result.updatedCount} contracts to EXPIRED status`,
          updatedCount: result.updatedCount,
        });
      } else {
        return NextResponse.json(
          { error: 'Failed to update contract statuses', details: result.error },
          { status: 500 }
        );
      }
    } catch (error) {
      console.error('Error in update-statuses API:', error);
      return NextResponse.json(
        { error: 'Failed to update contract statuses' },
        { status: 500 }
      );
    }
  }
);
```

### 4. UI Button for Manual Updates

The AMC Actions component has been updated to include an "Update Statuses" button for admins and managers:

```typescript
{isAdminOrManager && (
  <Button 
    variant="outline" 
    onClick={handleUpdateStatuses}
    disabled={isUpdatingStatuses}
  >
    <RefreshCw className={`h-4 w-4 mr-2 ${isUpdatingStatuses ? 'animate-spin' : ''}`} />
    Update Statuses
  </Button>
)}
```

## Future Enhancements

For a more comprehensive solution, consider implementing:

1. **Scheduled Job**: Set up a daily cron job to automatically run the `updateContractStatuses()` function, ensuring contract statuses are always up to date.

2. **Email Notifications**: Send notifications to customers and account managers when contracts are automatically marked as expired.

3. **Status History**: Track the history of status changes for each contract, including when they were marked as expired.

4. **Expiry Warning**: Implement a warning system that flags contracts approaching expiry (e.g., within 30 days) to prompt renewal discussions.

## Related Files

- `src/app/api/amc/contracts/route.ts`: API route for fetching AMC contracts
- `src/lib/jobs/update-contract-status.ts`: Utility function for updating contract statuses
- `src/app/api/amc/contracts/update-statuses/route.ts`: API endpoint for triggering status updates
- `src/components/amc/amc-actions.tsx`: UI component with the Update Statuses button
