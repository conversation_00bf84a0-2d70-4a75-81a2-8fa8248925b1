"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/service/[id]/page",{

/***/ "(app-pages-browser)/./src/app/service/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/service/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServiceReportDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ServiceReportDetailPage() {\n    _s();\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [serviceReport, setServiceReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [completing, setCompleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceReportDetailPage.useEffect\": ()=>{\n            if (params.id) {\n                loadServiceReport(params.id);\n            }\n        }\n    }[\"ServiceReportDetailPage.useEffect\"], [\n        params.id\n    ]);\n    const loadServiceReport = async (id)=>{\n        try {\n            const response = await fetch(\"/api/service/\".concat(id), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setServiceReport(data.serviceReport);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to load service report');\n                router.push('/service');\n            }\n        } catch (error) {\n            console.error('Error loading service report:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to load service report');\n            router.push('/service');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/service');\n    };\n    const handleEdit = ()=>{\n        router.push(\"/service/\".concat(params.id, \"/edit\"));\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('Are you sure you want to delete this service report?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/service/\".concat(params.id), {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success('Service report deleted successfully');\n                router.push('/service');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to delete service report');\n            }\n        } catch (error) {\n            console.error('Error deleting service report:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to delete service report');\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusConfig = {\n            OPEN: {\n                variant: 'secondary',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: 'Open'\n            },\n            IN_PROGRESS: {\n                variant: 'default',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                label: 'In Progress'\n            },\n            COMPLETED: {\n                variant: 'default',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                label: 'Completed'\n            },\n            CANCELLED: {\n                variant: 'destructive',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                label: 'Cancelled'\n            },\n            PENDING: {\n                variant: 'secondary',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                label: 'Pending'\n            }\n        };\n        const config = statusConfig[status] || statusConfig.OPEN;\n        const Icon = config.icon;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: config.variant,\n            className: \"flex items-center gap-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                config.label\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 101,\n            columnNumber: 12\n        }, this);\n    };\n    const getComplaintTypeBadge = (type)=>{\n        const typeConfig = {\n            REPAIR: 'bg-red-100 text-red-800',\n            MAINTENANCE: 'bg-blue-100 text-blue-800',\n            INSTALLATION: 'bg-green-100 text-green-800',\n            INSPECTION: 'bg-yellow-100 text-yellow-800',\n            WARRANTY: 'bg-purple-100 text-purple-800',\n            OTHER: 'bg-gray-100 text-gray-800'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(typeConfig[type] || typeConfig.OTHER),\n            children: type\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 12\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Loading service report...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 120,\n            columnNumber: 12\n        }, this);\n    }\n    if (!serviceReport) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Service report not found.\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 137,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleEdit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                onClick: handleDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                \"Service Report Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Report Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.reportDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            serviceReport.visitDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Visit Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.visitDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 43\n                                            }, this),\n                                            serviceReport.completionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Completion Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.completionDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 48\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Nature of Service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: serviceReport.natureOfService\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1\",\n                                                        children: getStatusBadge(serviceReport.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Complaint Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1\",\n                                                        children: getComplaintTypeBadge(serviceReport.complaintType)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Customer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: serviceReport.customer.name\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.customer.city\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    serviceReport.customer.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.customer.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 50\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Executive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: serviceReport.executive.name\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    serviceReport.executive.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.executive.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 51\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            serviceReport.actionTaken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Action Taken\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-1\",\n                                                children: serviceReport.actionTaken\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            serviceReport.remarks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Remarks\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-1\",\n                                                children: serviceReport.remarks\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                \"Service Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: serviceReport.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-4\",\n                                            children: [\n                                                \"Service Detail \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Machine Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.machineType\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Serial Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.serialNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Problem\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.problem\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Solution\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.solution\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this),\n                                                detail.partReplaced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Part Replaced\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.partReplaced\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 43\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, detail.id, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 59\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 10\n    }, this);\n}\n_s(ServiceReportDetailPage, \"VSdea1A+Y/clvNYryaSXt/iNff8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c1 = ServiceReportDetailPage;\n_s1(ServiceReportDetailPage, \"VSdea1A+Y/clvNYryaSXt/iNff8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = ServiceReportDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ServiceReportDetailPage\");\nvar _c1;\n$RefreshReg$(_c1, \"ServiceReportDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/service/[id]/page.tsx\n"));

/***/ })

});