import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * AMC Service Date Repository
 *
 * This repository handles database operations for the AMC Service Date entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class AMCServiceDateRepository extends PrismaRepository<
  Prisma.amc_service_datesGetPayload<{}>,
  string,
  Prisma.amc_service_datesCreateInput,
  Prisma.amc_service_datesUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('amc_service_dates');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find service dates by AMC contract ID
   */
  async findByContractId(
    contractId: string,
    options?: {
      skip?: number;
      take?: number;
      orderBy?: Prisma.amc_service_datesOrderByWithRelationInput;
    }
  ): Promise<Prisma.amc_service_datesGetPayload<{}>[]> {
    try {
      const { skip = 0, take = 50, orderBy = { serviceDate: 'asc' } } = options || {};

      if (!this.model) {
        throw new Error('Model is not available');
      }

      return await this.model.findMany({
        where: { amcContractId: contractId },
        skip,
        take,
        orderBy,
      });
    } catch (error) {
      console.error('AMCServiceDateRepository.findByContractId error:', error);
      throw error;
    }
  }

  /**
   * Find service dates by date range
   */
  async findByDateRange(
    startDate: Date,
    endDate: Date,
    options?: {
      contractId?: string;
      skip?: number;
      take?: number;
      orderBy?: Prisma.amc_service_datesOrderByWithRelationInput;
    }
  ): Promise<Prisma.amc_service_datesGetPayload<{}>[]> {
    try {
      const { contractId, skip = 0, take = 50, orderBy = { serviceDate: 'asc' } } = options || {};

      if (!this.model) {
        throw new Error('Model is not available');
      }

      const where: Prisma.amc_service_datesWhereInput = {
        serviceDate: {
          gte: startDate,
          lte: endDate,
        },
      };

      if (contractId) {
        where.amcContractId = contractId;
      }

      return await this.model.findMany({
        where,
        skip,
        take,
        orderBy,
      });
    } catch (error) {
      console.error('AMCServiceDateRepository.findByDateRange error:', error);
      throw error;
    }
  }

  /**
   * Find upcoming service dates (scheduled but not completed)
   */
  async findUpcoming(
    contractId?: string,
    options?: {
      skip?: number;
      take?: number;
      orderBy?: Prisma.amc_service_datesOrderByWithRelationInput;
    }
  ): Promise<Prisma.amc_service_datesGetPayload<{}>[]> {
    try {
      const { skip = 0, take = 50, orderBy = { serviceDate: 'asc' } } = options || {};

      if (!this.model) {
        throw new Error('Model is not available');
      }

      const where: Prisma.amc_service_datesWhereInput = {
        serviceDate: {
          gte: new Date(),
        },
        completedDate: null,
      };

      if (contractId) {
        where.amcContractId = contractId;
      }

      return await this.model.findMany({
        where,
        skip,
        take,
        orderBy,
      });
    } catch (error) {
      console.error('AMCServiceDateRepository.findUpcoming error:', error);
      throw error;
    }
  }

  /**
   * Find overdue service dates (past due but not completed)
   */
  async findOverdue(
    contractId?: string,
    options?: {
      skip?: number;
      take?: number;
      orderBy?: Prisma.amc_service_datesOrderByWithRelationInput;
    }
  ): Promise<Prisma.amc_service_datesGetPayload<{}>[]> {
    try {
      const { skip = 0, take = 50, orderBy = { serviceDate: 'asc' } } = options || {};

      if (!this.model) {
        throw new Error('Model is not available');
      }

      const where: Prisma.amc_service_datesWhereInput = {
        serviceDate: {
          lt: new Date(),
        },
        completedDate: null,
      };

      if (contractId) {
        where.amcContractId = contractId;
      }

      return await this.model.findMany({
        where,
        skip,
        take,
        orderBy,
      });
    } catch (error) {
      console.error('AMCServiceDateRepository.findOverdue error:', error);
      throw error;
    }
  }

  /**
   * Mark service date as completed
   */
  async markCompleted(
    id: string,
    completedDate: Date = new Date(),
    serviceFlag?: string
  ): Promise<Prisma.amc_service_datesGetPayload<{}>> {
    try {
      if (!this.model) {
        throw new Error('Model is not available');
      }

      return await this.model.update({
        where: { id },
        data: {
          completedDate,
          serviceFlag: serviceFlag || 'C', // C for Completed
        },
      });
    } catch (error) {
      console.error('AMCServiceDateRepository.markCompleted error:', error);
      throw error;
    }
  }

  /**
   * Generate service dates for a contract
   */
  async generateServiceDates(
    contractId: string,
    numberOfServices: number,
    startDate: Date,
    endDate: Date,
    replaceExisting: boolean = false
  ): Promise<Prisma.amc_service_datesGetPayload<{}>[]> {
    try {
      if (!this.model) {
        throw new Error('Model is not available');
      }

      // Delete existing service dates if requested
      if (replaceExisting) {
        await this.model.deleteMany({
          where: { amcContractId: contractId },
        });
      }

      // Calculate interval between service dates
      const contractDuration = endDate.getTime() - startDate.getTime();
      const interval = Math.floor(contractDuration / (numberOfServices - 1));

      // Generate service dates
      const serviceDates = [];
      for (let i = 0; i < numberOfServices; i++) {
        const date = new Date(startDate.getTime() + i * interval);
        serviceDates.push({
          amcContractId: contractId,
          serviceDate: date,
          serviceFlag: 'S', // S for Scheduled
          serviceNumber: i + 1,
        });
      }

      // Create service dates
      await this.model.createMany({
        data: serviceDates,
      });

      // Return the created service dates
      return await this.findByContractId(contractId);
    } catch (error) {
      console.error('AMCServiceDateRepository.generateServiceDates error:', error);
      throw error;
    }
  }

  /**
   * Get service date statistics for a contract
   */
  async getStatistics(contractId: string): Promise<{
    total: number;
    completed: number;
    pending: number;
    overdue: number;
    upcoming: number;
    completionPercentage: number;
  }> {
    try {
      if (!this.model) {
        throw new Error('Model is not available');
      }

      const now = new Date();

      const [total, completed, overdue, upcoming] = await Promise.all([
        this.model.count({ where: { amcContractId: contractId } }),
        this.model.count({
          where: {
            amcContractId: contractId,
            completedDate: { not: null },
          },
        }),
        this.model.count({
          where: {
            amcContractId: contractId,
            serviceDate: { lt: now },
            completedDate: null,
          },
        }),
        this.model.count({
          where: {
            amcContractId: contractId,
            serviceDate: { gte: now },
            completedDate: null,
          },
        }),
      ]);

      const pending = total - completed;
      const completionPercentage = total > 0 ? Math.round((completed / total) * 100) : 0;

      return {
        total,
        completed,
        pending,
        overdue,
        upcoming,
        completionPercentage,
      };
    } catch (error) {
      console.error('AMCServiceDateRepository.getStatistics error:', error);
      throw error;
    }
  }

  /**
   * Delete service dates by contract ID
   */
  async deleteByContractId(contractId: string): Promise<number> {
    try {
      if (!this.model) {
        throw new Error('Model is not available');
      }

      const result = await this.model.deleteMany({
        where: { amcContractId: contractId },
      });

      return result.count;
    } catch (error) {
      console.error('AMCServiceDateRepository.deleteByContractId error:', error);
      throw error;
    }
  }
}
