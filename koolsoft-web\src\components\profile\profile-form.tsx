'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Profile form validation schema
const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be at most 100 characters'),
  email: z.string().email('Please enter a valid email address').optional(),
  phone: z.string().optional(),
  designation: z.string().optional(),
});

// Type for profile form data
type ProfileFormData = z.infer<typeof profileSchema>;

// Props for ProfileForm component
interface ProfileFormProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    phone?: string;
    designation?: string;
  };
}

/**
 * Profile Form Component
 *
 * This component allows users to update their profile information.
 */
export function ProfileForm({ user }: ProfileFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Initialize form with react-hook-form
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user.name || '',
      email: user.email || '',
      phone: user.phone || '',
      designation: user.designation || '',
    },
  });

  // Update form values when user data changes
  useEffect(() => {
    if (user) {
      setValue('name', user.name || '');
      setValue('email', user.email || '');
      setValue('phone', user.phone || '');
      setValue('designation', user.designation || '');
    }
  }, [user, setValue]);

  // Handle form submission
  const onSubmit = async (data: ProfileFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      const response = await fetch('/api/users/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: data.name,
          phone: data.phone,
          designation: data.designation,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || 'Failed to update profile');
        return;
      }

      setSuccess(true);

      // Show success message for 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error updating profile:', error);
      setError('An error occurred while updating your profile');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium leading-6 text-black">Personal Information</h3>
        <p className="mt-1 text-sm text-black">
          Update your personal information and contact details.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <Label htmlFor="name" className="text-black">
              Full Name
            </Label>
            <div className="mt-1">
              <Input
                type="text"
                id="name"
                autoComplete="name"
                className="text-black"
                {...register('name')}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-[#ef4444]">{errors.name.message}</p>
              )}
            </div>
          </div>

          <div className="sm:col-span-3">
            <Label htmlFor="email" className="text-black">
              Email Address
            </Label>
            <div className="mt-1">
              <Input
                type="email"
                id="email"
                autoComplete="email"
                disabled
                className="bg-[#f3f4f6] text-black cursor-not-allowed"
                {...register('email')}
              />
              <p className="mt-1 text-xs text-black">
                Email address cannot be changed. Contact an administrator for assistance.
              </p>
            </div>
          </div>

          <div className="sm:col-span-3">
            <Label htmlFor="phone" className="text-black">
              Phone Number
            </Label>
            <div className="mt-1">
              <Input
                type="text"
                id="phone"
                autoComplete="tel"
                className="text-black"
                {...register('phone')}
              />
            </div>
          </div>

          <div className="sm:col-span-3">
            <Label htmlFor="designation" className="text-black">
              Designation
            </Label>
            <div className="mt-1">
              <Input
                type="text"
                id="designation"
                className="text-black"
                {...register('designation')}
              />
            </div>
          </div>

          <div className="sm:col-span-6">
            <Label htmlFor="role" className="text-black">
              Role
            </Label>
            <div className="mt-1">
              <Input
                type="text"
                id="role"
                value={user.role}
                disabled
                className="bg-[#f3f4f6] text-black cursor-not-allowed"
              />
              <p className="mt-1 text-xs text-black">
                User role cannot be changed. Contact an administrator for assistance.
              </p>
            </div>
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-green-50 text-green-800 border-green-200">
            <AlertDescription className="text-green-800">
              Profile updated successfully!
            </AlertDescription>
          </Alert>
        )}

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-[#0F52BA] text-white hover:bg-[#0F52BA]/90"
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  );
}
