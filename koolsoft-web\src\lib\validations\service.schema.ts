import { z } from 'zod';

/**
 * Service Detail schema
 */
export const serviceDetailSchema = z.object({
  id: z.string().uuid().optional(),
  machineType: z.string().min(1, 'Machine type is required').max(100),
  serialNumber: z.string().min(1, 'Serial number is required').max(50),
  problem: z.string().min(1, 'Problem description is required').max(500),
  solution: z.string().min(1, 'Solution description is required').max(500),
  partReplaced: z.string().max(200).optional(),
});

/**
 * Base service report schema
 */
export const serviceReportBaseSchema = z.object({
  customerId: z.string().uuid('Valid customer ID is required'),
  executiveId: z.string().uuid('Valid executive ID is required'),
  reportDate: z.coerce.date({ message: 'Valid report date is required' }),
  visitDate: z.coerce.date().optional(),
  completionDate: z.coerce.date().optional(),
  natureOfService: z.string().min(1, 'Nature of service is required').max(200),
  complaintType: z.enum(['REPAIR', 'MAINTENANCE', 'INSTALLATION', 'INSPECTION', 'WARRANTY', 'OTHER'], {
    errorMap: () => ({ message: 'Complaint type must be one of: REPAIR, MAINTENANCE, INSTALLATION, INSPECTION, WARRANTY, OTHER' })
  }),
  actionTaken: z.string().max(500).optional(),
  remarks: z.string().max(500).optional(),
  status: z.enum(['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'PENDING'], {
    errorMap: () => ({ message: 'Status must be one of: OPEN, IN_PROGRESS, COMPLETED, CANCELLED, PENDING' })
  }).default('OPEN'),
  originalId: z.number().int().optional(),
});

/**
 * Service report creation schema
 */
export const createServiceReportSchema = serviceReportBaseSchema.extend({
  details: z.array(serviceDetailSchema).min(1, 'At least one service detail is required'),
}).refine(
  (data) => {
    // Ensure visit date is after report date if both are provided
    if (data.reportDate && data.visitDate) {
      return data.visitDate >= data.reportDate;
    }
    return true;
  },
  {
    message: 'Visit date must be after or equal to report date',
    path: ['visitDate'],
  }
).refine(
  (data) => {
    // Ensure completion date is after visit date if both are provided
    if (data.visitDate && data.completionDate) {
      return data.completionDate >= data.visitDate;
    }
    return true;
  },
  {
    message: 'Completion date must be after or equal to visit date',
    path: ['completionDate'],
  }
).refine(
  (data) => {
    // If status is COMPLETED, completion date is required
    if (data.status === 'COMPLETED') {
      return !!data.completionDate;
    }
    return true;
  },
  {
    message: 'Completion date is required when status is COMPLETED',
    path: ['completionDate'],
  }
);

/**
 * Service report update schema
 */
export const updateServiceReportSchema = serviceReportBaseSchema
  .partial()
  .extend({
    id: z.string().uuid(),
    details: z.array(serviceDetailSchema).optional(),
  })
  .refine(
    (data) => {
      // Ensure visit date is after report date if both are provided
      if (data.reportDate && data.visitDate) {
        return data.visitDate >= data.reportDate;
      }
      return true;
    },
    {
      message: 'Visit date must be after or equal to report date',
      path: ['visitDate'],
    }
  )
  .refine(
    (data) => {
      // Ensure completion date is after visit date if both are provided
      if (data.visitDate && data.completionDate) {
        return data.completionDate >= data.visitDate;
      }
      return true;
    },
    {
      message: 'Completion date must be after or equal to visit date',
      path: ['completionDate'],
    }
  )
  .refine(
    (data) => {
      // If status is COMPLETED, completion date is required
      if (data.status === 'COMPLETED') {
        return !!data.completionDate;
      }
      return true;
    },
    {
      message: 'Completion date is required when status is COMPLETED',
      path: ['completionDate'],
    }
  );

/**
 * Service report query schema for API filtering
 */
export const serviceReportQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  search: z.string().optional(),
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  status: z.enum(['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'PENDING']).optional(),
  complaintType: z.enum(['REPAIR', 'MAINTENANCE', 'INSTALLATION', 'INSPECTION', 'WARRANTY', 'OTHER']).optional(),
  reportDateFrom: z.coerce.date().optional(),
  reportDateTo: z.coerce.date().optional(),
  visitDateFrom: z.coerce.date().optional(),
  visitDateTo: z.coerce.date().optional(),
  completionDateFrom: z.coerce.date().optional(),
  completionDateTo: z.coerce.date().optional(),
  sortBy: z.enum(['reportDate', 'visitDate', 'completionDate', 'status', 'customer', 'executive']).default('reportDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
}).refine(
  (data) => {
    // Ensure reportDateTo is after reportDateFrom if both are provided
    if (data.reportDateFrom && data.reportDateTo) {
      return data.reportDateTo >= data.reportDateFrom;
    }
    return true;
  },
  {
    message: 'Report date "to" must be after or equal to "from" date',
    path: ['reportDateTo'],
  }
).refine(
  (data) => {
    // Ensure visitDateTo is after visitDateFrom if both are provided
    if (data.visitDateFrom && data.visitDateTo) {
      return data.visitDateTo >= data.visitDateFrom;
    }
    return true;
  },
  {
    message: 'Visit date "to" must be after or equal to "from" date',
    path: ['visitDateTo'],
  }
).refine(
  (data) => {
    // Ensure completionDateTo is after completionDateFrom if both are provided
    if (data.completionDateFrom && data.completionDateTo) {
      return data.completionDateTo >= data.completionDateFrom;
    }
    return true;
  },
  {
    message: 'Completion date "to" must be after or equal to "from" date',
    path: ['completionDateTo'],
  }
);

/**
 * Service detail query schema for API filtering
 */
export const serviceDetailQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  search: z.string().optional(),
  serviceReportId: z.string().uuid().optional(),
  machineType: z.string().optional(),
  serialNumber: z.string().optional(),
  sortBy: z.enum(['createdAt', 'machineType', 'serialNumber', 'problem']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Service report status update schema
 */
export const serviceReportStatusUpdateSchema = z.object({
  status: z.enum(['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'PENDING']),
  completionDate: z.coerce.date().optional(),
  remarks: z.string().max(500).optional(),
}).refine(
  (data) => {
    // If status is COMPLETED, completion date is required
    if (data.status === 'COMPLETED') {
      return !!data.completionDate;
    }
    return true;
  },
  {
    message: 'Completion date is required when status is COMPLETED',
    path: ['completionDate'],
  }
);

/**
 * Service scheduling schema
 */
export const serviceSchedulingSchema = z.object({
  serviceReportId: z.string().uuid('Valid service report ID is required'),
  scheduledDate: z.coerce.date({ message: 'Valid scheduled date is required' }),
  technicianId: z.string().uuid().optional(),
  estimatedDuration: z.number().int().min(1).max(480).optional(), // Duration in minutes, max 8 hours
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  notes: z.string().max(500).optional(),
}).refine(
  (data) => {
    // Ensure scheduled date is not in the past
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Start of today
    return data.scheduledDate >= now;
  },
  {
    message: 'Scheduled date cannot be in the past',
    path: ['scheduledDate'],
  }
);

/**
 * Service export filters schema (without pagination)
 */
export const serviceExportFiltersSchema = z.object({
  search: z.string().optional(),
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  status: z.enum(['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'PENDING']).optional(),
  complaintType: z.enum(['REPAIR', 'MAINTENANCE', 'INSTALLATION', 'INSPECTION', 'WARRANTY', 'OTHER']).optional(),
  reportDateFrom: z.coerce.date().optional(),
  reportDateTo: z.coerce.date().optional(),
  visitDateFrom: z.coerce.date().optional(),
  visitDateTo: z.coerce.date().optional(),
  completionDateFrom: z.coerce.date().optional(),
  completionDateTo: z.coerce.date().optional(),
  sortBy: z.enum(['reportDate', 'visitDate', 'completionDate', 'status', 'customer', 'executive']).default('reportDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Service export schema
 */
export const serviceExportSchema = z.object({
  format: z.enum(['CSV', 'EXCEL', 'PDF']).default('CSV'),
  filters: serviceExportFiltersSchema.optional(),
  includeDetails: z.boolean().default(true),
  dateRange: z.object({
    from: z.coerce.date(),
    to: z.coerce.date(),
  }).optional(),
}).refine(
  (data) => {
    // Ensure date range is valid if provided
    if (data.dateRange) {
      return data.dateRange.to >= data.dateRange.from;
    }
    return true;
  },
  {
    message: 'Date range "to" must be after or equal to "from" date',
    path: ['dateRange'],
  }
);

/**
 * Service statistics query schema
 */
export const serviceStatisticsQuerySchema = z.object({
  period: z.enum(['WEEK', 'MONTH', 'QUARTER', 'YEAR']).default('MONTH'),
  executiveId: z.string().uuid().optional(),
  customerId: z.string().uuid().optional(),
  complaintType: z.enum(['REPAIR', 'MAINTENANCE', 'INSTALLATION', 'INSPECTION', 'WARRANTY', 'OTHER']).optional(),
});

/**
 * Bulk service operation schema
 */
export const bulkServiceOperationSchema = z.object({
  serviceReportIds: z.array(z.string().uuid()).min(1, 'At least one service report ID is required'),
  action: z.enum(['UPDATE_STATUS', 'DELETE', 'EXPORT', 'ASSIGN_TECHNICIAN']),
  status: z.enum(['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'PENDING']).optional(),
  technicianId: z.string().uuid().optional(),
  exportFormat: z.enum(['CSV', 'EXCEL', 'PDF']).optional(),
}).refine(data => {
  // If action is UPDATE_STATUS, status is required
  if (data.action === 'UPDATE_STATUS') {
    return !!data.status;
  }
  // If action is ASSIGN_TECHNICIAN, technicianId is required
  if (data.action === 'ASSIGN_TECHNICIAN') {
    return !!data.technicianId;
  }
  // If action is EXPORT, exportFormat is required
  if (data.action === 'EXPORT') {
    return !!data.exportFormat;
  }
  return true;
}, {
  message: "Required fields missing for the selected action",
});
