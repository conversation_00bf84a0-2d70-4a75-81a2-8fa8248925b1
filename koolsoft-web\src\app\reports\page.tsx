'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  ShieldCheck,
  Clock,
  DollarSign,
  FileCheck
} from 'lucide-react';

/**
 * Reports Dashboard Page
 *
 * This page is only accessible to manager and admin users.
 * It provides access to various reports and analytics.
 */
export default function ReportsDashboardPage() {
  const [reportSummary, setReportSummary] = useState<any>(null);
  const [isLoadingReports, setIsLoadingReports] = useState(false);

  // Fetch report summary on component mount
  useEffect(() => {
    const fetchReportSummary = async () => {
      setIsLoadingReports(true);
      try {
        const response = await fetch('/api/reports/summary');
        const data = await response.json();
        setReportSummary(data.summary);
      } catch (error) {
        console.error('Error fetching report summary:', error);
      } finally {
        setIsLoadingReports(false);
      }
    };

    fetchReportSummary();
  }, []);

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      {/* Report Cards */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-primary rounded-md p-3">
              <ShieldCheck className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Active AMCs
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    {isLoadingReports ? 'Loading...' : reportSummary?.amc?.active || 0}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/reports/amc" className="font-medium text-primary hover:text-primary/80">
              View AMC Reports
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-success rounded-md p-3">
              <FileCheck className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Active Warranties
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    {isLoadingReports ? 'Loading...' : reportSummary?.warranty?.active || 0}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/reports/warranty" className="font-medium text-primary hover:text-primary/80">
              View Warranty Reports
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-warning rounded-md p-3">
              <Clock className="h-6 w-6 text-black" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Pending Services
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    {isLoadingReports ? 'Loading...' : reportSummary?.service?.pending || 0}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/reports/service" className="font-medium text-primary hover:text-primary/80">
              View Service Reports
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-info rounded-md p-3">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Sales Orders
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    {isLoadingReports ? 'Loading...' : reportSummary?.sales?.orders || 0}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/reports/sales" className="font-medium text-primary hover:text-primary/80">
              View Sales Reports
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
