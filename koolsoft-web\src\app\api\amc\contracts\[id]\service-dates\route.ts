import { NextRequest, NextResponse } from 'next/server';
import { getAMCServiceDateRepository, getAMCContractRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';

/**
 * Service date query schema for contract
 */
const contractServiceDateQuerySchema = z.object({
  skip: z.coerce.number().int().nonnegative().default(0),
  take: z.coerce.number().int().positive().max(100).default(50),
  orderBy: z.enum(['serviceDate', 'completedDate', 'createdAt']).default('serviceDate'),
  orderDirection: z.enum(['asc', 'desc']).default('asc'),
  includeStatistics: z.coerce.boolean().default(false),
});

/**
 * GET /api/amc/contracts/[id]/service-dates
 * Get service dates for a specific AMC contract
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const { searchParams } = new URL(request.url);
      const queryParams = Object.fromEntries(searchParams.entries());

      // Validate query parameters
      const validatedQuery = contractServiceDateQuerySchema.parse(queryParams);

      const amcServiceDateRepository = getAMCServiceDateRepository();
      const amcContractRepository = getAMCContractRepository();

      // Check if contract exists
      const contract = await amcContractRepository.findById(id);
      if (!contract) {
        return NextResponse.json(
          { error: 'AMC contract not found' },
          { status: 404 }
        );
      }

      // Get service dates for the contract
      const serviceDates = await amcServiceDateRepository.findByContractId(id, {
        skip: validatedQuery.skip,
        take: validatedQuery.take,
        orderBy: {
          [validatedQuery.orderBy]: validatedQuery.orderDirection,
        },
      });

      // Get total count
      const total = await amcServiceDateRepository.count({
        amcContractId: id,
      });

      const response: any = {
        serviceDates,
        meta: {
          total,
          skip: validatedQuery.skip,
          take: validatedQuery.take,
          orderBy: validatedQuery.orderBy,
          orderDirection: validatedQuery.orderDirection,
        },
      };

      // Include statistics if requested
      if (validatedQuery.includeStatistics) {
        const statistics = await amcServiceDateRepository.getStatistics(id);
        response.statistics = statistics;
      }

      return NextResponse.json(response);
    } catch (error) {
      console.error('Error fetching contract service dates:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid query parameters', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch service dates' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/amc/contracts/[id]/service-dates
 * Delete all service dates for a specific AMC contract
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcServiceDateRepository = getAMCServiceDateRepository();
      const amcContractRepository = getAMCContractRepository();

      // Check if contract exists
      const contract = await amcContractRepository.findById(id);
      if (!contract) {
        return NextResponse.json(
          { error: 'AMC contract not found' },
          { status: 404 }
        );
      }

      // Delete all service dates for the contract
      const deletedCount = await amcServiceDateRepository.deleteByContractId(id);

      return NextResponse.json({
        message: `Successfully deleted ${deletedCount} service dates`,
        deletedCount,
      });
    } catch (error) {
      console.error('Error deleting contract service dates:', error);
      return NextResponse.json(
        { error: 'Failed to delete service dates' },
        { status: 500 }
      );
    }
  }
);
