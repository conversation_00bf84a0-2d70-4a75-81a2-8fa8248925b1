'use client';

import { useState, useEffect } from 'react';

// Define the executive type
interface Executive {
  id: string;
  name: string;
  email?: string;
  role?: string;
}

/**
 * Custom hook for fetching executives
 */
export function useExecutives() {
  const [executives, setExecutives] = useState<Executive[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchExecutives = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/users?role=EXECUTIVE', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch executives');
        }

        const data = await response.json();
        setExecutives(data.users || []);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
        console.error('Error fetching executives:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchExecutives();
  }, []);

  return { executives, isLoading, error };
}
