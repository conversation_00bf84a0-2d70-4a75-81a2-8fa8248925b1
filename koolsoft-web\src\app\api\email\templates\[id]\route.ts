import { NextRequest, NextResponse } from 'next/server';
import { getEmailTemplateRepository } from '@/lib/repositories';
import { z } from 'zod';

/**
 * Email template update schema
 */
const updateEmailTemplateSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  subject: z.string().min(2).max(200).optional(),
  bodyHtml: z.string().min(10).optional(),
  bodyText: z.string().min(10).optional(),
  body: z.string().min(10).optional(), // For backward compatibility
  description: z.string().optional(),
  variables: z.array(z.string()).optional(),
  category: z.string().optional(),
  isActive: z.boolean().optional(),
}).transform(data => {
  // If body is provided but bodyHtml is not, use body for bodyHtml
  if (data.body && !data.bodyHtml) {
    data.bodyHtml = data.body;
    delete data.body; // Remove body to avoid conflicts
  }

  return data;
});

/**
 * GET /api/email/templates/[id]
 * Get a specific email template by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const emailTemplateRepository = getEmailTemplateRepository();

    // Get email template
    const template = await emailTemplateRepository.findById(id);

    if (!template) {
      return NextResponse.json(
        { error: 'Email template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error('Error fetching email template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email template' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/email/templates/[id]
 * Update a specific email template
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // Validate request body
    const validatedData = updateEmailTemplateSchema.parse(body);

    const emailTemplateRepository = getEmailTemplateRepository();

    // Check if email template exists
    const existingTemplate = await emailTemplateRepository.findById(id);

    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Email template not found' },
        { status: 404 }
      );
    }

    // Check if name is being updated and if it already exists
    if (validatedData.name && validatedData.name !== existingTemplate.name) {
      const templateWithSameName = await emailTemplateRepository.findByName(validatedData.name);
      if (templateWithSameName && templateWithSameName.id !== id) {
        return NextResponse.json(
          { error: `Email template with name '${validatedData.name}' already exists` },
          { status: 400 }
        );
      }
    }

    // Update email template
    const updatedTemplate = await emailTemplateRepository.update(id, validatedData);

    return NextResponse.json(updatedTemplate);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating email template:', error);
    return NextResponse.json(
      { error: 'Failed to update email template' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/email/templates/[id]
 * Delete a specific email template
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const emailTemplateRepository = getEmailTemplateRepository();

    // Check if email template exists
    const existingTemplate = await emailTemplateRepository.findById(id);

    if (!existingTemplate) {
      return NextResponse.json(
        { error: 'Email template not found' },
        { status: 404 }
      );
    }

    // Delete email template
    await emailTemplateRepository.delete(id);

    return NextResponse.json(
      { message: 'Email template deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting email template:', error);
    return NextResponse.json(
      { error: 'Failed to delete email template' },
      { status: 500 }
    );
  }
}
