'use client';

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

type AMCStatusBadgeProps = {
  status: string;
  className?: string;
  showDot?: boolean;
};

/**
 * AMC Status Badge Component
 * 
 * This component displays a badge with appropriate styling based on the AMC contract status.
 * It follows the UI standards with consistent colors and styling.
 */
export function AMCStatusBadge({ status, className, showDot = true }: AMCStatusBadgeProps) {
  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case 'EXPIRED':
        return "bg-red-100 text-red-800 hover:bg-red-100";
      case 'PENDING':
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
      case 'CANCELLED':
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
      case 'RENEWED':
        return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  const getDotColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return "bg-green-500";
      case 'EXPIRED':
        return "bg-red-500";
      case 'PENDING':
        return "bg-yellow-500";
      case 'CANCELLED':
        return "bg-gray-500";
      case 'RENEWED':
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <Badge 
      variant="outline" 
      className={cn(
        "font-medium py-1 px-2 flex items-center gap-1.5", 
        getStatusColor(status),
        className
      )}
    >
      {showDot && (
        <span className={cn("h-2 w-2 rounded-full", getDotColor(status))} />
      )}
      {status}
    </Badge>
  );
}
