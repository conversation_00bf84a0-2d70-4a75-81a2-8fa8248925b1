import { NextRequest, NextResponse } from 'next/server';
import { getCustomerRepository, getHistoryCardRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/role-check';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';
import { PrismaClient } from '@prisma/client';

/**
 * GET /api/customers/[id]/details
 * Get detailed customer information including all related data
 */
async function getCustomerDetails(
  request: NextRequest,
  context?: any
) {
  try {
    // Extract ID from URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    // ID is second-to-last part in /customers/[id]/details
    const id = pathParts[pathParts.length - 2];

    const customerRepository = getCustomerRepository();
    const historyCardRepository = getHistoryCardRepository();
    const prisma = new PrismaClient();

    // Get customer with all related data
    const customer = await customerRepository.findWithRelations(id);

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Get history cards for this customer with complaints from the modern database
    let historyCards = [];
    try {
      // Get history cards from the repository
      historyCards = await historyCardRepository.findByCustomerId(id, 0, 100);

      // If we have history cards but no complaints, try to get complaints directly
      if (historyCards && historyCards.length > 0) {
        // Check if any history card has complaints
        const hasComplaints = historyCards.some(card =>
          card.complaints && card.complaints.length > 0
        );

        // If no complaints found, try to get them directly from the database
        if (!hasComplaints) {
          console.log('No complaints found in history cards, trying direct database query');

          // For each history card, get complaints directly from the database
          const historyCardsWithComplaints = await Promise.all(
            historyCards.map(async (card) => {
              try {
                const complaints = await prisma.$queryRaw`
                  SELECT
                    id,
                    history_card_id,
                    complaint_date,
                    description,
                    complaint_type,
                    resolution,
                    resolution_date,
                    original_card_no,
                    original_complaint_id,
                    created_at,
                    updated_at
                  FROM history_complaints
                  WHERE history_card_id = ${card.id}
                  ORDER BY complaint_date DESC
                `;

                return {
                  ...card,
                  complaints: Array.isArray(complaints) ? complaints : []
                };
              } catch (error) {
                console.error(`Error fetching complaints for history card ${card.id}:`, error);
                return card;
              }
            })
          );

          historyCards = historyCardsWithComplaints;
        }
      }
    } catch (error) {
      console.error('Error fetching history cards:', error);
      // Continue execution even if history cards fetch fails
    }

    // Close the Prisma client
    await prisma.$disconnect();

    // Format response
    const response = {
      customer: {
        id: customer.id,
        name: customer.name,
        address: customer.address,
        city: customer.city,
        state: customer.state,
        pinCode: customer.pinCode,
        phone: customer.phone,
        fax: customer.fax,
        email: customer.email,
        mobile: customer.mobile,
        website: customer.website,
        phone1: customer.phone1,
        phone2: customer.phone2,
        phone3: customer.phone3,
        location: customer.location,
        birthDate: customer.birthDate,
        birthYear: customer.birthYear,
        anniversaryDate: customer.anniversaryDate,
        anniversaryYear: customer.anniversaryYear,
        segmentId: customer.segmentId,
        designation: customer.designation,
        visitCardPath: customer.visitCardPath,
        originalId: customer.originalId,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt
      },
      contacts: customer.contacts.map(contact => ({
        id: contact.id,
        name: contact.name,
        designation: contact.designation || '',
        phone: contact.phone || '',
        email: contact.email || '',
        isPrimary: contact.isPrimary || false
      })),
      visitCards: customer.visitCards,
      amcContracts: customer.amcContracts.map(contract => ({
        id: contract.id,
        contractNumber: contract.contractNumber,
        startDate: contract.startDate,
        endDate: contract.endDate,
        status: contract.status,
        amount: contract.amount,
        machines: contract.machines,
        serviceDates: contract.serviceDates,
        payments: contract.payments,
        divisions: contract.divisions
      })),
      warranties: customer.warranties.map(warranty => ({
        id: warranty.id,
        warrantyNumber: warranty.bslNo || `WTY-${warranty.id.substring(0, 8)}`,
        startDate: warranty.installDate,
        endDate: warranty.warrantyDate,
        status: warranty.status,
        machines: warranty.machines,
        numberOfMachines: warranty.numberOfMachines
      })),
      historyCards: Array.isArray(historyCards) ? historyCards.map(card => {
        // Determine the type based on related records
        let type = 'UNKNOWN';
        let description = '';
        let date = card.createdAt;
        let status = 'UNKNOWN';
        // Format reference as HC-{card_no}
        let reference = card.cardNo ? `HC-${card.cardNo}` : `HC-${card.id.substring(0, 8)}`;

        // Check for repairs
        if (card.repairs && card.repairs.length > 0) {
          type = 'REPAIR';
          description = card.repairs[0].description || '';
          date = card.repairs[0].repairDate || card.createdAt;
          status = 'COMPLETED';
        }
        // Check for maintenance
        else if (card.maintenance && card.maintenance.length > 0) {
          type = 'MAINTENANCE';
          description = card.maintenance[0].description || '';
          date = card.maintenance[0].maintenanceDate || card.createdAt;
          status = 'COMPLETED';
        }
        // Check for complaints
        else if (card.complaints && card.complaints.length > 0) {
          const complaint = card.complaints[0];
          type = 'COMPLAINT';

          // Handle both snake_case and camelCase property names
          const complaintDesc = complaint.description || '';
          const complaintDate = complaint.complaintDate || complaint.complaint_date || card.createdAt;
          const complaintType = complaint.complaintType || complaint.complaint_type || '';
          const resolutionText = complaint.resolution || '';
          const resolutionDate = complaint.resolutionDate || complaint.resolution_date;

          // Set the description - include complaint type in square brackets if available
          description = complaintType && complaintType.trim() !== ''
            ? `[${complaintType}] ${complaintDesc}`
            : complaintDesc;

          date = complaintDate;

          // Set status based on resolution date and resolution text
          if (resolutionDate) {
            status = 'RESOLVED';
          } else if (resolutionText && resolutionText.trim() !== '') {
            status = 'IN PROGRESS';
          } else {
            status = 'PENDING';
          }
        }
        // Check for AMC details
        else if (card.amcDetails && card.amcDetails.length > 0) {
          type = 'AMC';
          description = card.amcDetails[0].description || '';
          date = card.amcDetails[0].startDate || card.createdAt;
          status = 'ACTIVE';
        }
        // Check for addon details
        else if (card.addonDetails && card.addonDetails.length > 0) {
          type = 'ADDON';
          description = card.addonDetails[0].description || '';
          date = card.addonDetails[0].addonDate || card.createdAt;
          status = 'COMPLETED';
        }

        // Build the response object
        const responseObj = {
          id: card.id,
          reference: reference,
          date: date,
          type: type,
          description: description,
          status: status,
          notes: '',
          source: card.source || ''
        };

        // Add additional data for complaints
        if (type === 'COMPLAINT' && card.complaints && card.complaints.length > 0) {
          const complaint = card.complaints[0];

          // Standardize complaint data format
          responseObj.complaintData = {
            id: complaint.id,
            history_card_id: complaint.history_card_id || complaint.historyCardId,
            complaint_date: complaint.complaint_date || complaint.complaintDate,
            description: complaint.description || '',
            complaint_type: complaint.complaint_type || complaint.complaintType || '',
            resolution: complaint.resolution || '',
            resolution_date: complaint.resolution_date || complaint.resolutionDate,
          };
        }

        return responseObj;
      }) : []
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching customer details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer details' },
      { status: 500 }
    );
  }
}

// Export handler with role protection and activity logging
export const GET = ActivityLoggerMiddlewareFactory.forCustomerRoutes(
  withRoleProtection(getCustomerDetails, ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']),
  {
    action: 'view_customer_details',
    getEntityId: (req) => {
      try {
        // Extract ID from URL path
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        return pathParts[pathParts.length - 2]; // ID is second-to-last part in /customers/[id]/details
      } catch (error) {
        console.error('Error getting entity ID:', error);
        return 'unknown';
      }
    },
  }
);
