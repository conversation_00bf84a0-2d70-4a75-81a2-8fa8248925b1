import { toast } from "@/components/ui/use-toast";

/**
 * Helper functions for displaying toast notifications with consistent styling
 */

/**
 * Display a success toast notification
 * @param title The title of the toast
 * @param description Optional description text
 */
export function showSuccessToast(title: string, description?: string) {
  // Clear any existing toasts to prevent duplicates
  document.querySelectorAll('[role="status"]').forEach(el => {
    const toastId = el.getAttribute('data-toast-id');
    if (toastId) {
      // Find the dismiss button and click it
      const dismissButton = el.querySelector('[toast-close]');
      if (dismissButton instanceof HTMLElement) {
        dismissButton.click();
      }
    }
  });

  // Show the toast with success variant
  toast({
    variant: "success",
    title,
    description,
  });
}

/**
 * Display an error toast notification
 * @param title The title of the toast
 * @param description Optional description text
 */
export function showErrorToast(title: string, description?: string) {
  // Clear any existing toasts to prevent duplicates
  document.querySelectorAll('[role="status"]').forEach(el => {
    const toastId = el.getAttribute('data-toast-id');
    if (toastId) {
      // Find the dismiss button and click it
      const dismissButton = el.querySelector('[toast-close]');
      if (dismissButton instanceof HTMLElement) {
        dismissButton.click();
      }
    }
  });

  // Show the toast with destructive variant
  toast({
    variant: "destructive",
    title,
    description,
  });
}

/**
 * Display a warning toast notification
 * @param title The title of the toast
 * @param description Optional description text
 */
export function showWarningToast(title: string, description?: string) {
  // Clear any existing toasts to prevent duplicates
  document.querySelectorAll('[role="status"]').forEach(el => {
    const toastId = el.getAttribute('data-toast-id');
    if (toastId) {
      // Find the dismiss button and click it
      const dismissButton = el.querySelector('[toast-close]');
      if (dismissButton instanceof HTMLElement) {
        dismissButton.click();
      }
    }
  });

  // Show the toast with warning variant
  toast({
    variant: "warning",
    title,
    description,
  });
}

/**
 * Display an info toast notification
 * @param title The title of the toast
 * @param description Optional description text
 */
export function showInfoToast(title: string, description?: string) {
  // Clear any existing toasts to prevent duplicates
  document.querySelectorAll('[role="status"]').forEach(el => {
    const toastId = el.getAttribute('data-toast-id');
    if (toastId) {
      // Find the dismiss button and click it
      const dismissButton = el.querySelector('[toast-close]');
      if (dismissButton instanceof HTMLElement) {
        dismissButton.click();
      }
    }
  });

  // Show the toast with info variant
  toast({
    variant: "info",
    title,
    description,
  });
}

/**
 * Display a default toast notification
 * @param title The title of the toast
 * @param description Optional description text
 */
export function showToast(title: string, description?: string) {
  // Clear any existing toasts to prevent duplicates
  document.querySelectorAll('[role="status"]').forEach(el => {
    const toastId = el.getAttribute('data-toast-id');
    if (toastId) {
      // Find the dismiss button and click it
      const dismissButton = el.querySelector('[toast-close]');
      if (dismissButton instanceof HTMLElement) {
        dismissButton.click();
      }
    }
  });

  // Show the toast with default variant
  toast({
    title,
    description,
  });
}
