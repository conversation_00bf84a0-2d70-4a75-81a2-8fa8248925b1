import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCPaymentRepository, getAMCContractRepository } from '@/lib/repositories';

/**
 * GET /api/amc/contracts/[id]/payment-statistics
 * Get payment statistics for a specific AMC contract
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      // Verify contract exists
      const amcContractRepository = getAMCContractRepository();
      const contract = await amcContractRepository.findById(id);

      if (!contract) {
        return NextResponse.json(
          { error: 'AMC contract not found' },
          { status: 404 }
        );
      }

      const amcPaymentRepository = getAMCPaymentRepository();
      const statistics = await amcPaymentRepository.getPaymentStatistics(id);

      // Calculate balance
      const balance = contract.amount - statistics.totalPaid;

      return NextResponse.json({
        ...statistics,
        contractAmount: contract.amount,
        balance,
        paymentPercentage: contract.amount > 0 ? (statistics.totalPaid / contract.amount) * 100 : 0,
      });
    } catch (error) {
      console.error('Error fetching payment statistics:', error);
      return NextResponse.json(
        { error: 'Failed to fetch payment statistics' },
        { status: 500 }
      );
    }
  }
);
