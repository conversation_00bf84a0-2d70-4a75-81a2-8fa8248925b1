'use client';

import { ReactNode } from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import Link from 'next/link';

interface ReportsGateProps {
  /**
   * The content to render if the user has the required role
   */
  children: ReactNode;

  /**
   * Optional content to render if the user doesn't have the required role
   */
  fallback?: ReactNode;
}

/**
 * ReportsGate Component
 *
 * This component conditionally renders content based on whether the user
 * has access to reports (ADMIN, MANAGER, or EXECUTIVE roles).
 */
export function ReportsGate({ children, fallback }: ReportsGateProps) {
  const { userRole, isAuthenticated, isLoading } = useAuth();

  // If still loading, show nothing
  if (isLoading) {
    return null;
  }

  // If not authenticated, show login message
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Authentication Required</h2>
          <p className="text-gray-500">Please log in to access this page</p>
          <div className="mt-4">
            <Link href="/auth/login" className="text-blue-600 hover:text-blue-500">
              Go to Login
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Check if the user has the required role
  const allowedRoles = ['ADMIN', 'MANAGER', 'EXECUTIVE'];
  const normalizedUserRole = userRole?.toUpperCase();
  const hasAccess = allowedRoles.includes(normalizedUserRole as string);

  // If the user doesn't have access, show the fallback or a default message
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
          <p className="text-gray-500">You do not have permission to access reports</p>
          <div className="mt-4">
            <Link href="/dashboard" className="text-blue-600 hover:text-blue-500">
              Return to Dashboard
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // User has access, render the children
  return <>{children}</>;
}
