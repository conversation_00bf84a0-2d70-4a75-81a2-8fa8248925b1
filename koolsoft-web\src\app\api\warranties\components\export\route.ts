import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getWarrantyComponentRepository } from '@/lib/repositories';
import { format } from 'date-fns';

/**
 * Export Warranty Components
 * 
 * GET /api/warranties/components/export
 * 
 * Exports warranty components in CSV format
 */
async function exportHandler(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const exportFormat = searchParams.get('format') || 'CSV';
    
    // Get all components for export
    const warrantyComponentRepository = getWarrantyComponentRepository();
    const components = await warrantyComponentRepository.findWithFilter({
      filter: {},
      skip: 0,
      take: 10000, // Large number to get all components
      orderBy: { createdAt: 'desc' }
    });

    // Generate CSV export
    return generateCSVExport(components.components);
  } catch (error) {
    console.error('Error exporting warranty components:', error);
    return NextResponse.json(
      { error: 'Failed to export warranty components' },
      { status: 500 }
    );
  }
}

/**
 * Generate CSV export for warranty components
 */
function generateCSVExport(components: any[]) {
  const headers = [
    'Component No',
    'Serial Number',
    'Section',
    'Warranty Date',
    'Status',
    'Machine Serial Number',
    'Machine Location',
    'Customer Name',
    'Customer City',
    'BSL No',
    'Product Name',
    'Model Name',
    'Brand Name',
    'Created At'
  ];

  const csvRows = components.map(component => {
    // Calculate warranty status
    const warrantyDate = component.warrantyDate ? new Date(component.warrantyDate) : null;
    const today = new Date();
    let status = 'Unknown';
    
    if (warrantyDate) {
      const daysUntilExpiry = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      if (daysUntilExpiry < 0) {
        status = 'Expired';
      } else if (daysUntilExpiry <= 30) {
        status = 'Expiring Soon';
      } else {
        status = 'Active';
      }
    }

    return [
      component.componentNo?.toString() || '',
      component.serialNumber || '',
      component.section || '',
      warrantyDate ? format(warrantyDate, 'yyyy-MM-dd') : '',
      status,
      component.machine?.serialNumber || '',
      component.machine?.location || '',
      component.machine?.warranty?.customer?.name || '',
      component.machine?.warranty?.customer?.city || '',
      component.machine?.warranty?.bslNo || '',
      component.machine?.product?.name || '',
      component.machine?.model?.name || '',
      component.machine?.brand?.name || '',
      component.createdAt ? format(new Date(component.createdAt), 'yyyy-MM-dd HH:mm:ss') : ''
    ];
  });

  // Escape CSV values
  const escapeCsvValue = (value: string) => {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  };

  const csvHeader = headers.map(escapeCsvValue).join(',');
  const csvData = csvRows.map(row => 
    row.map(cell => escapeCsvValue(cell.toString())).join(',')
  ).join('\n');

  const csv = [csvHeader, csvData].join('\n');

  // Generate filename
  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `warranty_components_${now}.csv`;

  return new NextResponse(csv, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

// Wrap the handler with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  exportHandler
);
