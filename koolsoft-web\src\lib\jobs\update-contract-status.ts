import { PrismaClient } from '@prisma/client';

/**
 * Update the status of AMC contracts based on their end dates
 *
 * This function finds all ACTIVE contracts with end dates in the past
 * and updates their status to EXPIRED.
 *
 * @returns Object with success status and count of updated contracts
 */
export async function updateContractStatuses() {
  const prisma = new PrismaClient();
  const today = new Date();

  try {
    console.log('Starting contract status update process...');
    console.log('Current date:', today.toISOString());

    // Find all ACTIVE contracts with end dates in the past
    console.log('Querying for ACTIVE contracts with end dates in the past...');
    const expiredContracts = await prisma.amc_contracts.findMany({
      where: {
        status: 'ACTIVE',
        endDate: {
          lt: today,
        },
      },
      select: {
        id: true,
        customerId: true,
        startDate: true,
        endDate: true,
        // Note: contractNumber field doesn't exist in the amc_contracts model
        // Using originalId instead which can serve as a reference number
        originalId: true,
      }
    });

    console.log(`Found ${expiredContracts.length} expired contracts to update`);

    // Update their status to EXPIRED
    if (expiredContracts.length > 0) {
      const updateResult = await prisma.amc_contracts.updateMany({
        where: {
          id: {
            in: expiredContracts.map(contract => contract.id),
          },
        },
        data: {
          status: 'EXPIRED',
        },
      });

      console.log(`Updated ${updateResult.count} contracts to EXPIRED status`);

      // Log the updated contracts for audit purposes
      const updatedContractIds = expiredContracts.map(c => c.id);
      console.log('Updated contract IDs:', updatedContractIds);

      // Create activity logs for the updated contracts
      const activityLogs = expiredContracts.map(contract => ({
        entityType: 'AMC_CONTRACT',
        entityId: contract.id,
        action: 'STATUS_UPDATED',
        details: JSON.stringify({
          previousStatus: 'ACTIVE',
          newStatus: 'EXPIRED',
          reason: 'End date passed',
          endDate: contract.endDate,
          originalId: contract.originalId, // Use originalId as reference
        }),
        userId: null, // System-generated update
      }));

      if (activityLogs.length > 0) {
        try {
          await prisma.activity_logs.createMany({
            data: activityLogs,
          });
          console.log(`Created ${activityLogs.length} activity logs for updated contracts`);
        } catch (logError) {
          console.error('Error creating activity logs:', logError);
        }
      }
    }

    return {
      success: true,
      updatedCount: expiredContracts.length,
    };
  } catch (error) {
    console.error('Error updating contract statuses:', error);

    // Provide more detailed error information
    let errorMessage = 'Unknown error';
    let errorDetails = {};

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = {
        name: error.name,
        stack: error.stack,
      };

      // Check for Prisma-specific errors
      if (error.name === 'PrismaClientKnownRequestError') {
        const prismaError = error as any;
        errorDetails = {
          ...errorDetails,
          code: prismaError.code,
          meta: prismaError.meta,
          clientVersion: prismaError.clientVersion,
        };
      }
    }

    console.error('Error details:', errorDetails);

    return {
      success: false,
      error: errorMessage,
      details: errorDetails,
    };
  } finally {
    // Always disconnect from Prisma client
    try {
      await prisma.$disconnect();
      console.log('Prisma client disconnected successfully');
    } catch (disconnectError) {
      console.error('Error disconnecting Prisma client:', disconnectError);
    }
  }
}
