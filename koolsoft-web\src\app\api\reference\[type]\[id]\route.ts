import { NextRequest, NextResponse } from 'next/server';
import { getReferenceDataRepository } from '@/lib/repositories';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * Reference data update schema
 */
const updateReferenceDataSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  description: z.string().optional(),
});

/**
 * GET /api/reference/[type]/[id]
 * Get a specific reference data entry
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ type: string; id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Extract the type and id from the URL path
    const { type, id } = await params;

    try {
      const referenceDataRepository = getReferenceDataRepository(type);

      // Get reference data
      const data = await referenceDataRepository.findById(id);

      if (!data) {
        return NextResponse.json(
          { error: `${type} with ID ${id} not found` },
          { status: 404 }
        );
      }

      return NextResponse.json(data);
    } catch (error) {
      if (error instanceof Error && error.message.includes('Invalid reference model')) {
        return NextResponse.json(
          { error: `Invalid reference type: ${type}` },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error(`Error fetching ${type} reference data:`, error);
    return NextResponse.json(
      { error: `Failed to fetch ${type} reference data` },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/reference/[type]/[id]
 * Update a specific reference data entry
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ type: string; id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check authorization - only ADMIN and MANAGER can update reference data
    const userRole = session.user.role?.toUpperCase();
    if (userRole !== 'ADMIN' && userRole !== 'MANAGER') {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      );
    }

    // Extract the type and id from the URL path
    const { type, id } = await params;
    const body = await request.json();

    // Validate request body
    const validatedData = updateReferenceDataSchema.parse(body);

    try {
      const referenceDataRepository = getReferenceDataRepository(type);

      // Check if reference data exists
      const existingData = await referenceDataRepository.findById(id);
      if (!existingData) {
        return NextResponse.json(
          { error: `${type} with ID ${id} not found` },
          { status: 404 }
        );
      }

      // Check if name is being updated and if it already exists (exact match)
      if (validatedData.name && validatedData.name !== existingData.name) {
        const dataWithSameName = await referenceDataRepository.findByName(validatedData.name, 0, 10, true);

        // Filter out the current item from the results
        const duplicates = dataWithSameName.filter(item => item.id !== id);

        if (duplicates.length > 0) {
          return NextResponse.json(
            { error: `${type} with name '${validatedData.name}' already exists` },
            { status: 400 }
          );
        }
      }

      // Update reference data
      const updatedData = await referenceDataRepository.update(id, validatedData);

      return NextResponse.json(updatedData);
    } catch (error) {
      if (error instanceof Error && error.message.includes('Invalid reference model')) {
        return NextResponse.json(
          { error: `Invalid reference type: ${type}` },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error(`Error updating ${type} reference data:`, error);
    return NextResponse.json(
      { error: `Failed to update ${type} reference data` },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/reference/[type]/[id]
 * Delete a specific reference data entry
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ type: string; id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check authorization - only ADMIN and MANAGER can delete reference data
    const userRole = session.user.role?.toUpperCase();
    if (userRole !== 'ADMIN' && userRole !== 'MANAGER') {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      );
    }

    // Extract the type and id from the URL path
    const { type, id } = await params;

    try {
      const referenceDataRepository = getReferenceDataRepository(type);

      // Check if reference data exists
      const existingData = await referenceDataRepository.findById(id);
      if (!existingData) {
        return NextResponse.json(
          { error: `${type} with ID ${id} not found` },
          { status: 404 }
        );
      }

      // Delete reference data
      await referenceDataRepository.delete(id);

      return NextResponse.json(
        { message: `${type} with ID ${id} deleted successfully` },
        { status: 200 }
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes('Invalid reference model')) {
        return NextResponse.json(
          { error: `Invalid reference type: ${type}` },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error(`Error deleting ${type} reference data:`, error);
    return NextResponse.json(
      { error: `Failed to delete ${type} reference data` },
      { status: 500 }
    );
  }
}
