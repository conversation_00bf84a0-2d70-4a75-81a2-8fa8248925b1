'use client';

import React, { useState } from 'react';
import { usePayments, Payment } from '@/lib/hooks/usePayments';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Search, Filter, Plus, Edit, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface PaymentListProps {
  amcContractId?: string;
  showContractInfo?: boolean;
  onCreatePayment?: () => void;
  onEditPayment?: (payment: Payment) => void;
  onDeletePayment?: (payment: Payment) => void;
}

export function PaymentList({
  amcContractId,
  showContractInfo = true,
  onCreatePayment,
  onEditPayment,
  onDeletePayment,
}: PaymentListProps) {
  const [filters, setFilters] = useState({
    amcContractId,
    paymentMode: '',
    dateFrom: undefined as Date | undefined,
    dateTo: undefined as Date | undefined,
    amountMin: undefined as number | undefined,
    amountMax: undefined as number | undefined,
    skip: 0,
    take: 10,
    orderBy: 'paymentDate',
    orderDirection: 'desc' as 'asc' | 'desc',
  });

  const { payments, total, isLoading, error } = usePayments(filters);

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      skip: 0, // Reset pagination when filters change
    }));
  };

  const handlePageChange = (page: number, pageSize: number) => {
    setFilters(prev => ({
      ...prev,
      skip: page * pageSize,
      take: pageSize,
    }));
  };

  const getPaymentModeColor = (mode: string) => {
    switch (mode) {
      case 'CASH':
        return 'bg-green-100 text-green-800';
      case 'CHEQUE':
        return 'bg-blue-100 text-blue-800';
      case 'BANK_TRANSFER':
        return 'bg-purple-100 text-purple-800';
      case 'ONLINE':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      key: 'receiptNo',
      label: 'Receipt No.',
      sortable: true,
      render: (payment: Payment) => (
        <span className="font-medium text-black">
          {payment.receiptNo || 'N/A'}
        </span>
      ),
    },
    {
      key: 'paymentDate',
      label: 'Payment Date',
      sortable: true,
      render: (payment: Payment) => (
        <span className="text-black">
          {format(new Date(payment.paymentDate), 'dd MMM yyyy')}
        </span>
      ),
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (payment: Payment) => (
        <span className="font-medium text-black">
          ₹{payment.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
        </span>
      ),
    },
    {
      key: 'paymentMode',
      label: 'Payment Mode',
      render: (payment: Payment) => (
        <Badge className={getPaymentModeColor(payment.paymentMode || 'UNKNOWN')}>
          {payment.paymentMode || 'Unknown'}
        </Badge>
      ),
    },
    ...(showContractInfo ? [{
      key: 'customer',
      label: 'Customer',
      render: (payment: Payment) => (
        <span className="text-black">
          {payment.amcContract?.customer?.name || 'N/A'}
        </span>
      ),
    }] : []),
    {
      key: 'particulars',
      label: 'Particulars',
      render: (payment: Payment) => (
        <span className="text-black text-sm">
          {payment.particulars || '-'}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (payment: Payment) => (
        <div className="flex items-center space-x-2">
          {onEditPayment && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEditPayment(payment)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {onDeletePayment && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeletePayment(payment)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            Error loading payments: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
        <div>
          <CardTitle className="text-white">Payments</CardTitle>
          <p className="text-gray-100 text-sm">
            {total} payment{total !== 1 ? 's' : ''} found
          </p>
        </div>
        {onCreatePayment && (
          <Button variant="secondary" onClick={onCreatePayment}>
            <Plus className="h-4 w-4 mr-2" />
            Add Payment
          </Button>
        )}
      </CardHeader>
      <CardContent className="p-6">
        {/* Filters */}
        <div className="mb-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Payment Mode Filter */}
            <div>
              <label className="text-sm font-medium text-black mb-1 block">
                Payment Mode
              </label>
              <Select
                value={filters.paymentMode}
                onValueChange={(value) => handleFilterChange('paymentMode', value === 'all' ? '' : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All modes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All modes</SelectItem>
                  <SelectItem value="CASH">Cash</SelectItem>
                  <SelectItem value="CHEQUE">Cheque</SelectItem>
                  <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                  <SelectItem value="ONLINE">Online</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date From Filter */}
            <div>
              <label className="text-sm font-medium text-black mb-1 block">
                From Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.dateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateFrom ? format(filters.dateFrom, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateFrom}
                    onSelect={(date) => handleFilterChange('dateFrom', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Date To Filter */}
            <div>
              <label className="text-sm font-medium text-black mb-1 block">
                To Date
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.dateTo && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateTo ? format(filters.dateTo, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateTo}
                    onSelect={(date) => handleFilterChange('dateTo', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => setFilters({
                  amcContractId,
                  paymentMode: '',
                  dateFrom: undefined,
                  dateTo: undefined,
                  amountMin: undefined,
                  amountMax: undefined,
                  skip: 0,
                  take: 10,
                  orderBy: 'paymentDate',
                  orderDirection: 'desc',
                })}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </div>

        {/* Data Table */}
        <DataTable
          data={payments}
          columns={columns}
          loading={isLoading}
          pagination={{
            page: Math.floor(filters.skip / filters.take),
            pageSize: filters.take,
            total,
            onPageChange: handlePageChange,
          }}
          sorting={{
            column: filters.orderBy,
            direction: filters.orderDirection,
            onSort: (column, direction) => {
              handleFilterChange('orderBy', column);
              handleFilterChange('orderDirection', direction);
            },
          }}
        />
      </CardContent>
    </Card>
  );
}
