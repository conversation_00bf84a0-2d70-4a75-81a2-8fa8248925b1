import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCComponentRepository } from '@/lib/repositories';

/**
 * GET /api/amc/components/statistics
 * Get component statistics for dashboard
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const amcComponentRepository = getAMCComponentRepository();

      // Get all components with warranty information
      const components = await amcComponentRepository.findAll({
        includeWarrantyInfo: true
      });

      const today = new Date();
      const thirtyDaysFromNow = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);

      let activeWarranty = 0;
      let expiredWarranty = 0;
      let expiringSoon = 0;

      components.forEach((component: any) => {
        if (component.warrantyDate) {
          const warrantyDate = new Date(component.warrantyDate);
          
          if (warrantyDate < today) {
            expiredWarranty++;
          } else if (warrantyDate <= thirtyDaysFromNow) {
            expiringSoon++;
          } else {
            activeWarranty++;
          }
        } else {
          // Components without warranty date are considered expired
          expiredWarranty++;
        }
      });

      const statistics = {
        total: components.length,
        activeWarranty,
        expiredWarranty,
        expiringSoon
      };

      return NextResponse.json(statistics);
    } catch (error) {
      console.error('Error fetching component statistics:', error);
      return NextResponse.json(
        { error: 'Failed to fetch component statistics' },
        { status: 500 }
      );
    }
  }
);
