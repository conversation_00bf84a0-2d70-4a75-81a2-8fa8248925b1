"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/service/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ LayoutDashboard)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.510.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"7\",\n            height: \"9\",\n            x: \"3\",\n            y: \"3\",\n            rx: \"1\",\n            key: \"10lvy0\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"7\",\n            height: \"5\",\n            x: \"14\",\n            y: \"3\",\n            rx: \"1\",\n            key: \"16une8\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"7\",\n            height: \"9\",\n            x: \"14\",\n            y: \"12\",\n            rx: \"1\",\n            key: \"1hutg5\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"7\",\n            height: \"5\",\n            x: \"3\",\n            y: \"16\",\n            rx: \"1\",\n            key: \"ldoo1y\"\n        }\n    ]\n];\nconst LayoutDashboard = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"layout-dashboard\", __iconNode);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/service/dashboard/page.tsx":
/*!********************************************!*\
  !*** ./src/app/service/dashboard/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServiceDashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Calendar,CheckCircle,Clock,FileText,LayoutDashboard,Target,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\nfunction ServiceDashboardPage() {\n    _s();\n    _s1();\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [period, setPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('MONTH');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceDashboardPage.useEffect\": ()=>{\n            loadStatistics();\n        }\n    }[\"ServiceDashboardPage.useEffect\"], [\n        period\n    ]);\n    const loadStatistics = async ()=>{\n        try {\n            const response = await fetch(\"/api/service/statistics?period=\".concat(period), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setStatistics(data.statistics);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load service statistics');\n            }\n        } catch (error) {\n            console.error('Error loading service statistics:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to load service statistics');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        const colors = {\n            OPEN: 'text-orange-600',\n            IN_PROGRESS: 'text-blue-600',\n            COMPLETED: 'text-green-600',\n            CANCELLED: 'text-red-600',\n            PENDING: 'text-yellow-600'\n        };\n        return colors[status] || 'text-gray-600';\n    };\n    const getComplaintTypeColor = (type)=>{\n        const colors = {\n            REPAIR: 'text-red-600',\n            MAINTENANCE: 'text-blue-600',\n            INSTALLATION: 'text-green-600',\n            INSPECTION: 'text-yellow-600',\n            WARRANTY: 'text-purple-600',\n            OTHER: 'text-gray-600'\n        };\n        return colors[type] || 'text-gray-600';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Service Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                className: \"text-gray-100\",\n                                children: \"Loading service metrics and analytics...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-200 rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 40\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n            lineNumber: 57,\n            columnNumber: 12\n        }, this);\n    }\n    if (!statistics) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dashboard, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                \"Service Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            className: \"text-gray-100\",\n                            children: \"Failed to load service statistics.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dashboard, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Service Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                    value: period,\n                                    onValueChange: setPeriod,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                            className: \"w-[180px] bg-white text-primary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                placeholder: \"Select period\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                    value: \"WEEK\",\n                                                    children: \"This Week\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                    value: \"MONTH\",\n                                                    children: \"This Month\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                    value: \"QUARTER\",\n                                                    children: \"This Quarter\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                    value: \"YEAR\",\n                                                    children: \"This Year\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            className: \"text-gray-100\",\n                            children: [\n                                \"Service metrics and analytics for \",\n                                statistics.period.name.toLowerCase(),\n                                \" period.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Total Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.totalReports\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Open Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.openReports\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.completedReports\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Completion Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    statistics.overview.completionRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                statistics.period.name,\n                                \" Performance\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: [\n                                                \"Reports This \",\n                                                statistics.period.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: statistics.period.totalReports\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: [\n                                                \"Completed This \",\n                                                statistics.period.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: statistics.period.completedReports\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: [\n                                                statistics.period.name,\n                                                \" Completion Rate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: [\n                                                statistics.period.completionRate.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Avg Resolution Time\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    statistics.overview.averageResolutionTime.toFixed(1),\n                                                    \" days\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Service Details\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.totalDetails\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Part Replacement Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    statistics.overview.partReplacementRate.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Unique Machines\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: statistics.overview.uniqueSerialNumbers\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"bg-primary text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Status Breakdown\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: statistics.breakdowns.status.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium \".concat(getStatusColor(item.status)),\n                                                    children: item.status.replace('_', ' ')\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: item.count\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.status, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 57\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"bg-primary text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Complaint Type Breakdown\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: statistics.breakdowns.complaintType.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium \".concat(getComplaintTypeColor(item.complaintType)),\n                                                    children: item.complaintType\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: item.count\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.complaintType, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 64\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"bg-primary text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Most Common Problems\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: statistics.insights.mostCommonProblems.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium truncate flex-1 mr-2\",\n                                                    title: item.problem,\n                                                    children: item.problem\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-sm\",\n                                                    children: item.count\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 88\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"bg-primary text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Calendar_CheckCircle_Clock_FileText_LayoutDashboard_Target_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Most Replaced Parts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: statistics.insights.mostReplacedParts.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium truncate flex-1 mr-2\",\n                                                    title: item.part,\n                                                    children: item.part\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-sm\",\n                                                    children: item.count\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 87\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\dashboard\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 10\n    }, this);\n}\n_s(ServiceDashboardPage, \"cHLAL4XywB85jmaKy41PmFldicw=\");\n_c1 = ServiceDashboardPage;\n_s1(ServiceDashboardPage, \"cHLAL4XywB85jmaKy41PmFldicw=\");\n_c = ServiceDashboardPage;\nvar _c;\n$RefreshReg$(_c, \"ServiceDashboardPage\");\nvar _c1;\n$RefreshReg$(_c1, \"ServiceDashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/service/dashboard/page.tsx\n"));

/***/ })

});