import { NextRequest, NextResponse } from 'next/server';
import { getAMCContractRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';

/**
 * GET /api/amc/contracts/by-customer/[customerId]
 * Get AMC contracts for a specific customer
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ customerId: string }> }
  ) => {
    try {
      const { customerId } = await params;
      const { searchParams } = new URL(request.url);
      const skip = parseInt(searchParams.get('skip') || '0');
      const take = parseInt(searchParams.get('take') || '10');
      const status = searchParams.get('status');
      
      const amcContractRepository = getAMCContractRepository();
      
      // Prepare filter
      const filter: Record<string, any> = { customerId };
      if (status) {
        filter.status = status;
      }
      
      // Get contracts for the customer
      const contracts = await amcContractRepository.findWithFilter(
        filter,
        skip,
        take,
        { startDate: 'desc' }
      );
      
      // Count contracts
      const total = await amcContractRepository.countWithFilter(filter);
      
      return NextResponse.json({
        data: contracts,
        pagination: {
          total,
          skip,
          take,
        },
      });
    } catch (error) {
      console.error('Error fetching AMC contracts for customer:', error);
      return NextResponse.json(
        { error: 'Failed to fetch AMC contracts for customer' },
        { status: 500 }
      );
    }
  }
);
