/**
 * This module loads environment variables from .env files based on the current environment.
 * It should be imported at the entry point of the application.
 */

import { config } from 'dotenv';
import { expand } from 'dotenv-expand';
import path from 'path';

/**
 * Load environment variables from .env files
 * Priority (highest to lowest):
 * 1. .env.local
 * 2. .env.[environment].local
 * 3. .env.[environment]
 * 4. .env
 */
export function loadEnvConfig() {
  const env = process.env.NODE_ENV || 'development';
  const isDev = env === 'development';
  const isTest = env === 'test';

  // Load .env file
  const envFile = path.resolve(process.cwd(), '.env');
  expand(config({ path: envFile }));

  // Load environment-specific .env file
  const envSpecificFile = path.resolve(process.cwd(), `.env.${env}`);
  expand(config({ path: envSpecificFile }));

  // Load environment-specific local .env file (not committed to git)
  const envSpecificLocalFile = path.resolve(process.cwd(), `.env.${env}.local`);
  expand(config({ path: envSpecificLocalFile }));

  // Load local .env file (not committed to git)
  const envLocalFile = path.resolve(process.cwd(), '.env.local');
  expand(config({ path: envLocalFile }));

  // In development, log which files were loaded
  if (isDev) {
    console.log('📄 Loaded environment variables from:');
    [envFile, envSpecificFile, envSpecificLocalFile, envLocalFile].forEach(file => {
      try {
        if (require('fs').existsSync(file)) {
          console.log(`   - ${path.relative(process.cwd(), file)}`);
        }
      } catch (error) {
        // Ignore errors
      }
    });
  }

  return {
    env,
    isDev,
    isTest,
    isProd: env === 'production',
  };
}
