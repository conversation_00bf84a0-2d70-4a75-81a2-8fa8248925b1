import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getWarrantyRepository } from '@/lib/repositories';
import { warrantyExportSchema } from '@/lib/validations/warranty.schema';
import { format } from 'date-fns';

/**
 * Export Warranties
 * 
 * GET /api/warranties/export
 * 
 * Exports warranties in CSV, Excel, or PDF format
 */
async function exportHandler(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate export parameters
    const exportParams = {
      format: searchParams.get('format') || 'CSV',
      includeComponents: searchParams.get('includeComponents') === 'true',
      includeMachines: searchParams.get('includeMachines') !== 'false',
      filters: {
        status: searchParams.get('status'),
        customerId: searchParams.get('customerId'),
        executiveId: searchParams.get('executiveId'),
        search: searchParams.get('search'),
        startDate: searchParams.get('startDate'),
        endDate: searchParams.get('endDate'),
      }
    };

    const validatedParams = warrantyExportSchema.parse(exportParams);
    
    // Get warranties for export
    const warrantyRepository = getWarrantyRepository();
    const warranties = await warrantyRepository.findWithFilter({
      filter: validatedParams.filters,
      skip: 0,
      take: 10000, // Large number to get all warranties
      orderBy: { createdAt: 'desc' }
    });

    // Generate export based on format
    switch (validatedParams.format) {
      case 'CSV':
        return generateCSVExport(warranties.warranties, validatedParams);
      case 'EXCEL':
        return generateExcelExport(warranties.warranties, validatedParams);
      case 'PDF':
        return generatePDFExport(warranties.warranties, validatedParams);
      default:
        return NextResponse.json(
          { error: 'Unsupported export format' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error exporting warranties:', error);
    return NextResponse.json(
      { error: 'Failed to export warranties' },
      { status: 500 }
    );
  }
}

/**
 * Generate CSV export
 */
function generateCSVExport(warranties: any[], params: any) {
  const headers = [
    'BSL No',
    'Customer Name',
    'Customer City',
    'Executive',
    'Contact Person',
    'BSL Date',
    'BSL Amount',
    'Install Date',
    'Warranty Date',
    'Warning Date',
    'Number of Machines',
    'Status',
    'Created At'
  ];

  if (params.includeMachines) {
    headers.push('Machine Serial Numbers', 'Machine Products', 'Machine Models');
  }

  if (params.includeComponents) {
    headers.push('Total Components', 'Component Serial Numbers');
  }

  const csvRows = warranties.map(warranty => {
    const row = [
      warranty.bslNo || '',
      warranty.customer?.name || '',
      warranty.customer?.city || '',
      warranty.executive?.name || '',
      warranty.contactPerson?.name || '',
      warranty.bslDate ? format(new Date(warranty.bslDate), 'yyyy-MM-dd') : '',
      warranty.bslAmount?.toString() || '0',
      warranty.installDate ? format(new Date(warranty.installDate), 'yyyy-MM-dd') : '',
      warranty.warrantyDate ? format(new Date(warranty.warrantyDate), 'yyyy-MM-dd') : '',
      warranty.warningDate ? format(new Date(warranty.warningDate), 'yyyy-MM-dd') : '',
      warranty.numberOfMachines?.toString() || '0',
      warranty.status || '',
      warranty.createdAt ? format(new Date(warranty.createdAt), 'yyyy-MM-dd HH:mm:ss') : ''
    ];

    if (params.includeMachines && warranty.machines) {
      row.push(
        warranty.machines.map((m: any) => m.serialNumber).join('; '),
        warranty.machines.map((m: any) => m.product?.name || '').join('; '),
        warranty.machines.map((m: any) => m.model?.name || '').join('; ')
      );
    } else if (params.includeMachines) {
      row.push('', '', '');
    }

    if (params.includeComponents && warranty.machines) {
      const allComponents = warranty.machines.flatMap((m: any) => m.components || []);
      row.push(
        allComponents.length.toString(),
        allComponents.map((c: any) => c.serialNumber).join('; ')
      );
    } else if (params.includeComponents) {
      row.push('0', '');
    }

    return row;
  });

  // Escape CSV values
  const escapeCsvValue = (value: string) => {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  };

  const csvHeader = headers.map(escapeCsvValue).join(',');
  const csvData = csvRows.map(row => 
    row.map(cell => escapeCsvValue(cell.toString())).join(',')
  ).join('\n');

  const csv = [csvHeader, csvData].join('\n');

  // Generate filename
  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `warranties_${now}.csv`;

  return new NextResponse(csv, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * Generate Excel export (placeholder - would need a library like xlsx)
 */
function generateExcelExport(warranties: any[], params: any) {
  // For now, return CSV with Excel MIME type
  // In a real implementation, you'd use a library like 'xlsx' to generate proper Excel files
  const csvResponse = generateCSVExport(warranties, params);
  
  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `warranties_${now}.xlsx`;

  return new NextResponse(csvResponse.body, {
    headers: {
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * Generate PDF export (placeholder - would need a library like jsPDF or puppeteer)
 */
function generatePDFExport(warranties: any[], params: any) {
  // For now, return a simple text response
  // In a real implementation, you'd use a library like 'jsPDF' or 'puppeteer' to generate PDFs
  const textContent = warranties.map(warranty => 
    `BSL No: ${warranty.bslNo}\nCustomer: ${warranty.customer?.name}\nStatus: ${warranty.status}\n\n`
  ).join('');

  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `warranties_${now}.pdf`;

  return new NextResponse(textContent, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

// Wrap the handler with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  exportHandler
);
