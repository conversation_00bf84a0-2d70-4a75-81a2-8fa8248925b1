'use client';

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle, Download, Upload, Search } from 'lucide-react';

/**
 * Customer Actions Component
 *
 * This component provides action buttons for the customer list page.
 */
export function CustomerActions() {
  return (
    <div className="flex items-center space-x-2">
      <Button asChild variant="secondary">
        <Link href="/customers/new">
          <PlusCircle className="h-4 w-4 mr-2" />
          Add Customer
        </Link>
      </Button>

      <Button asChild variant="outline">
        <Link href="/customers/search">
          <Search className="h-4 w-4 mr-2" />
          Search
        </Link>
      </Button>

      <Button variant="outline">
        <Download className="h-4 w-4 mr-2" />
        Export
      </Button>

      <Button variant="outline">
        <Upload className="h-4 w-4 mr-2" />
        Import
      </Button>
    </div>
  );
}
