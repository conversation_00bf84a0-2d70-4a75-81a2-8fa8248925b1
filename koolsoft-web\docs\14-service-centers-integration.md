# Service Centers Integration with Reference Data Management

## Overview

This document outlines the complete integration of service centers into the KoolSoft reference data management system, including API fixes, database integration, and comprehensive CRUD operations.

## Completed Tasks

### 1. Fixed Service Centers API Import Path ✅

**Issue**: Import path was incorrectly set to `'../auth/[...nextauth]'`
**Solution**: Updated to use `'@/lib/auth'` for proper authOptions import

**Files Updated**:
- `src/pages/api/service-centers/index.js` - Fixed authOptions import path from `'../auth/[...nextauth]'` to `'@/lib/auth'`
- Added complete CRUD operations (PUT, DELETE methods)
- Enhanced error handling and validation

**Impact**: This fix resolved 500 server errors that were affecting multiple API endpoints including:
- `/api/service-centers?vendor=BLUESTAR`
- `/api/warranties?vendor=bluestar`
- Home page (`/`) loading
- Warranties page (`/warranties`) loading

### 2. Enhanced Service Centers API ✅

**Added Features**:
- **PUT Method**: Update existing service centers (Admin/Manager access)
- **DELETE Method**: Soft delete service centers (Admin only)
- **Comprehensive Validation**: Name uniqueness, required fields
- **Role-Based Access Control**: Admin for delete, Admin/Manager for create/update
- **Detailed Error Handling**: Proper error messages and status codes

### 3. Created Reference Data API Endpoints ✅

**New API Endpoints**:
- `src/pages/api/reference/serviceCenters.js` - Main service centers reference API
- `src/pages/api/reference/serviceCenters/[id].js` - Individual service center operations

**Features**:
- Pagination support with skip/take parameters
- Advanced filtering by vendor, city, active status
- Search functionality across name, vendor, city, contact person
- Filter options retrieval for dynamic dropdowns
- Complete CRUD operations with proper authentication

### 4. Integrated Service Centers into Reference Data System ✅

**Reference Data Integration**:
- Added service centers to reference data categories list
- Updated reference data page with service centers card
- Custom routing to dedicated service centers page
- Consistent UI patterns with other reference data

**Files Updated**:
- `src/app/reference-data/page.tsx` - Added service centers category
- `src/app/reference-data/[type]/page.tsx` - Added service centers metadata
- `src/lib/repositories/reference-data.repository.ts` - Added service centers mapping
- `src/lib/hooks/useReferenceData.ts` - Added ServiceCenter interface

### 5. Created Custom Service Centers Management Page ✅

**New Page**: `src/app/reference-data/service-centers/page.tsx`

**Features**:
- **Comprehensive Form**: All service center fields (name, vendor, address, city, state, pincode, phone, email, contact person, active status)
- **Advanced Filtering**: Filter by vendor, city, active status
- **Search Functionality**: Search across multiple fields
- **Pagination**: Proper pagination with page size controls
- **Export Functionality**: CSV export capability
- **CRUD Operations**: Create, read, update, delete with proper validation
- **Role-Based Access**: Proper authentication and authorization
- **Responsive Design**: Mobile-friendly interface
- **Error Handling**: Comprehensive error handling with toast notifications

### 6. Database Schema Integration ✅

**ServiceCenter Model** (Already implemented):
```prisma
model ServiceCenter {
  id            String   @id @default(uuid())
  name          String
  vendor        String?  // e.g., 'BLUESTAR', 'GENERAL'
  address       String?
  city          String?
  state         String?
  pincode       String?
  phone         String?
  email         String?
  contactPerson String?  @map("contact_person")
  active        Boolean  @default(true)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([vendor])
  @@index([city])
  @@index([active])
  @@map("service_centers")
}
```

### 7. Updated BLUESTAR Warranty Integration ✅

**Dynamic Service Centers**:
- BLUESTAR warranty pages now load service centers from database
- Replaced hardcoded dropdown values with API-driven data
- Real-time filtering by vendor (BLUESTAR)
- Proper error handling for service center loading

## API Endpoints Summary

### Service Centers API (`/api/service-centers`)
- **GET**: Fetch service centers with filtering (vendor, city, active)
- **POST**: Create new service center (Admin only)
- **PUT**: Update service center (Admin/Manager)
- **DELETE**: Soft delete service center (Admin only)

### Reference Data API (`/api/reference/serviceCenters`)
- **GET**: Paginated service centers with advanced filtering and search
- **POST**: Create service center with validation

### Individual Service Center API (`/api/reference/serviceCenters/[id]`)
- **GET**: Fetch specific service center
- **PATCH**: Update specific service center
- **DELETE**: Soft delete specific service center

## UI Features

### Service Centers Management Page
1. **Header with Actions**: Export and Add Service Center buttons
2. **Advanced Filtering**: Vendor, city, and status filters
3. **Search**: Multi-field search functionality
4. **Data Table**: Comprehensive service center information display
5. **CRUD Operations**: Create, edit, and delete with proper validation
6. **Form Validation**: Email validation, required field validation
7. **Status Management**: Active/inactive status with visual indicators
8. **Responsive Design**: Works on all device sizes

### Integration with Warranty Management
1. **Dynamic Dropdowns**: Service centers loaded from database
2. **Vendor Filtering**: Automatic filtering by vendor (e.g., BLUESTAR)
3. **Real-time Updates**: Changes in reference data reflect immediately
4. **Error Handling**: Graceful handling of service center loading errors

## Testing Requirements ✅

### API Testing
- ✅ Service centers API endpoints functional
- ✅ CRUD operations working correctly
- ✅ Authentication and authorization verified
- ✅ Proper error handling implemented

### UI Testing
- ✅ Service centers page loads correctly
- ✅ Filtering and search functionality working
- ✅ Form validation working properly
- ✅ CRUD operations through UI functional

### Integration Testing
- ✅ Service centers appear in BLUESTAR warranty dropdowns
- ✅ Reference data integration working
- ✅ Dynamic data loading verified

## Security Implementation

### Authentication
- All API endpoints require valid session
- Proper session validation using NextAuth

### Authorization
- **Admin Only**: Delete operations, service center creation via main API
- **Admin/Manager**: Update operations, service center creation via reference API
- **All Authenticated Users**: Read operations

### Data Validation
- Server-side validation for all fields
- Email format validation
- Required field validation
- Duplicate name prevention

## Benefits Achieved

1. **Centralized Management**: Service centers managed through reference data system
2. **Dynamic Integration**: Warranty pages use real-time service center data
3. **Scalability**: Easy to add new service centers and vendors
4. **Data Consistency**: Single source of truth for service center information
5. **User Experience**: Intuitive interface with advanced filtering and search
6. **Security**: Proper role-based access control
7. **Maintainability**: Clean separation of concerns and proper error handling

## Next Steps

1. **Data Migration**: Import existing service center data if available
2. **Additional Vendors**: Add service centers for other vendors beyond BLUESTAR
3. **Reporting**: Implement service center utilization reports
4. **Integration**: Connect service centers with service reports and technician assignments
5. **Bulk Operations**: Add bulk import/export functionality for service centers

## Conclusion

The service centers integration is now complete and fully functional. Service centers are properly integrated into the reference data management system with comprehensive CRUD operations, advanced filtering, and seamless integration with warranty management pages. The system maintains data consistency, provides excellent user experience, and follows security best practices.
