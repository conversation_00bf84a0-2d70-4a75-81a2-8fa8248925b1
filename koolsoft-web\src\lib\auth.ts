import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { compare } from 'bcrypt';
import { getUserRepository } from '@/lib/repositories';
import { PrismaAdapter } from '@auth/prisma-adapter';
import { prisma } from '@/lib/prisma';

import { getActivityLogService } from '@/lib/services/activity-log.service';

/**
 * NextAuth.js configuration options
 *
 * This configuration sets up authentication with:
 * - Credentials provider for username/password login
 * - JWT-based session handling
 * - Role-based access control via JWT claims
 */
export const authOptions: NextAuthOptions = {
  // Use Prisma adapter for database session storage
  adapter: PrismaAdapter(prisma),

  // Configure providers
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Find user by email
          const userRepository = getUserRepository();
          const user = await userRepository.findByEmail(credentials.email);

          // Get activity log service
          const activityLogService = getActivityLogService();

          // If user not found or inactive, log failed login and return null
          if (!user || !user.isActive) {
            await activityLogService.logAuthEvent(
              'failed_login',
              undefined,
              {
                email: credentials.email,
                reason: !user ? 'user_not_found' : 'user_inactive',
              }
            );
            return null;
          }

          // Verify password
          const isPasswordValid = await compare(credentials.password, user.password);
          if (!isPasswordValid) {
            await activityLogService.logAuthEvent(
              'failed_login',
              user.id,
              {
                email: user.email,
                reason: 'invalid_password',
              }
            );
            return null;
          }

          // Return user data (excluding password)
          return {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
          };
        } catch (error) {
          console.error('Authentication error:', error);

          // Log authentication error
          try {
            const activityLogService = getActivityLogService();
            await activityLogService.logAuthEvent(
              'failed_login',
              undefined,
              {
                email: credentials.email,
                reason: 'error',
                error: error instanceof Error ? error.message : String(error),
              }
            );
          } catch (logError) {
            console.error('Error logging failed login:', logError);
          }

          return null;
        }
      },
    }),
  ],

  // Configure session handling
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours - how frequently to update the token
  },

  // Configure JWT
  jwt: {
    secret: process.env.JWT_SECRET,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  // Configure pages
  pages: {
    signIn: '/auth/login',
    signOut: '/auth/logout',
    error: '/auth/error',
  },

  // Configure callbacks
  callbacks: {
    // Add user role to JWT token
    async jwt({ token, user, account, profile, trigger }) {
      // Log JWT callback for debugging
      console.log('JWT Callback:', {
        trigger,
        tokenHasRole: !!token.role,
        userProvided: !!user,
        userRole: user ? (user as any).role : undefined
      });

      // Always ensure the role is set from the user object when available
      if (user) {
        // Make sure we're getting the role from the user object
        const userRole = (user as any).role;
        console.log(`Setting JWT token role to "${userRole}" from user object`);

        token.role = userRole;
        token.userId = user.id;
      }

      // Log the final token for debugging
      console.log('JWT Token after processing:', {
        role: token.role,
        userId: token.userId
      });

      return token;
    },

    // Add user role to session
    async session({ session, token, user, newSession, trigger }) {
      // Log session callback for debugging
      console.log('Session Callback:', {
        trigger,
        tokenHasRole: !!token.role,
        sessionUserExists: !!session.user
      });

      if (session.user) {
        // Ensure role is copied from token to session
        session.user.role = token.role as string;
        session.user.id = token.userId as string;

        console.log(`Setting session user role to "${session.user.role}" from token`);
      }

      return session;
    },
  },

  // Enable debug in development
  debug: process.env.NODE_ENV === 'development',

  // Configure events for activity logging
  events: {
    async signIn({ user, account, isNewUser, profile }) {
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAuthEvent(
          'login',
          user.id,
          {
            provider: account?.provider || 'credentials',
            isNewUser,
          }
        );
      } catch (error) {
        console.error('Error logging sign in event:', error);
      }
    },
    async signOut({ token }) {
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAuthEvent(
          'logout',
          token.userId as string
        );
      } catch (error) {
        console.error('Error logging sign out event:', error);
      }
    },
    async createUser({ user }) {
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAction(
          'create_user',
          user.id,
          'user',
          user.id,
          {
            name: user.name,
            email: user.email,
          }
        );
      } catch (error) {
        console.error('Error logging create user event:', error);
      }
    },
    async updateUser({ user }) {
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAction(
          'update_user',
          user.id,
          'user',
          user.id
        );
      } catch (error) {
        console.error('Error logging update user event:', error);
      }
    },
    async linkAccount({ user, account, profile }) {
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAction(
          'link_account',
          user.id,
          'user',
          user.id,
          {
            provider: account.provider,
            providerAccountId: account.providerAccountId,
          }
        );
      } catch (error) {
        console.error('Error logging link account event:', error);
      }
    },
  },
};

/**
 * Types for authenticated session
 */
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      role: string;
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role?: string;
    userId?: string;
  }
}
