'use client';

import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Mail, Eye, List, PenSquare } from 'lucide-react';

/**
 * Email Management Landing Page
 *
 * This page provides links to the various email management features.
 */
export default function EmailManagementPage() {
  const router = useRouter();

  const features = [
    {
      title: 'Email Templates',
      description: 'Manage email templates used throughout the application',
      icon: <List className="h-8 w-8 text-primary" />,
      href: '/admin/email/templates',
      color: 'bg-blue-50',
    },
    {
      title: 'Preview Templates',
      description: 'Preview how email templates will look when sent to recipients',
      icon: <Eye className="h-8 w-8 text-primary" />,
      href: '/admin/email/preview',
      color: 'bg-green-50',
    },
    {
      title: 'Create Template',
      description: 'Create a new email template',
      icon: <PenSquare className="h-8 w-8 text-primary" />,
      href: '/admin/email/templates/create',
      color: 'bg-purple-50',
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="text-2xl font-bold text-black">Email Management</CardTitle>
            <CardDescription>
              Manage email templates and preview functionality
            </CardDescription>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {features.map((feature) => (
          <Card
            key={feature.href}
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => router.push(feature.href)}
          >
            <CardHeader className={`${feature.color} rounded-t-lg`}>
              <div className="flex justify-between items-center">
                <CardTitle className="text-black">{feature.title}</CardTitle>
                {feature.icon}
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <CardDescription className="text-gray-600 text-sm">
                {feature.description}
              </CardDescription>
              <Button
                variant="link"
                className="p-0 mt-2 text-primary"
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(feature.href);
                }}
              >
                Open {feature.title}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-black">About Email Management</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">
              The Email Management section allows you to create, edit, and preview email templates
              used throughout the application. Templates can include dynamic content using the
              <code className="bg-gray-100 px-1 py-0.5 rounded">{'{{'}<span>variableName</span>{'}}' }</code> syntax.
            </p>
            <p className="text-gray-700 mt-4">
              When creating or editing templates, you can specify both HTML and plain text versions
              of the email content. The system will automatically use the appropriate version based
              on the recipient's email client capabilities.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
