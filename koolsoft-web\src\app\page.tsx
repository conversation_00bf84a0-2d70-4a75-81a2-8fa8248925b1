'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../lib/hooks/useAuth';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button, ButtonLink } from '@/components/ui/button';
import { ArrowRight, LayoutDashboard, Users, ShieldCheck, Clock } from 'lucide-react';

export default function HomePage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <h2 className="text-xl font-semibold mt-4 text-foreground">Loading...</h2>
          <p className="text-foreground">Please wait</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <h1 className="text-3xl font-bold text-foreground">KoolSoft</h1>
          <div className="flex items-center space-x-4">
            <ButtonLink
              href="/auth/login"
              variant="default"
              size="sm"
            >
              Login
            </ButtonLink>
          </div>
        </div>
      </header>

      <main className="flex-grow flex flex-col py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto w-full">
          <Card className="mb-8 border-none shadow-lg">
            <CardHeader className="text-center pb-2">
              <CardTitle className="text-5xl font-extrabold text-primary">KoolSoft</CardTitle>
              <CardDescription className="text-3xl font-extrabold text-foreground mt-4">
                Welcome to KoolSoft Management System
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-lg text-foreground">
                A comprehensive solution for managing your business operations.
              </p>
              <div className="mt-8 flex justify-center">
                <ButtonLink
                  href="/auth/login"
                  variant="default"
                  size="lg"
                  className="gap-2"
                >
                  Get Started
                  <ArrowRight className="h-5 w-5" />
                </ButtonLink>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
            {/* Feature Cards */}
            <Card className="overflow-hidden shadow">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <LayoutDashboard className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-foreground truncate">
                        Dashboard
                      </dt>
                      <dd>
                        <div className="text-lg font-medium text-foreground">
                          Centralized View
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-secondary px-4 py-4 sm:px-6">
                <div className="text-sm">
                  <Link href="/auth/login" className="font-medium text-primary hover:text-primary/80">
                    Access dashboard
                  </Link>
                </div>
              </div>
            </Card>

            <Card className="overflow-hidden shadow">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-foreground truncate">
                        Customers
                      </dt>
                      <dd>
                        <div className="text-lg font-medium text-foreground">
                          Customer Management
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-secondary px-4 py-4 sm:px-6">
                <div className="text-sm">
                  <Link href="/auth/login" className="font-medium text-primary hover:text-primary/80">
                    Manage customers
                  </Link>
                </div>
              </div>
            </Card>

            <Card className="overflow-hidden shadow">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <ShieldCheck className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-foreground truncate">
                        AMC Contracts
                      </dt>
                      <dd>
                        <div className="text-lg font-medium text-foreground">
                          Contract Management
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-secondary px-4 py-4 sm:px-6">
                <div className="text-sm">
                  <Link href="/auth/login" className="font-medium text-primary hover:text-primary/80">
                    Manage contracts
                  </Link>
                </div>
              </div>
            </Card>

            <Card className="overflow-hidden shadow">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-primary rounded-md p-3">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-foreground truncate">
                        Services
                      </dt>
                      <dd>
                        <div className="text-lg font-medium text-foreground">
                          Service Management
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-secondary px-4 py-4 sm:px-6">
                <div className="text-sm">
                  <Link href="/auth/login" className="font-medium text-primary hover:text-primary/80">
                    Manage services
                  </Link>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </main>

      <footer className="bg-white shadow-inner mt-auto">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <p className="text-center text-foreground">
            &copy; {new Date().getFullYear()} KoolSoft. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
