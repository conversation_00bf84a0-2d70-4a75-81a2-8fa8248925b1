-- Check if admin user exists
DO $$
DECLARE
    user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM users WHERE email = '<EMAIL>';
    
    IF user_count = 0 THEN
        -- Insert admin user with password 'Admin@123' (hashed)
        INSERT INTO users (
            id, 
            name, 
            email, 
            password, 
            role, 
            is_active, 
            created_at, 
            updated_at
        ) VALUES (
            gen_random_uuid(), 
            'Admin User', 
            '<EMAIL>', 
            '$2b$10$XJrEhKGrMxfXCn5JDkKzuOewwjzIcgKT1nWgZ1QcFBCjYKjwaXKHa', -- hashed 'Admin@123'
            'admin', 
            true, 
            NOW(), 
            NOW()
        );
        
        RAISE NOTICE 'Admin user created successfully';
    ELSE
        RAISE NOTICE 'Admin user already exists';
    END IF;
END $$;
