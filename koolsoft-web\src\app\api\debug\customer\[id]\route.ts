import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { withRoleProtection } from '@/lib/auth/role-check';

const prisma = new PrismaClient();

/**
 * GET /api/debug/customer/[id]
 * Debug endpoint to examine customer data
 */
async function debugCustomer(
  request: NextRequest,
  context?: any
) {
  try {
    // Extract ID from URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    // ID is last part in /debug/customer/[id]
    const id = pathParts[pathParts.length - 1];

    // Get customer with contacts
    const customer = await prisma.customers.findUnique({
      where: { id },
      include: {
        contacts: true,
      }
    });

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Get history cards
    const historyCards = await prisma.history_cards.findMany({
      where: { customerId: id },
      include: {
        complaints: true,
      }
    });

    // Get complaints directly
    const directComplaints = await prisma.$queryRaw`
      SELECT * FROM history_complaints
      WHERE history_card_id IN (
        SELECT id FROM history_cards
        WHERE customer_id = ${id}
      )
    `;

    // Check for legacy complaints
    let legacyComplaints = [];
    try {
      const legacyTableExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'History_Complaint'
        ) AS table_exists
      `;

      if (legacyTableExists[0].table_exists) {
        legacyComplaints = await prisma.$queryRaw`
          SELECT hc.*, c.* 
          FROM "History_Complaint" hc
          JOIN "CUSTOMERS" c ON hc."Customer_ID" = c."ID"
          WHERE c."ID" = ${customer.originalId}
          LIMIT 10
        `;
      }
    } catch (error) {
      console.error('Error fetching legacy complaints:', error);
    }

    // Format response
    const response = {
      customer: {
        id: customer.id,
        name: customer.name,
        address: customer.address,
        originalId: customer.originalId,
      },
      contacts: customer.contacts,
      historyCards,
      directComplaints,
      legacyComplaints,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error debugging customer:', error);
    return NextResponse.json(
      { error: 'Failed to debug customer', details: error },
      { status: 500 }
    );
  }
}

// Export handler with role protection
export const GET = withRoleProtection(debugCustomer, ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']);
