import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getServiceReportRepository } from '@/lib/repositories';
import { serviceReportStatusUpdateSchema } from '@/lib/validations/service.schema';

/**
 * PATCH /api/service/[id]/status
 * Update the status of a specific service report
 */
export const PATCH = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = serviceReportStatusUpdateSchema.parse(body);

      const serviceReportRepository = getServiceReportRepository();

      // Check if service report exists
      const existingServiceReport = await serviceReportRepository.findById(id);
      if (!existingServiceReport) {
        return NextResponse.json(
          { error: 'Service report not found' },
          { status: 404 }
        );
      }

      // Update the service report status
      const updatedServiceReport = await serviceReportRepository.updateStatus(
        id,
        validatedData.status,
        validatedData.completionDate
      );

      // If remarks are provided, update them as well
      if (validatedData.remarks) {
        await serviceReportRepository.update(id, {
          remarks: validatedData.remarks,
        });
      }

      return NextResponse.json({
        message: 'Service report status updated successfully',
        serviceReport: updatedServiceReport,
      });
    } catch (error) {
      console.error('Error updating service report status:', error);

      if (error instanceof Error) {
        // Handle validation errors
        if (error.message.includes('validation')) {
          return NextResponse.json(
            { error: 'Validation failed', details: error.message },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Failed to update service report status' },
        { status: 500 }
      );
    }
  }
);
