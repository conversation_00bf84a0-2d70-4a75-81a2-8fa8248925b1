'use client';

import { useState } from 'react';
import { useAMCContracts } from '@/lib/hooks/useAMCContracts';
import { AMCFilterForm } from '@/components/amc/amc-filter-form';
import { AMCList } from '@/components/amc/amc-list';
import { AMCActions } from '@/components/amc/amc-actions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * AMC List Page
 *
 * This page displays a list of AMC contracts with filtering, sorting, and pagination.
 * It follows the UI standards and uses the API routes we created.
 */
export default function AMCPage() {
  const [activeTab, setActiveTab] = useState('all');

  const {
    contracts,
    isLoading,
    pagination,
    filters,
    setFilters,
    setPagination,
    refreshContracts,
    deleteContract
  } = useAMCContracts();

  // Handle filter change
  const handleFilterChange = (newFilters: Record<string, any>) => {
    setFilters(newFilters);
    // Reset pagination when filters change
    setPagination({ ...pagination, skip: 0 });
  };

  // Handle pagination change
  const handlePaginationChange = (skip: number, take: number) => {
    setPagination({ skip, take });
  };

  // Clear filters
  const handleClearFilters = () => {
    setFilters({});
    setPagination({ ...pagination, skip: 0 });
    refreshContracts();
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Create a clean filter object without null values
    const cleanFilters: Record<string, any> = { ...filters };

    // Remove any null values from filters
    Object.keys(cleanFilters).forEach(key => {
      if (cleanFilters[key] === null) {
        delete cleanFilters[key];
      }
    });

    // Update filters based on tab
    switch (value) {
      case 'active':
        setFilters({ ...cleanFilters, status: 'ACTIVE' });
        break;
      case 'expired':
        setFilters({ ...cleanFilters, status: 'EXPIRED' });
        break;
      case 'pending':
        setFilters({ ...cleanFilters, status: 'PENDING' });
        break;
      case 'all':
      default:
        // Create a new filter object without the status property
        const { status, ...filtersWithoutStatus } = cleanFilters;
        setFilters(filtersWithoutStatus);
        break;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle>AMC Management</CardTitle>
            <CardDescription className="text-gray-100">
              View, search, and manage all your AMC contracts
            </CardDescription>
          </div>
          <AMCActions />
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Contracts</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="expired">Expired</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="space-y-4">
              <AMCFilterForm
                onFilter={handleFilterChange}
                initialValues={filters}
              />
              <AMCList
                contracts={contracts}
                isLoading={isLoading}
                pagination={pagination}
                onPaginationChange={handlePaginationChange}
                onRefresh={refreshContracts}
                onDelete={deleteContract}
                onClearFilters={handleClearFilters}
                isFiltered={Object.keys(filters).length > 0}
              />
            </TabsContent>
            <TabsContent value="active" className="space-y-4">
              <AMCFilterForm
                onFilter={handleFilterChange}
                initialValues={{ ...filters, status: 'ACTIVE' }}
              />
              <AMCList
                contracts={contracts}
                isLoading={isLoading}
                pagination={pagination}
                onPaginationChange={handlePaginationChange}
                onRefresh={refreshContracts}
                onDelete={deleteContract}
                onClearFilters={handleClearFilters}
                isFiltered={Object.keys(filters).length > 0 || filters.status === 'ACTIVE'}
              />
            </TabsContent>
            <TabsContent value="expired" className="space-y-4">
              <AMCFilterForm
                onFilter={handleFilterChange}
                initialValues={{ ...filters, status: 'EXPIRED' }}
              />
              <AMCList
                contracts={contracts}
                isLoading={isLoading}
                pagination={pagination}
                onPaginationChange={handlePaginationChange}
                onRefresh={refreshContracts}
                onDelete={deleteContract}
                onClearFilters={handleClearFilters}
                isFiltered={Object.keys(filters).length > 0 || filters.status === 'EXPIRED'}
              />
            </TabsContent>
            <TabsContent value="pending" className="space-y-4">
              <AMCFilterForm
                onFilter={handleFilterChange}
                initialValues={{ ...filters, status: 'PENDING' }}
              />
              <AMCList
                contracts={contracts}
                isLoading={isLoading}
                pagination={pagination}
                onPaginationChange={handlePaginationChange}
                onRefresh={refreshContracts}
                onDelete={deleteContract}
                onClearFilters={handleClearFilters}
                isFiltered={Object.keys(filters).length > 0 || filters.status === 'PENDING'}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
