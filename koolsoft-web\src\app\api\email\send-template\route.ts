import { NextRequest, NextResponse } from 'next/server';
import { getEmailService } from '@/lib/services/email.service';
import { z } from 'zod';

/**
 * Template email sending schema
 */
const sendTemplateEmailSchema = z.object({
  templateName: z.string().min(2).max(100),
  data: z.record(z.any()),
  to: z.union([z.string().email(), z.array(z.string().email())]),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  customerId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
});

/**
 * POST /api/email/send-template
 * Send an email using a template
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = sendTemplateEmailSchema.parse(body);
    
    const emailService = getEmailService();
    
    // Send email using template
    const emailLog = await emailService.sendTemplateEmail(
      validatedData.templateName,
      validatedData.data,
      validatedData.to,
      validatedData.cc,
      validatedData.bcc,
      validatedData.customerId,
      validatedData.userId
    );
    
    return NextResponse.json({
      message: 'Email sent successfully',
      emailLog,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    console.error('Error sending template email:', error);
    return NextResponse.json(
      { error: 'Failed to send template email' },
      { status: 500 }
    );
  }
}
