'use client';

import React from 'react';
import { AMCForm<PERSON>rovider, useAMCForm, AMCFormStep } from '@/contexts/amc-form-context';
import { AMCFormStepper } from './amc-form-stepper';
import { AMCFormStep1 } from './form-steps/amc-form-step1';
import { AMCFormStep2 } from './form-steps/amc-form-step2';
import { AMCFormStep3 } from './form-steps/amc-form-step3';
import { AMCFormStep4 } from './form-steps/amc-form-step4';
import { AMCFormStep5 } from './form-steps/amc-form-step5';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Placeholder components for remaining steps

// AMCFormStep5 is now imported from the separate file

function AMCFormStep6() {
  const { state, goToPreviousStep, clearStorage } = useAMCForm();
  
  const handleSubmit = async () => {
    try {
      const response = await fetch('/api/amc/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(state.formData),
      });

      if (response.ok) {
        clearStorage();
        // Redirect to AMC list or show success message
        window.location.href = '/amc';
      } else {
        const error = await response.json();
        console.error('Failed to create AMC contract:', error);
        alert('Failed to create AMC contract. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('An error occurred. Please try again.');
    }
  };
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold">Review & Submit</h3>
            <p className="text-muted-foreground">
              Review all contract details before submitting.
            </p>
          </div>
          
          {/* Form Data Summary */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-sm text-gray-600">Customer ID</h4>
                <p className="text-sm">{state.formData.customerId || 'Not selected'}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-600">Executive ID</h4>
                <p className="text-sm">{state.formData.executiveId || 'Not assigned'}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-600">Start Date</h4>
                <p className="text-sm">
                  {state.formData.startDate ? new Date(state.formData.startDate).toLocaleDateString() : 'Not set'}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-600">End Date</h4>
                <p className="text-sm">
                  {state.formData.endDate ? new Date(state.formData.endDate).toLocaleDateString() : 'Not set'}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-600">Amount</h4>
                <p className="text-sm">
                  {state.formData.amount ? `₹${state.formData.amount.toLocaleString()}` : 'Not set'}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-600">Status</h4>
                <p className="text-sm">{state.formData.status || 'ACTIVE'}</p>
              </div>
            </div>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Please review all details carefully. Once submitted, the AMC contract will be created
              and you will be redirected to the AMC management page.
            </AlertDescription>
          </Alert>

          <div className="flex justify-between">
            <button
              type="button"
              onClick={goToPreviousStep}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Previous
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={state.isSubmitting}
              className="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary/90 disabled:opacity-50"
            >
              {state.isSubmitting ? 'Creating Contract...' : 'Create AMC Contract'}
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Step component renderer
function AMCFormStepRenderer() {
  const { state } = useAMCForm();

  switch (state.currentStep) {
    case AMCFormStep.CUSTOMER_DETAILS:
      return <AMCFormStep1 />;
    case AMCFormStep.CONTRACT_DETAILS:
      return <AMCFormStep2 />;
    case AMCFormStep.MACHINE_MANAGEMENT:
      return <AMCFormStep3 />;
    case AMCFormStep.SERVICE_SCHEDULING:
      return <AMCFormStep4 />;
    case AMCFormStep.PAYMENT_DIVISION:
      return <AMCFormStep5 />;
    case AMCFormStep.REVIEW_SUBMIT:
      return <AMCFormStep6 />;
    default:
      return <AMCFormStep1 />;
  }
}

// Main form component content
function AMCFormContent() {
  return (
    <div className="space-y-8">
      <AMCFormStepper />
      <AMCFormStepRenderer />
    </div>
  );
}

// Main exported component with provider
export function AMCForm() {
  return (
    <AMCFormProvider>
      <AMCFormContent />
    </AMCFormProvider>
  );
}
