# KoolSoft Database Access Guidelines

This document outlines the guidelines for accessing the database in the KoolSoft modernization project. It provides specific instructions on using the Prisma ORM with the legacy database schema during the transition period.

## Overview

The KoolSoft modernization project involves migrating from a legacy Microsoft Access database to a modern PostgreSQL database. The legacy tables have already been imported into the PostgreSQL database, and we are now in the process of migrating data from these legacy tables to the modern schema tables within the same database.

### Direct Migration Approach

Unlike traditional ETL processes that extract data to intermediate files:

1. **All legacy tables are already in the PostgreSQL database** with their original names (e.g., "CUSTOMERS", "AMC_sum")
2. **Modern schema tables exist in the same database** with lowercase names (e.g., "customers", "amc_contracts")
3. **Migration scripts directly query legacy tables** and insert data into modern tables
4. **No JSON or CSV files are used** as intermediaries in the migration process

The Prisma schema has been configured to map to both the legacy tables (uppercase names like "CUSTOMERS") and the modern tables (lowercase names like "customers"). Now that the data migration is complete, **all new database operations should use the modern schema models**.

## Prisma Schema Configuration

The Prisma schema has been configured with:

1. **Legacy Models**: Map to uppercase table names in the database (e.g., "CUSTOMERS", "BRAND")
2. **Modern Models**: Map to lowercase table names (e.g., "customers", "brands")

Each model uses `@@map` annotations to specify the actual table name in the database, and `@map` annotations on fields to specify the actual column names.

### Example: Legacy Customer Model

```prisma
model LegacyCustomer {
  id              Int     @id @default(0) @map("ID")
  name            String? @map("Name") @db.VarChar(50)
  address         String? @map("CustAdd") @db.VarChar(255)
  // other fields...

  @@index([id], map: "CUSTOMERS_ID")
  @@index([segmentId], map: "CUSTOMERS_SegId")
  @@map("CUSTOMERS")
}
```

### Example: Modern Customer Model (Future Implementation)

```prisma
model Customer {
  id              String    @id @default(uuid())
  name            String
  address         String?
  // other fields...
  originalId      Int?      @map("original_id")

  @@index([name], map: "customers_name_idx")
  @@index([originalId], map: "customers_original_id_idx")
  @@map("customers")
}
```

## Database Access Guidelines

### 1. Use Modern Schema Models for All New Database Operations

Now that the data migration is complete, use the modern schema models for all new database operations:

- Use `prisma.customer` instead of `prisma.legacyCustomer` (maps to "customers" table)
- Use `prisma.brand` instead of legacy equivalent (maps to "brands" table)
- Use `prisma.product` instead of legacy equivalent (maps to "products" table)
- Use `prisma.model` instead of legacy equivalent (maps to "models" table)
- Use `prisma.amcContract` instead of legacy equivalent (maps to "amc_contracts" table)
- Use `prisma.warranty` instead of legacy equivalent (maps to "warranties" table)
- Use `prisma.serviceVisitType`, `prisma.complaintType`, etc. for all reference tables
- Use `prisma.historyRepair`, `prisma.historyMaintenance`, etc. for all history detail tables
- Use `prisma.visitCard` for visit cards
- Use `prisma.emailTemplate` and `prisma.emailLog` for email system

### 2. Legacy Models for Reference Only

The legacy models should only be used for reference or during the transition period:

- Use legacy models only when data is not yet available in the modern tables
- When using legacy models, clearly document the reason and plan for transition
- All new API routes should use modern schema models exclusively

### 3. Use Camel Case Property Names

When referencing fields, use the camelCase property names defined in the Prisma schema, not the database column names:

```typescript
// CORRECT: Use modern schema models with camelCase property names
const customer = await prisma.customer.findUnique({
  where: { id: customerId },
  select: {
    id: true,
    name: true,
    address: true,
    phone1: true,
    email: true
  }
});

// INCORRECT: Don't use legacy models for new operations
const customer = await prisma.legacyCustomer.findUnique({
  where: { id: customerId },
  select: {
    id: true,
    name: true,
    address: true,
    phone1: true,
    email: true
  }
});

// INCORRECT: Don't use database column names
const customer = await prisma.customer.findUnique({
  where: { ID: customerId },
  select: {
    ID: true,
    Name: true,
    CustAdd: true,
    CustPhone1: true,
    CustEmail: true
  }
});
```

### 4. Example: Querying Related Data

When querying related data, use the modern schema models and take advantage of Prisma's relation queries:

```typescript
// Get a customer and their AMC contracts
const customer = await prisma.customer.findUnique({
  where: { id: customerId },
  include: {
    amcContracts: true
  }
});

// Get AMC contract with related machines
const amcContract = await prisma.amcContract.findUnique({
  where: { id: amcContractId },
  include: {
    machines: true
  }
});

// Get history card with all related detail records
const historyCard = await prisma.history_cards.findUnique({
  where: { id: historyCardId },
  include: {
    repairs: true,
    maintenance: true,
    waterWashes: true,
    amcDetails: true,
    addonDetails: true,
    audits: true,
    complaints: true,
    componentReplacements: true
  }
});
```

### 5. Example: Creating New Records

When creating new records, use the modern schema models:

```typescript
// Create a new customer
const newCustomer = await prisma.customer.create({
  data: {
    name: "New Customer",
    address: "123 Main St",
    phone1: "555-1234",
    email: "<EMAIL>"
  }
});

// Create a new AMC contract with related machines
const newContract = await prisma.amcContract.create({
  data: {
    customerId: customerId,
    executiveId: executiveId,
    startDate: new Date(),
    endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    amount: 5000,
    numberOfServices: 4,
    machines: {
      create: [
        {
          modelId: modelId1,
          serialNumber: "SN12345",
          installationDate: new Date()
        },
        {
          modelId: modelId2,
          serialNumber: "SN67890",
          installationDate: new Date()
        }
      ]
    }
  }
});

// Create a new history repair record
const newRepair = await prisma.historyRepair.create({
  data: {
    historyCardId: historyCardId,
    repairDate: new Date(),
    description: "Compressor replacement",
    technicianId: technicianId,
    partsReplaced: "Compressor",
    cost: 5000
  }
});
```

### 6. Example: Updating Records

When updating records, use the modern schema models:

```typescript
// Update a customer
const updatedCustomer = await prisma.customer.update({
  where: { id: customerId },
  data: {
    name: "Updated Customer Name",
    phone1: "555-5678"
  }
});

// Update an AMC contract and related machines
const updatedContract = await prisma.amcContract.update({
  where: { id: amcContractId },
  data: {
    amount: 6000,
    numberOfServices: 6,
    machines: {
      update: {
        where: { id: machineId },
        data: {
          serialNumber: "SN-UPDATED"
        }
      }
    }
  }
});

// Update a history repair record
const updatedRepair = await prisma.historyRepair.update({
  where: { id: repairId },
  data: {
    description: "Updated repair description",
    cost: 6000
  }
});
```

## Migration Status

The migration from legacy tables to modern tables has been completed. All data has been successfully migrated to the modern schema.

### Completed Migration

The following modules have been migrated:
- Core data (customers, products, models, divisions)
- AMC contracts and related data
- In-warranty and out-of-warranty records
- History cards and sections
- History detail records (repairs, maintenance, water washes, etc.)
- Service reports and details
- Visit cards
- Reference tables (territories, segments, complaint types, spare_types, measurement_types, etc.)
- Email system tables

> **Note**: For a complete list of reference data tables and recent schema updates, see [Database Schema Updates](./database-schema-updates.md).

### Modern Schema Benefits

The modern schema provides several benefits:
1. **UUID Primary Keys**: More secure and scalable than sequential IDs
2. **Proper Relationships**: Defined in the Prisma schema for easier querying
3. **Consistent Naming**: All tables and columns follow a consistent naming convention
4. **Better Data Types**: Proper data types for all fields (dates, numbers, etc.)
5. **Indexes and Constraints**: Properly defined for better performance and data integrity

### Legacy Tables

The legacy tables remain in the database for reference purposes:
- They contain the original data in its original format
- They can be used for verification or troubleshooting
- They may be removed in a future update after a verification period

## Modern Repository Pattern

The application now uses a modern repository pattern for database access. This pattern provides a clean separation between the data access logic and the business logic.

### Repository Structure

- `BaseRepository`: Interface defining common CRUD operations
- `PrismaRepository`: Base implementation using Prisma
- Entity-specific repositories: Implement domain-specific operations

### Using Repositories

```typescript
import { getUserRepository, getCustomerRepository, getAMCContractRepository } from '@/lib/repositories';

// Get user repository
const userRepository = getUserRepository();

// Get customer repository
const customerRepository = getCustomerRepository();

// Get AMC contract repository
const amcContractRepository = getAMCContractRepository();
```

### Available Repositories

The following repositories are available:

- `UserRepository`: For user management
- `CustomerRepository`: For customer management
- `AMCContractRepository`: For AMC contract management
- `AMCMachineRepository`: For AMC machine management

### Repository Methods

Each repository provides standard CRUD operations:

- `findAll`: Get all records with optional pagination
- `findById`: Get a record by ID
- `findOne`: Get a record by a unique condition
- `findBy`: Get records by a filter condition
- `count`: Count records matching a filter condition
- `create`: Create a new record
- `createMany`: Create multiple records
- `update`: Update a record by ID
- `updateMany`: Update multiple records by a filter condition
- `delete`: Delete a record by ID
- `deleteMany`: Delete multiple records by a filter condition
- `transaction`: Execute a function within a transaction

Each repository also provides domain-specific methods. For example:

- `CustomerRepository.findByName`: Find customers by name
- `AMCContractRepository.findActive`: Find active AMC contracts
- `AMCContractRepository.findExpiring`: Find expiring AMC contracts

## API Implementation Guidelines

Now that the migration to the modern schema is complete, all new API routes should follow these guidelines:

1. **Use Modern Repository Pattern**: All API routes should use the modern repository pattern
2. **Use Modern Schema Models**: All API routes should use the modern schema models exclusively
3. **Use UUID Primary Keys**: All queries should use UUID primary keys instead of integer IDs
4. **Leverage Relationships**: Take advantage of Prisma's relationship queries
5. **Consistent Response Format**: All API responses should follow a consistent format
6. **Proper Error Handling**: Include proper error handling for all API routes
7. **Documentation**: Document all API routes with clear descriptions and examples
8. **Validation**: Use Zod schemas to validate input data

### Example API Route (App Router)

```typescript
// Example API route using modern repository pattern
import { NextRequest, NextResponse } from 'next/server';
import { getCustomerRepository } from '@/lib/repositories';
import { z } from 'zod';

// Validation schema
const createCustomerSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email().optional(),
  phone: z.string().optional()
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');

    const customerRepository = getCustomerRepository();
    const customers = await customerRepository.findAll(skip, take);
    const total = await customerRepository.count();

    return NextResponse.json({
      customers,
      meta: {
        total,
        skip,
        take,
      },
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    const validatedData = createCustomerSchema.parse(body);

    const customerRepository = getCustomerRepository();

    // Create customer
    const customer = await customerRepository.create(validatedData);

    return NextResponse.json(customer, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating customer:', error);
    return NextResponse.json(
      { error: 'Failed to create customer' },
      { status: 500 }
    );
  }
}
```

## Migration Scripts

The migration process was implemented through a series of Node.js scripts in the `scripts` directory. These scripts have already been run and the migration is complete, but they are documented here for reference.

### Core Migration Scripts

- **db-migrate.js**: Main migration script for core tables
- **create-reference-tables.js**: Script to create and populate reference tables
- **create-history-detail-tables.js**: Script to create and populate history detail tables
- **create-visit-cards-table.js**: Script to create and populate visit cards table
- **create-email-system-tables.js**: Script to create and set up email templates and logs tables
- **update-indexes.js**: Script to create performance indexes and update sequences
- **update-prisma-schema.js**: Script to update the Prisma schema with models for all new tables
- **test-prisma-models.js**: Script to test the Prisma client with the new models
- **complete-migration.js**: Master script to run all migration tasks
- **verify-migration.js**: Script to verify the status of all tables

### How Migration Scripts Worked

1. **Direct Database Queries**: Scripts used Prisma's `$queryRawUnsafe` to query legacy tables
   ```javascript
   const legacyHistoryCards = await prisma.$queryRawUnsafe(`SELECT * FROM "History_Sum"`);
   ```

2. **Data Transformation**: Scripts transformed data to match the modern schema
   ```javascript
   const historyCardData = {
     customerId: customerId,
     cardNo: legacyCard.Card_No,
     source: legacyCard.Source || null,
     toCardNo: legacyCard.ToCardNo || null,
     originalId: legacyCard.Card_No
   };
   ```

3. **Data Insertion**: Scripts used Prisma models to insert data into modern tables
   ```javascript
   const historyCard = await prisma.history_cards.create({ data: historyCardData });
   ```

4. **Relationship Preservation**: Scripts maintained relationships between tables
   ```javascript
   const sectionData = {
     historyCardId: historyCard.id,
     sectionCode: section.Section,
     content: content,
     originalCardNo: legacyCard.Card_No,
     originalSectionCode: section.Section
   };
   ```

### Verifying Migration

To verify the migration status:

```bash
npm run db:verify-migration
```

This script checks the status of all tables and reports on their record counts.

## Troubleshooting

### Common Issues

1. **"Column does not exist" errors**: Ensure you're using the correct field names (camelCase property names from the Prisma schema, not database column names)

2. **"Record not found" errors**: Verify that you're using the correct model and the correct ID field (UUID for modern schema)

3. **Relationship issues**: Make sure you're using the proper include syntax for relationships in the modern schema

4. **Legacy data references**: If you need to reference legacy data that wasn't migrated, you may need to use the legacy models, but document this clearly

5. **UUID format issues**: Ensure that UUIDs are properly formatted when used in queries

### Transitioning from Legacy to Modern Schema

When updating existing API routes to use the modern schema:

1. **Identify all legacy model references**: Search for all instances of legacy model usage
2. **Replace with modern repository pattern**: Update to use the modern repository pattern
3. **Replace with modern model equivalents**: Update to use the modern schema models
4. **Update ID references**: Change from numeric IDs to UUIDs
5. **Update relationship queries**: Use Prisma's include syntax for relationships
6. **Add validation**: Use Zod schemas to validate input data
7. **Test thoroughly**: Ensure all functionality works with the modern schema

#### Example: Transitioning from Legacy to Modern API Route

**Legacy API Route (Pages Router):**

```typescript
// Legacy API route using direct Prisma client
import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { id } = req.query;

    // Using legacy model directly
    const legacyCustomer = await prisma.legacyCustomer.findUnique({
      where: { id: parseInt(id as string) }
    });

    if (!legacyCustomer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    return res.status(200).json({ data: legacyCustomer });
  } catch (error) {
    console.error('Error fetching customer:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
```

**Modern API Route (App Router):**

```typescript
// Modern API route using repository pattern
import { NextRequest, NextResponse } from 'next/server';
import { getCustomerRepository } from '@/lib/repositories';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const customerRepository = getCustomerRepository();

    // Using modern repository pattern
    const customer = await customerRepository.findById(id);

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error fetching customer:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer' },
      { status: 500 }
    );
  }
}
```

### Getting Help

If you encounter issues with database access:

1. Review this guide and the Prisma schema
2. Check the Prisma documentation for general Prisma usage
3. Run the test-prisma-models script to verify model access
4. Consult with the development team for project-specific questions

## Conclusion

Following these guidelines will ensure consistent database access in the KoolSoft modernization project. Now that the data migration is complete, all new development should use the modern schema models exclusively to take advantage of the improved data structure and relationships.

The modern schema provides several advantages:
- UUID primary keys for better security and scalability
- Proper relationships defined in the Prisma schema
- Consistent naming conventions
- Better data types and validation
- Improved performance through proper indexing

The completed migration process has successfully:
- Migrated all data from legacy tables to modern tables
- Preserved all relationships between tables
- Created proper indexes for performance
- Updated the Prisma schema with models for all tables
- Provided a clear path for API development using modern models
