import { PrismaClient } from '@prisma/client';
import { prisma as globalPrisma } from '@/lib/prisma';

/**
 * Legacy Data Repository
 *
 * This repository provides methods to directly query legacy data tables
 * that may not be properly mapped in the Prisma schema.
 */
export class LegacyDataRepository {
  private prisma: PrismaClient;

  constructor(prismaClient?: PrismaClient) {
    this.prisma = prismaClient || globalPrisma;
  }

  /**
   * Get history complaints for a specific history card
   * @param historyCardId History card ID
   * @returns Promise resolving to an array of complaints
   */
  async getHistoryComplaints(historyCardId: string): Promise<any[]> {
    try {
      const complaints = await this.prisma.$queryRaw`
        SELECT
          id,
          history_card_id,
          complaint_date,
          description,
          complaint_type,
          resolution,
          resolution_date,
          original_card_no,
          original_complaint_id,
          created_at,
          updated_at
        FROM history_complaints
        WHERE history_card_id = ${historyCardId}
        ORDER BY complaint_date DESC
      `;
      return Array.isArray(complaints) ? complaints : [];
    } catch (error) {
      console.error('Error fetching history complaints:', error);
      return [];
    }
  }

  /**
   * Get history complaints for a customer
   * @param customerId Customer ID
   * @returns Promise resolving to an array of complaints with history card info
   */
  async getCustomerHistoryComplaints(customerId: string): Promise<any[]> {
    try {
      const complaints = await this.prisma.$queryRaw`
        SELECT
          hc.id as history_card_id,
          hc.card_no,
          hc.source,
          hc.customer_id,
          comp.id as complaint_id,
          comp.complaint_date,
          comp.description,
          comp.complaint_type,
          comp.resolution,
          comp.resolution_date,
          comp.original_card_no,
          comp.original_complaint_id,
          comp.created_at,
          comp.updated_at
        FROM history_cards hc
        JOIN history_complaints comp ON hc.id = comp.history_card_id
        WHERE hc.customer_id = ${customerId}
        ORDER BY comp.complaint_date DESC
      `;
      return Array.isArray(complaints) ? complaints : [];
    } catch (error) {
      console.error('Error fetching customer history complaints:', error);
      return [];
    }
  }

  /**
   * Get all history cards with their complaints for a customer
   * @param customerId Customer ID
   * @returns Promise resolving to an array of history cards with complaints
   */
  async getCustomerHistoryCardsWithComplaints(customerId: string): Promise<any[]> {
    try {
      // First get all history cards for the customer
      const historyCards = await this.prisma.history_cards.findMany({
        where: { customerId },
        orderBy: { createdAt: 'desc' },
        include: {
          customer: true,
          repairs: {
            orderBy: { repairDate: 'desc' }
          },
          maintenance: {
            orderBy: { maintenanceDate: 'desc' }
          },
          amcDetails: {
            orderBy: { startDate: 'desc' }
          },
          addonDetails: {
            orderBy: { addonDate: 'desc' }
          },
          waterWashes: {
            orderBy: { washDate: 'desc' }
          },
          componentReplacements: {
            orderBy: { replacementDate: 'desc' }
          },
        }
      });

      // For each history card, get complaints directly from the database
      const historyCardsWithComplaints = await Promise.all(
        historyCards.map(async (card) => {
          const complaints = await this.getHistoryComplaints(card.id);
          return {
            ...card,
            complaints
          };
        })
      );

      return historyCardsWithComplaints;
    } catch (error) {
      console.error('Error fetching customer history cards with complaints:', error);
      return [];
    }
  }
}

/**
 * Get a new instance of the LegacyDataRepository
 * @returns LegacyDataRepository instance
 */
export function getLegacyDataRepository(): LegacyDataRepository {
  return new LegacyDataRepository();
}
