-- Check the structure of history_cards table
SELECT column_name, data_type, character_maximum_length
FROM information_schema.columns
WHERE table_name = 'history_cards';

-- Check the structure of history_complaints table
SELECT column_name, data_type, character_maximum_length
FROM information_schema.columns
WHERE table_name = 'history_complaints';

-- Count records in history_cards
SELECT COUNT(*) FROM history_cards;

-- Count records in history_complaints
SELECT COUNT(*) FROM history_complaints;

-- Check for a specific customer's history cards
SELECT * FROM history_cards 
WHERE customer_id = 'c7b7933e-6fa3-45ac-aac8-8505e8488189' 
LIMIT 5;

-- Check for complaints related to the customer's history cards
SELECT hc.id as history_card_id, hc.customer_id, hc.card_no, 
       comp.id as complaint_id, comp.complaint_date, comp.description, comp.complaint_type, 
       comp.resolution, comp.resolution_date
FROM history_cards hc
LEFT JOIN history_complaints comp ON hc.id = comp.history_card_id
WHERE hc.customer_id = 'c7b7933e-6fa3-45ac-aac8-8505e8488189'
LIMIT 10;

-- Check for any history cards with complaints
SELECT hc.id as history_card_id, hc.customer_id, hc.card_no, 
       comp.id as complaint_id, comp.complaint_date, comp.description, comp.complaint_type, 
       comp.resolution, comp.resolution_date
FROM history_cards hc
JOIN history_complaints comp ON hc.id = comp.history_card_id
LIMIT 10;

-- Check the relationship between history_cards and history_complaints
SELECT 
    hc.id as history_card_id,
    hc.card_no,
    hc.customer_id,
    COUNT(comp.id) as complaint_count
FROM 
    history_cards hc
LEFT JOIN 
    history_complaints comp ON hc.id = comp.history_card_id
GROUP BY 
    hc.id, hc.card_no, hc.customer_id
HAVING 
    COUNT(comp.id) > 0
LIMIT 10;

-- Check for any legacy data in History_Complaint table if it exists
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'History_Complaint'
) AS table_exists;
