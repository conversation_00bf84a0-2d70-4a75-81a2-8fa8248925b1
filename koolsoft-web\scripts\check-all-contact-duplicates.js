const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAllContactDuplicates() {
  try {
    console.log('Checking for contact duplicates across all customers...\n');

    // Check for duplicate contacts within each customer
    const duplicateContacts = await prisma.$queryRaw`
      SELECT customer_id, name, phone, email, COUNT(*) as count,
             array_agg(id ORDER BY created_at ASC) as ids,
             array_agg(created_at ORDER BY created_at ASC) as created_dates
      FROM contacts 
      GROUP BY customer_id, name, phone, email
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;
    
    console.log('=== CONTACT DUPLICATES ANALYSIS ===');
    if (duplicateContacts.length > 0) {
      console.log(`Found ${duplicateContacts.length} duplicate contact groups:`);
      
      let totalDuplicates = 0;
      duplicateContacts.forEach((dup, index) => {
        if (index < 10) { // Show first 10
          console.log(`\n${index + 1}. Customer: ${dup.customer_id}`);
          console.log(`   Name: "${dup.name}"`);
          console.log(`   Phone: "${dup.phone || 'N/A'}"`);
          console.log(`   Email: "${dup.email || 'N/A'}"`);
          console.log(`   Count: ${dup.count}`);
          console.log(`   IDs: ${dup.ids.join(', ')}`);
        }
        totalDuplicates += (dup.count - 1); // Count extras (keep one)
      });
      
      if (duplicateContacts.length > 10) {
        console.log(`\n... and ${duplicateContacts.length - 10} more duplicate groups`);
      }
      
      console.log(`\nTotal duplicate records to be removed: ${totalDuplicates}`);
    } else {
      console.log('✅ No duplicate contacts found');
    }

    // Get total counts
    console.log('\n=== SUMMARY ===');
    const totalContacts = await prisma.contact.count();
    const totalCustomers = await prisma.customer.count();
    
    console.log(`Total contacts: ${totalContacts}`);
    console.log(`Total customers: ${totalCustomers}`);
    console.log(`Duplicate groups: ${duplicateContacts.length}`);

    // Check specific customer again
    console.log('\n=== SPECIFIC CUSTOMER (82c3c4ac-3f0c-4aee-b0bb-a5330ba490e9) ===');
    const specificCustomerDuplicates = duplicateContacts.filter(
      dup => dup.customer_id === '82c3c4ac-3f0c-4aee-b0bb-a5330ba490e9'
    );
    
    if (specificCustomerDuplicates.length > 0) {
      console.log('Duplicate contacts for this customer:');
      specificCustomerDuplicates.forEach(dup => {
        console.log(`- Name: "${dup.name}", Phone: "${dup.phone}", Count: ${dup.count}`);
        console.log(`  IDs to keep: ${dup.ids[0]} (oldest)`);
        console.log(`  IDs to delete: ${dup.ids.slice(1).join(', ')}`);
      });
    } else {
      console.log('No duplicates found for this specific customer');
    }

  } catch (error) {
    console.error('Error checking contact duplicates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAllContactDuplicates();
