'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  AlertCircle,
  Calendar,
  Wrench
} from 'lucide-react';
import { format } from 'date-fns';
import { useComponents, Component, ComponentQueryParams } from '@/lib/hooks/useComponents';
import { 
  getWarrantyStatusLabel, 
  getWarrantyStatusColor,
  ComponentType,
  getComponentTypeLabel 
} from '@/lib/validations/component.schema';

interface ComponentListProps {
  machineId?: string;
  onAddComponent?: () => void;
  onEditComponent?: (component: Component) => void;
  onDeleteComponent?: (component: Component) => void;
  showMachineInfo?: boolean;
}

export function ComponentList({ 
  machineId, 
  onAddComponent, 
  onEditComponent, 
  onDeleteComponent,
  showMachineInfo = false 
}: ComponentListProps) {
  const [components, setComponents] = useState<Component[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [warrantyFilter, setWarrantyFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalComponents, setTotalComponents] = useState(0);

  const { fetchComponents, fetchMachineComponents, deleteComponent } = useComponents();

  const pageSize = 10;

  const loadComponents = async () => {
    setLoading(true);
    try {
      const params: ComponentQueryParams = {
        skip: (currentPage - 1) * pageSize,
        take: pageSize,
        orderBy: 'createdAt',
        orderDirection: 'desc',
      };

      if (searchQuery) {
        params.query = searchQuery;
      }

      if (warrantyFilter !== 'all') {
        params.warrantyStatus = warrantyFilter as any;
      }

      if (machineId) {
        params.machineId = machineId;
      }

      const result = machineId 
        ? await fetchMachineComponents(machineId, params)
        : await fetchComponents(params);

      setComponents(result.components);
      setTotalComponents(result.meta.total);
      setTotalPages(Math.ceil(result.meta.total / pageSize));
    } catch (error) {
      console.error('Error loading components:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadComponents();
  }, [currentPage, searchQuery, warrantyFilter, machineId]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  };

  const handleWarrantyFilterChange = (filter: string) => {
    setWarrantyFilter(filter);
    setCurrentPage(1);
  };

  const handleDelete = async (component: Component) => {
    if (window.confirm('Are you sure you want to delete this component?')) {
      try {
        await deleteComponent(component.id);
        loadComponents(); // Reload the list
      } catch (error) {
        console.error('Error deleting component:', error);
      }
    }
  };

  const getWarrantyBadgeVariant = (warrantyDate?: Date) => {
    const status = getWarrantyStatusLabel(warrantyDate);
    switch (status) {
      case 'Active': return 'secondary';
      case 'Expiring Soon': return 'default';
      case 'Expired': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
        <div>
          <CardTitle className="text-white">
            {machineId ? 'Machine Components' : 'Components'}
          </CardTitle>
          <p className="text-gray-100 text-sm">
            {totalComponents} component{totalComponents !== 1 ? 's' : ''} found
          </p>
        </div>
        {onAddComponent && (
          <Button variant="secondary" onClick={onAddComponent}>
            <Plus className="h-4 w-4 mr-2" />
            Add Component
          </Button>
        )}
      </CardHeader>
      <CardContent className="pt-6">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Label htmlFor="search" className="text-black">Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="search"
                placeholder="Search by serial number, section..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="w-full sm:w-48">
            <Label htmlFor="warrantyFilter" className="text-black">Warranty Status</Label>
            <Select value={warrantyFilter} onValueChange={handleWarrantyFilterChange}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by warranty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Components</SelectItem>
                <SelectItem value="ACTIVE">Active Warranty</SelectItem>
                <SelectItem value="EXPIRING_SOON">Expiring Soon</SelectItem>
                <SelectItem value="EXPIRED">Expired Warranty</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Components Table */}
        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
            ))}
          </div>
        ) : components.length === 0 ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-black">
              No components found. {onAddComponent && 'Click "Add Component" to create the first one.'}
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-black">Component</TableHead>
                  {showMachineInfo && <TableHead className="text-black">Machine</TableHead>}
                  <TableHead className="text-black">Serial Number</TableHead>
                  <TableHead className="text-black">Warranty</TableHead>
                  <TableHead className="text-black">Section</TableHead>
                  <TableHead className="text-black">Created</TableHead>
                  <TableHead className="text-black w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {components.map((component) => (
                  <TableRow key={component.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium text-black">
                          Component #{component.componentNo || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-600">
                          ID: {component.id.slice(0, 8)}...
                        </div>
                      </div>
                    </TableCell>
                    {showMachineInfo && (
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium text-black">
                            {component.machine?.serialNumber || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-600">
                            {component.machine?.location || 'No location'}
                          </div>
                        </div>
                      </TableCell>
                    )}
                    <TableCell>
                      <div className="font-medium text-black">
                        {component.serialNumber || 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <Badge variant={getWarrantyBadgeVariant(component.warrantyDate)}>
                          {getWarrantyStatusLabel(component.warrantyDate)}
                        </Badge>
                        {component.warrantyDate && (
                          <div className="text-xs text-gray-600">
                            {format(component.warrantyDate, 'MMM dd, yyyy')}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-black">
                        {component.section || 'N/A'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-black">
                        {format(component.createdAt, 'MMM dd, yyyy')}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {onEditComponent && (
                            <DropdownMenuItem onClick={() => onEditComponent(component)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                          )}
                          {onDeleteComponent && (
                            <DropdownMenuItem 
                              onClick={() => handleDelete(component)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalComponents)} of {totalComponents} components
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-black">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
