import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * AMC Component Repository
 *
 * This repository handles database operations for the AMC Component entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class AMCComponentRepository extends PrismaRepository<
  Prisma.amc_componentsGetPayload<{}>,
  string,
  Prisma.amc_componentsCreateInput,
  Prisma.amc_componentsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('amc_components');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find components by machine ID
   */
  async findByMachineId(
    machineId: string,
    options?: {
      skip?: number;
      take?: number;
      orderBy?: Prisma.amc_componentsOrderByWithRelationInput;
    }
  ): Promise<Prisma.amc_componentsGetPayload<{}>[]> {
    try {
      const { skip = 0, take = 50, orderBy = { createdAt: 'desc' } } = options || {};

      if (!this.model) {
        throw new Error('Model is not available');
      }

      return await this.model.findMany({
        where: { machineId },
        skip,
        take,
        orderBy,
      });
    } catch (error) {
      console.error('AMCComponentRepository.findByMachineId error:', error);
      throw error;
    }
  }

  /**
   * Find component by serial number
   */
  async findBySerialNumber(serialNumber: string): Promise<Prisma.amc_componentsGetPayload<{}> | null> {
    try {
      if (!this.model) {
        throw new Error('Model is not available');
      }

      return await this.model.findFirst({
        where: { serialNumber },
        include: {
          machine: {
            include: {
              amcContract: true,
              product: true,
              model: true,
              brand: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('AMCComponentRepository.findBySerialNumber error:', error);
      throw error;
    }
  }

  /**
   * Find components by component number
   */
  async findByComponentNumber(
    componentNo: number,
    options?: {
      skip?: number;
      take?: number;
      orderBy?: Prisma.amc_componentsOrderByWithRelationInput;
    }
  ): Promise<Prisma.amc_componentsGetPayload<{}>[]> {
    try {
      const { skip = 0, take = 50, orderBy = { createdAt: 'desc' } } = options || {};

      if (!this.model) {
        throw new Error('Model is not available');
      }

      return await this.model.findMany({
        where: { componentNo },
        skip,
        take,
        orderBy,
        include: {
          machine: {
            include: {
              amcContract: true,
              product: true,
              model: true,
              brand: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('AMCComponentRepository.findByComponentNumber error:', error);
      throw error;
    }
  }

  /**
   * Find components with warranty expiring soon
   */
  async findExpiringWarranties(
    daysAhead: number = 30,
    options?: {
      skip?: number;
      take?: number;
      orderBy?: Prisma.amc_componentsOrderByWithRelationInput;
    }
  ): Promise<Prisma.amc_componentsGetPayload<{}>[]> {
    try {
      const { skip = 0, take = 50, orderBy = { warrantyDate: 'asc' } } = options || {};

      if (!this.model) {
        throw new Error('Model is not available');
      }

      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + daysAhead);

      return await this.model.findMany({
        where: {
          warrantyDate: {
            gte: new Date(),
            lte: futureDate,
          },
        },
        skip,
        take,
        orderBy,
        include: {
          machine: {
            include: {
              amcContract: true,
              product: true,
              model: true,
              brand: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('AMCComponentRepository.findExpiringWarranties error:', error);
      throw error;
    }
  }

  /**
   * Find components by warranty status
   */
  async findByWarrantyStatus(
    status: 'ACTIVE' | 'EXPIRED' | 'EXPIRING_SOON',
    daysAhead: number = 30,
    options?: {
      skip?: number;
      take?: number;
      orderBy?: Prisma.amc_componentsOrderByWithRelationInput;
    }
  ): Promise<Prisma.amc_componentsGetPayload<{}>[]> {
    try {
      const { skip = 0, take = 50, orderBy = { warrantyDate: 'asc' } } = options || {};

      if (!this.model) {
        throw new Error('Model is not available');
      }

      const now = new Date();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + daysAhead);

      let whereClause: Prisma.amc_componentsWhereInput = {};

      switch (status) {
        case 'ACTIVE':
          whereClause = {
            warrantyDate: {
              gt: futureDate,
            },
          };
          break;
        case 'EXPIRED':
          whereClause = {
            warrantyDate: {
              lt: now,
            },
          };
          break;
        case 'EXPIRING_SOON':
          whereClause = {
            warrantyDate: {
              gte: now,
              lte: futureDate,
            },
          };
          break;
      }

      return await this.model.findMany({
        where: whereClause,
        skip,
        take,
        orderBy,
        include: {
          machine: {
            include: {
              amcContract: true,
              product: true,
              model: true,
              brand: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('AMCComponentRepository.findByWarrantyStatus error:', error);
      throw error;
    }
  }

  /**
   * Get component statistics for a machine
   */
  async getStatistics(machineId: string): Promise<{
    total: number;
    activeWarranty: number;
    expiredWarranty: number;
    expiringSoon: number;
  }> {
    try {
      if (!this.model) {
        throw new Error('Model is not available');
      }

      const now = new Date();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30); // 30 days ahead

      const [total, activeWarranty, expiredWarranty, expiringSoon] = await Promise.all([
        this.model.count({ where: { machineId } }),
        this.model.count({
          where: {
            machineId,
            warrantyDate: { gt: futureDate },
          },
        }),
        this.model.count({
          where: {
            machineId,
            warrantyDate: { lt: now },
          },
        }),
        this.model.count({
          where: {
            machineId,
            warrantyDate: {
              gte: now,
              lte: futureDate,
            },
          },
        }),
      ]);

      return {
        total,
        activeWarranty,
        expiredWarranty,
        expiringSoon,
      };
    } catch (error) {
      console.error('AMCComponentRepository.getStatistics error:', error);
      throw error;
    }
  }

  /**
   * Delete components by machine ID
   */
  async deleteByMachineId(machineId: string): Promise<number> {
    try {
      if (!this.model) {
        throw new Error('Model is not available');
      }

      const result = await this.model.deleteMany({
        where: { machineId },
      });

      return result.count;
    } catch (error) {
      console.error('AMCComponentRepository.deleteByMachineId error:', error);
      throw error;
    }
  }

  /**
   * Find component with all related data
   */
  async findWithRelations(id: string): Promise<any | null> {
    try {
      if (!this.model) {
        throw new Error('Model is not available');
      }

      return await this.model.findUnique({
        where: { id },
        include: {
          machine: {
            include: {
              amcContract: {
                include: {
                  customer: true,
                  executive: true,
                },
              },
              product: true,
              model: true,
              brand: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('AMCComponentRepository.findWithRelations error:', error);
      throw error;
    }
  }

  /**
   * Search components with filters
   */
  async search(filters: {
    query?: string;
    machineId?: string;
    componentNo?: number;
    warrantyStatus?: 'ACTIVE' | 'EXPIRED' | 'EXPIRING_SOON';
    skip?: number;
    take?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
  }): Promise<{
    components: Prisma.amc_componentsGetPayload<{}>[];
    total: number;
  }> {
    try {
      const {
        query,
        machineId,
        componentNo,
        warrantyStatus,
        skip = 0,
        take = 50,
        orderBy = 'createdAt',
        orderDirection = 'desc',
      } = filters;

      if (!this.model) {
        throw new Error('Model is not available');
      }

      const whereClause: Prisma.amc_componentsWhereInput = {};

      // Text search
      if (query) {
        whereClause.OR = [
          { serialNumber: { contains: query, mode: 'insensitive' } },
          { section: { contains: query, mode: 'insensitive' } },
        ];
      }

      // Machine filter
      if (machineId) {
        whereClause.machineId = machineId;
      }

      // Component number filter
      if (componentNo) {
        whereClause.componentNo = componentNo;
      }

      // Warranty status filter
      if (warrantyStatus) {
        const now = new Date();
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 30);

        switch (warrantyStatus) {
          case 'ACTIVE':
            whereClause.warrantyDate = { gt: futureDate };
            break;
          case 'EXPIRED':
            whereClause.warrantyDate = { lt: now };
            break;
          case 'EXPIRING_SOON':
            whereClause.warrantyDate = { gte: now, lte: futureDate };
            break;
        }
      }

      const [components, total] = await Promise.all([
        this.model.findMany({
          where: whereClause,
          skip,
          take,
          orderBy: { [orderBy]: orderDirection },
          include: {
            machine: {
              include: {
                amcContract: true,
                product: true,
                model: true,
                brand: true,
              },
            },
          },
        }),
        this.model.count({ where: whereClause }),
      ]);

      return { components, total };
    } catch (error) {
      console.error('AMCComponentRepository.search error:', error);
      throw error;
    }
  }
}
