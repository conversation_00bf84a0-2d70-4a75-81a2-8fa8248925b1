import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCPaymentRepository } from '@/lib/repositories';
import { createPaymentSchema, paymentFilterSchema } from '@/lib/validations/amc-contract.schema';
import { z } from 'zod';

/**
 * GET /api/amc/payments
 * Fetch payments with filtering and pagination
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filters = {
        amcContractId: searchParams.get('amcContractId') || undefined,
        paymentMode: searchParams.get('paymentMode') || undefined,
        dateFrom: searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined,
        dateTo: searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined,
        amountMin: searchParams.get('amountMin') ? parseFloat(searchParams.get('amountMin')!) : undefined,
        amountMax: searchParams.get('amountMax') ? parseFloat(searchParams.get('amountMax')!) : undefined,
        skip: parseInt(searchParams.get('skip') || '0'),
        take: parseInt(searchParams.get('take') || '10'),
        orderBy: searchParams.get('orderBy') || 'paymentDate',
        orderDirection: (searchParams.get('orderDirection') as 'asc' | 'desc') || 'desc',
      };

      // Validate filters
      const validatedFilters = paymentFilterSchema.parse(filters);

      const amcPaymentRepository = getAMCPaymentRepository();

      // Build order by clause
      const orderBy = {
        [validatedFilters.orderBy]: validatedFilters.orderDirection,
      };

      // Fetch payments with filters
      const result = await amcPaymentRepository.findWithFilters(
        {
          amcContractId: validatedFilters.amcContractId,
          paymentMode: validatedFilters.paymentMode,
          dateFrom: validatedFilters.dateFrom,
          dateTo: validatedFilters.dateTo,
          amountMin: validatedFilters.amountMin,
          amountMax: validatedFilters.amountMax,
        },
        {
          skip: validatedFilters.skip,
          take: validatedFilters.take,
          orderBy,
        }
      );

      return NextResponse.json({
        payments: result.payments,
        total: result.total,
        meta: {
          skip: validatedFilters.skip,
          take: validatedFilters.take,
          orderBy: validatedFilters.orderBy,
          orderDirection: validatedFilters.orderDirection,
        },
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.errors },
          { status: 400 }
        );
      }

      console.error('Error fetching payments:', error);
      return NextResponse.json(
        { error: 'Failed to fetch payments' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/amc/payments
 * Create a new payment
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createPaymentSchema.parse(body);

      const amcPaymentRepository = getAMCPaymentRepository();

      // Check if receipt number is unique (if provided)
      if (validatedData.receiptNo) {
        const isUnique = await amcPaymentRepository.isReceiptNumberUnique(validatedData.receiptNo);
        if (!isUnique) {
          return NextResponse.json(
            { error: 'Receipt number already exists' },
            { status: 400 }
          );
        }
      }

      // Create payment with automatic receipt number generation if not provided
      const payment = await amcPaymentRepository.createWithReceiptNumber({
        amcContractId: validatedData.amcContractId,
        paymentDate: validatedData.paymentDate,
        amount: validatedData.amount,
        paymentMode: validatedData.paymentMode,
        receiptNo: validatedData.receiptNo,
        particulars: validatedData.particulars,
      });

      // Update contract paid amount
      try {
        await amcPaymentRepository.updateContractPaidAmount(validatedData.amcContractId);
      } catch (updateError) {
        console.warn('Failed to update contract paid amount:', updateError);
        // Continue anyway as the payment was created successfully
      }

      return NextResponse.json(payment, { status: 201 });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: error.errors },
          { status: 400 }
        );
      }

      console.error('Error creating payment:', error);
      
      // Handle specific error types
      if (error instanceof Error) {
        if (error.message.includes('Foreign key constraint')) {
          return NextResponse.json(
            { error: 'Invalid AMC contract ID' },
            { status: 400 }
          );
        }
        
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to create payment' },
        { status: 500 }
      );
    }
  }
);
