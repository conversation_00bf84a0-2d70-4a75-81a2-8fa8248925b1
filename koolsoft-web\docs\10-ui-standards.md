# KoolSoft UI Standards

This document outlines the standardized UI components and patterns used throughout the KoolSoft web application to ensure consistency and maintainability.

## Table of Contents

1. [Enhanced Navigation System](#enhanced-navigation-system)
2. [Layout Components](#layout-components)
3. [Color Scheme](#color-scheme)
4. [Typography](#typography)
5. [UI Components](#ui-components)
6. [Responsive Design](#responsive-design)
7. [Toast Notifications](#toast-notifications)
8. [Best Practices](#best-practices)

## Enhanced Navigation System

The KoolSoft web application features a comprehensive navigation system with expandable menus and consistent header styling across all modules.

### Sidebar Navigation Structure

The left sidebar navigation includes the following main sections with expandable sub-menus:

#### Main Navigation Items

1. **Dashboard** (`/dashboard`)
   - Icon: LayoutDashboard
   - Direct link to main dashboard

2. **Customers** (`/customers`)
   - Icon: Users
   - Direct link to customer management

3. **AMC Management** (`/amc`)
   - Icon: Calendar
   - Expandable menu with sub-items:
     - All Contracts (`/amc`)
     - Payments (`/amc/payments`)
     - Service Dates (`/amc/service-dates`)
     - New Contract (`/amc/new`) - Admin/Manager/Executive only

4. **Warranty Management** (`/warranties`)
   - Icon: Shield
   - Expandable menu with sub-items:
     - Overview (`/warranties`)
     - In-Warranty (`/warranties/in-warranty`)
     - Out-of-Warranty (`/warranties/out-warranty`)
     - Components (`/warranties/components`)
     - Status Dashboard (`/warranties/status`)
     - Alerts (`/warranties/alerts`)
     - BLUESTAR (`/warranties/bluestar`)

5. **Reference Data** (`/reference-data`)
   - Icon: Database
   - Admin/Manager only
   - Direct link to reference data management

6. **Service** (`/service`)
   - Icon: Wrench
   - Direct link to service management

7. **Sales** (`/sales`)
   - Icon: BarChart4
   - Direct link to sales management

8. **Reports** (`/reports`)
   - Icon: FileText
   - Admin/Manager/Executive only
   - Direct link to reports

9. **Admin** (`/admin`)
   - Icon: Settings
   - Admin only
   - Direct link to admin dashboard

### Navigation Features

#### Expandable Menus
- Menu items with sub-items display a chevron icon that rotates when expanded
- Clicking on a parent menu item toggles the expansion state
- Sub-menus automatically expand when navigating to a child page
- Collapsed sidebar hides sub-menus for space efficiency

#### Active State Highlighting
- Current page is highlighted with primary blue background
- Parent menu items are highlighted when any child page is active
- Sub-menu items use a subtle highlight with left border for active state

#### Role-Based Visibility
- Menu items are automatically hidden based on user roles
- Admin-only sections (Reference Data, Admin) are hidden for non-admin users
- Role-specific items within sub-menus are also properly filtered

#### Responsive Behavior
- Sidebar collapses on mobile devices
- Touch-friendly navigation for mobile users
- Proper spacing and sizing for different screen sizes

### Decentralized Header Standards

Each page implements its own header for maximum control and flexibility:

#### Standard Page Header Structure
```tsx
<Card>
  <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
    <div>
      <CardTitle className="flex items-center space-x-2">
        <ModuleIcon className="h-5 w-5" />
        <span>Page Title</span>
      </CardTitle>
      <CardDescription className="text-gray-100">
        Page description
      </CardDescription>
    </div>
    <div className="flex space-x-2">
      <Button variant="secondary">Action 1</Button>
      <Button variant="secondary">Action 2</Button>
    </div>
  </CardHeader>
  <CardContent className="pt-6">
    {/* Page content */}
  </CardContent>
</Card>
```

#### Header Requirements
1. **Background Color**: Always use `bg-primary` (#0F52BA) with white text
2. **Layout**: Use flex layout with `justify-between` for content and actions
3. **Icon**: Include relevant page icon in the title
4. **Description**: Use `text-gray-100` for description text
5. **Actions**: Position action buttons inside the header for each page
6. **Button Styling**: Use `variant="secondary"` for header action buttons
7. **Page Control**: Each page manages its own header and actions independently

### Implementation Guidelines

#### DashboardLayout Integration
The enhanced navigation is built into the `DashboardLayout` component and automatically handles:
- Menu expansion state management
- Active page highlighting
- Role-based menu filtering
- Responsive behavior
- Breadcrumb integration

#### Usage Example
```tsx
<DashboardLayout
  title="Page Title"
  breadcrumbs={breadcrumbs}
  requireAuth={true}
  allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
>
  {/* Page content with its own header */}
</DashboardLayout>
```

The navigation system automatically handles all menu states and highlighting based on the current route. Pages manage their own headers and actions independently.

## Layout Components

The application uses a set of standardized layout components to ensure consistency across all pages:

### PageHeader

The `PageHeader` component provides a consistent header for all pages, including breadcrumb navigation, page title, and action buttons.

```tsx
import { PageHeader } from '@/components/layout';

<PageHeader
  title="Page Title"
  breadcrumbs={[
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Current Page', current: true }
  ]}
  actions={<Button>Action</Button>}
  showDashboardLink={true}
  showAdminLink={false}
/>
```

### PageLayout

The `PageLayout` component is used for most content pages, providing a standard layout with header and main content area.

```tsx
import { PageLayout } from '@/components/layout';

<PageLayout
  title="Page Title"
  breadcrumbs={breadcrumbs}
  actions={actions}
>
  {/* Page content */}
</PageLayout>
```

### DashboardLayout

The `DashboardLayout` component is used for all non-admin pages, providing a layout with a collapsible sidebar navigation. This should be the standard layout for all regular user-facing pages.

```tsx
import { DashboardLayout } from '@/components/layout';
import { BreadcrumbItemType } from '@/components/layout/page-header';
import Link from 'next/link';

// Define breadcrumbs
const breadcrumbs: BreadcrumbItemType[] = [
  { label: 'Dashboard', href: '/dashboard' },
  { label: 'Current Page', current: true }
];

// Define profile actions (optional)
const profileActions = (
  <div className="flex items-center space-x-2">
    <Link href="/profile" className="text-sm font-medium text-gray-700 hover:text-primary">
      My Profile
    </Link>
    <Link href="/api/auth/signout" className="text-sm font-medium text-destructive hover:text-destructive/80">
      Sign Out
    </Link>
  </div>
);

<DashboardLayout
  title="Page Title"
  breadcrumbs={breadcrumbs}
  actions={profileActions} // Optional - default actions will be used if not provided
  requireAuth={true}
  allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
  showAdminLink={false}
>
  {/* Page content */}
</DashboardLayout>
```

### Layout Nesting Rules

**CRITICAL**: To prevent duplicate UI elements (headers, sidebars), follow these strict rules:

#### 1. Single Layout Principle
- Only ONE layout component should render navigation elements per page
- Never nest DashboardLayout components
- Use Next.js layout.tsx files in directories to provide layout for all child pages

#### 2. Directory Layout Pattern
```typescript
// ✅ CORRECT: app/amc/layout.tsx - Provides DashboardLayout for all AMC pages
export default function AMCLayout({ children }) {
  return (
    <DashboardLayout title="AMC Management" ...>
      {children}
    </DashboardLayout>
  );
}

// ✅ CORRECT: app/amc/components/page.tsx - Returns content only
export default function ComponentsPage() {
  return (
    <div className="space-y-6">
      <ComponentList />
      {/* Other content */}
    </div>
  );
}
```

#### 3. What NOT to Do
```typescript
// ❌ WRONG: app/amc/components/page.tsx - Creates duplicate layout
export default function ComponentsPage() {
  return (
    <DashboardLayout title="Components" ...> {/* DUPLICATE! */}
      <ComponentList />
    </DashboardLayout>
  );
}
```

#### 4. Page Component Rules
- Page components should include their own CardHeader with action buttons for maximum control
- Each page manages its own specific actions and header content
- Use DashboardLayout for navigation and breadcrumbs only
- Focus on page-specific content and functionality with embedded headers
- Follow consistent header styling patterns across all pages

#### 5. Decentralized Header Management
Each page manages its own header and action buttons for maximum flexibility:
- **Page Responsibility**: Each page includes its own CardHeader with title, description, and action buttons
- **Consistent Styling**: All page headers use `bg-primary text-white` for consistent appearance
- **Action Control**: Pages have full control over their specific action buttons and behavior
- **Layout Simplicity**: DashboardLayout handles only navigation, breadcrumbs, and page structure

#### DashboardLayout Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| title | string | Yes | The title of the page |
| breadcrumbs | BreadcrumbItemType[] | No | Array of breadcrumb items |
| requireAuth | boolean | No | Whether authentication is required (default: true) |
| allowedRoles | string[] | No | Array of roles allowed to access the page |
| showAdminLink | boolean | No | Whether to show the admin link (default: false) |

**Note**: The `actions` prop has been removed. Pages now manage their own action buttons within their individual headers.

### AdminLayout

The `AdminLayout` component is used exclusively for admin pages, providing a layout with a collapsible sidebar specifically designed for administrative functions.

```tsx
import { AdminLayout } from '@/components/layout';

<AdminLayout
  title="Admin Dashboard"
  actions={actions}
>
  {/* Admin content */}
</AdminLayout>
```

#### AdminLayout Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| title | string | Yes | The title of the admin page |
| actions | ReactNode | No | Action buttons to display in the header |

## Color Scheme

The application uses a consistent color scheme defined in `tailwind.config.js` and `globals.css`:

| Color Name | Hex Code | Usage |
|------------|----------|-------|
| Primary | #0F52BA | Primary buttons, links, active states |
| Secondary | #f3f4f6 | Secondary buttons, backgrounds |
| Destructive | #ef4444 | Error states, delete buttons |
| Background | #f8f9fa | Page background |
| Foreground | #000000 | Text color |

Always use Tailwind theme variables instead of hardcoded color values:

```tsx
// Good
<div className="bg-primary text-primary-foreground">
  Primary Button
</div>

// Bad
<div className="bg-[#0F52BA] text-white">
  Primary Button
</div>
```

## Typography

The application uses the Geist Sans and Geist Mono fonts:

```tsx
// Font sizes
text-xs    // Extra small text (12px)
text-sm    // Small text (14px)
text-base  // Base text (16px)
text-lg    // Large text (18px)
text-xl    // Extra large text (20px)
text-2xl   // 2X large text (24px)
text-3xl   // 3X large text (30px)

// Font weights
font-normal  // Normal weight
font-medium  // Medium weight
font-semibold // Semi-bold weight
font-bold    // Bold weight
```

## UI Components

### Buttons

Use the `Button` component for all buttons:

```tsx
import { Button } from '@/components/ui/button';

<Button variant="default">Primary Button</Button>
<Button variant="secondary">Secondary Button</Button>
<Button variant="destructive">Destructive Button</Button>
<Button variant="outline">Outline Button</Button>
<Button variant="ghost">Ghost Button</Button>
<Button variant="link">Link Button</Button>
```

For links that look like buttons, use the `ButtonLink` component:

```tsx
import { ButtonLink } from '@/components/ui/button';

<ButtonLink href="/dashboard" variant="default">
  Dashboard
</ButtonLink>
```

### Form Elements

Use the form components for all form elements:

```tsx
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';

<div className="space-y-4">
  <div className="space-y-2">
    <Label htmlFor="name">Name</Label>
    <Input id="name" placeholder="Enter your name" />
  </div>

  <div className="space-y-2">
    <Label htmlFor="role">Role</Label>
    <Select>
      <SelectTrigger id="role">
        <SelectValue placeholder="Select a role" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="admin">Admin</SelectItem>
        <SelectItem value="user">User</SelectItem>
      </SelectContent>
    </Select>
  </div>
</div>
```

## Responsive Design

The application uses Tailwind's responsive utilities to ensure proper display on all devices:

```tsx
// Responsive layout
<div className="flex flex-col md:flex-row">
  {/* Content */}
</div>

// Responsive spacing
<div className="p-4 md:p-6 lg:p-8">
  {/* Content */}
</div>

// Responsive typography
<h1 className="text-2xl md:text-3xl lg:text-4xl">
  Heading
</h1>
```

## Toast Notifications

Use the toast utilities for displaying notifications:

```tsx
import { showSuccessToast, showErrorToast, showWarningToast, showInfoToast } from '@/lib/toast';

// Success toast
showSuccessToast('Operation successful', 'The item was created successfully');

// Error toast
showErrorToast('Operation failed', 'An error occurred while creating the item');

// Warning toast
showWarningToast('Warning', 'This action cannot be undone');

// Info toast
showInfoToast('Information', 'The process is running in the background');
```

## Page Header Standards

All page headers should follow these standards:

1. **Card Headers**: All card headers should use the primary blue background color (#0F52BA) with white text.
2. **Header Layout**: Card headers should use a flex layout with items centered and space between content and actions.
3. **Header Content**: Headers should include a title and description.
4. **Action Buttons**: Action buttons should be positioned within the card header, not floating separately.

### Breadcrumb Navigation

Breadcrumb navigation should follow these standards:

1. **Structure**: Use the Breadcrumb components from the UI library.
2. **Home Link**: Always include a Home link as the first item.
3. **Current Page**: The current page should be displayed as a plain text span, not a link.
4. **Parent Pages**: Include links to all parent pages in the hierarchy.
5. **Avoid Duplication**: The page title should not be displayed separately if it's the same as the last breadcrumb item.
6. **Consistent Hierarchy**: Maintain a consistent hierarchy across all pages (Dashboard > Section > Subsection).

Example:
```tsx
<Breadcrumb>
  <BreadcrumbList>
    <BreadcrumbItem>
      <BreadcrumbLink href="/">
        <Home className="h-4 w-4 mr-2" />
        Home
      </BreadcrumbLink>
    </BreadcrumbItem>
    <BreadcrumbSeparator />
    <BreadcrumbItem>
      <BreadcrumbLink href="/parent">Parent Page</BreadcrumbLink>
    </BreadcrumbItem>
    <BreadcrumbSeparator />
    <BreadcrumbItem>
      <span className="text-gray-700">Current Page</span>
    </BreadcrumbItem>
  </BreadcrumbList>
</Breadcrumb>
```

Example:
```tsx
<CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
  <div>
    <CardTitle>Page Title</CardTitle>
    <CardDescription className="text-gray-100">
      Page description
    </CardDescription>
  </div>
  <Button variant="secondary">
    Action
  </Button>
</CardHeader>
```

## Layout Usage Guidelines

### When to Use DashboardLayout vs AdminLayout

1. **DashboardLayout**:
   - Use for all regular user-facing pages (dashboard, customers, visit cards, service, sales, reports, etc.)
   - Use when the page is accessible to multiple user roles (not just admins)
   - Use when the page is part of the main application workflow
   - Always provide breadcrumbs with a consistent hierarchy
   - Avoid duplicate titles between breadcrumbs and page headers

2. **AdminLayout**:
   - Use exclusively for admin-specific pages
   - Use when the page is only accessible to users with the ADMIN role
   - Use for system configuration, user management, and other administrative functions

### Breadcrumb Handling

1. **Standard Structure**:
   - First item should always be "Dashboard" linking to the dashboard page
   - Middle items should represent the hierarchy of pages
   - Last item should be the current page (not a link)

2. **Dynamic Content**:
   - For detail pages (e.g., customer details), update the breadcrumb text dynamically with the entity name
   - Use the useEffect hook to update breadcrumbs when data is loaded

```tsx
// Example of dynamic breadcrumb update
useEffect(() => {
  if (data?.name) {
    const pageHeaderTitle = document.getElementById('page-title');
    if (pageHeaderTitle) {
      pageHeaderTitle.textContent = data.name;
    }

    // Update breadcrumb
    const breadcrumbItems = document.querySelectorAll('.breadcrumb-item');
    if (breadcrumbItems.length > 0) {
      const lastItem = breadcrumbItems[breadcrumbItems.length - 1];
      const span = lastItem.querySelector('span');
      if (span) {
        span.textContent = data.name;
      }
    }
  }
}, [data]);
```

## Text Color Standards

### Dialog and Modal Text
All text within dialogs, modals, and popover components must use black color (#000000) for proper readability:

```tsx
// Correct text color implementation
<DialogHeader>
  <DialogTitle className="text-black">Dialog Title</DialogTitle>
  <DialogDescription className="text-black">
    Dialog description text
  </DialogDescription>
</DialogHeader>

<Label htmlFor="field" className="text-black">Field Label</Label>
```

### Form Labels and Descriptions
All form labels, field descriptions, and help text must use black color (#000000):

```tsx
<Label htmlFor="field" className="text-black">Field Label</Label>
<span className="text-sm text-black">Field description</span>
```

## Export Functionality Standards

### Single Export Button Pattern
Each page should have only one export button to avoid confusion:

1. **Location**: Export buttons should be positioned within card headers alongside other action buttons
2. **Avoid Duplication**: Remove any duplicate export buttons from list components when header actions exist
3. **API Endpoints**: Ensure proper export API endpoints exist and return appropriate file formats (CSV, Excel, JSON)
4. **Error Handling**: Implement proper error handling for export failures with user feedback

### Export Implementation Example
```tsx
// In action components (e.g., AMCActions)
<Button variant="outline" onClick={handleExport}>
  <FileDown className="h-4 w-4 mr-2" />
  Export
</Button>

// Remove duplicate export buttons from list components
// Export functionality should be centralized in action components
```

## Best Practices

1. **Standardized Layout**: Always use DashboardLayout for regular pages and AdminLayout for admin pages.
2. **Complete Props**: Always provide all required props to layout components (title, breadcrumbs, allowedRoles).
3. **Role-Based Access**: Always specify allowedRoles for proper access control.
4. **Consistent Breadcrumbs**: Follow the standard breadcrumb structure for all pages.
5. **Avoid Duplicate UI Elements**: Never include redundant breadcrumbs or navigation elements in page components.
6. **Use Layout Components**: Always create a layout.tsx file for each section of the application.
7. **Theme Variables**: Always use Tailwind theme variables instead of hardcoded values.
8. **Responsive Design**: Ensure all pages are responsive and work well on mobile devices.
9. **Consistent Spacing**: Use consistent spacing throughout the application.
10. **Accessibility**: Ensure all UI elements are accessible, with proper contrast and keyboard navigation.
11. **Form Validation**: Use consistent form validation patterns with clear error messages.
12. **Loading States**: Show appropriate loading states for asynchronous operations.
13. **Error Handling**: Handle errors gracefully with clear error messages.
14. **Page Headers**: Each page should include its own CardHeader with consistent styling (bg-primary text-white).
15. **Action Buttons**: Position action buttons within each page's card header for maximum control.
16. **Text Colors**: Use black (#000000) for all dialog, modal, and form text to ensure proper readability.
17. **Export Functionality**: Implement export buttons within page headers with proper API endpoints and error handling.
18. **Header Independence**: Each page manages its own header, title, description, and action buttons independently.
