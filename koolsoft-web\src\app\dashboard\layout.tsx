'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { LayoutDashboard, User, LogOut } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

/**
 * Dashboard Layout Component
 *
 * This component provides a consistent layout for the dashboard by using
 * the standardized DashboardLayout component with collapsible sidebar.
 */
export default function AppDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard', icon: <LayoutDashboard className="h-4 w-4" />, current: true }
  ];

  // Default profile actions using Button components for consistent styling
  const profileActions = (
    <div className="flex items-center space-x-2">
      <Button asChild variant="outline" size="sm">
        <Link href="/profile">
          <User className="h-4 w-4 mr-1" />
          My Profile
        </Link>
      </Button>
      <Button variant="destructive" size="sm" asChild>
        <Link href="/api/auth/signout">
          <LogOut className="h-4 w-4 mr-1" />
          Sign Out
        </Link>
      </Button>
    </div>
  );

  return (
    <DashboardLayout
      title="Dashboard"
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      breadcrumbs={breadcrumbs}
      actions={profileActions}
    >
      {children}
    </DashboardLayout>
  );
}
