"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-sidecar";
exports.ids = ["vendor-chunks/use-sidecar"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-sidecar/dist/es2015/exports.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es2015/exports.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exportSidecar: () => (/* binding */ exportSidecar)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nvar SideCar = function (_a) {\n  var sideCar = _a.sideCar,\n    rest = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__rest)(_a, [\"sideCar\"]);\n  if (!sideCar) {\n    throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n  }\n  var Target = sideCar.read();\n  if (!Target) {\n    throw new Error('Sidecar medium not found');\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.createElement(Target, (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, rest));\n};\nSideCar.isSideCarExport = true;\nfunction exportSidecar(medium, exported) {\n  medium.useMedium(exported);\n  return SideCar;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es2015/exports.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-sidecar/dist/es2015/medium.js":
/*!********************************************************!*\
  !*** ./node_modules/use-sidecar/dist/es2015/medium.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMedium: () => (/* binding */ createMedium),\n/* harmony export */   createSidecarMedium: () => (/* binding */ createSidecarMedium)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n\nfunction ItoI(a) {\n  return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n  if (middleware === void 0) {\n    middleware = ItoI;\n  }\n  var buffer = [];\n  var assigned = false;\n  var medium = {\n    read: function () {\n      if (assigned) {\n        throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n      }\n      if (buffer.length) {\n        return buffer[buffer.length - 1];\n      }\n      return defaults;\n    },\n    useMedium: function (data) {\n      var item = middleware(data, assigned);\n      buffer.push(item);\n      return function () {\n        buffer = buffer.filter(function (x) {\n          return x !== item;\n        });\n      };\n    },\n    assignSyncMedium: function (cb) {\n      assigned = true;\n      while (buffer.length) {\n        var cbs = buffer;\n        buffer = [];\n        cbs.forEach(cb);\n      }\n      buffer = {\n        push: function (x) {\n          return cb(x);\n        },\n        filter: function () {\n          return buffer;\n        }\n      };\n    },\n    assignMedium: function (cb) {\n      assigned = true;\n      var pendingQueue = [];\n      if (buffer.length) {\n        var cbs = buffer;\n        buffer = [];\n        cbs.forEach(cb);\n        pendingQueue = buffer;\n      }\n      var executeQueue = function () {\n        var cbs = pendingQueue;\n        pendingQueue = [];\n        cbs.forEach(cb);\n      };\n      var cycle = function () {\n        return Promise.resolve().then(executeQueue);\n      };\n      cycle();\n      buffer = {\n        push: function (x) {\n          pendingQueue.push(x);\n          cycle();\n        },\n        filter: function (filter) {\n          pendingQueue = pendingQueue.filter(filter);\n          return buffer;\n        }\n      };\n    }\n  };\n  return medium;\n}\nfunction createMedium(defaults, middleware) {\n  if (middleware === void 0) {\n    middleware = ItoI;\n  }\n  return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction createSidecarMedium(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var medium = innerCreateMedium(null);\n  medium.options = (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({\n    async: true,\n    ssr: false\n  }, options);\n  return medium;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-sidecar/dist/es2015/medium.js\n");

/***/ })

};
;