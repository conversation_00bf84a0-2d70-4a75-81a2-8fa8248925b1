import { NextRequest, NextResponse } from 'next/server';
import { getReferenceDataRepository } from '@/lib/repositories';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * Reference data creation schema
 */
const createReferenceDataSchema = z.object({
  name: z.string().min(2).max(100),
  description: z.string().optional(),
});

/**
 * GET /api/reference/[type]
 * Get all reference data for a specific type
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ type: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Extract the type from the URL path
    const { type } = await params;
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');
    const search = searchParams.get('search');
    const activeOnly = searchParams.get('activeOnly') === 'true';
    const exactMatch = searchParams.get('exactMatch') === 'true';

    try {
      const referenceDataRepository = getReferenceDataRepository(type);

      let data = [];
      let total = 0;

      if (search) {
        data = await referenceDataRepository.findByName(search, skip, take, exactMatch);
        total = await referenceDataRepository.count({
          name: exactMatch
            ? { equals: search, mode: 'insensitive' }
            : { contains: search, mode: 'insensitive' },
        });
      } else if (activeOnly) {
        data = await referenceDataRepository.findActive(skip, take);
        total = await referenceDataRepository.count();
      } else {
        data = await referenceDataRepository.findAll(skip, take);
        total = await referenceDataRepository.count();
      }

      return NextResponse.json({
        items: data,
        meta: {
          total,
          skip,
          take,
        },
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes('Invalid reference model')) {
        return NextResponse.json(
          { error: `Invalid reference type: ${type}` },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error) {
    // Use a generic error message to avoid reference errors
    console.error(`Error fetching reference data:`, error);
    return NextResponse.json(
      { error: `Failed to fetch reference data` },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reference/[type]
 * Create a new reference data entry
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ type: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check authorization - only ADMIN and MANAGER can create reference data
    const userRole = session.user.role?.toUpperCase();
    if (userRole !== 'ADMIN' && userRole !== 'MANAGER') {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions' },
        { status: 403 }
      );
    }

    // Extract the type from the URL path
    const { type } = await params;
    const body = await request.json();

    // Validate request body
    const validatedData = createReferenceDataSchema.parse(body);

    try {
      const referenceDataRepository = getReferenceDataRepository(type);

      // Check if reference data with the same name already exists (exact match)
      const existingData = await referenceDataRepository.findByName(validatedData.name, 0, 10, true);

      if (existingData.length > 0) {
        return NextResponse.json(
          { error: `${type} with name '${validatedData.name}' already exists` },
          { status: 400 }
        );
      }

      // Create reference data
      const data = await referenceDataRepository.create(validatedData);

      return NextResponse.json(data, { status: 201 });
    } catch (error) {
      if (error instanceof Error && error.message.includes('Invalid reference model')) {
        return NextResponse.json(
          { error: `Invalid reference type: ${type}` },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    // Use a generic error message to avoid reference errors
    console.error(`Error creating reference data:`, error);
    return NextResponse.json(
      { error: `Failed to create reference data` },
      { status: 500 }
    );
  }
}
