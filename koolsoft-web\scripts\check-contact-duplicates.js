const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkContactDuplicates() {
  try {
    console.log('Checking for contact duplicate issues...\n');

    // Check specific customer from the URL
    const specificCustomerId = '82c3c4ac-3f0c-4aee-b0bb-a5330ba490e9';
    
    console.log('=== SPECIFIC CUSTOMER CONTACT ANALYSIS ===');
    const specificCustomer = await prisma.customer.findUnique({
      where: { id: specificCustomerId },
      include: {
        contacts: true
      }
    });

    if (specificCustomer) {
      console.log(`Customer: ${specificCustomer.name}`);
      console.log(`Total contacts in database: ${specificCustomer.contacts.length}`);
      
      if (specificCustomer.contacts.length > 0) {
        console.log('\nContact details:');
        specificCustomer.contacts.forEach((contact, index) => {
          console.log(`  ${index + 1}. ID: ${contact.id}`);
          console.log(`     Name: ${contact.name}`);
          console.log(`     Designation: ${contact.designation || 'N/A'}`);
          console.log(`     Phone: ${contact.phone || 'N/A'}`);
          console.log(`     Email: ${contact.email || 'N/A'}`);
          console.log(`     Primary: ${contact.isPrimary}`);
          console.log(`     Created: ${contact.createdAt}`);
          console.log('');
        });

        // Check for duplicates by name and phone
        const duplicatesByNamePhone = specificCustomer.contacts.reduce((acc, contact, index) => {
          const key = `${contact.name}-${contact.phone || 'no-phone'}`;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push({ index: index + 1, contact });
          return acc;
        }, {});

        const duplicateGroups = Object.entries(duplicatesByNamePhone).filter(([key, contacts]) => contacts.length > 1);
        
        if (duplicateGroups.length > 0) {
          console.log('🚨 DUPLICATE CONTACTS FOUND:');
          duplicateGroups.forEach(([key, contacts]) => {
            console.log(`\nDuplicate group (${key}):`);
            contacts.forEach(({ index, contact }) => {
              console.log(`  ${index}. ID: ${contact.id}, Created: ${contact.createdAt}`);
            });
          });
        } else {
          console.log('✅ No duplicate contacts found for this customer');
        }
      } else {
        console.log('No contacts found for this customer');
      }
    } else {
      console.log('Customer not found');
    }

    console.log('\n=== GLOBAL CONTACT DUPLICATES ANALYSIS ===');
    
    // Check for duplicate contacts across all customers
    const allContactDuplicates = await prisma.$queryRaw`
      SELECT customer_id, name, phone, email, COUNT(*) as count,
             array_agg(id ORDER BY created_at ASC) as ids
      FROM contacts 
      GROUP BY customer_id, name, phone, email
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;
    
    if (allContactDuplicates.length > 0) {
      console.log(`Found ${allContactDuplicates.length} duplicate contact groups across all customers:`);
      allContactDuplicates.slice(0, 10).forEach(dup => {
        console.log(`- Customer: ${dup.customer_id}, Name: "${dup.name}", Phone: "${dup.phone}", Count: ${dup.count}`);
      });
      if (allContactDuplicates.length > 10) {
        console.log(`... and ${allContactDuplicates.length - 10} more`);
      }
    } else {
      console.log('✅ No duplicate contacts found across all customers');
    }

    // Check for contacts with same name but different customer (potential data migration issue)
    console.log('\n=== CROSS-CUSTOMER NAME DUPLICATES ===');
    const crossCustomerDuplicates = await prisma.$queryRaw`
      SELECT name, COUNT(DISTINCT customer_id) as customer_count, COUNT(*) as total_count
      FROM contacts 
      GROUP BY name 
      HAVING COUNT(DISTINCT customer_id) > 1 AND COUNT(*) > 2
      ORDER BY total_count DESC
      LIMIT 10
    `;

    if (crossCustomerDuplicates.length > 0) {
      console.log('Contacts with same name across multiple customers:');
      crossCustomerDuplicates.forEach(dup => {
        console.log(`- Name: "${dup.name}", Customers: ${dup.customer_count}, Total records: ${dup.total_count}`);
      });
    } else {
      console.log('✅ No suspicious cross-customer name duplicates found');
    }

    // Get total counts
    console.log('\n=== TOTAL COUNTS ===');
    const totalContacts = await prisma.contact.count();
    const totalCustomers = await prisma.customer.count();
    const customersWithContacts = await prisma.customer.count({
      where: {
        contacts: {
          some: {}
        }
      }
    });
    
    console.log(`Total contacts: ${totalContacts}`);
    console.log(`Total customers: ${totalCustomers}`);
    console.log(`Customers with contacts: ${customersWithContacts}`);
    console.log(`Average contacts per customer: ${(totalContacts / customersWithContacts).toFixed(2)}`);

  } catch (error) {
    console.error('Error checking contact duplicates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkContactDuplicates();
