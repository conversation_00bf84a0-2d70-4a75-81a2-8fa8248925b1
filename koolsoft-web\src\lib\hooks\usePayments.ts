'use client';

import { useState, useCallback, useEffect } from 'react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

export interface Payment {
  id: string;
  amcContractId: string;
  receiptNo?: string;
  paymentDate: Date;
  paymentMode?: string;
  amount: number;
  particulars?: string;
  originalAmcId?: number;
  originalReceiptNo?: number;
  createdAt: Date;
  updatedAt: Date;
  amcContract?: {
    id: string;
    customer: {
      id: string;
      name: string;
    };
  };
}

export interface PaymentFilters {
  amcContractId?: string;
  paymentMode?: string;
  dateFrom?: Date;
  dateTo?: Date;
  amountMin?: number;
  amountMax?: number;
  skip?: number;
  take?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface CreatePaymentData {
  amcContractId: string;
  paymentDate: Date;
  amount: number;
  paymentMode: 'CASH' | 'CHEQUE' | 'BANK_TRANSFER' | 'ONLINE';
  receiptNo?: string;
  particulars?: string;
}

export interface UpdatePaymentData {
  paymentDate?: Date;
  amount?: number;
  paymentMode?: 'CASH' | 'CHEQUE' | 'BANK_TRANSFER' | 'ONLINE';
  receiptNo?: string;
  particulars?: string;
}

export interface PaymentStatistics {
  totalPaid: number;
  paymentCount: number;
  lastPaymentDate: Date | null;
  averagePaymentAmount: number;
  paymentModes: { mode: string; count: number; total: number }[];
}

/**
 * Hook for managing payments
 */
export function usePayments(filters?: PaymentFilters) {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Build query parameters
  const buildQueryParams = useCallback(() => {
    const queryParams = new URLSearchParams();
    if (filters?.amcContractId) queryParams.set('amcContractId', filters.amcContractId);
    if (filters?.paymentMode) queryParams.set('paymentMode', filters.paymentMode);
    if (filters?.dateFrom) queryParams.set('dateFrom', filters.dateFrom.toISOString());
    if (filters?.dateTo) queryParams.set('dateTo', filters.dateTo.toISOString());
    if (filters?.amountMin !== undefined) queryParams.set('amountMin', filters.amountMin.toString());
    if (filters?.amountMax !== undefined) queryParams.set('amountMax', filters.amountMax.toString());
    if (filters?.skip !== undefined) queryParams.set('skip', filters.skip.toString());
    if (filters?.take !== undefined) queryParams.set('take', filters.take.toString());
    if (filters?.orderBy) queryParams.set('orderBy', filters.orderBy);
    if (filters?.orderDirection) queryParams.set('orderDirection', filters.orderDirection);
    return queryParams;
  }, [filters]);

  // Fetch payments
  const fetchPayments = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const queryParams = buildQueryParams();
      const response = await fetch(`/api/amc/payments?${queryParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payments');
      }

      const data = await response.json();
      setPayments(data.payments || []);
      setTotal(data.total || 0);
    } catch (err) {
      setError(err as Error);
      setPayments([]);
      setTotal(0);
    } finally {
      setIsLoading(false);
    }
  }, [buildQueryParams]);

  // Load payments on mount and when filters change
  useEffect(() => {
    fetchPayments();
  }, [fetchPayments]);

  // Create payment
  const createPayment = useCallback(async (data: CreatePaymentData) => {
    try {
      setIsCreating(true);
      setError(null);

      const response = await fetch('/api/amc/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create payment');
      }

      const result = await response.json();
      showSuccessToast('Payment created successfully');

      // Refresh payments list
      await fetchPayments();

      return result;
    } catch (err) {
      const error = err as Error;
      setError(error);
      showErrorToast('Failed to create payment', error.message);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [fetchPayments]);

  // Update payment
  const updatePayment = useCallback(async ({ id, data }: { id: string; data: UpdatePaymentData }) => {
    try {
      setIsUpdating(true);
      setError(null);

      const response = await fetch(`/api/amc/payments/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update payment');
      }

      const result = await response.json();
      showSuccessToast('Payment updated successfully');

      // Refresh payments list
      await fetchPayments();

      return result;
    } catch (err) {
      const error = err as Error;
      setError(error);
      showErrorToast('Failed to update payment', error.message);
      throw error;
    } finally {
      setIsUpdating(false);
    }
  }, [fetchPayments]);

  // Delete payment
  const deletePayment = useCallback(async (id: string) => {
    try {
      setIsDeleting(true);
      setError(null);

      const response = await fetch(`/api/amc/payments/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete payment');
      }

      const result = await response.json();
      showSuccessToast('Payment deleted successfully');

      // Refresh payments list
      await fetchPayments();

      return result;
    } catch (err) {
      const error = err as Error;
      setError(error);
      showErrorToast('Failed to delete payment', error.message);
      throw error;
    } finally {
      setIsDeleting(false);
    }
  }, [fetchPayments]);

  // Generate receipt number
  const generateReceiptNumber = useCallback(async (): Promise<string> => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/amc/payments/generate-receipt-number', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to generate receipt number');
      }

      const data = await response.json();
      return data.receiptNumber;
    } catch (error) {
      showErrorToast('Failed to generate receipt number');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Validate receipt number
  const validateReceiptNumber = useCallback(async (receiptNo: string, excludeId?: string): Promise<boolean> => {
    try {
      const params = new URLSearchParams({ receiptNo });
      if (excludeId) params.set('excludeId', excludeId);

      const response = await fetch(`/api/amc/payments/validate-receipt?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to validate receipt number');
      }

      const data = await response.json();
      return data.isUnique;
    } catch (error) {
      console.error('Error validating receipt number:', error);
      return false;
    }
  }, []);

  return {
    // Data
    payments,
    total,

    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,

    // Error states
    error,

    // Actions
    createPayment,
    updatePayment,
    deletePayment,
    refetch: fetchPayments,
    generateReceiptNumber,
    validateReceiptNumber,
  };
}

/**
 * Hook for fetching payment statistics for a contract
 */
export function usePaymentStatistics(amcContractId: string) {
  const [statistics, setStatistics] = useState<PaymentStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchStatistics = useCallback(async () => {
    if (!amcContractId) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/amc/contracts/${amcContractId}/payment-statistics`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment statistics');
      }

      const data = await response.json();
      setStatistics(data);
    } catch (err) {
      setError(err as Error);
      setStatistics(null);
    } finally {
      setIsLoading(false);
    }
  }, [amcContractId]);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    data: statistics,
    isLoading,
    error,
    refetch: fetchStatistics,
  };
}

/**
 * Hook for fetching payments for a specific contract
 */
export function useContractPayments(amcContractId: string) {
  return usePayments({ amcContractId });
}
