import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Activity Log Repository
 * 
 * This repository handles database operations for the Activity Log entity.
 * It provides methods for creating and querying activity logs.
 */
export class ActivityLogRepository extends PrismaRepository<
  Prisma.activity_logsGetPayload<{}>,
  string,
  Prisma.activity_logsCreateInput,
  Prisma.activity_logsUpdateInput
> {
  constructor() {
    super('activity_logs');
  }

  /**
   * Find activity logs by user ID
   * @param userId User ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of activity logs
   */
  async findByUserId(userId: string, skip?: number, take?: number): Promise<Prisma.activity_logsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { userId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
      },
    });
  }

  /**
   * Find activity logs by action
   * @param action Action type
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of activity logs
   */
  async findByAction(action: string, skip?: number, take?: number): Promise<Prisma.activity_logsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { action },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
      },
    });
  }

  /**
   * Find activity logs by entity type and ID
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of activity logs
   */
  async findByEntity(entityType: string, entityId: string, skip?: number, take?: number): Promise<Prisma.activity_logsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { 
        entityType,
        entityId,
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
      },
    });
  }

  /**
   * Find activity logs by date range
   * @param startDate Start date
   * @param endDate End date
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of activity logs
   */
  async findByDateRange(startDate: Date, endDate: Date, skip?: number, take?: number): Promise<Prisma.activity_logsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { 
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
      },
    });
  }

  /**
   * Find activity logs with advanced filtering
   * @param filter Filter options
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of activity logs and total count
   */
  async findWithFilters(
    filter: {
      userId?: string;
      action?: string;
      entityType?: string;
      entityId?: string;
      startDate?: Date;
      endDate?: Date;
      search?: string;
    },
    skip?: number,
    take?: number
  ): Promise<{ logs: Prisma.activity_logsGetPayload<{}>[], total: number }> {
    const where: any = {};

    if (filter.userId) {
      where.userId = filter.userId;
    }

    if (filter.action) {
      where.action = filter.action;
    }

    if (filter.entityType) {
      where.entityType = filter.entityType;
    }

    if (filter.entityId) {
      where.entityId = filter.entityId;
    }

    if (filter.startDate || filter.endDate) {
      where.createdAt = {};
      
      if (filter.startDate) {
        where.createdAt.gte = filter.startDate;
      }
      
      if (filter.endDate) {
        where.createdAt.lte = filter.endDate;
      }
    }

    if (filter.search) {
      where.OR = [
        {
          action: {
            contains: filter.search,
            mode: 'insensitive',
          },
        },
        {
          entityType: {
            contains: filter.search,
            mode: 'insensitive',
          },
        },
        {
          user: {
            name: {
              contains: filter.search,
              mode: 'insensitive',
            },
          },
        },
      ];
    }

    const [logs, total] = await Promise.all([
      this.model.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        include: {
          user: true,
        },
      }),
      this.model.count({ where }),
    ]);

    return { logs, total };
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<Prisma.activity_logsGetPayload<{}>, string, Prisma.activity_logsCreateInput, Prisma.activity_logsUpdateInput> {
    const repo = new ActivityLogRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
