const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function backupContacts() {
  try {
    console.log('Creating backup of all contacts...\n');

    const backupDir = path.join(__dirname, 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir);
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `contacts-backup-${timestamp}.json`);

    // Backup all contacts
    console.log('Backing up contacts...');
    const contacts = await prisma.contact.findMany({
      orderBy: { createdAt: 'asc' }
    });

    const backup = {
      timestamp: new Date().toISOString(),
      contacts: contacts,
      count: contacts.length
    };

    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    console.log(`✅ Backup created: ${backupFile}`);
    console.log(`Backed up ${contacts.length} contacts\n`);

    return backupFile;

  } catch (error) {
    console.error('Error creating backup:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

backupContacts();
