'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, ChevronsUpDown, Search, User, Building } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAMCForm, AMCFormStep } from '@/contexts/amc-form-context';
import { useCustomers } from '@/lib/hooks/useCustomers';
import { useExecutives } from '@/lib/hooks/useExecutives';

// Step 1 validation schema
const step1Schema = z.object({
  customerId: z.string().optional().refine((val) => {
    if (!val) return false; // Required field
    return z.string().uuid().safeParse(val).success; // Must be valid UUID
  }, { message: 'Please select a customer' }),
  contactPersonId: z.string().optional(),
  executiveId: z.string().optional(),
  natureOfService: z.string().optional(),
  contractNumber: z.string().optional(),
  remarks: z.string().optional(),
});

type Step1FormValues = z.infer<typeof step1Schema>;

export function AMCFormStep1() {
  const { state, updateFormData, goToNextStep, dispatch } = useAMCForm();
  const { customers, isLoading: customersLoading } = useCustomers();
  const { executives, isLoading: executivesLoading } = useExecutives();
  
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [customerContacts, setCustomerContacts] = useState<any[]>([]);

  // Initialize form
  const form = useForm<Step1FormValues>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      customerId: state.formData.customerId || undefined,
      contactPersonId: state.formData.contactPersonId || 'none',
      executiveId: state.formData.executiveId || 'none',
      natureOfService: state.formData.natureOfService || '',
      contractNumber: state.formData.contractNumber || '',
      remarks: state.formData.remarks || '',
    },
    mode: 'onChange', // Enable validation on change to prevent submission with invalid data
  });

  // Watch only specific form values for validation
  const customerId = form.watch('customerId');

  // Validate step whenever customer selection changes
  useEffect(() => {
    // Only validate if customerId is defined (not undefined from initial state)
    if (customerId !== undefined) {
      const isValid = !!customerId && z.string().uuid().safeParse(customerId).success;
      dispatch({
        type: 'SET_STEP_VALIDATION',
        payload: { step: AMCFormStep.CUSTOMER_DETAILS, isValid },
      });
    }
  }, [customerId, dispatch]);

  // Initialize selected customer if form already has a customer ID
  useEffect(() => {
    const currentCustomerId = form.getValues('customerId');
    if (currentCustomerId && customers.length > 0 && !selectedCustomer) {
      const customer = customers.find(c => c.id === currentCustomerId);
      if (customer) {
        setSelectedCustomer(customer);
      }
    }
  }, [customers, selectedCustomer]); // Removed 'form' to prevent infinite re-renders

  // Load customer contacts when customer is selected
  useEffect(() => {
    if (selectedCustomer?.id) {
      fetchCustomerContacts(selectedCustomer.id);
    }
  }, [selectedCustomer]);

  const fetchCustomerContacts = async (customerId: string) => {
    try {
      const response = await fetch(`/api/customers/${customerId}/contacts`, {
        credentials: 'include',
      });
      if (response.ok) {
        const contacts = await response.json();
        setCustomerContacts(contacts);
      }
    } catch (error) {
      console.error('Failed to fetch customer contacts:', error);
    }
  };

  const handleCustomerSelect = (customer: any) => {
    if (!customer || !customer.id) {
      console.error('Invalid customer object:', customer);
      return;
    }

    // Prevent unnecessary updates if the same customer is already selected
    if (selectedCustomer?.id === customer.id) {
      setCustomerSearchOpen(false);
      return;
    }

    // Update state
    setSelectedCustomer(customer);

    // Update form values
    form.setValue('customerId', customer.id, { shouldValidate: true });
    form.setValue('contactPersonId', 'none'); // Reset contact person to "none"

    // Close the dropdown
    setCustomerSearchOpen(false);

    // Fetch contacts for the selected customer
    if (customer.id) {
      fetchCustomerContacts(customer.id);
    }
  };



  const onSubmit = (values: Step1FormValues) => {
    // Convert "none" values to undefined for API compatibility
    const processedValues = {
      ...values,
      contactPersonId: values.contactPersonId === 'none' ? undefined : values.contactPersonId,
      executiveId: values.executiveId === 'none' ? undefined : values.executiveId,
    };
    updateFormData(processedValues);
    goToNextStep();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Customer & Basic Details
        </CardTitle>
        <CardDescription>
          Select the customer and provide basic contract information
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Customer Selection */}
            <FormField
              control={form.control}
              name="customerId"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel className="text-base font-medium">
                    Customer <span className="text-red-500">*</span>
                  </FormLabel>
                  <Popover open={customerSearchOpen} onOpenChange={setCustomerSearchOpen}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={customerSearchOpen}
                          className={cn(
                            'w-full justify-between',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          {selectedCustomer ? (
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4" />
                              <span>{selectedCustomer.name}</span>
                              {selectedCustomer.city && (
                                <span className="text-muted-foreground">
                                  - {selectedCustomer.city}
                                </span>
                              )}
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <Search className="h-4 w-4" />
                              <span>Search and select customer...</span>
                            </div>
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[300px] p-0" align="start">
                      <Command>
                        <CommandInput placeholder="Search customers..." />
                        <CommandEmpty>
                          {customersLoading ? 'Loading customers...' : 'No customers found.'}
                        </CommandEmpty>
                        <CommandGroup className="max-h-64 overflow-auto">
                          {customers.map((customer) => (
                            <div
                              key={customer.id}
                              className="relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                              onClick={() => handleCustomerSelect(customer)}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  selectedCustomer?.id === customer.id
                                    ? 'opacity-100'
                                    : 'opacity-0'
                                )}
                              />
                              <div className="flex flex-col">
                                <span className="font-medium">{customer.name}</span>
                                {customer.city && (
                                  <span className="text-sm text-muted-foreground">
                                    {customer.city}
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Contact Person Selection */}
            {customerContacts.length > 0 && (
              <FormField
                control={form.control}
                name="contactPersonId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Person</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select contact person" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No specific contact</SelectItem>
                        {customerContacts.map((contact) => (
                          <SelectItem key={contact.id} value={contact.id}>
                            <div className="flex flex-col">
                              <span>{contact.name}</span>
                              {contact.designation && (
                                <span className="text-sm text-muted-foreground">
                                  {contact.designation}
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Executive Assignment */}
            <FormField
              control={form.control}
              name="executiveId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Assigned Executive</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select executive" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">No assignment</SelectItem>
                      {executives.map((executive) => (
                        <SelectItem key={executive.id} value={executive.id}>
                          {executive.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Nature of Service */}
              <FormField
                control={form.control}
                name="natureOfService"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nature of Service</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Preventive Maintenance" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Contract Number */}
              <FormField
                control={form.control}
                name="contractNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contract Number</FormLabel>
                    <FormControl>
                      <Input placeholder="Auto-generated if empty" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Remarks */}
            <FormField
              control={form.control}
              name="remarks"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Remarks</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes or comments..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Navigation */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={!customerId}
              >
                Next: Contract Details
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

