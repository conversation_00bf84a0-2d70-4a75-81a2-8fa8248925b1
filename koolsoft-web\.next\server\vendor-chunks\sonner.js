"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ function __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsDocumentHidden.useEffect\": ()=>{\n            const callback = {\n                \"useIsDocumentHidden.useEffect.callback\": ()=>{\n                    setIsDocumentHidden(document.hidden);\n                }\n            }[\"useIsDocumentHidden.useEffect.callback\"];\n            document.addEventListener('visibilitychange', callback);\n            return ({\n                \"useIsDocumentHidden.useEffect\": ()=>window.removeEventListener('visibilitychange', callback)\n            })[\"useIsDocumentHidden.useEffect\"];\n        }\n    }[\"useIsDocumentHidden.useEffect\"], []);\n    return isDocumentHidden;\n};\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[heightIndex]\": ()=>heights.findIndex({\n                \"Toast.useMemo[heightIndex]\": (height)=>height.toastId === toast.id\n            }[\"Toast.useMemo[heightIndex]\"]) || 0\n    }[\"Toast.useMemo[heightIndex]\"], [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[closeButton]\": ()=>{\n            var _toast_closeButton;\n            return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n        }\n    }[\"Toast.useMemo[closeButton]\"], [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[duration]\": ()=>toast.duration || durationFromToaster || TOAST_LIFETIME\n    }[\"Toast.useMemo[duration]\"], [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[toastsHeightBefore]\": ()=>{\n            return heights.reduce({\n                \"Toast.useMemo[toastsHeightBefore]\": (prev, curr, reducerIndex)=>{\n                    // Calculate offset up until current toast\n                    if (reducerIndex >= heightIndex) {\n                        return prev;\n                    }\n                    return prev + curr.height;\n                }\n            }[\"Toast.useMemo[toastsHeightBefore]\"], 0);\n        }\n    }[\"Toast.useMemo[toastsHeightBefore]\"], [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo\": ()=>heightIndex * gap + toastsHeightBefore\n    }[\"Toast.useMemo\"], [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            remainingTime.current = duration;\n        }\n    }[\"Toast.useEffect\"], [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            setMounted(true);\n        }\n    }[\"Toast.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            const toastNode = toastRef.current;\n            if (toastNode) {\n                const height = toastNode.getBoundingClientRect().height;\n                // Add toast height to heights array after the toast is mounted\n                setInitialHeight(height);\n                setHeights({\n                    \"Toast.useEffect\": (h)=>[\n                            {\n                                toastId: toast.id,\n                                height,\n                                position: toast.position\n                            },\n                            ...h\n                        ]\n                }[\"Toast.useEffect\"]);\n                return ({\n                    \"Toast.useEffect\": ()=>setHeights({\n                            \"Toast.useEffect\": (h)=>h.filter({\n                                    \"Toast.useEffect\": (height)=>height.toastId !== toast.id\n                                }[\"Toast.useEffect\"])\n                        }[\"Toast.useEffect\"])\n                })[\"Toast.useEffect\"];\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"Toast.useLayoutEffect\": ()=>{\n            // Keep height up to date with the content in case it updates\n            if (!mounted) return;\n            const toastNode = toastRef.current;\n            const originalHeight = toastNode.style.height;\n            toastNode.style.height = 'auto';\n            const newHeight = toastNode.getBoundingClientRect().height;\n            toastNode.style.height = originalHeight;\n            setInitialHeight(newHeight);\n            setHeights({\n                \"Toast.useLayoutEffect\": (heights)=>{\n                    const alreadyExists = heights.find({\n                        \"Toast.useLayoutEffect.alreadyExists\": (height)=>height.toastId === toast.id\n                    }[\"Toast.useLayoutEffect.alreadyExists\"]);\n                    if (!alreadyExists) {\n                        return [\n                            {\n                                toastId: toast.id,\n                                height: newHeight,\n                                position: toast.position\n                            },\n                            ...heights\n                        ];\n                    } else {\n                        return heights.map({\n                            \"Toast.useLayoutEffect\": (height)=>height.toastId === toast.id ? {\n                                    ...height,\n                                    height: newHeight\n                                } : height\n                        }[\"Toast.useLayoutEffect\"]);\n                    }\n                }\n            }[\"Toast.useLayoutEffect\"]);\n        }\n    }[\"Toast.useLayoutEffect\"], [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id,\n        toast.jsx,\n        toast.action,\n        toast.cancel\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toast.useCallback[deleteToast]\": ()=>{\n            // Save the offset for the exit swipe animation\n            setRemoved(true);\n            setOffsetBeforeRemove(offset.current);\n            setHeights({\n                \"Toast.useCallback[deleteToast]\": (h)=>h.filter({\n                        \"Toast.useCallback[deleteToast]\": (height)=>height.toastId !== toast.id\n                    }[\"Toast.useCallback[deleteToast]\"])\n            }[\"Toast.useCallback[deleteToast]\"]);\n            setTimeout({\n                \"Toast.useCallback[deleteToast]\": ()=>{\n                    removeToast(toast);\n                }\n            }[\"Toast.useCallback[deleteToast]\"], TIME_BEFORE_UNMOUNT);\n        }\n    }[\"Toast.useCallback[deleteToast]\"], [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n            let timeoutId;\n            // Pause the timer on each hover\n            const pauseTimer = {\n                \"Toast.useEffect.pauseTimer\": ()=>{\n                    if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                        // Get the elapsed time since the timer started\n                        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                        remainingTime.current = remainingTime.current - elapsedTime;\n                    }\n                    lastCloseTimerStartTimeRef.current = new Date().getTime();\n                }\n            }[\"Toast.useEffect.pauseTimer\"];\n            const startTimer = {\n                \"Toast.useEffect.startTimer\": ()=>{\n                    // setTimeout(, Infinity) behaves as if the delay is 0.\n                    // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n                    // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n                    if (remainingTime.current === Infinity) return;\n                    closeTimerStartTimeRef.current = new Date().getTime();\n                    // Let the toast know it has started\n                    timeoutId = setTimeout({\n                        \"Toast.useEffect.startTimer\": ()=>{\n                            toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                            deleteToast();\n                        }\n                    }[\"Toast.useEffect.startTimer\"], remainingTime.current);\n                }\n            }[\"Toast.useEffect.startTimer\"];\n            if (expanded || interacting || isDocumentHidden) {\n                pauseTimer();\n            } else {\n                startTimer();\n            }\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.delete) {\n                deleteToast();\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n            '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, // Apply transform using both x and y values\n            _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (true) return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSonner.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"useSonner.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        setTimeout({\n                            \"useSonner.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"useSonner.useEffect\": ()=>{\n                                        setActiveToasts({\n                                            \"useSonner.useEffect\": (toasts)=>toasts.filter({\n                                                    \"useSonner.useEffect\": (t)=>t.id !== toast.id\n                                                }[\"useSonner.useEffect\"])\n                                        }[\"useSonner.useEffect\"]);\n                                    }\n                                }[\"useSonner.useEffect\"]);\n                            }\n                        }[\"useSonner.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"useSonner.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"useSonner.useEffect\": ()=>{\n                                    setActiveToasts({\n                                        \"useSonner.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"useSonner.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"useSonner.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"useSonner.useEffect\"]);\n                                }\n                            }[\"useSonner.useEffect\"]);\n                        }\n                    }[\"useSonner.useEffect\"]);\n                }\n            }[\"useSonner.useEffect\"]);\n        }\n    }[\"useSonner.useEffect\"], []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Toaster(props, ref) {\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[possiblePositions]\": ()=>{\n            return Array.from(new Set([\n                position\n            ].concat(toasts.filter({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]).map({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]))));\n        }\n    }[\"Toaster.Toaster.useMemo[possiblePositions]\"], [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== 'system' ? theme :  false ? 0 : 'light');\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toaster.Toaster.useCallback[removeToast]\": (toastToRemove)=>{\n            setToasts({\n                \"Toaster.Toaster.useCallback[removeToast]\": (toasts)=>{\n                    var _toasts_find;\n                    if (!((_toasts_find = toasts.find({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (toast)=>toast.id === toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"])) == null ? void 0 : _toasts_find.delete)) {\n                        ToastState.dismiss(toastToRemove.id);\n                    }\n                    return toasts.filter({\n                        \"Toaster.Toaster.useCallback[removeToast]\": ({ id })=>id !== toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n                }\n            }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n        }\n    }[\"Toaster.Toaster.useCallback[removeToast]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"Toaster.Toaster.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        // Prevent batching of other state updates\n                        requestAnimationFrame({\n                            \"Toaster.Toaster.useEffect\": ()=>{\n                                setToasts({\n                                    \"Toaster.Toaster.useEffect\": (toasts)=>toasts.map({\n                                            \"Toaster.Toaster.useEffect\": (t)=>t.id === toast.id ? {\n                                                    ...t,\n                                                    delete: true\n                                                } : t\n                                        }[\"Toaster.Toaster.useEffect\"])\n                                }[\"Toaster.Toaster.useEffect\"]);\n                            }\n                        }[\"Toaster.Toaster.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"Toaster.Toaster.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Toaster.Toaster.useEffect\": ()=>{\n                                    setToasts({\n                                        \"Toaster.Toaster.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"Toaster.Toaster.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"Toaster.Toaster.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"Toaster.Toaster.useEffect\"]);\n                                }\n                            }[\"Toaster.Toaster.useEffect\"]);\n                        }\n                    }[\"Toaster.Toaster.useEffect\"]);\n                }\n            }[\"Toaster.Toaster.useEffect\"]);\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (theme !== 'system') {\n                setActualTheme(theme);\n                return;\n            }\n            if (theme === 'system') {\n                // check if current preference is dark\n                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    // it's currently dark\n                    setActualTheme('dark');\n                } else {\n                    // it's not dark\n                    setActualTheme('light');\n                }\n            }\n            if (true) return;\n            const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            try {\n                // Chrome & Firefox\n                darkMediaQuery.addEventListener('change', {\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        if (matches) {\n                            setActualTheme('dark');\n                        } else {\n                            setActualTheme('light');\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            } catch (error) {\n                // Safari < 14\n                darkMediaQuery.addListener({\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        try {\n                            if (matches) {\n                                setActualTheme('dark');\n                            } else {\n                                setActualTheme('light');\n                            }\n                        } catch (e) {\n                            console.error(e);\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            // Ensure expanded is always false when no toasts are present / only one left\n            if (toasts.length <= 1) {\n                setExpanded(false);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Toaster.Toaster.useEffect.handleKeyDown\": (event)=>{\n                    var _listRef_current;\n                    const isHotkeyPressed = hotkey.every({\n                        \"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\": (key)=>event[key] || event.code === key\n                    }[\"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\"]);\n                    if (isHotkeyPressed) {\n                        var _listRef_current1;\n                        setExpanded(true);\n                        (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n                    }\n                    if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                        setExpanded(false);\n                    }\n                }\n            }[\"Toaster.Toaster.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Toaster.Toaster.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Toaster.Toaster.useEffect\"];\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (listRef.current) {\n                return ({\n                    \"Toaster.Toaster.useEffect\": ()=>{\n                        if (lastFocusedElementRef.current) {\n                            lastFocusedElementRef.current.focus({\n                                preventScroll: true\n                            });\n                            lastFocusedElementRef.current = null;\n                            isFocusWithinRef.current = false;\n                        }\n                    }\n                })[\"Toaster.Toaster.useEffect\"];\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        listRef.current\n    ]);\n    return(// Remove item from normal navigation flow, only available via hotkey\n    /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    })));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;