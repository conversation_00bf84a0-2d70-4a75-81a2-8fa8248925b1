'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { signOut } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Props for AccountSettingsForm component
interface AccountSettingsFormProps {
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    isActive?: boolean;
  };
}

/**
 * Account Settings Form Component
 *
 * This component allows users to manage their account settings.
 */
export function AccountSettingsForm({ user }: AccountSettingsFormProps) {
  const [isDeactivating, setIsDeactivating] = useState(false);
  const [showDeactivateConfirm, setShowDeactivateConfirm] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState(user);
  const router = useRouter();

  // Update local state when user prop changes
  useEffect(() => {
    if (user) {
      setUserData(user);
    }
  }, [user]);

  // Handle account deactivation
  const handleDeactivateAccount = async () => {
    setIsDeactivating(true);
    setError(null);

    try {
      const response = await fetch('/api/users/me', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: false,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || 'Failed to deactivate account');
        setIsDeactivating(false);
        return;
      }

      // Sign out the user
      await signOut({ redirect: false });

      // Redirect to login page
      router.push('/auth/login?message=Your account has been deactivated');
    } catch (error) {
      console.error('Error deactivating account:', error);
      setError('An error occurred while deactivating your account');
      setIsDeactivating(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    await signOut({ redirect: false });
    router.push('/auth/login');
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium leading-6 text-black">Account Settings</h3>
        <p className="mt-1 text-sm text-black">
          Manage your account settings and preferences.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-black">Account Information</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="divide-y divide-gray-200">
            <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
              <dt className="text-sm font-medium text-black">Full name</dt>
              <dd className="mt-1 text-sm text-black sm:mt-0 sm:col-span-2">{userData?.name || 'Not available'}</dd>
            </div>
            <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
              <dt className="text-sm font-medium text-black">Email address</dt>
              <dd className="mt-1 text-sm text-black sm:mt-0 sm:col-span-2">{userData?.email || 'Not available'}</dd>
            </div>
            <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
              <dt className="text-sm font-medium text-black">Role</dt>
              <dd className="mt-1 text-sm text-black sm:mt-0 sm:col-span-2">{userData?.role || 'Not available'}</dd>
            </div>
            <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
              <dt className="text-sm font-medium text-black">Phone</dt>
              <dd className="mt-1 text-sm text-black sm:mt-0 sm:col-span-2">{userData?.phone || 'Not provided'}</dd>
            </div>
            <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
              <dt className="text-sm font-medium text-black">Designation</dt>
              <dd className="mt-1 text-sm text-black sm:mt-0 sm:col-span-2">{userData?.designation || 'Not provided'}</dd>
            </div>
            <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
              <dt className="text-sm font-medium text-black">Account status</dt>
              <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                <Badge variant="success" className="bg-green-500 text-white">
                  Active
                </Badge>
              </dd>
            </div>
          </dl>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-black">Session Management</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-black mb-4">
            Sign out from all devices if you suspect unauthorized access to your account.
          </p>
          <Button
            type="button"
            onClick={handleSignOut}
            className="bg-[#0F52BA] text-white hover:bg-[#0F52BA]/90"
          >
            Sign Out
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-[#ef4444]">Danger Zone</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-black mb-4">
            Deactivating your account will make your profile and data inaccessible. This action can be reversed by an administrator.
          </p>

          {!showDeactivateConfirm ? (
            <Button
              type="button"
              onClick={() => setShowDeactivateConfirm(true)}
              variant="destructive"
            >
              Deactivate Account
            </Button>
          ) : (
            <div className="space-y-4">
              <Alert variant="destructive">
                <AlertTitle>Are you sure you want to deactivate your account?</AlertTitle>
                <AlertDescription>
                  This action will make your profile and data inaccessible.
                  You will be signed out immediately.
                </AlertDescription>
              </Alert>

              <div className="flex space-x-4">
                <Button
                  type="button"
                  onClick={() => setShowDeactivateConfirm(false)}
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={handleDeactivateAccount}
                  disabled={isDeactivating}
                  variant="destructive"
                >
                  {isDeactivating ? 'Deactivating...' : 'Confirm Deactivation'}
                </Button>
              </div>
            </div>
          )}

          {error && (
            <div className="mt-4">
              <Alert variant="destructive">
                <AlertDescription>
                  {error}
                </AlertDescription>
              </Alert>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
