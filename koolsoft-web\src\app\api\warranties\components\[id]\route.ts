import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { WarrantyComponentRepository } from '@/lib/repositories/warranty-component.repository';
import { z } from 'zod';

const updateComponentSchema = z.object({
  serialNumber: z.string().min(1, 'Serial number is required').optional(),
  warrantyDate: z.string().datetime().optional(),
  section: z.string().optional().nullable(),
  componentNo: z.number().int().optional()
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id: componentId } = await params;
    if (!componentId) {
      return NextResponse.json({ error: 'Component ID is required' }, { status: 400 });
    }

    const warrantyComponentRepo = new WarrantyComponentRepository();
    const component = await warrantyComponentRepo.findById(componentId);

    if (!component) {
      return NextResponse.json({ error: 'Component not found' }, { status: 404 });
    }

    return NextResponse.json(component);
  } catch (error) {
    console.error('Error fetching component:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check user role - only ADMIN, MANAGER, and EXECUTIVE can update components
    const userRole = session.user.role?.toUpperCase();
    if (!['ADMIN', 'MANAGER', 'EXECUTIVE'].includes(userRole || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id: componentId } = await params;
    if (!componentId) {
      return NextResponse.json({ error: 'Component ID is required' }, { status: 400 });
    }

    const body = await request.json();
    
    // Validate request body
    const validationResult = updateComponentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const warrantyComponentRepo = new WarrantyComponentRepository();
    
    // Check if component exists
    const existingComponent = await warrantyComponentRepo.findById(componentId);
    if (!existingComponent) {
      return NextResponse.json({ error: 'Component not found' }, { status: 404 });
    }

    // Update component
    const updatedComponent = await warrantyComponentRepo.update(componentId, validationResult.data);

    return NextResponse.json(updatedComponent);
  } catch (error) {
    console.error('Error updating component:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check user role - only ADMIN and MANAGER can delete components
    const userRole = session.user.role?.toUpperCase();
    if (!['ADMIN', 'MANAGER'].includes(userRole || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id: componentId } = await params;
    if (!componentId) {
      return NextResponse.json({ error: 'Component ID is required' }, { status: 400 });
    }

    const warrantyComponentRepo = new WarrantyComponentRepository();
    
    // Check if component exists
    const existingComponent = await warrantyComponentRepo.findById(componentId);
    if (!existingComponent) {
      return NextResponse.json({ error: 'Component not found' }, { status: 404 });
    }

    // Delete component
    await warrantyComponentRepo.delete(componentId);

    return NextResponse.json({ message: 'Component deleted successfully' });
  } catch (error) {
    console.error('Error deleting component:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
