import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getWarrantyRepository } from '@/lib/repositories';

/**
 * GET /api/warranties/expiring
 * Get warranties that are expiring within a specified number of days
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const days = parseInt(searchParams.get('days') || '30');
      const skip = parseInt(searchParams.get('skip') || '0');
      const take = parseInt(searchParams.get('take') || '10');
      
      const warrantyRepository = getWarrantyRepository();
      
      // Get expiring warranties
      const warranties = await warrantyRepository.findExpiring(days, skip, take);
      
      // Count expiring warranties
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);
      
      const totalCount = await warrantyRepository.countWithFilter({
        warrantyDate: {
          gte: today,
          lte: futureDate,
        },
        status: 'ACTIVE',
      });
      
      return NextResponse.json({
        warranties,
        pagination: {
          skip,
          take,
          total: totalCount,
          hasMore: skip + take < totalCount,
        },
        expirationInfo: {
          days,
          expiringCount: totalCount,
        },
      });
    } catch (error) {
      console.error('Error fetching expiring warranties:', error);
      return NextResponse.json(
        { error: 'Failed to fetch expiring warranties' },
        { status: 500 }
      );
    }
  }
);
