'use client';

import { useState, useEffect } from 'react';

export interface Machine {
  id: string;
  amcContractId: string;
  productId?: string;
  modelId?: string;
  brandId?: string;
  location?: string;
  locationFlag?: string;
  serialNumber?: string;
  assetNo?: number;
  historyCardNo?: number;
  section?: string;
  originalAmcId?: number;
  originalAssetNo?: number;
  installationDate?: Date;
  tonnage?: number;
  status: 'ACTIVE' | 'INACTIVE';
  createdAt: Date;
  updatedAt: Date;
  // Relations
  product?: {
    id: string;
    name: string;
  };
  model?: {
    id: string;
    name: string;
    tonnage?: number;
  };
  brand?: {
    id: string;
    name: string;
  };
  amcContract?: {
    id: string;
    customer: {
      id: string;
      name: string;
    };
  };
}

export interface CreateMachineData {
  amcContractId: string;
  productId?: string;
  modelId?: string;
  brandId?: string;
  serialNumber?: string;
  location?: string;
  installationDate?: Date;
  tonnage?: number;
  status?: 'ACTIVE' | 'INACTIVE';
  originalAmcId?: number;
  originalAssetNo?: number;
}

export interface UpdateMachineData {
  productId?: string;
  modelId?: string;
  brandId?: string;
  serialNumber?: string;
  location?: string;
  installationDate?: Date;
  tonnage?: number;
  status?: 'ACTIVE' | 'INACTIVE';
}

export function useMachines(amcContractId?: string) {
  const [machines, setMachines] = useState<Machine[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMachines = async () => {
    try {
      setIsLoading(true);
      setError(null);

      let url = '/api/amc/machines';
      if (amcContractId) {
        url += `?amcContractId=${amcContractId}`;
      }

      const response = await fetch(url, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch machines');
      }

      const result = await response.json();
      setMachines(result.machines || result || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      console.error('Error fetching machines:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMachines();
  }, [amcContractId]);

  const createMachine = async (data: CreateMachineData): Promise<Machine> => {
    const response = await fetch('/api/amc/machines', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create machine');
    }

    const machine = await response.json();
    setMachines(prev => [...prev, machine]);
    return machine;
  };

  const updateMachine = async (id: string, data: UpdateMachineData): Promise<Machine> => {
    const response = await fetch(`/api/amc/machines/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update machine');
    }

    const machine = await response.json();
    setMachines(prev => prev.map(m => m.id === id ? machine : m));
    return machine;
  };

  const deleteMachine = async (id: string): Promise<void> => {
    const response = await fetch(`/api/amc/machines/${id}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete machine');
    }

    setMachines(prev => prev.filter(m => m.id !== id));
  };

  const getMachine = async (id: string): Promise<Machine> => {
    const response = await fetch(`/api/amc/machines/${id}`, {
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch machine');
    }

    return response.json();
  };

  return {
    machines,
    isLoading,
    error,
    createMachine,
    updateMachine,
    deleteMachine,
    getMachine,
    refetch: fetchMachines,
  };
}
