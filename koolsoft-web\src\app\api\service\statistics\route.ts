import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getServiceReportRepository, getServiceDetailRepository } from '@/lib/repositories';
import { serviceStatisticsQuerySchema } from '@/lib/validations/service.schema';

/**
 * GET /api/service/statistics
 * Get service statistics and metrics
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const queryParams = Object.fromEntries(searchParams.entries());

      // Validate query parameters
      const validatedQuery = serviceStatisticsQuerySchema.parse(queryParams);

      const serviceReportRepository = getServiceReportRepository();
      const serviceDetailRepository = getServiceDetailRepository();

      // Calculate date range based on period
      const now = new Date();
      let startDate: Date;

      switch (validatedQuery.period) {
        case 'WEEK':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'MONTH':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'QUARTER':
          const quarterStart = Math.floor(now.getMonth() / 3) * 3;
          startDate = new Date(now.getFullYear(), quarterStart, 1);
          break;
        case 'YEAR':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      }

      // Build filter for the period
      const periodFilter: any = {
        reportDate: {
          gte: startDate,
          lte: now,
        },
      };

      // Add additional filters
      if (validatedQuery.executiveId) {
        periodFilter.executiveId = validatedQuery.executiveId;
      }

      if (validatedQuery.customerId) {
        periodFilter.customerId = validatedQuery.customerId;
      }

      if (validatedQuery.complaintType) {
        periodFilter.complaintType = validatedQuery.complaintType;
      }

      // Get basic statistics
      const [
        basicStats,
        detailStats,
        periodStats,
        statusBreakdown,
        complaintTypeBreakdown,
        mostCommonProblems,
        mostReplacedParts,
      ] = await Promise.all([
        serviceReportRepository.getStatistics(),
        serviceDetailRepository.getStatistics(),
        serviceReportRepository.countWithFilter(periodFilter),
        getStatusBreakdown(serviceReportRepository, periodFilter),
        getComplaintTypeBreakdown(serviceReportRepository, periodFilter),
        serviceDetailRepository.getMostCommonProblems(5),
        serviceDetailRepository.getMostReplacedParts(5),
      ]);

      // Calculate period-specific metrics
      const periodCompletedFilter = {
        ...periodFilter,
        status: 'COMPLETED',
      };
      const periodCompleted = await serviceReportRepository.countWithFilter(periodCompletedFilter);
      const periodCompletionRate = periodStats > 0 ? (periodCompleted / periodStats) * 100 : 0;

      // Get average resolution time for completed reports in the period
      const completedReports = await serviceReportRepository.findWithFilter(
        periodCompletedFilter,
        0,
        1000 // Limit to avoid memory issues
      );

      let averageResolutionTime = 0;
      if (completedReports.length > 0) {
        const totalResolutionTime = completedReports.reduce((sum, report) => {
          if (report.reportDate && report.completionDate) {
            const resolutionTime = new Date(report.completionDate).getTime() - new Date(report.reportDate).getTime();
            return sum + resolutionTime;
          }
          return sum;
        }, 0);
        averageResolutionTime = totalResolutionTime / completedReports.length / (1000 * 60 * 60 * 24); // Convert to days
      }

      const statistics = {
        overview: {
          ...basicStats,
          ...detailStats,
          averageResolutionTime: Math.round(averageResolutionTime * 10) / 10, // Round to 1 decimal place
        },
        period: {
          name: validatedQuery.period,
          startDate,
          endDate: now,
          totalReports: periodStats,
          completedReports: periodCompleted,
          completionRate: Math.round(periodCompletionRate * 10) / 10,
        },
        breakdowns: {
          status: statusBreakdown,
          complaintType: complaintTypeBreakdown,
        },
        insights: {
          mostCommonProblems,
          mostReplacedParts,
        },
      };

      return NextResponse.json({ statistics });
    } catch (error) {
      console.error('Error fetching service statistics:', error);
      return NextResponse.json(
        { error: 'Failed to fetch service statistics' },
        { status: 500 }
      );
    }
  }
);

/**
 * Helper function to get status breakdown
 */
async function getStatusBreakdown(repository: any, filter: any): Promise<any[]> {
  const statuses = ['OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'PENDING'];
  
  const breakdown = await Promise.all(
    statuses.map(async (status) => {
      const count = await repository.countWithFilter({
        ...filter,
        status,
      });
      return { status, count };
    })
  );

  return breakdown.filter(item => item.count > 0);
}

/**
 * Helper function to get complaint type breakdown
 */
async function getComplaintTypeBreakdown(repository: any, filter: any): Promise<any[]> {
  const complaintTypes = ['REPAIR', 'MAINTENANCE', 'INSTALLATION', 'INSPECTION', 'WARRANTY', 'OTHER'];
  
  const breakdown = await Promise.all(
    complaintTypes.map(async (complaintType) => {
      const count = await repository.countWithFilter({
        ...filter,
        complaintType,
      });
      return { complaintType, count };
    })
  );

  return breakdown.filter(item => item.count > 0);
}
