# KoolSoft Modernization Project - Augment AI Implementation Guide

This document provides detailed guidance for implementing the KoolSoft modernization project using the Augment AI Coding agent. It includes phased implementation timelines, specific instructions for handling complex migration patterns, testing procedures, deployment configurations, and rollback procedures.

## Phased Implementation Timeline

### Phase 1: Foundation (Weeks 1-4)
- Project setup and configuration
- Database schema design and initial migration
- Authentication system implementation
- Core UI component development
- **Checkpoint 1**: Functional login system with basic UI components

### Phase 2: Core Modules (Weeks 5-9)
- Customer management module
- AMC management module
- **Checkpoint 2**: Ability to create and manage customers and AMC contracts

### Phase 3: Extended Modules (Weeks 10-15)
- Warranty management module
- Service management module
- Sales tracking module
- Reporting system
- **Checkpoint 3**: Complete functional system with all modules

### Phase 4: Finalization (Weeks 16-18)
- Testing and optimization
- Deployment configuration
- Documentation completion
- **Checkpoint 4**: Production-ready application deployed on Vercel

## Augment AI Implementation Instructions

### General Guidelines for Augment AI

1. **Code Generation Approach**:
   - Generate code in small, testable chunks
   - Follow the TypeScript type definitions strictly
   - Implement one feature at a time, ensuring it works before moving to the next
   - Use consistent naming conventions throughout the codebase

2. **Documentation Standards**:
   - Add JSDoc comments for all functions and components
   - Include purpose, parameters, and return values in comments
   - Document complex business logic with detailed comments
   - Create README files for each major module

3. **Testing Requirements**:
   - Write unit tests for all utility functions
   - Create component tests for UI elements
   - Implement API route tests for backend functionality
   - Ensure test coverage for critical business logic

### Handling Complex Migration Patterns

#### 1. Database Schema Conversion

> **IMPORTANT**: For database access guidelines during the transition period, please refer to the [Database Access Guidelines](./database-access-guidelines.md) document. All database operations should use the legacy models until the full migration is complete.

When converting the VB6 Access database schema to Prisma:

```typescript
// Example: Converting VB6 recordset to Prisma schema
// VB6 code:
// sql = "select * from amc_sum where amcid=" & AmcId
// Set rsamc(1) = db.OpenRecordset(sql, dbOpenDynaset)

// Prisma equivalent using legacy models:
const amcContract = await prisma.amcContract.findUnique({
  where: { originalId: amcId }
});

// Get related customer
const customer = await prisma.legacyCustomer.findUnique({
  where: { id: amcContract.customerId }
});

// Get related machines
const machines = await prisma.amcMachine.findMany({
  where: { amcId: amcContract.originalId }
});

// Get related payments
const payments = await prisma.amcPayment.findMany({
  where: { amcId: amcContract.originalId }
});
```

**Instructions for Augment AI**:
1. Analyze SQL queries in VB6 code to understand data relationships
2. Create appropriate Prisma models with relations
3. Use Prisma's include functionality to replace nested recordset queries
4. Implement proper error handling for database operations

#### 2. Form Conversion

When converting VB6 forms to React components:

```tsx
// Example: Converting VB6 form to React component
// VB6 form: frmAMCEntry with multiple controls

// React equivalent:
import { useState } from 'react';
import { useForm } from 'react-hook-form';

export default function AMCEntryForm() {
  const { register, handleSubmit, formState: { errors } } = useForm();
  const [activeTab, setActiveTab] = useState('general');

  const onSubmit = async (data) => {
    // Form submission logic
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="tabs">
        <button
          type="button"
          className={activeTab === 'general' ? 'active' : ''}
          onClick={() => setActiveTab('general')}
        >
          AMC General
        </button>
        <button
          type="button"
          className={activeTab === 'machine' ? 'active' : ''}
          onClick={() => setActiveTab('machine')}
        >
          Machine Details
        </button>
        {/* More tabs */}
      </div>

      {activeTab === 'general' && (
        <div className="tab-content">
          {/* General information fields */}
        </div>
      )}

      {activeTab === 'machine' && (
        <div className="tab-content">
          {/* Machine details fields */}
        </div>
      )}

      {/* More tab content */}

      <button type="submit">Save</button>
    </form>
  );
}
```

**Instructions for Augment AI**:
1. Analyze VB6 form layout and controls
2. Create equivalent React components with proper state management
3. Implement form validation using React Hook Form
4. Use conditional rendering for tab-based interfaces
5. Ensure responsive design with Tailwind CSS

#### 3. Business Logic Migration

When converting VB6 business logic to TypeScript:

```typescript
// Example: Converting VB6 business logic to TypeScript
// VB6 code:
// Function UserID_check()
//   Dim user As String
//   Set rs_pass = db.OpenRecordset("USERpwd", dbOpenDynaset)
//   If txtPass(0).Text <> "" Then
//     LogInUser = Trim(LCase(txtPass(0)))
//     user = encr_fn(LogInUser)
//     rs_pass.FindFirst "user='" & user & "'"
//     If rs_pass.NoMatch Then
//       MsgBox "No Such User!!!", vbOKOnly + vbCritical, "Error"
//       txtPass(0).Text = ""
//       txtPass(1).Text = ""
//       txtPass(0).SetFocus
//     Else
//       If decr_fn(rs_pass!pwd) = LCase(Trim(txtPass(1))) Then
//         LogInPwd = LCase(Trim(txtPass(1)))
//         LogInRight = decr_fn(rs_pass!Key)
//         LogInID = rs_pass!Userid
//         Unload Me
//         MDITechno.Show
//       Else
//         MsgBox "Invalid Password!!!", vbOKOnly + vbCritical, "Error"
//         txtPass(0).Text = ""
//         txtPass(1).Text = ""
//         txtPass(0).SetFocus
//       End If
//     End If
//   End If
// End Function

// TypeScript equivalent using legacy models:
import { compare } from 'bcrypt';
import { prisma } from '@/lib/prisma';

async function checkUserCredentials(username: string, password: string) {
  try {
    // Use the legacy UserPwd model that maps to the USERpwd table
    const userPwd = await prisma.userPwd.findFirst({
      where: {
        User: username.toLowerCase().trim()
      }
    });

    if (!userPwd) {
      return { success: false, message: 'No such user' };
    }

    // In the legacy system, passwords were stored with custom encryption
    // Here we're assuming they've been migrated to bcrypt hashes
    const isPasswordValid = await compare(password.toLowerCase().trim(), userPwd.Pwd);

    if (!isPasswordValid) {
      return { success: false, message: 'Invalid password' };
    }

    return {
      success: true,
      user: {
        id: userPwd.UserID,
        username: username.toLowerCase().trim(),
        role: userPwd.Key === 'Admin' ? 'admin' : 'user'
      }
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return { success: false, message: 'Authentication error' };
  }
}
```

**Instructions for Augment AI**:
1. Identify core business logic in VB6 code
2. Convert to TypeScript using modern patterns (async/await, try/catch)
3. Replace direct database access with Prisma client operations
4. Implement proper error handling and logging
5. Use TypeScript interfaces to define data structures

#### 4. Module Conversion Implementation

When implementing module conversions (e.g., In-Warranty to AMC):

```typescript
// Example: Converting In-Warranty to AMC using legacy models
// lib/conversions/inwarranty-to-amc.ts
import { prisma } from '@/lib/prisma';
import { sendEmail } from '@/lib/email/nodemailer';
import { formatDate } from '@/lib/utils/date-utils';

export interface ConversionResult {
  success: boolean;
  message: string;
  newId?: number;
  historyCardId?: number;
}

export async function convertInwarrantyToAMC(
  inwarrantyId: number,
  amcData: {
    startDate: Date;
    endDate: Date;
    amount: number;
    numberOfServices: number;
    executiveId: number;
  }
): Promise<ConversionResult> {
  try {
    // Start a transaction
    return await prisma.$transaction(async (tx) => {
      // 1. Get the in-warranty record
      const inwarranty = await tx.inWarranty.findUnique({
        where: { originalId: inwarrantyId }
      });

      if (!inwarranty) {
        throw new Error(`In-warranty record with ID ${inwarrantyId} not found`);
      }

      // Get the customer
      const customer = await tx.legacyCustomer.findUnique({
        where: { id: inwarranty.customerId }
      });

      if (!customer) {
        throw new Error(`Customer with ID ${inwarranty.customerId} not found`);
      }

      // Get in-warranty machines
      const inwarrantyMachines = await tx.inWarrantyMachine.findMany({
        where: { inWarrantyId: inwarranty.originalId }
      });

      // Get in-warranty components
      const inwarrantyComponents = await tx.inWarrantyComponent.findMany({
        where: { inWarrantyId: inwarranty.originalId }
      });

      // 2. Get the next AMC ID
      const lastAmc = await tx.amcContract.findFirst({
        orderBy: { originalId: 'desc' }
      });
      const nextAmcId = lastAmc ? lastAmc.originalId + 1 : 1;

      // 3. Create new AMC contract
      const amcContract = await tx.amcContract.create({
        data: {
          originalId: nextAmcId,
          customerId: inwarranty.customerId,
          executiveId: amcData.executiveId,
          contactPerson: inwarranty.contactPerson,
          contactPhone: inwarranty.contactPhone,
          numberOfServices: amcData.numberOfServices,
          startDate: amcData.startDate,
          endDate: amcData.endDate,
          amount: amcData.amount,
          fresh: "N",
          renewalFlag: "Y",
          blstrFlag: "D",
          newId: 0,
          outId: 0
        }
      });

      // 4. Create machines for the AMC
      for (const machine of inwarrantyMachines) {
        await tx.amcMachine.create({
          data: {
            amcId: amcContract.originalId,
            PrdID: machine.productId,
            modelId: machine.modelId,
            BrID: machine.BrID || 0,
            location: machine.location,
            LoctFlag: machine.locationFlag,
            serialNumber: machine.serialNumber,
            assetNo: machine.assetNo,
            historyCardNo: machine.historyCardNo,
            Section: machine.section
          }
        });
      }

      // 5. Create components for the AMC
      for (const component of inwarrantyComponents) {
        await tx.amcComponent.create({
          data: {
            amcId: amcContract.originalId,
            assetNo: component.assetNo,
            componentNo: component.componentNo,
            serialNumber: component.serialNumber,
            WDate: component.warrantyDate,
            Section: component.section
          }
        });
      }

      // 6. Create service dates based on number of services
      const serviceDates = generateServiceDates(
        amcData.startDate,
        amcData.endDate,
        amcData.numberOfServices
      );

      for (const serviceDate of serviceDates) {
        await tx.amcServiceDate.create({
          data: {
            amcId: amcContract.originalId,
            serviceDate: serviceDate,
            serviceFlag: "P", // Pending
            serviceNumber: 0
          }
        });
      }

      // 7. Create history card for the conversion
      // Get the next history card number
      const lastHistoryCard = await tx.history_Sum.findFirst({
        orderBy: { Card_No: 'desc' }
      });
      const nextHistoryCardNo = lastHistoryCard ? lastHistoryCard.Card_No + 1 : 1;

      const historyCard = await tx.history_Sum.create({
        data: {
          Card_No: nextHistoryCardNo,
          CustID: inwarranty.customerId,
          CPerson: inwarranty.contactPerson,
          Phone: inwarranty.contactPhone,
          AddonFlag: "N",
          Source: "AMC",
          ToCardNo: 0
        }
      });

      // Create history sections
      await tx.historySection.create({
        data: {
          Card_No: historyCard.Card_No,
          Section: "A" // Conversion section
        }
      });

      // 8. Send email notification
      await sendEmail({
        to: customer.email || '<EMAIL>',
        subject: 'In-Warranty Converted to AMC',
        html: `
          <h1>In-Warranty Converted to AMC</h1>
          <p>Dear ${customer.name},</p>
          <p>Your in-warranty contract has been converted to an Annual Maintenance Contract (AMC).</p>
          <p><strong>AMC Details:</strong></p>
          <ul>
            <li>AMC ID: ${amcContract.originalId}</li>
            <li>Start Date: ${formatDate(amcData.startDate)}</li>
            <li>End Date: ${formatDate(amcData.endDate)}</li>
            <li>Amount: ₹${amcData.amount}</li>
          </ul>
          <p>Please contact us if you have any questions.</p>
          <p>Thank you,<br>KoolSoft Team</p>
        `,
        text: `
          In-Warranty Converted to AMC

          Dear ${customer.name},

          Your in-warranty contract has been converted to an Annual Maintenance Contract (AMC).

          AMC Details:
          - AMC ID: ${amcContract.originalId}
          - Start Date: ${formatDate(amcData.startDate)}
          - End Date: ${formatDate(amcData.endDate)}
          - Amount: ₹${amcData.amount}

          Please contact us if you have any questions.

          Thank you,
          KoolSoft Team
        `
      });

      return {
        success: true,
        message: 'In-warranty successfully converted to AMC',
        newId: amcContract.originalId,
        historyCardId: historyCard.Card_No
      };
    });
  } catch (error) {
    console.error('Error converting in-warranty to AMC:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

// Helper function to generate service dates based on frequency
function generateServiceDates(startDate: Date, endDate: Date, frequency: string): Date[] {
  const dates: Date[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Parse frequency (e.g., "M" for monthly, "Q" for quarterly)
  let monthInterval = 1;
  switch (frequency) {
    case 'M': monthInterval = 1; break;
    case 'Q': monthInterval = 3; break;
    case 'H': monthInterval = 6; break;
    case 'Y': monthInterval = 12; break;
    default: monthInterval = 1;
  }

  let currentDate = new Date(start);
  while (currentDate <= end) {
    dates.push(new Date(currentDate));
    currentDate.setMonth(currentDate.getMonth() + monthInterval);
  }

  return dates;
}

// Helper function to get the next history card number
async function getNextHistoryCardNumber(tx: any): Promise<number> {
  const lastCard = await tx.historyCard.findFirst({
    orderBy: { cardNo: 'desc' }
  });

  return lastCard ? lastCard.cardNo + 1 : 1;
}
```

**Instructions for Augment AI**:
1. Implement all module conversions as separate utility functions
2. Use database transactions to ensure data integrity
3. Create history cards to track conversions
4. Send email notifications for important conversions
5. Implement proper error handling and validation
6. Create UI components for conversion workflows

#### 5. Email Integration

When implementing email functionality:

```typescript
// lib/email/templates.ts
import { formatDate, formatCurrency } from '@/lib/utils/formatters';

export interface EmailTemplateData {
  [key: string]: any;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export function getAMCRenewalTemplate(data: {
  customerName: string;
  amcId: string;
  startDate: Date;
  endDate: Date;
  amount: number;
  machines: Array<{ model: string; serialNumber: string; location?: string }>;
}): EmailTemplate {
  const html = `
    <h1>AMC Renewal Reminder</h1>
    <p>Dear ${data.customerName},</p>
    <p>Your Annual Maintenance Contract (AMC) with ID ${data.amcId} is due for renewal.</p>
    <p><strong>AMC Details:</strong></p>
    <ul>
      <li>Start Date: ${formatDate(data.startDate)}</li>
      <li>End Date: ${formatDate(data.endDate)}</li>
      <li>Amount: ${formatCurrency(data.amount)}</li>
    </ul>
    <p><strong>Machines Covered:</strong></p>
    <ul>
      ${data.machines.map(machine => `
        <li>
          <strong>${machine.model}</strong><br>
          Serial Number: ${machine.serialNumber}<br>
          ${machine.location ? `Location: ${machine.location}` : ''}
        </li>
      `).join('')}
    </ul>
    <p>Please contact us to renew your AMC before it expires.</p>
    <p>Thank you,<br>KoolSoft Team</p>
  `;

  const text = `
    AMC Renewal Reminder

    Dear ${data.customerName},

    Your Annual Maintenance Contract (AMC) with ID ${data.amcId} is due for renewal.

    AMC Details:
    - Start Date: ${formatDate(data.startDate)}
    - End Date: ${formatDate(data.endDate)}
    - Amount: ${formatCurrency(data.amount)}

    Machines Covered:
    ${data.machines.map(machine => `
    * ${machine.model}
      Serial Number: ${machine.serialNumber}
      ${machine.location ? `Location: ${machine.location}` : ''}
    `).join('\n')}

    Please contact us to renew your AMC before it expires.

    Thank you,
    KoolSoft Team
  `;

  return {
    subject: `AMC Renewal Reminder - ${data.amcId}`,
    html,
    text
  };
}

// lib/email/scheduler.ts
import { prisma } from '@/lib/prisma';
import { sendEmail } from '@/lib/email/nodemailer';
import { getAMCRenewalTemplate } from '@/lib/email/templates';

export async function scheduleAMCRenewalEmails(daysBeforeExpiry: number = 30) {
  try {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + daysBeforeExpiry);

    // Find AMCs expiring in the specified number of days using legacy models
    const expiringAMCs = await prisma.amcContract.findMany({
      where: {
        endDate: {
          gte: new Date(),
          lte: expiryDate
        }
      }
    });

    let emailsSent = 0;

    // Send renewal emails
    for (const amc of expiringAMCs) {
      // Get customer information
      const customer = await prisma.legacyCustomer.findUnique({
        where: { id: amc.customerId }
      });

      if (!customer) {
        console.log(`Customer not found for AMC ${amc.originalId}`);
        continue;
      }

      // Get machines for this AMC
      const machines = await prisma.amcMachine.findMany({
        where: { amcId: amc.originalId }
      });

      // Get model information for each machine
      const machinesWithModels = await Promise.all(
        machines.map(async (machine) => {
          const model = await prisma.model.findUnique({
            where: { id: machine.modelId }
          });

          const product = model ? await prisma.product.findUnique({
            where: { id: model.productId }
          }) : null;

          return {
            model: model ? `${product?.name || ''} ${model.name}` : 'Unknown Model',
            serialNumber: machine.serialNumber || 'N/A',
            location: machine.location || 'N/A'
          };
        })
      );

      // Check if a renewal email was already sent recently
      // Using a simple check since we might not have an email log table yet
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      // In a real implementation, you would check your email log table
      // const recentEmail = await prisma.emailLog.findFirst({
      //   where: {
      //     subject: { contains: `AMC Renewal Reminder - ${amc.originalId}` },
      //     sentDate: { gte: sevenDaysAgo }
      //   }
      // });

      // For now, we'll assume no recent email was sent
      const recentEmail = false;

      if (!recentEmail) {
        const template = getAMCRenewalTemplate({
          customerName: customer.name || 'Valued Customer',
          amcId: amc.originalId.toString(),
          startDate: amc.startDate,
          endDate: amc.endDate,
          amount: Number(amc.amount),
          machines: machinesWithModels
        });

        await sendEmail({
          to: customer.email || '<EMAIL>',
          subject: template.subject,
          html: template.html,
          text: template.text
        });

        console.log(`Sent renewal email for AMC ${amc.originalId}`);
        emailsSent++;

        // In a real implementation, log the email
        // await prisma.emailLog.create({
        //   data: {
        //     to: customer.email || '<EMAIL>',
        //     subject: template.subject,
        //     body: template.html,
        //     sentDate: new Date(),
        //     sourceType: 'AMC',
        //     sourceId: amc.originalId
        //   }
        // });
      }
    }

    return {
      success: true,
      emailsSent: emailsSent
    };
  } catch (error) {
    console.error('Error scheduling AMC renewal emails:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
```

**Instructions for Augment AI**:
1. Create reusable email templates for different notifications
2. Implement email scheduling for automated notifications
3. Use proper HTML and text alternatives for emails
4. Log all sent emails for tracking
5. Implement retry logic for failed email sending
6. Create a preview interface for email templates:
   - Implement a template selection dropdown
   - Generate dynamic form fields based on template variables
   - Create a real-time preview renderer with mobile/desktop views
   - Add template editing capabilities
   - Ensure proper styling and accessibility

## Testing Instructions

### Unit Testing

For each utility function or helper:

```typescript
// Example: Testing a utility function
import { formatDate } from '@/lib/utils/date-utils';

describe('Date Utilities', () => {
  test('formatDate should format date correctly', () => {
    const date = new Date('2023-01-15');
    expect(formatDate(date, 'MM/dd/yyyy')).toBe('01/15/2023');
  });

  test('formatDate should handle invalid dates', () => {
    expect(formatDate(null, 'MM/dd/yyyy')).toBe('');
  });
});
```

**Instructions for Augment AI**:
1. Create test files for each utility module
2. Test both success and error cases
3. Mock external dependencies
4. Ensure high test coverage for critical functions

### Component Testing

For UI components:

```typescript
// Example: Testing a React component
import { render, screen, fireEvent } from '@testing-library/react';
import CustomerForm from '@/components/forms/CustomerForm';

describe('CustomerForm', () => {
  test('renders form fields correctly', () => {
    render(<CustomerForm />);
    expect(screen.getByLabelText(/name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/phone/i)).toBeInTheDocument();
  });

  test('validates required fields', async () => {
    render(<CustomerForm />);
    fireEvent.click(screen.getByRole('button', { name: /save/i }));
    expect(await screen.findByText(/name is required/i)).toBeInTheDocument();
  });
});
```

**Instructions for Augment AI**:
1. Test component rendering
2. Test user interactions
3. Test form validation
4. Test conditional rendering

### API Testing

For API routes:

```typescript
// Example: Testing an API route using legacy models
import { createMocks } from 'node-mocks-http';
import { GET, POST } from '@/app/api/customers/route';

jest.mock('@/lib/prisma', () => ({
  prisma: {
    legacyCustomer: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn()
    }
  }
}));

describe('/api/customers', () => {
  test('GET returns customers', async () => {
    const { req, res } = createMocks({ method: 'GET' });

    // Mock the legacy customer model
    prisma.legacyCustomer.findMany.mockResolvedValue([
      {
        id: 1,
        name: 'Test Customer',
        address: '123 Main St',
        phone1: '555-1234',
        email: '<EMAIL>'
      }
    ]);

    await GET(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual([
      {
        id: 1,
        name: 'Test Customer',
        address: '123 Main St',
        phone1: '555-1234',
        email: '<EMAIL>'
      }
    ]);
  });

  test('POST creates a customer', async () => {
    // Get the next available ID
    prisma.legacyCustomer.findMany.mockResolvedValue([
      { id: 1 }, { id: 2 }, { id: 3 }
    ]);

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        name: 'New Customer',
        address: '456 Oak St',
        phone1: '555-5678',
        email: '<EMAIL>'
      }
    });

    prisma.legacyCustomer.create.mockResolvedValue({
      id: 4,
      name: 'New Customer',
      address: '456 Oak St',
      phone1: '555-5678',
      email: '<EMAIL>'
    });

    await POST(req, res);

    expect(res._getStatusCode()).toBe(201);
    expect(JSON.parse(res._getData())).toEqual({
      id: 4,
      name: 'New Customer',
      address: '456 Oak St',
      phone1: '555-5678',
      email: '<EMAIL>'
    });
  });
});
```

**Instructions for Augment AI**:
1. Mock Prisma client for database operations
2. Test success and error responses
3. Test input validation
4. Test authentication and authorization

## Vercel Deployment Configuration

### Environment Setup

Create a `.env.local` file for local development:

```
DATABASE_URL="postgresql://username:password@localhost:5432/koolsoft"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

Configure Vercel environment variables:

1. DATABASE_URL: Connection string for Vercel Postgres
2. NEXTAUTH_SECRET: Secret key for JWT encryption
3. NEXTAUTH_URL: Production URL of the application

### Vercel Configuration

Create a `vercel.json` file:

```json
{
  "version": 2,
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "framework": "nextjs",
  "regions": ["iad1"],
  "env": {
    "DATABASE_URL": "@database_url",
    "NEXTAUTH_SECRET": "@nextauth_secret",
    "NEXTAUTH_URL": "@nextauth_url"
  }
}
```

**Instructions for Augment AI**:
1. Set up environment variables in Vercel dashboard
2. Configure build settings
3. Set up database connection
4. Configure domain settings

## Rollback Procedures

### Database Rollback

If database migration issues occur:

1. Keep backup of Access database before migration
2. Create database snapshots before major schema changes
3. Use Prisma migrations for schema versioning
4. Implement rollback scripts for each migration

```bash
# Example: Rolling back a Prisma migration
npx prisma migrate resolve --rolled-back "20230601120000_add_new_feature"
npx prisma migrate resolve --applied "20230531120000_previous_version"
```

### Code Rollback

If code deployment issues occur:

1. Use Vercel's deployment history to roll back to previous version
2. Maintain git tags for stable releases
3. Document dependencies between modules for partial rollbacks

```bash
# Example: Rolling back to a previous git tag
git checkout v1.0.0
```

**Instructions for Augment AI**:
1. Create git tags for each major milestone
2. Document dependencies between modules
3. Test rollback procedures during development
4. Create database backup scripts

## Performance Optimization Guidelines

### Frontend Optimization

1. Implement code splitting with dynamic imports
2. Use Next.js Image component for optimized images
3. Implement client-side caching with SWR or React Query
4. Minimize JavaScript bundle size

```typescript
// Example: Dynamic import for a large component
import dynamic from 'next/dynamic';

const ReportViewer = dynamic(() => import('@/components/reports/ReportViewer'), {
  loading: () => <p>Loading report viewer...</p>,
  ssr: false
});
```

### Backend Optimization

1. Optimize database queries with proper indexing
2. Implement API route caching
3. Use edge functions for global performance
4. Implement connection pooling for database

```typescript
// Example: Optimized Prisma query with select using legacy models
const customers = await prisma.legacyCustomer.findMany({
  where: {
    name: {
      contains: searchTerm
    }
  },
  select: {
    id: true,
    name: true,
    email: true,
    phone1: true,
    address: true,
    location: true
  },
  take: 10,
  skip: page * 10
});
```

**Instructions for Augment AI**:
1. Profile application performance during development
2. Optimize critical rendering paths
3. Implement lazy loading for non-critical components
4. Use pagination for large data sets
