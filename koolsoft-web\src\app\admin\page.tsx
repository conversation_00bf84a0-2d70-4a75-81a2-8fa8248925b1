'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import Link from 'next/link';
import { Users, Settings, FileText, ShieldCheck, RefreshCw, Activity } from 'lucide-react';

/**
 * Admin Dashboard Page
 *
 * This page is only accessible to admin users.
 * It provides access to admin-only features like user management.
 */
export default function AdminDashboardPage() {
  const [userCount, setUserCount] = useState<number | null>(null);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);

  // Fetch user count on component mount
  useEffect(() => {
    const fetchUserCount = async () => {
      setIsLoadingUsers(true);
      try {
        const response = await fetch('/api/admin/users?take=1');
        const data = await response.json();
        setUserCount(data.meta.total);
      } catch (error) {
        console.error('Error fetching user count:', error);
      } finally {
        setIsLoadingUsers(false);
      }
    };

    fetchUserCount();
  }, []);

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
      {/* Admin Cards */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-primary rounded-md p-3">
              <Users className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Total Users
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    {isLoadingUsers ? 'Loading...' : userCount}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/admin/users" className="font-medium text-primary hover:text-primary/80">
              Manage Users
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-success rounded-md p-3">
              <Settings className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  System Settings
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    Configuration
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/admin/settings" className="font-medium text-primary hover:text-primary/80">
              Manage Settings
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-info rounded-md p-3">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Audit Logs
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    System Activity
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/admin/activity-logs" className="font-medium text-primary hover:text-primary/80">
              View Logs
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-warning rounded-md p-3">
              <ShieldCheck className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  AMC Management
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    Contract Management
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/amc" className="font-medium text-primary hover:text-primary/80">
              Manage AMC Contracts
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-destructive rounded-md p-3">
              <RefreshCw className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Contract Status
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    Status Management
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/admin/amc-status" className="font-medium text-primary hover:text-primary/80">
              Update Contract Status
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 bg-secondary rounded-md p-3">
              <Activity className="h-6 w-6 text-white" />
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Email Templates
                </dt>
                <dd>
                  <div className="text-lg font-medium text-gray-900">
                    Email Management
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <Link href="/admin/email" className="font-medium text-primary hover:text-primary/80">
              Manage Email Templates
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
