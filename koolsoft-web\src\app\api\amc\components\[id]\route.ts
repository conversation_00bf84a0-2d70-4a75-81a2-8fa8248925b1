import { NextRequest, NextResponse } from 'next/server';
import { getAMCComponentRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';
import { updateComponentSchema } from '@/lib/validations/component.schema';

/**
 * GET /api/amc/components/[id]
 * Get a specific component
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcComponentRepository = getAMCComponentRepository();

      const component = await amcComponentRepository.findWithRelations(id);

      if (!component) {
        return NextResponse.json(
          { error: 'Component not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ component });
    } catch (error) {
      console.error('Error fetching component:', error);
      return NextResponse.json(
        { error: 'Failed to fetch component' },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/amc/components/[id]
 * Update a component
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateComponentSchema.parse(body);

      const amcComponentRepository = getAMCComponentRepository();

      // Check if component exists
      const existingComponent = await amcComponentRepository.findById(id);
      if (!existingComponent) {
        return NextResponse.json(
          { error: 'Component not found' },
          { status: 404 }
        );
      }

      // Check if serial number already exists (if being updated)
      if (validatedData.serialNumber && validatedData.serialNumber !== existingComponent.serialNumber) {
        const duplicateComponent = await amcComponentRepository.findBySerialNumber(validatedData.serialNumber);
        if (duplicateComponent && duplicateComponent.id !== id) {
          return NextResponse.json(
            { error: 'A component with this serial number already exists' },
            { status: 409 }
          );
        }
      }

      // Prepare update data
      const updateData: any = {};

      if (validatedData.machineId) updateData.machineId = validatedData.machineId;
      if (validatedData.componentNo !== undefined) updateData.componentNo = validatedData.componentNo;
      if (validatedData.serialNumber) updateData.serialNumber = validatedData.serialNumber;
      if (validatedData.warrantyDate !== undefined) updateData.warrantyDate = validatedData.warrantyDate;
      if (validatedData.section !== undefined) updateData.section = validatedData.section;
      if (validatedData.originalAmcId !== undefined) updateData.originalAmcId = validatedData.originalAmcId;
      if (validatedData.originalAssetNo !== undefined) updateData.originalAssetNo = validatedData.originalAssetNo;
      if (validatedData.originalComponentNo !== undefined) updateData.originalComponentNo = validatedData.originalComponentNo;

      // Update the component
      const updatedComponent = await amcComponentRepository.update(id, updateData);

      return NextResponse.json({
        message: 'Component updated successfully',
        component: updatedComponent,
      });
    } catch (error) {
      console.error('Error updating component:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to update component' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/amc/components/[id]
 * Delete a component
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcComponentRepository = getAMCComponentRepository();

      // Check if component exists
      const existingComponent = await amcComponentRepository.findById(id);
      if (!existingComponent) {
        return NextResponse.json(
          { error: 'Component not found' },
          { status: 404 }
        );
      }

      // Delete the component
      await amcComponentRepository.delete(id);

      return NextResponse.json({
        message: 'Component deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting component:', error);
      return NextResponse.json(
        { error: 'Failed to delete component' },
        { status: 500 }
      );
    }
  }
);
