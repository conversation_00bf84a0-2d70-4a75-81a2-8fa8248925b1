import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getWarrantyRepository } from '@/lib/repositories';

/**
 * GET /api/warranties/by-customer/[customerId]
 * Get warranties for a specific customer
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ customerId: string }> }
  ) => {
    try {
      const { customerId } = await params;
      const { searchParams } = new URL(request.url);
      const skip = parseInt(searchParams.get('skip') || '0');
      const take = parseInt(searchParams.get('take') || '10');
      const status = searchParams.get('status');
      
      const warrantyRepository = getWarrantyRepository();
      
      // Prepare filter
      const filter: Record<string, any> = { customerId };
      if (status) {
        filter.status = status;
      }
      
      // Get warranties for the customer
      const warranties = await warrantyRepository.findWithFilter(filter, skip, take);
      
      // Get total count for pagination
      const totalCount = await warrantyRepository.countWithFilter(filter);
      
      return NextResponse.json({
        warranties,
        pagination: {
          skip,
          take,
          total: totalCount,
          hasMore: skip + take < totalCount,
        },
        customerId,
        status: status || 'all',
      });
    } catch (error) {
      console.error('Error fetching customer warranties:', error);
      return NextResponse.json(
        { error: 'Failed to fetch customer warranties' },
        { status: 500 }
      );
    }
  }
);
