import { NextRequest, NextResponse } from 'next/server';
import { getAMCComponentRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';
import { componentSerialValidationSchema } from '@/lib/validations/component.schema';

/**
 * POST /api/amc/components/validate-serial
 * Validate component serial number uniqueness
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = componentSerialValidationSchema.parse(body);

      const amcComponentRepository = getAMCComponentRepository();

      // Check if serial number exists
      const existingComponent = await amcComponentRepository.findBySerialNumber(validatedData.serialNumber);

      // If excludeId is provided, ignore that specific component
      const isUnique = !existingComponent || 
        (validatedData.excludeId && existingComponent.id === validatedData.excludeId);

      return NextResponse.json({
        isUnique,
        message: isUnique 
          ? 'Serial number is available' 
          : 'Serial number already exists',
        existingComponent: isUnique ? null : {
          id: existingComponent?.id,
          serialNumber: existingComponent?.serialNumber,
          machineId: existingComponent?.machineId,
        },
      });
    } catch (error) {
      console.error('Error validating component serial number:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to validate serial number' },
        { status: 500 }
      );
    }
  }
);
