import { NextRequest, NextResponse } from 'next/server';
import { getWarrantyMachineRepository } from '@/lib/repositories';
import { warrantyMachineSchema, warrantyMachineFilterSchema } from '@/lib/validations/warranty.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { z } from 'zod';

/**
 * GET /api/warranties/machines
 * Get warranty machines with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const queryParams = {
        warrantyId: searchParams.get('warrantyId') || undefined,
        productId: searchParams.get('productId') || undefined,
        modelId: searchParams.get('modelId') || undefined,
        brandId: searchParams.get('brandId') || undefined,
        serialNumber: searchParams.get('serialNumber') || undefined,
        location: searchParams.get('location') || undefined,
        search: searchParams.get('search') || undefined,
        skip: parseInt(searchParams.get('skip') || '0'),
        take: parseInt(searchParams.get('take') || '10'),
        sortBy: searchParams.get('sortBy') || 'serialNumber',
        sortOrder: searchParams.get('sortOrder') || 'asc',
      };

      try {
        const validatedParams = warrantyMachineFilterSchema.parse(queryParams);
        
        const warrantyMachineRepository = getWarrantyMachineRepository();
        
        // Build filter object
        const filter: Record<string, any> = {};
        
        if (validatedParams.warrantyId) {
          filter.warrantyId = validatedParams.warrantyId;
        }
        
        if (validatedParams.productId) {
          filter.productId = validatedParams.productId;
        }
        
        if (validatedParams.modelId) {
          filter.modelId = validatedParams.modelId;
        }
        
        if (validatedParams.brandId) {
          filter.brandId = validatedParams.brandId;
        }
        
        if (validatedParams.serialNumber) {
          filter.serialNumber = {
            contains: validatedParams.serialNumber,
            mode: 'insensitive',
          };
        }
        
        if (validatedParams.location) {
          filter.location = {
            contains: validatedParams.location,
            mode: 'insensitive',
          };
        }
        
        if (validatedParams.search) {
          filter.OR = [
            {
              serialNumber: {
                contains: validatedParams.search,
                mode: 'insensitive',
              },
            },
            {
              location: {
                contains: validatedParams.search,
                mode: 'insensitive',
              },
            },
            {
              product: {
                name: {
                  contains: validatedParams.search,
                  mode: 'insensitive',
                },
              },
            },
            {
              model: {
                name: {
                  contains: validatedParams.search,
                  mode: 'insensitive',
                },
              },
            },
            {
              brand: {
                name: {
                  contains: validatedParams.search,
                  mode: 'insensitive',
                },
              },
            },
          ];
        }
        
        // Build order by object
        const orderBy: Record<string, any> = {};
        if (validatedParams.sortBy.includes('.')) {
          const [relation, field] = validatedParams.sortBy.split('.');
          orderBy[relation] = { [field]: validatedParams.sortOrder };
        } else {
          orderBy[validatedParams.sortBy] = validatedParams.sortOrder;
        }
        
        // Get warranty machines with filter
        const machines = await warrantyMachineRepository.findWithFilter(
          filter,
          validatedParams.skip,
          validatedParams.take,
          orderBy
        );
        
        // Get total count for pagination
        const totalCount = await warrantyMachineRepository.countWithFilter(filter);
        
        return NextResponse.json({
          machines,
          pagination: {
            skip: validatedParams.skip,
            take: validatedParams.take,
            total: totalCount,
            hasMore: validatedParams.skip + validatedParams.take < totalCount,
          },
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Invalid query parameters', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error fetching warranty machines:', error);
      return NextResponse.json(
        { error: 'Failed to fetch warranty machines' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/warranties/machines
 * Create a new warranty machine
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = warrantyMachineSchema.parse(body);

        const warrantyMachineRepository = getWarrantyMachineRepository();

        // Create warranty machine with validation
        const machine = await warrantyMachineRepository.createWithValidation(validatedData);

        return NextResponse.json(machine, { status: 201 });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error creating warranty machine:', error);
      
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          return NextResponse.json(
            { error: 'A machine with this serial number already exists in warranty' },
            { status: 409 }
          );
        }
        if (error.code === 'P2003') {
          return NextResponse.json(
            { error: 'Referenced warranty, product, model, or brand does not exist' },
            { status: 400 }
          );
        }
      }
      
      if (error instanceof Error && error.message.includes('already exists')) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to create warranty machine' },
        { status: 500 }
      );
    }
  }
);
