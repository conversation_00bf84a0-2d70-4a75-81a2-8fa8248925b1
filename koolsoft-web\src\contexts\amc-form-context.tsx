'use client';

import React, { createContext, useContext, useReducer, useEffect, useMemo, useCallback } from 'react';
import { z } from 'zod';
import { createAMCContractSchema } from '@/lib/validations/amc-contract.schema';

// Define the form data type
export type AMCFormData = z.infer<typeof createAMCContractSchema>;

// Define the form steps
export enum AMCFormStep {
  CUSTOMER_DETAILS = 1,
  CONTRACT_DETAILS = 2,
  MACHINE_MANAGEMENT = 3,
  SERVICE_SCHEDULING = 4,
  PAYMENT_DIVISION = 5,
  REVIEW_SUBMIT = 6,
}

// Define the form state
export interface AMCFormState {
  currentStep: AMCFormStep;
  formData: Partial<AMCFormData>;
  isValid: boolean;
  stepValidation: Record<AMCFormStep, boolean>;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

// Define form actions
export type AMCFormAction =
  | { type: 'SET_STEP'; payload: AMCFormStep }
  | { type: 'UPDATE_FORM_DATA'; payload: Partial<AMCFormData> }
  | { type: 'SET_STEP_VALIDATION'; payload: { step: AMCFormStep; isValid: boolean } }
  | { type: 'SET_SUBMITTING'; payload: boolean }
  | { type: 'SET_ERRORS'; payload: Record<string, string> }
  | { type: 'RESET_FORM' }
  | { type: 'LOAD_FROM_STORAGE'; payload: Partial<AMCFormState> };

// Initial state
const initialState: AMCFormState = {
  currentStep: AMCFormStep.CUSTOMER_DETAILS,
  formData: {
    status: 'ACTIVE',
    machines: [],
    serviceDates: [],
    payments: [],
    divisions: [],
  },
  isValid: false,
  stepValidation: {
    [AMCFormStep.CUSTOMER_DETAILS]: false,
    [AMCFormStep.CONTRACT_DETAILS]: false,
    [AMCFormStep.MACHINE_MANAGEMENT]: false,
    [AMCFormStep.SERVICE_SCHEDULING]: false,
    [AMCFormStep.PAYMENT_DIVISION]: false,
    [AMCFormStep.REVIEW_SUBMIT]: false,
  },
  isSubmitting: false,
  errors: {},
};

// Form reducer
function amcFormReducer(state: AMCFormState, action: AMCFormAction): AMCFormState {
  switch (action.type) {
    case 'SET_STEP':
      return {
        ...state,
        currentStep: action.payload,
      };

    case 'UPDATE_FORM_DATA':
      const updatedFormData = {
        ...state.formData,
        ...action.payload,
      };
      return {
        ...state,
        formData: updatedFormData,
      };

    case 'SET_STEP_VALIDATION':
      return {
        ...state,
        stepValidation: {
          ...state.stepValidation,
          [action.payload.step]: action.payload.isValid,
        },
      };

    case 'SET_SUBMITTING':
      return {
        ...state,
        isSubmitting: action.payload,
      };

    case 'SET_ERRORS':
      return {
        ...state,
        errors: action.payload,
      };

    case 'RESET_FORM':
      return {
        ...initialState,
      };

    case 'LOAD_FROM_STORAGE':
      return {
        ...state,
        ...action.payload,
      };

    default:
      return state;
  }
}

// Context type
interface AMCFormContextType {
  state: AMCFormState;
  dispatch: React.Dispatch<AMCFormAction>;
  goToStep: (step: AMCFormStep) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  updateFormData: (data: Partial<AMCFormData>) => void;
  validateStep: (step: AMCFormStep, data: Partial<AMCFormData>) => boolean;
  canProceedToStep: (step: AMCFormStep) => boolean;
  saveToStorage: () => void;
  loadFromStorage: () => void;
  clearStorage: () => void;
}

// Create context
const AMCFormContext = createContext<AMCFormContextType | undefined>(undefined);

// Storage key
const STORAGE_KEY = 'amc-form-data';

// Provider component
export function AMCFormProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(amcFormReducer, initialState);

  // Load from storage on mount
  useEffect(() => {
    loadFromStorage();
  }, []);

  // Save to storage whenever form data changes (debounced)
  useEffect(() => {
    if (Object.keys(state.formData).length > 1) { // Only save if there's actual data
      const timeoutId = setTimeout(() => {
        saveToStorage();
      }, 500); // Debounce saves by 500ms

      return () => clearTimeout(timeoutId);
    }
  }, [state.formData]);

  const goToStep = useCallback((step: AMCFormStep) => {
    dispatch({ type: 'SET_STEP', payload: step });
  }, []);

  const goToNextStep = useCallback(() => {
    const nextStep = Math.min(state.currentStep + 1, AMCFormStep.REVIEW_SUBMIT) as AMCFormStep;
    dispatch({ type: 'SET_STEP', payload: nextStep });
  }, [state.currentStep]);

  const goToPreviousStep = useCallback(() => {
    const prevStep = Math.max(state.currentStep - 1, AMCFormStep.CUSTOMER_DETAILS) as AMCFormStep;
    dispatch({ type: 'SET_STEP', payload: prevStep });
  }, [state.currentStep]);

  const updateFormData = useCallback((data: Partial<AMCFormData>) => {
    dispatch({ type: 'UPDATE_FORM_DATA', payload: data });
  }, []);

  const validateStep = useCallback((step: AMCFormStep, data: Partial<AMCFormData>): boolean => {
    try {
      switch (step) {
        case AMCFormStep.CUSTOMER_DETAILS:
          return !!(data.customerId);

        case AMCFormStep.CONTRACT_DETAILS:
          return !!(data.startDate && data.endDate && data.amount);

        case AMCFormStep.MACHINE_MANAGEMENT:
          return true; // Optional step

        case AMCFormStep.SERVICE_SCHEDULING:
          // Service dates are optional, but if provided, should be valid
          if (data.serviceDates && data.serviceDates.length > 0) {
            return data.serviceDates.every(sd => sd.scheduledDate);
          }
          return true; // Optional step

        case AMCFormStep.PAYMENT_DIVISION:
          // Validate divisions if any are assigned
          if (data.divisions && data.divisions.length > 0) {
            const totalPercentage = data.divisions.reduce((sum, div) => sum + div.percentage, 0);
            const primaryCount = data.divisions.filter(div => div.isPrimary).length;

            // Check percentage sum and primary division count
            return Math.abs(totalPercentage - 100) < 0.01 && primaryCount <= 1;
          }
          return true; // Optional step

        case AMCFormStep.REVIEW_SUBMIT:
          // Validate the entire form
          createAMCContractSchema.parse(data);
          return true;

        default:
          return false;
      }
    } catch (error) {
      return false;
    }
  }, []);

  const canProceedToStep = useCallback((step: AMCFormStep): boolean => {
    // Check if all previous steps are valid
    for (let i = AMCFormStep.CUSTOMER_DETAILS; i < step; i++) {
      if (!state.stepValidation[i as AMCFormStep]) {
        return false;
      }
    }
    return true;
  }, [state.stepValidation]);

  const saveToStorage = useCallback(() => {
    try {
      const dataToSave = {
        formData: state.formData,
        currentStep: state.currentStep,
        stepValidation: state.stepValidation,
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Failed to save form data to storage:', error);
    }
  }, [state.formData, state.currentStep, state.stepValidation]);

  const loadFromStorage = useCallback(() => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        dispatch({ type: 'LOAD_FROM_STORAGE', payload: parsedData });
      }
    } catch (error) {
      console.error('Failed to load form data from storage:', error);
    }
  }, []);

  const clearStorage = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear form data from storage:', error);
    }
  }, []);

  const contextValue: AMCFormContextType = useMemo(() => ({
    state,
    dispatch,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    updateFormData,
    validateStep,
    canProceedToStep,
    saveToStorage,
    loadFromStorage,
    clearStorage,
  }), [
    state,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    updateFormData,
    validateStep,
    canProceedToStep,
    saveToStorage,
    loadFromStorage,
    clearStorage,
  ]);

  return (
    <AMCFormContext.Provider value={contextValue}>
      {children}
    </AMCFormContext.Provider>
  );
}

// Hook to use the context
export function useAMCForm() {
  const context = useContext(AMCFormContext);
  if (context === undefined) {
    throw new Error('useAMCForm must be used within an AMCFormProvider');
  }
  return context;
}
