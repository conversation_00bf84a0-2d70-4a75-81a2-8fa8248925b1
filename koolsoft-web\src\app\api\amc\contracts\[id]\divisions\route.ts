import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCContractRepository } from '@/lib/repositories';
import { PrismaClient } from '@/generated/prisma';
import { z } from 'zod';

const prisma = new PrismaClient();

/**
 * Division assignment schema
 */
const divisionAssignmentSchema = z.object({
  divisionId: z.string().uuid({ message: 'Valid division ID is required' }),
  percentage: z.number().min(0).max(100, { message: 'Percentage must be between 0 and 100' }),
  isPrimary: z.boolean().default(false),
});

const divisionAssignmentsSchema = z.array(divisionAssignmentSchema).refine(
  (divisions) => {
    // Ensure percentages sum to 100
    const totalPercentage = divisions.reduce((sum, div) => sum + div.percentage, 0);
    return Math.abs(totalPercentage - 100) < 0.01; // Allow for small floating point errors
  },
  {
    message: 'Division percentages must sum to 100%',
  }
).refine(
  (divisions) => {
    // Ensure only one primary division
    const primaryCount = divisions.filter(div => div.isPrimary).length;
    return primaryCount <= 1;
  },
  {
    message: 'Only one division can be marked as primary',
  }
);

/**
 * GET /api/amc/contracts/[id]/divisions
 * Get divisions for a specific AMC contract
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      // Verify contract exists
      const amcContractRepository = getAMCContractRepository();
      const contract = await amcContractRepository.findById(id);

      if (!contract) {
        return NextResponse.json(
          { error: 'AMC contract not found' },
          { status: 404 }
        );
      }

      // Get divisions for this contract
      const amcDivisions = await prisma.amc_divisions.findMany({
        where: { amcContractId: id },
        include: {
          division: true,
        },
        orderBy: [
          { isPrimary: 'desc' }, // Primary division first
          { percentage: 'desc' }, // Then by percentage
        ],
      });

      // Get all available divisions
      const allDivisions = await prisma.divisions.findMany({
        where: { isActive: true },
        orderBy: { name: 'asc' },
      });

      // Format divisions for response
      const divisions = amcDivisions.map(amcDivision => ({
        id: amcDivision.id,
        divisionId: amcDivision.divisionId,
        name: amcDivision.division.name,
        description: amcDivision.division.description,
        percentage: amcDivision.percentage,
        isPrimary: amcDivision.isPrimary,
        originalAmcId: amcDivision.originalAmcId,
        originalDivisionId: amcDivision.originalDivisionId,
      }));

      // Calculate statistics
      const totalPercentage = divisions.reduce((sum, div) => sum + div.percentage, 0);
      const primaryDivision = divisions.find(div => div.isPrimary);

      return NextResponse.json({
        contract: {
          id: contract.id,
          customer: contract.customer,
        },
        divisions,
        allDivisions,
        statistics: {
          totalDivisions: divisions.length,
          availableDivisions: allDivisions.length,
          totalPercentage,
          isComplete: Math.abs(totalPercentage - 100) < 0.01,
          primaryDivision: primaryDivision?.name || null,
        },
      });
    } catch (error) {
      console.error('Error fetching contract divisions:', error);
      return NextResponse.json(
        { error: 'Failed to fetch contract divisions' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/amc/contracts/[id]/divisions
 * Update divisions for a specific AMC contract
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = divisionAssignmentsSchema.parse(body.divisions || []);

      // Verify contract exists
      const amcContractRepository = getAMCContractRepository();
      const contract = await amcContractRepository.findById(id);

      if (!contract) {
        return NextResponse.json(
          { error: 'AMC contract not found' },
          { status: 404 }
        );
      }

      // Verify all divisions exist
      const divisionIds = validatedData.map(d => d.divisionId);
      const existingDivisions = await prisma.divisions.findMany({
        where: {
          id: { in: divisionIds },
          isActive: true,
        },
      });

      if (existingDivisions.length !== divisionIds.length) {
        return NextResponse.json(
          { error: 'One or more divisions not found or inactive' },
          { status: 400 }
        );
      }

      // Update divisions in a transaction
      await prisma.$transaction(async (tx) => {
        // Delete existing divisions for this contract
        await tx.amc_divisions.deleteMany({
          where: { amcContractId: id },
        });

        // Create new division assignments
        if (validatedData.length > 0) {
          await tx.amc_divisions.createMany({
            data: validatedData.map(division => ({
              amcContractId: id,
              divisionId: division.divisionId,
              percentage: division.percentage,
              isPrimary: division.isPrimary,
            })),
          });
        }
      });

      // Fetch updated divisions
      const updatedDivisions = await prisma.amc_divisions.findMany({
        where: { amcContractId: id },
        include: {
          division: true,
        },
        orderBy: [
          { isPrimary: 'desc' },
          { percentage: 'desc' },
        ],
      });

      const formattedDivisions = updatedDivisions.map(amcDivision => ({
        id: amcDivision.id,
        divisionId: amcDivision.divisionId,
        name: amcDivision.division.name,
        description: amcDivision.division.description,
        percentage: amcDivision.percentage,
        isPrimary: amcDivision.isPrimary,
      }));

      return NextResponse.json({
        message: 'Divisions updated successfully',
        divisions: formattedDivisions,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation failed', details: error.errors },
          { status: 400 }
        );
      }

      console.error('Error updating contract divisions:', error);
      return NextResponse.json(
        { error: 'Failed to update contract divisions' },
        { status: 500 }
      );
    }
  }
);
