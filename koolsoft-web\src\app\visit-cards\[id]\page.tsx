'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import {
  Home,
  FileText,
  Calendar,
  Download,
  Eye,
  Edit,
  Trash,
  RefreshCw,
  User,
  Building,
  Phone,
  Mail,
  Clock,
  FileCheck,
  MessageSquare
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

/**
 * Visit Card Detail Page
 *
 * This page displays detailed information about a specific visit card.
 */
export default function VisitCardDetailPage() {
  const router = useRouter();
  const params = useParams();
  const visitCardId = params.id as string;

  const [visitCard, setVisitCard] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch visit card data
  useEffect(() => {
    const fetchVisitCard = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/visit-cards/${visitCardId}`, {
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error('Failed to fetch visit card');
        }

        const data = await response.json();
        setVisitCard(data);
      } catch (error: any) {
        console.error('Error fetching visit card:', error);
        setError(error.message || 'Failed to load visit card');
        toast({
          title: 'Error',
          description: 'Failed to load visit card. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchVisitCard();
  }, [visitCardId]);

  // Handle refresh
  const handleRefresh = () => {
    setIsLoading(true);
    setError(null);

    fetch(`/api/visit-cards/${visitCardId}`, {
      credentials: 'include'
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to refresh visit card');
        }
        return response.json();
      })
      .then(data => {
        setVisitCard(data);
        toast({
          title: 'Success',
          description: 'Visit card refreshed successfully'
        });
      })
      .catch(error => {
        console.error('Error refreshing visit card:', error);
        setError(error.message || 'Failed to refresh visit card');
        toast({
          title: 'Error',
          description: 'Failed to refresh visit card. Please try again.',
          variant: 'destructive'
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      setIsDeleting(true);

      const response = await fetch(`/api/visit-cards/${visitCardId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to delete visit card');
      }

      toast({
        title: 'Success',
        description: 'Visit card deleted successfully'
      });

      // Redirect to the visit cards list
      router.push('/visit-cards');
    } catch (error: any) {
      console.error('Error deleting visit card:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete visit card. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'PENDING':
        return <Badge className="bg-yellow-500 text-white">Pending</Badge>;
      case 'COMPLETED':
        return <Badge className="bg-green-500 text-white">Completed</Badge>;
      case 'CANCELLED':
        return <Badge className="bg-red-500 text-white">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-500 text-white">Unknown</Badge>;
    }
  };

  // Render loading state
  if (isLoading && !visitCard) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-64 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render error state
  if (error || !visitCard) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 flex flex-row items-center justify-between">
            <div>
              <CardTitle>Visit Card Details</CardTitle>
              <CardDescription>
                Error loading visit card information
              </CardDescription>
            </div>
            <Button variant="outline" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardHeader>
          <CardContent>
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error || 'Visit card not found'}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Action buttons
  const visitCardActions = (
    <div className="flex space-x-2">
      <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isLoading}>
        <RefreshCw className="h-4 w-4 mr-2" />
        Refresh
      </Button>
      <Button variant="outline" size="sm" asChild>
        <Link href={`/visit-cards/${visitCardId}/edit`}>
          <Edit className="h-4 w-4 mr-2" />
          Edit
        </Link>
      </Button>
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button variant="outline" size="sm" className="text-red-600">
            <Trash className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              visit card and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 text-white hover:bg-red-700"
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );

  return (
    <div className="space-y-6">

      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <div className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Visit Card Details</CardTitle>
              <CardDescription className="text-gray-100">
                Detailed information about the visit card
              </CardDescription>
            </div>
            {visitCardActions}
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* File Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">File Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">File Name</p>
                    <p>{visitCard.filePath.split('/').pop()}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Upload Date</p>
                    <p>{formatDate(visitCard.uploadDate)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <FileCheck className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Status</p>
                    <div className="mt-1">{getStatusBadge(visitCard.status)}</div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(visitCard.filePath, '_blank')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(visitCard.filePath, '_blank')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </CardFooter>
            </Card>

            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Customer Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <Building className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Customer</p>
                    <Link href={`/customers/${visitCard.customerId}`} className="text-blue-600 hover:underline">
                      {visitCard.customer?.name || 'Unknown Customer'}
                    </Link>
                  </div>
                </div>
                <div className="flex items-center">
                  <User className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Contact Person</p>
                    <p>{visitCard.contactPerson || 'Not specified'}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Contact Phone</p>
                    <p>{visitCard.contactPhone || 'Not specified'}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 mr-3 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Contact Email</p>
                    <p>{visitCard.contactEmail || 'Not specified'}</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="w-full"
                >
                  <Link href={`/customers/${visitCard.customerId}`}>
                    <Building className="h-4 w-4 mr-2" />
                    View Customer
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Visit Details */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="text-lg">Visit Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 mr-3 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Visit Date</p>
                      <p>{formatDate(visitCard.visitDate)}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <User className="h-5 w-5 mr-3 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Created By</p>
                      <p>{visitCard.user?.name || 'Unknown User'}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 mr-3 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Follow-up Date</p>
                      <p>{visitCard.followUpDate ? formatDate(visitCard.followUpDate) : 'Not specified'}</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <p className="text-sm font-medium mb-2">Purpose</p>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {visitCard.purpose || 'No purpose specified'}
                  </div>
                </div>

                <div className="mt-4">
                  <p className="text-sm font-medium mb-2">Notes</p>
                  <div className="p-3 bg-gray-50 rounded-md min-h-[100px]">
                    {visitCard.notes || 'No notes available'}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
