import { NextRequest, NextResponse } from 'next/server';
import { getVisitCardRepository } from '@/lib/repositories';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';
import { unlink } from 'fs/promises';
import { join } from 'path';

/**
 * Visit card update schema
 */
const updateVisitCardSchema = z.object({
  customerId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
  visitDate: z.coerce.date().optional(),
  purpose: z.string().optional(),
  notes: z.string().optional(),
  followUpDate: z.coerce.date().optional(),
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']).optional(),
  contactPerson: z.string().optional(),
  contactPhone: z.string().optional(),
  contactEmail: z.string().email().optional().or(z.literal('')),
  filePath: z.string().optional(),
});

/**
 * GET /api/visit-cards/[id]
 * Get a specific visit card by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    const visitCardRepository = getVisitCardRepository();

    // Get visit card with all related data
    const visitCard = await visitCardRepository.findWithRelations(id);

    if (!visitCard) {
      return NextResponse.json(
        { error: 'Visit card not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(visitCard);
  } catch (error) {
    console.error('Error fetching visit card:', error);
    return NextResponse.json(
      { error: 'Failed to fetch visit card' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/visit-cards/[id]
 * Update a specific visit card
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const body = await request.json();

    // Validate request body
    const validatedData = updateVisitCardSchema.parse(body);

    const visitCardRepository = getVisitCardRepository();

    // Check if visit card exists
    const existingVisitCard = await visitCardRepository.findById(id);

    if (!existingVisitCard) {
      return NextResponse.json(
        { error: 'Visit card not found' },
        { status: 404 }
      );
    }

    // Update visit card
    const updatedVisitCard = await visitCardRepository.update(id, validatedData);

    return NextResponse.json(updatedVisitCard);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating visit card:', error);
    return NextResponse.json(
      { error: 'Failed to update visit card' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/visit-cards/[id]
 * Delete a specific visit card
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    const visitCardRepository = getVisitCardRepository();

    // Check if visit card exists
    const existingVisitCard = await visitCardRepository.findById(id);

    if (!existingVisitCard) {
      return NextResponse.json(
        { error: 'Visit card not found' },
        { status: 404 }
      );
    }

    // Try to delete the file if it exists
    try {
      if (existingVisitCard.filePath) {
        const filePath = existingVisitCard.filePath.replace(/^\//, ''); // Remove leading slash
        const fullPath = join(process.cwd(), 'public', filePath);
        await unlink(fullPath);
      }
    } catch (fileError) {
      console.error('Error deleting file:', fileError);
      // Continue with deleting the database record even if file deletion fails
    }

    // Delete visit card
    await visitCardRepository.delete(id);

    return NextResponse.json(
      { message: 'Visit card deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting visit card:', error);
    return NextResponse.json(
      { error: 'Failed to delete visit card' },
      { status: 500 }
    );
  }
}
