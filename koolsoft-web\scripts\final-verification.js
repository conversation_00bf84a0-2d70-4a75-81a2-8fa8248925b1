const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalVerification() {
  try {
    console.log('=== FINAL VERIFICATION OF DUPLICATE FIXES ===\n');

    // Check specific customer
    const customerId = '82c3c4ac-3f0c-4aee-b0bb-a5330ba490e9';
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        contacts: true,
        amcContracts: {
          include: {
            payments: true
          }
        }
      }
    });

    if (customer) {
      console.log(`✅ Customer: ${customer.name}`);
      console.log(`   Contacts: ${customer.contacts.length}`);
      console.log(`   AMC Contracts: ${customer.amcContracts.length}`);
      
      // Check for contact duplicates
      const contactNames = customer.contacts.map(c => c.name);
      const uniqueContactNames = [...new Set(contactNames)];
      
      if (contactNames.length === uniqueContactNames.length) {
        console.log('   ✅ No duplicate contact names');
      } else {
        console.log('   ❌ Still has duplicate contact names');
      }

      // Check AMC payments for duplicates
      let totalPayments = 0;
      customer.amcContracts.forEach(contract => {
        totalPayments += contract.payments.length;
      });
      console.log(`   Total AMC Payments: ${totalPayments}`);
    }

    // Global verification
    console.log('\n=== GLOBAL VERIFICATION ===');
    
    // Check for any remaining customer duplicates
    const customerDuplicates = await prisma.$queryRaw`
      SELECT name, COUNT(*) as count
      FROM customers 
      GROUP BY name 
      HAVING COUNT(*) > 1
    `;
    
    console.log(`Customer duplicates: ${customerDuplicates.length}`);

    // Check for any remaining contact duplicates
    const contactDuplicates = await prisma.$queryRaw`
      SELECT customer_id, name, phone, COUNT(*) as count
      FROM contacts 
      GROUP BY customer_id, name, phone
      HAVING COUNT(*) > 1
    `;
    
    console.log(`Contact duplicates: ${contactDuplicates.length}`);

    // Check for any remaining payment duplicates
    const paymentDuplicates = await prisma.$queryRaw`
      SELECT amc_contract_id, receipt_no, payment_date, amount, COUNT(*) as count
      FROM amc_payments 
      GROUP BY amc_contract_id, receipt_no, payment_date, amount
      HAVING COUNT(*) > 1
    `;
    
    console.log(`Payment duplicates: ${paymentDuplicates.length}`);

    // Final counts
    const totalCustomers = await prisma.customer.count();
    const totalContacts = await prisma.contact.count();
    const totalPayments = await prisma.amc_payments.count();
    
    console.log('\n=== FINAL COUNTS ===');
    console.log(`Total customers: ${totalCustomers}`);
    console.log(`Total contacts: ${totalContacts}`);
    console.log(`Total AMC payments: ${totalPayments}`);

    if (customerDuplicates.length === 0 && contactDuplicates.length === 0 && paymentDuplicates.length === 0) {
      console.log('\n🎉 ALL DUPLICATE ISSUES RESOLVED SUCCESSFULLY!');
      console.log('✅ Customer edit page should now display unique records only');
      console.log('✅ AMC payments page should now display unique records only');
    } else {
      console.log('\n⚠️ Some duplicates may still exist');
    }

  } catch (error) {
    console.error('Error in verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

finalVerification();
