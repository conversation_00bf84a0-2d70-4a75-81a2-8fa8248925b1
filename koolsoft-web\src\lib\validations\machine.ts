import { z } from 'zod';

export const machineFormSchema = z.object({
  amcContractId: z.string().uuid('Invalid AMC contract ID'),
  productId: z.string().optional(),
  modelId: z.string().optional(),
  brandId: z.string().optional(),
  serialNumber: z.string()
    .min(1, 'Serial number is required')
    .max(50, 'Serial number must be less than 50 characters'),
  location: z.string()
    .min(1, 'Location is required')
    .max(100, 'Location must be less than 100 characters'),
  installationDate: z.coerce.date().optional(),
  tonnage: z.number()
    .min(0, 'Tonnage must be positive')
    .optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).default('ACTIVE'),
  originalAmcId: z.number().int().optional(),
  originalAssetNo: z.number().int().optional(),
});

export const updateMachineFormSchema = machineFormSchema.partial().omit(['amcContractId']);

export type MachineFormData = z.infer<typeof machineFormSchema>;
export type UpdateMachineFormData = z.infer<typeof updateMachineFormSchema>;

// Validation for serial number uniqueness
export const validateSerialNumberUniqueness = async (
  serialNumber: string,
  excludeId?: string
): Promise<boolean> => {
  try {
    const response = await fetch(
      `/api/amc/machines/validate-serial?serialNumber=${encodeURIComponent(serialNumber)}${
        excludeId ? `&excludeId=${excludeId}` : ''
      }`,
      {
        credentials: 'include',
      }
    );

    if (!response.ok) {
      return false;
    }

    const result = await response.json();
    return result.isUnique;
  } catch (error) {
    console.error('Error validating serial number:', error);
    return false;
  }
};

// Enhanced schema with async validation
export const createMachineFormSchemaWithValidation = (excludeId?: string) =>
  machineFormSchema.refine(
    async (data) => {
      if (!data.serialNumber) return true;
      return await validateSerialNumberUniqueness(data.serialNumber, excludeId);
    },
    {
      message: 'Serial number already exists',
      path: ['serialNumber'],
    }
  );
