'use client';

import React from 'react';
import { AMCForm } from '@/components/amc/amc-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

/**
 * New AMC Contract Page
 * 
 * This page provides a multi-step form for creating new AMC contracts.
 * It follows the UI standards and implements the requirements from Task 7.3.
 */
export default function NewAMCPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle>Create New AMC Contract</CardTitle>
            <CardDescription className="text-gray-100">
              Follow the step-by-step process to create a comprehensive AMC contract
            </CardDescription>
          </div>
          <Button asChild variant="secondary">
            <Link href="/amc">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to AMC List
            </Link>
          </Button>
        </CardHeader>
      </Card>

      {/* AMC Form */}
      <AMCForm />
    </div>
  );
}
