'use client';

import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { usePayments, CreatePaymentData, UpdatePaymentData, Payment } from '@/lib/hooks/usePayments';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { showErrorToast } from '@/lib/toast';

const paymentFormSchema = z.object({
  amcContractId: z.string().uuid({ message: 'Valid AMC contract ID is required' }),
  paymentDate: z.date({ message: 'Payment date is required' }),
  amount: z.number({
    required_error: 'Amount is required',
    invalid_type_error: 'Amount must be a number'
  }).positive({ message: 'Amount must be greater than 0' }),
  paymentMode: z.enum(['CASH', 'CHEQUE', 'BANK_TRANSFER', 'ONLINE'], {
    errorMap: () => ({ message: 'Please select a payment mode' })
  }),
  receiptNo: z.string().optional(),
  particulars: z.string().optional(),
});

type PaymentFormData = z.infer<typeof paymentFormSchema>;

interface PaymentFormProps {
  amcContractId?: string;
  payment?: Payment;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function PaymentForm({
  amcContractId,
  payment,
  onSuccess,
  onCancel,
}: PaymentFormProps) {
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);
  const { createPayment, updatePayment, isCreating, isUpdating, generateReceiptNumber, validateReceiptNumber } = usePayments();

  const isEditing = !!payment;
  const isLoading = isCreating || isUpdating;

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      amcContractId: amcContractId || payment?.amcContractId || '',
      paymentDate: payment ? new Date(payment.paymentDate) : new Date(),
      amount: payment?.amount || 0,
      paymentMode: (payment?.paymentMode as any) || 'CASH',
      receiptNo: payment?.receiptNo || '',
      particulars: payment?.particulars || '',
    },
  });

  const { register, handleSubmit, formState: { errors }, setValue, watch, setError, clearErrors } = form;
  const watchedReceiptNo = watch('receiptNo');

  // Generate receipt number
  const handleGenerateReceiptNumber = async () => {
    try {
      setIsGeneratingReceipt(true);
      const receiptNumber = await generateReceiptNumber();
      setValue('receiptNo', receiptNumber);
      clearErrors('receiptNo');
    } catch (error) {
      showErrorToast('Failed to generate receipt number');
    } finally {
      setIsGeneratingReceipt(false);
    }
  };

  // Validate receipt number on change
  useEffect(() => {
    if (watchedReceiptNo && watchedReceiptNo.trim()) {
      const timeoutId = setTimeout(async () => {
        try {
          const isUnique = await validateReceiptNumber(watchedReceiptNo, payment?.id);
          if (!isUnique) {
            setError('receiptNo', {
              type: 'manual',
              message: 'Receipt number already exists',
            });
          } else {
            clearErrors('receiptNo');
          }
        } catch (error) {
          console.error('Error validating receipt number:', error);
        }
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [watchedReceiptNo, validateReceiptNumber, payment?.id, setError, clearErrors]);

  const onSubmit = async (data: PaymentFormData) => {
    try {
      if (isEditing && payment) {
        const updateData: UpdatePaymentData = {
          paymentDate: data.paymentDate,
          amount: data.amount,
          paymentMode: data.paymentMode,
          receiptNo: data.receiptNo || undefined,
          particulars: data.particulars || undefined,
        };
        updatePayment({ id: payment.id, data: updateData });
      } else {
        const createData: CreatePaymentData = {
          amcContractId: data.amcContractId,
          paymentDate: data.paymentDate,
          amount: data.amount,
          paymentMode: data.paymentMode,
          receiptNo: data.receiptNo || undefined,
          particulars: data.particulars || undefined,
        };
        createPayment(createData);
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error submitting payment form:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* AMC Contract ID (hidden if editing or pre-filled) */}
      {!isEditing && !amcContractId && (
        <div className="space-y-2">
          <Label htmlFor="amcContractId" className="text-black">
            AMC Contract ID *
          </Label>
          <Input
            id="amcContractId"
            {...register('amcContractId')}
            placeholder="Enter AMC contract ID"
            className={errors.amcContractId ? 'border-destructive' : ''}
          />
          {errors.amcContractId && (
            <p className="text-sm text-destructive">{errors.amcContractId.message}</p>
          )}
        </div>
      )}

      {/* Payment Date */}
      <div className="space-y-2">
        <Label className="text-black">Payment Date *</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !watch('paymentDate') && "text-muted-foreground",
                errors.paymentDate && "border-destructive"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {watch('paymentDate') ? format(watch('paymentDate'), "PPP") : "Pick a date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={watch('paymentDate')}
              onSelect={(date) => setValue('paymentDate', date || new Date())}
              initialFocus
            />
          </PopoverContent>
        </Popover>
        {errors.paymentDate && (
          <p className="text-sm text-destructive">{errors.paymentDate.message}</p>
        )}
      </div>

      {/* Amount */}
      <div className="space-y-2">
        <Label htmlFor="amount" className="text-black">
          Amount *
        </Label>
        <Input
          id="amount"
          type="number"
          step="0.01"
          min="0"
          {...register('amount', { valueAsNumber: true })}
          placeholder="0.00"
          className={errors.amount ? 'border-destructive' : ''}
        />
        {errors.amount && (
          <p className="text-sm text-destructive">{errors.amount.message}</p>
        )}
      </div>

      {/* Payment Mode */}
      <div className="space-y-2">
        <Label className="text-black">Payment Mode *</Label>
        <Select
          value={watch('paymentMode')}
          onValueChange={(value) => setValue('paymentMode', value as any)}
        >
          <SelectTrigger className={errors.paymentMode ? 'border-destructive' : ''}>
            <SelectValue placeholder="Select payment mode" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="CASH">Cash</SelectItem>
            <SelectItem value="CHEQUE">Cheque</SelectItem>
            <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
            <SelectItem value="ONLINE">Online</SelectItem>
          </SelectContent>
        </Select>
        {errors.paymentMode && (
          <p className="text-sm text-destructive">{errors.paymentMode.message}</p>
        )}
      </div>

      {/* Receipt Number */}
      <div className="space-y-2">
        <Label htmlFor="receiptNo" className="text-black">
          Receipt Number
        </Label>
        <div className="flex space-x-2">
          <Input
            id="receiptNo"
            {...register('receiptNo')}
            placeholder="Auto-generated if empty"
            className={errors.receiptNo ? 'border-destructive' : ''}
          />
          <Button
            type="button"
            variant="outline"
            onClick={handleGenerateReceiptNumber}
            disabled={isGeneratingReceipt}
          >
            {isGeneratingReceipt ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              'Generate'
            )}
          </Button>
        </div>
        {errors.receiptNo && (
          <p className="text-sm text-destructive">{errors.receiptNo.message}</p>
        )}
      </div>

      {/* Particulars */}
      <div className="space-y-2">
        <Label htmlFor="particulars" className="text-black">
          Particulars
        </Label>
        <Textarea
          id="particulars"
          {...register('particulars')}
          placeholder="Payment details or notes"
          rows={3}
        />
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isEditing ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            isEditing ? 'Update Payment' : 'Create Payment'
          )}
        </Button>
      </div>
    </form>
  );
}
