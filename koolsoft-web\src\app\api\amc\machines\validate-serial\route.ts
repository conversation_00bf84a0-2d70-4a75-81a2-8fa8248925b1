import { NextRequest, NextResponse } from 'next/server';
import { getAMCMachineRepository } from '@/lib/repositories';

/**
 * Validate serial number uniqueness
 * GET /api/amc/machines/validate-serial?serialNumber=ABC123&excludeId=uuid
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const serialNumber = searchParams.get('serialNumber');
    const excludeId = searchParams.get('excludeId');

    if (!serialNumber) {
      return NextResponse.json(
        { error: 'Serial number is required' },
        { status: 400 }
      );
    }

    const repository = getAMCMachineRepository();

    // Check if serial number exists
    const existingMachine = await repository.findFirst({
      where: {
        serialNumber: {
          equals: serialNumber,
          mode: 'insensitive', // Case-insensitive search
        },
        ...(excludeId && {
          id: {
            not: excludeId,
          },
        }),
      },
    });

    const isUnique = !existingMachine;

    return NextResponse.json({
      isUnique,
      serialNumber,
      ...(existingMachine && {
        existingMachine: {
          id: existingMachine.id,
          serialNumber: existingMachine.serialNumber,
          location: existingMachine.location,
        },
      }),
    });
  } catch (error) {
    console.error('Error validating serial number:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
