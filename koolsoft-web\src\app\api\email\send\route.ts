import { NextRequest, NextResponse } from 'next/server';
import { getEmailService } from '@/lib/services/email.service';
import { z } from 'zod';

/**
 * Direct email sending schema
 */
const sendDirectEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  subject: z.string().min(2).max(200),
  html: z.string().min(10),
  text: z.string().optional(),
  customerId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
});

/**
 * Template email sending schema
 */
const sendTemplateEmailSchema = z.object({
  templateName: z.string().min(2).max(100),
  data: z.record(z.any()),
  to: z.union([z.string().email(), z.array(z.string().email())]),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  customerId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
});

/**
 * POST /api/email/send
 * Send a direct email
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = sendDirectEmailSchema.parse(body);
    
    const emailService = getEmailService();
    
    // Send email
    const emailLog = await emailService.sendEmail(
      {
        to: validatedData.to,
        cc: validatedData.cc,
        bcc: validatedData.bcc,
        subject: validatedData.subject,
        html: validatedData.html,
        text: validatedData.text,
      },
      validatedData.customerId,
      validatedData.userId
    );
    
    return NextResponse.json({
      message: 'Email sent successfully',
      emailLog,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
}
