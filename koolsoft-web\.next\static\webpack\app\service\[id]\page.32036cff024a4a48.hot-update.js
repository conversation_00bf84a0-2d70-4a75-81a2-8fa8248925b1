"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/service/[id]/page",{

/***/ "(app-pages-browser)/./src/app/service/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/service/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServiceReportDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ServiceReportDetailPage() {\n    _s();\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [serviceReport, setServiceReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceReportDetailPage.useEffect\": ()=>{\n            if (params.id) {\n                loadServiceReport(params.id);\n            }\n        }\n    }[\"ServiceReportDetailPage.useEffect\"], [\n        params.id\n    ]);\n    const loadServiceReport = async (id)=>{\n        try {\n            const response = await fetch(\"/api/service/\".concat(id), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setServiceReport(data.serviceReport);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to load service report');\n                router.push('/service');\n            }\n        } catch (error) {\n            console.error('Error loading service report:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to load service report');\n            router.push('/service');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/service');\n    };\n    const handleEdit = ()=>{\n        router.push(\"/service/\".concat(params.id, \"/edit\"));\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('Are you sure you want to delete this service report?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/service/\".concat(params.id), {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success('Service report deleted successfully');\n                router.push('/service');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to delete service report');\n            }\n        } catch (error) {\n            console.error('Error deleting service report:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to delete service report');\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusConfig = {\n            OPEN: {\n                variant: 'secondary',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: 'Open'\n            },\n            IN_PROGRESS: {\n                variant: 'default',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                label: 'In Progress'\n            },\n            COMPLETED: {\n                variant: 'default',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                label: 'Completed'\n            },\n            CANCELLED: {\n                variant: 'destructive',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                label: 'Cancelled'\n            },\n            PENDING: {\n                variant: 'secondary',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                label: 'Pending'\n            }\n        };\n        const config = statusConfig[status] || statusConfig.OPEN;\n        const Icon = config.icon;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: config.variant,\n            className: \"flex items-center gap-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                config.label\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 12\n        }, this);\n    };\n    const getComplaintTypeBadge = (type)=>{\n        const typeConfig = {\n            REPAIR: 'bg-red-100 text-red-800',\n            MAINTENANCE: 'bg-blue-100 text-blue-800',\n            INSTALLATION: 'bg-green-100 text-green-800',\n            INSPECTION: 'bg-yellow-100 text-yellow-800',\n            WARRANTY: 'bg-purple-100 text-purple-800',\n            OTHER: 'bg-gray-100 text-gray-800'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(typeConfig[type] || typeConfig.OTHER),\n            children: type\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 114,\n            columnNumber: 12\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Loading service report...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 119,\n            columnNumber: 12\n        }, this);\n    }\n    if (!serviceReport) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Service report not found.\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 136,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleEdit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                onClick: handleDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                \"Service Report Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Report Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.reportDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, this),\n                                            serviceReport.visitDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Visit Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.visitDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 43\n                                            }, this),\n                                            serviceReport.completionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Completion Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.completionDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 48\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Nature of Service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: serviceReport.natureOfService\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1\",\n                                                        children: getStatusBadge(serviceReport.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Complaint Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1\",\n                                                        children: getComplaintTypeBadge(serviceReport.complaintType)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Customer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: serviceReport.customer.name\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.customer.city\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    serviceReport.customer.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.customer.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 50\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Executive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: serviceReport.executive.name\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    serviceReport.executive.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.executive.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 51\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            serviceReport.actionTaken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Action Taken\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-1\",\n                                                children: serviceReport.actionTaken\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            serviceReport.remarks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Remarks\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-1\",\n                                                children: serviceReport.remarks\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                \"Service Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: serviceReport.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-4\",\n                                            children: [\n                                                \"Service Detail \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Machine Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.machineType\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Serial Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.serialNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Problem\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.problem\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Solution\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.solution\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this),\n                                                detail.partReplaced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Part Replaced\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.partReplaced\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 43\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, detail.id, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 59\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 10\n    }, this);\n}\n_s(ServiceReportDetailPage, \"114MsJR8dQGMcKjI5aB2EYeOfug=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c1 = ServiceReportDetailPage;\n_s1(ServiceReportDetailPage, \"114MsJR8dQGMcKjI5aB2EYeOfug=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = ServiceReportDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ServiceReportDetailPage\");\nvar _c1;\n$RefreshReg$(_c1, \"ServiceReportDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/service/[id]/page.tsx\n"));

/***/ })

});