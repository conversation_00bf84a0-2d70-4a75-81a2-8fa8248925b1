// Script to migrate users from legacy USERS table to modern users table
const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcrypt');

const prisma = new PrismaClient();

async function migrateUsers() {
  try {
    console.log('Starting user migration from legacy USERS table to modern users table...');

    // Check if users already migrated
    const existingUsers = await prisma.users.count();
    if (existingUsers > 0) {
      console.log(`Found ${existingUsers} existing users in modern table. Checking for unmigrated users...`);

      // Get all legacy users that haven't been migrated yet
      const legacyUserCount = await prisma.$queryRaw`
        SELECT COUNT(*) as count FROM "USERS" u
        LEFT JOIN users m ON m.original_id = u."ID"
        WHERE m.id IS NULL
      `;

      const unmigrated = parseInt(legacyUserCount[0].count);
      if (unmigrated === 0) {
        console.log('All legacy users have already been migrated. Exiting.');
        return;
      }

      console.log(`Found ${unmigrated} unmigrated legacy users. Proceeding with migration...`);
    } else {
      console.log('No existing users in modern table. Proceeding with full migration...');
    }

    // Get legacy users
    const legacyUsers = await prisma.$queryRaw`
      SELECT
        u."ID" as id,
        u."Name" as name,
        u."Phone" as phone,
        u."Desig" as designation,
        u."DescDesig" as description
      FROM "USERS" u
      LEFT JOIN users m ON m.original_id = u."ID"
      WHERE m.id IS NULL
    `;

    console.log(`Found ${legacyUsers.length} legacy users to migrate`);

    // Get user passwords from UserPwd table
    const userPwds = await prisma.$queryRaw`
      SELECT * FROM "UserPwd"
    `;

    // Create a map of user IDs to passwords
    const userPwdMap = {};
    for (const userPwd of userPwds) {
      userPwdMap[userPwd.UserID] = userPwd;
    }

    // Migrate each user
    let migratedCount = 0;
    for (const legacyUser of legacyUsers) {
      try {
        // Create a default email if not available
        const email = `user${legacyUser.id}@koolsoft.com`;

        // Get user password if available, otherwise use a default
        const userPwd = userPwdMap[legacyUser.id];
        const password = await hash(userPwd?.Pwd || 'password123', 10);

        // Determine role based on designation and UserPwd.Key
        let role = 'USER';
        if (userPwd?.Key === 'Admin') {
          role = 'ADMIN';
        } else if (legacyUser.designation === 'E') {
          role = 'EXECUTIVE';
        } else if (legacyUser.designation === 'S') {
          role = 'MANAGER';
        }

        // Create user data
        const userData = {
          name: legacyUser.name || `User ${legacyUser.id}`,
          email: email,
          password: password,
          role: role.toUpperCase(),
          phone: legacyUser.phone || null,
          designation: legacyUser.description || null,
          isActive: true,
          originalId: legacyUser.id
        };

        // Create the user
        await prisma.users.create({ data: userData });
        migratedCount++;

        console.log(`Migrated user: ${userData.name} (ID: ${legacyUser.id}) with role ${userData.role}`);
      } catch (userError) {
        console.error(`Error migrating user ID ${legacyUser.id}:`, userError);
      }
    }

    console.log(`Migration complete. Successfully migrated ${migratedCount} out of ${legacyUsers.length} users.`);
  } catch (error) {
    console.error('Error during migration process:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the migration
migrateUsers()
  .then(() => console.log('Migration script completed'))
  .catch(err => console.error('Migration script failed:', err));
