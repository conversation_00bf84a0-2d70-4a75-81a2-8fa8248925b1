const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixDuplicates() {
  try {
    console.log('Starting duplicate cleanup process...\n');

    // Step 1: Fix AMC Payment Duplicates
    console.log('=== FIXING AMC PAYMENT DUPLICATES ===');
    
    // Get all duplicate payment groups
    const duplicatePayments = await prisma.$queryRaw`
      SELECT amc_contract_id, receipt_no, payment_date, amount, particulars, payment_mode,
             array_agg(id ORDER BY created_at ASC) as ids,
             COUNT(*) as count
      FROM amc_payments 
      GROUP BY amc_contract_id, receipt_no, payment_date, amount, particulars, payment_mode
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;

    console.log(`Found ${duplicatePayments.length} duplicate payment groups`);

    let deletedPayments = 0;
    for (const group of duplicatePayments) {
      // Keep the first record (oldest by created_at), delete the rest
      const idsToDelete = group.ids.slice(1); // Remove first element, keep rest for deletion
      
      if (idsToDelete.length > 0) {
        await prisma.amc_payments.deleteMany({
          where: {
            id: {
              in: idsToDelete
            }
          }
        });
        deletedPayments += idsToDelete.length;
        console.log(`Deleted ${idsToDelete.length} duplicate payments for receipt ${group.receipt_no}`);
      }
    }

    console.log(`Total AMC payment duplicates deleted: ${deletedPayments}\n`);

    // Step 2: Fix Customer Duplicates
    console.log('=== FIXING CUSTOMER DUPLICATES ===');
    
    // Get all duplicate customer groups
    const duplicateCustomers = await prisma.$queryRaw`
      SELECT name, 
             array_agg(id ORDER BY created_at ASC) as ids,
             COUNT(*) as count
      FROM customers 
      GROUP BY name 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;

    console.log(`Found ${duplicateCustomers.length} duplicate customer groups`);

    let deletedCustomers = 0;
    for (const group of duplicateCustomers) {
      // Keep the first record (oldest by created_at), delete the rest
      const idsToDelete = group.ids.slice(1);
      
      if (idsToDelete.length > 0) {
        // First, we need to handle foreign key constraints
        // Update any references to point to the kept customer
        const keepId = group.ids[0];
        
        for (const deleteId of idsToDelete) {
          // Update AMC contracts
          await prisma.amc_contracts.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update warranties
          await prisma.warranties.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update visit cards
          await prisma.visitCard.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update contacts
          await prisma.contact.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update history cards
          await prisma.history_cards.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update service reports
          await prisma.service_reports.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update sales leads
          await prisma.sales_leads.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update sales opportunities
          await prisma.sales_opportunities.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update sales prospects
          await prisma.sales_prospects.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update sales orders
          await prisma.sales_orders.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });

          // Update out warranties
          await prisma.out_warranties.updateMany({
            where: { customerId: deleteId },
            data: { customerId: keepId }
          });
        }

        // Now delete the duplicate customers
        await prisma.customer.deleteMany({
          where: {
            id: {
              in: idsToDelete
            }
          }
        });
        
        deletedCustomers += idsToDelete.length;
        console.log(`Deleted ${idsToDelete.length} duplicate customers for "${group.name}"`);
      }
    }

    console.log(`Total customer duplicates deleted: ${deletedCustomers}\n`);

    // Step 3: Update contract paid amounts
    console.log('=== UPDATING CONTRACT PAID AMOUNTS ===');
    
    const contracts = await prisma.amc_contracts.findMany({
      select: { id: true }
    });

    for (const contract of contracts) {
      const totalPaid = await prisma.amc_payments.aggregate({
        where: { amcContractId: contract.id },
        _sum: { amount: true }
      });

      await prisma.amc_contracts.update({
        where: { id: contract.id },
        data: { paidAmount: totalPaid._sum.amount || 0 }
      });
    }

    console.log(`Updated paid amounts for ${contracts.length} contracts\n`);

    // Step 4: Verify cleanup
    console.log('=== VERIFICATION ===');
    
    const remainingPaymentDuplicates = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM (
        SELECT amc_contract_id, receipt_no, payment_date, amount
        FROM amc_payments 
        GROUP BY amc_contract_id, receipt_no, payment_date, amount
        HAVING COUNT(*) > 1
      ) as duplicates
    `;

    const remainingCustomerDuplicates = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM (
        SELECT name
        FROM customers 
        GROUP BY name 
        HAVING COUNT(*) > 1
      ) as duplicates
    `;

    console.log(`Remaining payment duplicates: ${remainingPaymentDuplicates[0].count}`);
    console.log(`Remaining customer duplicates: ${remainingCustomerDuplicates[0].count}`);

    // Final counts
    const finalCustomerCount = await prisma.customer.count();
    const finalPaymentCount = await prisma.amc_payments.count();
    
    console.log(`\nFinal counts:`);
    console.log(`Total customers: ${finalCustomerCount}`);
    console.log(`Total AMC payments: ${finalPaymentCount}`);

    console.log('\n✅ Duplicate cleanup completed successfully!');

  } catch (error) {
    console.error('Error fixing duplicates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixDuplicates();
