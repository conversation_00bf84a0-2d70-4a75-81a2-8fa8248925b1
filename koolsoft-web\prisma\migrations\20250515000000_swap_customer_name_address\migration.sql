-- Swap the 'name' and 'address' columns in the customers table
-- This fixes a data mismatch where customer names were stored in the 'address' column
-- and addresses were stored in the 'name' column

-- Step 1: Rename 'name' to 'temp_name'
ALTER TABLE customers RENAME COLUMN name TO temp_name;

-- Step 2: Rename 'address' to 'name'
ALTER TABLE customers RENAME COLUMN address TO name;

-- Step 3: Rename 'temp_name' to 'address'
ALTER TABLE customers RENAME COLUMN temp_name TO address;
