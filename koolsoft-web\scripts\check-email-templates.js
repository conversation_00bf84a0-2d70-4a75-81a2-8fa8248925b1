/**
 * <PERSON>ript to check if email templates exist in the database and create default ones if needed
 *
 * Run with: node scripts/check-email-templates.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Checking email templates...');

  try {
    // Check if email_templates table exists
    try {
      await prisma.$queryRaw`SELECT 1 FROM email_templates LIMIT 1`;
      console.log('✅ email_templates table exists');
    } catch (error) {
      console.error('❌ email_templates table does not exist or cannot be accessed');
      console.error(error);
      return;
    }

    // Count existing templates
    // Use EmailTemplate model instead of email_templates
    const count = await prisma.EmailTemplate.count();
    console.log(`Found ${count} email templates in the database`);

    if (count === 0) {
      console.log('Creating default email templates...');

      // Create welcome template
      await prisma.EmailTemplate.create({
        data: {
          name: 'welcome',
          subject: 'Welcome to KoolSoft',
          bodyHtml: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <div style="background-color: #0F52BA; padding: 20px; text-align: center; color: white; border-radius: 5px 5px 0 0;">
                <h1 style="margin: 0;">Welcome to KoolSoft</h1>
              </div>
              <div style="padding: 20px; background-color: #ffffff;">
                <p>Dear {{name}},</p>
                <p>Thank you for joining KoolSoft. We're excited to have you on board!</p>
                <p>Your account has been successfully created with the email: <strong>{{email}}</strong></p>
                <p>If you have any questions, please don't hesitate to contact our support team.</p>
                <p>Best regards,<br>The KoolSoft Team</p>
              </div>
              <div style="background-color: #f3f4f6; padding: 10px; text-align: center; font-size: 12px; color: #666; border-radius: 0 0 5px 5px;">
                <p>&copy; 2023 KoolSoft. All rights reserved.</p>
              </div>
            </div>
          `,
          bodyText: `
            Welcome to KoolSoft

            Dear {{name}},

            Thank you for joining KoolSoft. We're excited to have you on board!

            Your account has been successfully created with the email: {{email}}

            If you have any questions, please don't hesitate to contact our support team.

            Best regards,
            The KoolSoft Team

            © 2023 KoolSoft. All rights reserved.
          `,
          description: 'Welcome email for new users',
          variables: ['name', 'email'],
          isActive: true,
        }
      });
      console.log('✅ Created welcome template');

      // Create password reset template
      await prisma.EmailTemplate.create({
        data: {
          name: 'password-reset',
          subject: 'Password Reset Request',
          bodyHtml: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <div style="background-color: #0F52BA; padding: 20px; text-align: center; color: white; border-radius: 5px 5px 0 0;">
                <h1 style="margin: 0;">Password Reset</h1>
              </div>
              <div style="padding: 20px; background-color: #ffffff;">
                <p>Dear {{name}},</p>
                <p>We received a request to reset your password. If you didn't make this request, you can ignore this email.</p>
                <p>To reset your password, click the button below:</p>
                <div style="text-align: center; margin: 30px 0;">
                  <a href="{{resetLink}}" style="background-color: #0F52BA; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
                </div>
                <p>This link will expire in 24 hours.</p>
                <p>Best regards,<br>The KoolSoft Team</p>
              </div>
              <div style="background-color: #f3f4f6; padding: 10px; text-align: center; font-size: 12px; color: #666; border-radius: 0 0 5px 5px;">
                <p>&copy; 2023 KoolSoft. All rights reserved.</p>
              </div>
            </div>
          `,
          bodyText: `
            Password Reset

            Dear {{name}},

            We received a request to reset your password. If you didn't make this request, you can ignore this email.

            To reset your password, please visit: {{resetLink}}

            This link will expire in 24 hours.

            Best regards,
            The KoolSoft Team

            © 2023 KoolSoft. All rights reserved.
          `,
          description: 'Password reset email',
          variables: ['name', 'resetLink'],
          isActive: true,
        }
      });
      console.log('✅ Created password reset template');

      console.log('✅ Default templates created successfully');
    } else {
      // List existing templates
      const templates = await prisma.EmailTemplate.findMany({
        select: {
          id: true,
          name: true,
          subject: true,
          isActive: true,
        }
      });

      console.log('Existing templates:');
      templates.forEach(template => {
        console.log(`- ${template.name} (${template.id}): "${template.subject}" [${template.isActive ? 'Active' : 'Inactive'}]`);
      });
    }
  } catch (error) {
    console.error('Error checking/creating email templates:', error);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch(e => {
    console.error(e);
    console.error(e.stack);
    process.exit(1);
  });
