import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { serviceSchedulingSchema } from '@/lib/validations/service.schema';
import { getServiceScheduleRepository } from '@/lib/repositories';

/**
 * GET /api/service/schedules
 * Get service schedules with optional filtering
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '50');
      const status = searchParams.get('status');
      const priority = searchParams.get('priority');
      const technicianId = searchParams.get('technicianId');
      const search = searchParams.get('search');

      const serviceScheduleRepository = getServiceScheduleRepository();

      // Build filter object
      const filter: any = {};

      if (status && status !== 'all') {
        filter.status = status;
      }

      if (priority && priority !== 'all') {
        filter.priority = priority;
      }

      if (technicianId && technicianId !== 'all') {
        filter.technicianId = technicianId;
      }

      if (search) {
        filter.OR = [
          {
            serviceReport: {
              customer: {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            },
          },
          {
            serviceReport: {
              natureOfService: {
                contains: search,
                mode: 'insensitive',
              },
            },
          },
          {
            notes: {
              contains: search,
              mode: 'insensitive',
            },
          },
        ];
      }

      const skip = (page - 1) * limit;

      // Get schedules with filtering
      const [schedules, total] = await Promise.all([
        serviceScheduleRepository.findWithFilter(filter, skip, limit),
        serviceScheduleRepository.countWithFilter(filter),
      ]);

      const totalPages = Math.ceil(total / limit);

      return NextResponse.json({
        schedules,
        pagination: {
          total,
          page,
          limit,
          totalPages,
        },
      });
    } catch (error) {
      console.error('Error fetching service schedules:', error);
      return NextResponse.json(
        { error: 'Failed to fetch service schedules' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/service/schedules
 * Create a new service schedule
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = serviceSchedulingSchema.parse(body);

      const serviceScheduleRepository = getServiceScheduleRepository();

      // Create the service schedule
      const newSchedule = await serviceScheduleRepository.create({
        serviceReportId: validatedData.serviceReportId,
        scheduledDate: validatedData.scheduledDate,
        technicianId: validatedData.technicianId,
        estimatedDuration: validatedData.estimatedDuration,
        priority: validatedData.priority,
        notes: validatedData.notes,
        status: 'SCHEDULED',
      });

      // Get the created schedule with relations
      const scheduleWithRelations = await serviceScheduleRepository.findWithRelations(newSchedule.id);

      return NextResponse.json(
        {
          message: 'Service scheduled successfully',
          schedule: scheduleWithRelations
        },
        { status: 201 }
      );
    } catch (error) {
      console.error('Error creating service schedule:', error);

      if (error instanceof Error) {
        // Handle validation errors
        if (error.message.includes('validation')) {
          return NextResponse.json(
            { error: 'Validation failed', details: error.message },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Failed to create service schedule' },
        { status: 500 }
      );
    }
  }
);
