'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  Edit, 
  Trash2, 
  ArrowLeft, 
  Calendar, 
  DollarSign, 
  User, 
  Building, 
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';
import Link from 'next/link';

import { format } from 'date-fns';

interface Warranty {
  id: string;
  bslNo: string;
  bslDate: string;
  bslAmount: number;
  installDate: string;
  warrantyDate: string;
  warningDate: string;
  numberOfMachines: number;
  status: string;
  customer: {
    id: string;
    name: string;
    city: string;
    phone: string;
  };
  contactPerson?: {
    id: string;
    name: string;
    phone: string;
  };
  machines: Array<{
    id: string;
    serialNumber: string;
    location: string;
    product?: { name: string };
    model?: { name: string };
    brand?: { name: string };
    components: Array<{
      id: string;
      componentNo: number;
      serialNumber: string;
      warrantyDate: string;
      section: string;
    }>;
  }>;
}

export default function WarrantyDetailPage() {
  const params = useParams();
  const router = useRouter();
  const warrantyId = params.id as string;
  
  const [warranty, setWarranty] = useState<Warranty | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const loadWarranty = async () => {
      try {
        setIsLoading(true);
        
        const response = await fetch(`/api/warranties/${warrantyId}`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Warranty not found');
          }
          throw new Error('Failed to load warranty details');
        }
        
        const data = await response.json();
        setWarranty(data);
        setError(null);
      } catch (err: any) {
        console.error('Error loading warranty:', err);
        setError(err.message || 'Failed to load warranty details');
      } finally {
        setIsLoading(false);
      }
    };

    if (warrantyId) {
      loadWarranty();
    }
  }, [warrantyId]);

  const handleDelete = async () => {
    if (!warranty || !confirm('Are you sure you want to delete this warranty? This action cannot be undone.')) {
      return;
    }

    try {
      setIsDeleting(true);
      
      const response = await fetch(`/api/warranties/${warrantyId}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete warranty');
      }
      
      // Redirect to warranties list
      router.push('/warranties');
    } catch (err: any) {
      console.error('Error deleting warranty:', err);
      setError(err.message || 'Failed to delete warranty');
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4" />;
      case 'EXPIRED':
        return <XCircle className="h-4 w-4" />;
      case 'PENDING':
        return <Clock className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !warranty) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-black mb-2">Error Loading Warranty</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button asChild>
              <Link href="/warranties">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Warranties
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header Card */}
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Warranty Details</span>
                </CardTitle>
                <CardDescription className="text-gray-100">
                  {warranty.customer.name} - {warranty.bslNo}
                </CardDescription>
              </div>
              <Badge className={`${getStatusColor(warranty.status)} flex items-center space-x-1`}>
                {getStatusIcon(warranty.status)}
                <span>{warranty.status}</span>
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Building className="h-4 w-4" />
                  <span className="text-sm">Customer</span>
                </div>
                <p className="font-medium text-black">{warranty.customer.name}</p>
                <p className="text-sm text-gray-600">{warranty.customer.city}</p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-gray-600">
                  <DollarSign className="h-4 w-4" />
                  <span className="text-sm">BSL Amount</span>
                </div>
                <p className="font-medium text-black">₹{warranty.bslAmount?.toLocaleString() || 0}</p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span className="text-sm">Install Date</span>
                </div>
                <p className="font-medium text-black">
                  {warranty.installDate ? format(new Date(warranty.installDate), 'MMM dd, yyyy') : 'Not set'}
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Shield className="h-4 w-4" />
                  <span className="text-sm">Warranty Date</span>
                </div>
                <p className="font-medium text-black">
                  {warranty.warrantyDate ? format(new Date(warranty.warrantyDate), 'MMM dd, yyyy') : 'Not set'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="machines">Machines ({warranty.machines?.length || 0})</TabsTrigger>
            <TabsTrigger value="components">Components</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-black">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">BSL Number</label>
                    <p className="text-black">{warranty.bslNo || 'Not set'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">BSL Date</label>
                    <p className="text-black">
                      {warranty.bslDate ? format(new Date(warranty.bslDate), 'MMM dd, yyyy') : 'Not set'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Number of Machines</label>
                    <p className="text-black">{warranty.numberOfMachines || 0}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Warning Date</label>
                    <p className="text-black">
                      {warranty.warningDate ? format(new Date(warranty.warningDate), 'MMM dd, yyyy') : 'Not set'}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-black">Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Customer</label>
                    <p className="text-black">{warranty.customer.name}</p>
                    <p className="text-sm text-gray-600">{warranty.customer.city}</p>
                  </div>
                  {warranty.contactPerson && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Contact Person</label>
                      <p className="text-black">{warranty.contactPerson.name}</p>
                      <p className="text-sm text-gray-600">{warranty.contactPerson.phone}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-600">Customer Phone</label>
                    <p className="text-black">{warranty.customer.phone || 'Not available'}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="machines" className="space-y-4">
            {warranty.machines && warranty.machines.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {warranty.machines.map((machine) => (
                  <Card key={machine.id}>
                    <CardHeader>
                      <CardTitle className="text-black">Machine {machine.serialNumber}</CardTitle>
                      <CardDescription>{machine.location}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600">Product</label>
                          <p className="text-black">{machine.product?.name || 'Not specified'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Model</label>
                          <p className="text-black">{machine.model?.name || 'Not specified'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-600">Brand</label>
                          <p className="text-black">{machine.brand?.name || 'Not specified'}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <Building className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-black mb-2">No Machines</h3>
                    <p className="text-gray-600">No machines have been assigned to this warranty yet.</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="components" className="space-y-4">
            {warranty.machines && warranty.machines.some(m => m.components?.length > 0) ? (
              <div className="space-y-4">
                {warranty.machines.map((machine) => 
                  machine.components && machine.components.length > 0 ? (
                    <Card key={machine.id}>
                      <CardHeader>
                        <CardTitle className="text-black">Components for {machine.serialNumber}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {machine.components.map((component) => (
                            <div key={component.id} className="flex items-center justify-between p-3 border rounded-lg">
                              <div>
                                <p className="font-medium text-black">Component #{component.componentNo}</p>
                                <p className="text-sm text-gray-600">{component.serialNumber}</p>
                                <p className="text-sm text-gray-600">Section: {component.section}</p>
                              </div>
                              <div className="text-right">
                                <p className="text-sm text-gray-600">Warranty Date</p>
                                <p className="text-black">
                                  {component.warrantyDate ? format(new Date(component.warrantyDate), 'MMM dd, yyyy') : 'Not set'}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ) : null
                )}
              </div>
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <AlertTriangle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-black mb-2">No Components</h3>
                    <p className="text-gray-600">No components have been assigned to this warranty's machines yet.</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
    </div>
  );
}
