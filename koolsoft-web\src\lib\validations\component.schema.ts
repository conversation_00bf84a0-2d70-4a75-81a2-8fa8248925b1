import { z } from 'zod';

/**
 * Component status enum
 */
export const ComponentStatus = {
  ACTIVE: 'ACTIVE',
  REPLACED: 'REPLACED',
  FAILED: 'FAILED',
  MAINTENANCE: 'MAINTENANCE',
} as const;

export type ComponentStatusType = typeof ComponentStatus[keyof typeof ComponentStatus];

/**
 * Component type enum
 */
export const ComponentType = {
  COMPRESSOR: 'COMPRESSOR',
  CONDENSER: 'CONDENSER',
  EVAPORATOR: 'EVAPORATOR',
  EXPANSION_VALVE: 'EXPANSION_VALVE',
  FAN_MOTOR: 'FAN_MOTOR',
  CONTROL_BOARD: 'CONTROL_BOARD',
  SENSOR: 'SENSOR',
  FILTER: 'FILTER',
  COIL: 'COIL',
  OTHER: 'OTHER',
} as const;

export type ComponentTypeType = typeof ComponentType[keyof typeof ComponentType];

/**
 * Base component schema
 */
export const componentBaseSchema = z.object({
  machineId: z.string().uuid({ message: 'Valid machine ID is required' }),
  componentNo: z.number().int().positive({ message: 'Component number must be a positive integer' }).optional(),
  serialNumber: z.string()
    .min(1, { message: 'Serial number is required' })
    .max(50, { message: 'Serial number must be less than 50 characters' })
    .optional(),
  warrantyDate: z.coerce.date().optional(),
  section: z.string().max(1, { message: 'Section must be a single character' }).optional(),
  componentType: z.enum([
    ComponentType.COMPRESSOR,
    ComponentType.CONDENSER,
    ComponentType.EVAPORATOR,
    ComponentType.EXPANSION_VALVE,
    ComponentType.FAN_MOTOR,
    ComponentType.CONTROL_BOARD,
    ComponentType.SENSOR,
    ComponentType.FILTER,
    ComponentType.COIL,
    ComponentType.OTHER,
  ]).optional(),
  status: z.enum([
    ComponentStatus.ACTIVE,
    ComponentStatus.REPLACED,
    ComponentStatus.FAILED,
    ComponentStatus.MAINTENANCE,
  ]).default(ComponentStatus.ACTIVE),
  installationDate: z.coerce.date().optional(),
  replacementDate: z.coerce.date().optional(),
  notes: z.string().max(500, { message: 'Notes must be less than 500 characters' }).optional(),
  originalAmcId: z.number().int().optional(),
  originalAssetNo: z.number().int().optional(),
  originalComponentNo: z.number().int().optional(),
});

/**
 * Component creation schema
 */
export const createComponentSchema = componentBaseSchema.extend({
  machineId: z.string().uuid({ message: 'Valid machine ID is required' }),
  serialNumber: z.string()
    .min(1, { message: 'Serial number is required' })
    .max(50, { message: 'Serial number must be less than 50 characters' }),
}).refine(
  (data) => {
    // If replacement date is provided, it should be after installation date
    if (data.installationDate && data.replacementDate) {
      return data.replacementDate >= data.installationDate;
    }
    return true;
  },
  {
    message: 'Replacement date must be after installation date',
    path: ['replacementDate'],
  }
).refine(
  (data) => {
    // If warranty date is provided, it should be after installation date
    if (data.installationDate && data.warrantyDate) {
      return data.warrantyDate >= data.installationDate;
    }
    return true;
  },
  {
    message: 'Warranty date must be after installation date',
    path: ['warrantyDate'],
  }
);

/**
 * Component update schema
 */
export const updateComponentSchema = componentBaseSchema.partial().refine(
  (data) => {
    // If replacement date is provided, it should be after installation date
    if (data.installationDate && data.replacementDate) {
      return data.replacementDate >= data.installationDate;
    }
    return true;
  },
  {
    message: 'Replacement date must be after installation date',
    path: ['replacementDate'],
  }
).refine(
  (data) => {
    // If warranty date is provided, it should be after installation date
    if (data.installationDate && data.warrantyDate) {
      return data.warrantyDate >= data.installationDate;
    }
    return true;
  },
  {
    message: 'Warranty date must be after installation date',
    path: ['warrantyDate'],
  }
);

/**
 * Component search schema
 */
export const componentSearchSchema = z.object({
  query: z.string().optional(),
  machineId: z.string().uuid().optional(),
  componentNo: z.number().int().optional(),
  componentType: z.enum([
    ComponentType.COMPRESSOR,
    ComponentType.CONDENSER,
    ComponentType.EVAPORATOR,
    ComponentType.EXPANSION_VALVE,
    ComponentType.FAN_MOTOR,
    ComponentType.CONTROL_BOARD,
    ComponentType.SENSOR,
    ComponentType.FILTER,
    ComponentType.COIL,
    ComponentType.OTHER,
  ]).optional(),
  status: z.enum([
    ComponentStatus.ACTIVE,
    ComponentStatus.REPLACED,
    ComponentStatus.FAILED,
    ComponentStatus.MAINTENANCE,
  ]).optional(),
  warrantyStatus: z.enum(['ACTIVE', 'EXPIRED', 'EXPIRING_SOON']).optional(),
  installationDateFrom: z.coerce.date().optional(),
  installationDateTo: z.coerce.date().optional(),
  warrantyDateFrom: z.coerce.date().optional(),
  warrantyDateTo: z.coerce.date().optional(),
  skip: z.coerce.number().int().nonnegative().default(0),
  take: z.coerce.number().int().positive().max(100).default(50),
  orderBy: z.enum(['createdAt', 'updatedAt', 'serialNumber', 'componentNo', 'warrantyDate', 'installationDate']).default('createdAt'),
  orderDirection: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Component replacement schema
 */
export const componentReplacementSchema = z.object({
  oldComponentId: z.string().uuid({ message: 'Valid old component ID is required' }),
  newSerialNumber: z.string()
    .min(1, { message: 'New serial number is required' })
    .max(50, { message: 'Serial number must be less than 50 characters' }),
  replacementDate: z.coerce.date({ required_error: 'Replacement date is required' }),
  warrantyDate: z.coerce.date().optional(),
  notes: z.string().max(500, { message: 'Notes must be less than 500 characters' }).optional(),
  reason: z.string()
    .min(1, { message: 'Replacement reason is required' })
    .max(200, { message: 'Reason must be less than 200 characters' }),
}).refine(
  (data) => {
    // Warranty date should be after replacement date
    if (data.warrantyDate) {
      return data.warrantyDate >= data.replacementDate;
    }
    return true;
  },
  {
    message: 'Warranty date must be after replacement date',
    path: ['warrantyDate'],
  }
);

/**
 * Component validation schema for serial number uniqueness
 */
export const componentSerialValidationSchema = z.object({
  serialNumber: z.string().min(1, { message: 'Serial number is required' }),
  excludeId: z.string().uuid().optional(),
});

/**
 * Component statistics schema
 */
export const componentStatisticsSchema = z.object({
  machineId: z.string().uuid().optional(),
  contractId: z.string().uuid().optional(),
  customerId: z.string().uuid().optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
});

/**
 * Component export schema
 */
export const componentExportSchema = z.object({
  format: z.enum(['CSV', 'EXCEL', 'JSON']).default('CSV'),
  filters: componentSearchSchema.optional(),
  includeRelations: z.boolean().default(true),
});

/**
 * Component warranty alert schema
 */
export const componentWarrantyAlertSchema = z.object({
  daysAhead: z.number().int().positive().max(365).default(30),
  includeExpired: z.boolean().default(false),
  machineId: z.string().uuid().optional(),
  contractId: z.string().uuid().optional(),
});

/**
 * Component type definitions for TypeScript
 */
export type ComponentBase = z.infer<typeof componentBaseSchema>;
export type CreateComponent = z.infer<typeof createComponentSchema>;
export type UpdateComponent = z.infer<typeof updateComponentSchema>;
export type ComponentSearch = z.infer<typeof componentSearchSchema>;
export type ComponentReplacement = z.infer<typeof componentReplacementSchema>;
export type ComponentSerialValidation = z.infer<typeof componentSerialValidationSchema>;
export type ComponentStatistics = z.infer<typeof componentStatisticsSchema>;
export type ComponentExport = z.infer<typeof componentExportSchema>;
export type ComponentWarrantyAlert = z.infer<typeof componentWarrantyAlertSchema>;

/**
 * Component display helpers
 */
export const getComponentTypeLabel = (type: ComponentTypeType): string => {
  const labels: Record<ComponentTypeType, string> = {
    [ComponentType.COMPRESSOR]: 'Compressor',
    [ComponentType.CONDENSER]: 'Condenser',
    [ComponentType.EVAPORATOR]: 'Evaporator',
    [ComponentType.EXPANSION_VALVE]: 'Expansion Valve',
    [ComponentType.FAN_MOTOR]: 'Fan Motor',
    [ComponentType.CONTROL_BOARD]: 'Control Board',
    [ComponentType.SENSOR]: 'Sensor',
    [ComponentType.FILTER]: 'Filter',
    [ComponentType.COIL]: 'Coil',
    [ComponentType.OTHER]: 'Other',
  };
  return labels[type] || type;
};

export const getComponentStatusLabel = (status: ComponentStatusType): string => {
  const labels: Record<ComponentStatusType, string> = {
    [ComponentStatus.ACTIVE]: 'Active',
    [ComponentStatus.REPLACED]: 'Replaced',
    [ComponentStatus.FAILED]: 'Failed',
    [ComponentStatus.MAINTENANCE]: 'Under Maintenance',
  };
  return labels[status] || status;
};

export const getWarrantyStatusLabel = (warrantyDate?: Date): string => {
  if (!warrantyDate) return 'No Warranty';
  
  const now = new Date();
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + 30);
  
  if (warrantyDate < now) return 'Expired';
  if (warrantyDate <= futureDate) return 'Expiring Soon';
  return 'Active';
};

export const getWarrantyStatusColor = (warrantyDate?: Date): string => {
  const status = getWarrantyStatusLabel(warrantyDate);
  switch (status) {
    case 'Active': return 'text-green-600';
    case 'Expiring Soon': return 'text-yellow-600';
    case 'Expired': return 'text-red-600';
    default: return 'text-gray-600';
  }
};
