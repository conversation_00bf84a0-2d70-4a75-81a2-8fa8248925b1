import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserRepository } from '@/lib/repositories';
import { compare, hash } from 'bcrypt';
import { withActivityLogging } from '@/lib/middleware/activity-logger.middleware';

/**
 * PUT /api/users/me/password
 * 
 * Update the current user's password
 * 
 * Request body:
 * - currentPassword: Current password
 * - newPassword: New password
 */
async function updatePassword(req: NextRequest) {
  // Check authentication
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  try {
    // Parse request body
    const { currentPassword, newPassword } = await req.json();
    
    // Validate request body
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: 'Current password and new password are required' },
        { status: 400 }
      );
    }
    
    // Validate new password
    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: 'New password must be at least 8 characters long' },
        { status: 400 }
      );
    }
    
    // Get user repository
    const userRepository = getUserRepository();
    
    // Get current user
    const user = await userRepository.findById(session.user.id);
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Verify current password
    const isPasswordValid = await compare(currentPassword, user.password);
    
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Current password is incorrect' },
        { status: 400 }
      );
    }
    
    // Hash new password
    const hashedPassword = await hash(newPassword, 10);
    
    // Update user password
    await userRepository.update(user.id, {
      password: hashedPassword,
    });
    
    // Return success response
    return NextResponse.json({
      message: 'Password updated successfully',
    });
  } catch (error) {
    console.error('Error updating password:', error);
    return NextResponse.json(
      { error: 'Failed to update password' },
      { status: 500 }
    );
  }
}

// Wrap the handler with activity logging middleware
export const PUT = withActivityLogging(updatePassword, {
  action: 'update_password',
  entityType: 'user',
  getEntityId: (req, session) => session?.user.id,
});
