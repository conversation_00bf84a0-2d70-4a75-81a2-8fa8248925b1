'use client';

import { useState, useEffect } from 'react';
import { useP<PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Edit, 
  Settings, 
  Calendar, 
  Wrench, 
  FileText,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { showErrorToast } from '@/lib/toast';
import { Skeleton } from '@/components/ui/skeleton';

interface Machine {
  id: string;
  serialNumber?: string;
  location?: string;
  installationDate?: string;
  warrantyDate?: string;
  status?: string;
  amcContractId?: string;
  amcContract?: {
    id: string;
    contractNumber?: string;
    customer?: {
      id: string;
      name: string;
    };
    startDate: string;
    endDate: string;
    status: string;
  };
  product?: {
    id: string;
    name: string;
  };
  model?: {
    id: string;
    name: string;
  };
  brand?: {
    id: string;
    name: string;
  };
  components?: any[];
  serviceHistory?: any[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Machine Detail Page
 * 
 * This page displays comprehensive information about a specific machine
 * including specifications, service history, components, and contract details.
 */
export default function MachineDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [machine, setMachine] = useState<Machine | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const machineId = params?.id as string;

  // Fetch machine details
  useEffect(() => {
    const fetchMachine = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/amc/machines/${machineId}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Machine not found');
          }
          throw new Error('Failed to fetch machine details');
        }

        const data = await response.json();
        setMachine(data.machine || data);
      } catch (error) {
        console.error('Error fetching machine:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch machine details';
        setError(errorMessage);
        showErrorToast('Error', errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (machineId) {
      fetchMachine();
    }
  }, [machineId]);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'INACTIVE':
        return 'bg-red-100 text-red-800';
      case 'MAINTENANCE':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-48 bg-white/20" />
                <Skeleton className="h-4 w-64 bg-white/20 mt-2" />
              </div>
              <Skeleton className="h-10 w-24 bg-white/20" />
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !machine) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-destructive text-white">
            <CardTitle>Error Loading Machine</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center py-8 space-y-4">
              <p className="text-black">{error || 'Machine not found'}</p>
              <div className="flex justify-center space-x-4">
                <Button asChild variant="outline">
                  <Link href="/amc/machines">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Machines
                  </Link>
                </Button>
                <Button onClick={() => window.location.reload()}>
                  <Settings className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Machine Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Machine #{machine.serialNumber || machine.id.slice(0, 8)}</span>
              {machine.status && (
                <Badge className={`ml-2 ${getStatusColor(machine.status)}`}>
                  {machine.status}
                </Badge>
              )}
            </CardTitle>
            <CardDescription className="text-gray-100">
              {machine.brand?.name} {machine.model?.name} • {machine.location || 'Location not specified'}
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button asChild variant="secondary" size="sm">
              <Link href={`/amc/machines/${machine.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            <Button asChild variant="secondary" size="sm">
              <Link href="/amc/machines">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Machine Details Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contract">Contract</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="service">Service History</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Machine Specifications */}
            <Card>
              <CardHeader className="pb-3 bg-primary text-white">
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-4 w-4" />
                  <span>Specifications</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                <div>
                  <span className="text-sm font-medium text-black">Serial Number:</span>
                  <p className="text-black">{machine.serialNumber || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Brand:</span>
                  <p className="text-black">{machine.brand?.name || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Model:</span>
                  <p className="text-black">{machine.model?.name || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Product:</span>
                  <p className="text-black">{machine.product?.name || 'N/A'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Installation Details */}
            <Card>
              <CardHeader className="pb-3 bg-primary text-white">
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>Installation</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                <div>
                  <span className="text-sm font-medium text-black">Location:</span>
                  <p className="text-black">{machine.location || 'Not specified'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Installation Date:</span>
                  <p className="text-black">
                    {machine.installationDate ? format(new Date(machine.installationDate), 'MMM dd, yyyy') : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Warranty Date:</span>
                  <p className="text-black">
                    {machine.warrantyDate ? format(new Date(machine.warrantyDate), 'MMM dd, yyyy') : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Status:</span>
                  <Badge className={getStatusColor(machine.status || 'UNKNOWN')}>
                    {machine.status || 'UNKNOWN'}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader className="pb-3 bg-primary text-white">
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Quick Stats</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                <div>
                  <span className="text-sm font-medium text-black">Components:</span>
                  <p className="text-black">{machine.components?.length || 0}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Service Records:</span>
                  <p className="text-black">{machine.serviceHistory?.length || 0}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Created:</span>
                  <p className="text-black">{format(new Date(machine.createdAt), 'MMM dd, yyyy')}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Last Updated:</span>
                  <p className="text-black">{format(new Date(machine.updatedAt), 'MMM dd, yyyy')}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Contract Tab - Placeholder */}
        <TabsContent value="contract">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white">
              <CardTitle>AMC Contract Details</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {machine.amcContract ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm font-medium text-black">Contract Number:</span>
                      <p className="text-black">{machine.amcContract.contractNumber || machine.amcContract.id.slice(0, 8)}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-black">Customer:</span>
                      <p className="text-black">{machine.amcContract.customer?.name || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-black">Start Date:</span>
                      <p className="text-black">{format(new Date(machine.amcContract.startDate), 'MMM dd, yyyy')}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-black">End Date:</span>
                      <p className="text-black">{format(new Date(machine.amcContract.endDate), 'MMM dd, yyyy')}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button asChild variant="outline">
                      <Link href={`/amc/contracts/${machine.amcContract.id}`}>
                        View Full Contract
                      </Link>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-black">No AMC contract assigned to this machine</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Components Tab - Placeholder */}
        <TabsContent value="components">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white">
              <CardTitle>Machine Components</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <p className="text-black">Component details will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Service History Tab - Placeholder */}
        <TabsContent value="service">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white">
              <CardTitle>Service History</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <p className="text-black">Service history will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Maintenance Tab - Placeholder */}
        <TabsContent value="maintenance">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white">
              <CardTitle>Maintenance Schedule</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <p className="text-black">Maintenance schedule will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
