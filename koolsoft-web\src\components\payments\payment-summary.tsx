'use client';

import React from 'react';
import { usePaymentStatistics } from '@/lib/hooks/usePayments';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { CreditCard, TrendingUp, Calendar, Percent } from 'lucide-react';
import { format } from 'date-fns';

interface PaymentSummaryProps {
  amcContractId: string;
  contractAmount?: number;
  showDetailedStats?: boolean;
}

export function PaymentSummary({
  amcContractId,
  contractAmount,
  showDetailedStats = true,
}: PaymentSummaryProps) {
  const { data: statistics, isLoading, error } = usePaymentStatistics(amcContractId);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            Error loading payment statistics
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!statistics) {
    return null;
  }

  const contractAmountValue = (statistics as any).contractAmount || contractAmount || 0;
  const paymentPercentage = contractAmountValue > 0 ? (statistics.totalPaid / contractAmountValue) * 100 : 0;
  const balance = contractAmountValue - statistics.totalPaid;

  const getPaymentModeColor = (mode: string) => {
    switch (mode) {
      case 'CASH':
        return 'bg-green-100 text-green-800';
      case 'CHEQUE':
        return 'bg-blue-100 text-blue-800';
      case 'BANK_TRANSFER':
        return 'bg-purple-100 text-purple-800';
      case 'ONLINE':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Paid */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-black">Total Paid</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-black">
              ₹{statistics.totalPaid.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-xs text-muted-foreground">
              {statistics.paymentCount} payment{statistics.paymentCount !== 1 ? 's' : ''}
            </p>
          </CardContent>
        </Card>

        {/* Balance */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-black">Balance</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${balance > 0 ? 'text-destructive' : 'text-green-600'}`}>
              ₹{Math.abs(balance).toLocaleString('en-IN', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-xs text-muted-foreground">
              {balance > 0 ? 'Outstanding' : balance < 0 ? 'Overpaid' : 'Fully Paid'}
            </p>
          </CardContent>
        </Card>

        {/* Payment Progress */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-black">Progress</CardTitle>
            <Percent className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-black">
              {paymentPercentage.toFixed(1)}%
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(paymentPercentage, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        {/* Last Payment */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-black">Last Payment</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-black">
              {statistics.lastPaymentDate
                ? format(new Date(statistics.lastPaymentDate), 'dd MMM')
                : 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg: ₹{statistics.averagePaymentAmount.toLocaleString('en-IN', { minimumFractionDigits: 0 })}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Statistics */}
      {showDetailedStats && statistics.paymentModes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-black">Payment Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Contract Amount vs Paid */}
              <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-black">Contract Amount</p>
                  <p className="text-2xl font-bold text-black">
                    ₹{contractAmountValue.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-black">Paid Amount</p>
                  <p className="text-2xl font-bold text-green-600">
                    ₹{statistics.totalPaid.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                  </p>
                </div>
              </div>

              {/* Payment Modes Breakdown */}
              <div>
                <h4 className="text-sm font-medium text-black mb-3">Payment Methods</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {statistics.paymentModes.map((modeStats) => (
                    <div
                      key={modeStats.mode}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <Badge className={getPaymentModeColor(modeStats.mode)}>
                          {modeStats.mode}
                        </Badge>
                        <span className="text-sm text-black">
                          {modeStats.count} payment{modeStats.count !== 1 ? 's' : ''}
                        </span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-black">
                          ₹{modeStats.total.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {((modeStats.total / statistics.totalPaid) * 100).toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
