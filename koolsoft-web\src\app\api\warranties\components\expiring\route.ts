import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getWarrantyComponentRepository } from '@/lib/repositories';

/**
 * GET /api/warranties/components/expiring
 * Get warranty components that are expiring within a specified number of days
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const days = parseInt(searchParams.get('days') || '30');
      const skip = parseInt(searchParams.get('skip') || '0');
      const take = parseInt(searchParams.get('take') || '10');
      
      const warrantyComponentRepository = getWarrantyComponentRepository();
      
      // Get expiring warranty components
      const components = await warrantyComponentRepository.findExpiring(days, skip, take);
      
      // Count expiring warranty components
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);
      
      const totalCount = await warrantyComponentRepository.countWithFilter({
        warrantyDate: {
          gte: today,
          lte: futureDate,
        },
      });
      
      return NextResponse.json({
        components,
        pagination: {
          skip,
          take,
          total: totalCount,
          hasMore: skip + take < totalCount,
        },
        expirationInfo: {
          days,
          expiringCount: totalCount,
        },
      });
    } catch (error) {
      console.error('Error fetching expiring warranty components:', error);
      return NextResponse.json(
        { error: 'Failed to fetch expiring warranty components' },
        { status: 500 }
      );
    }
  }
);
