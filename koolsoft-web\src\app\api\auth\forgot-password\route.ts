import { NextRequest, NextResponse } from 'next/server';
import { getUserRepository } from '@/lib/repositories';
import { z } from 'zod';
import { randomBytes } from 'crypto';
import { prisma } from '@/lib/prisma';
import { getEmailService } from '@/lib/services/email.service';
import { getActivityLogService } from '@/lib/services/activity-log.service';

/**
 * Forgot password schema
 */
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

// Simple in-memory rate limiting
// In a production environment, this should be replaced with a Redis-based solution
const rateLimitMap = new Map<string, { count: number, resetTime: number }>();
const MAX_ATTEMPTS = 5; // Maximum attempts per hour
const RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour in milliseconds

/**
 * Check if a request is rate limited
 * @param identifier The identifier to check (IP address or email)
 * @returns Whether the request is rate limited
 */
function isRateLimited(identifier: string): boolean {
  const now = Date.now();
  const rateLimit = rateLimitMap.get(identifier);

  if (!rateLimit) {
    // First attempt
    rateLimitMap.set(identifier, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return false;
  }

  if (now > rateLimit.resetTime) {
    // Reset window has passed
    rateLimitMap.set(identifier, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return false;
  }

  if (rateLimit.count >= MAX_ATTEMPTS) {
    // Too many attempts
    return true;
  }

  // Increment attempt count
  rateLimitMap.set(identifier, { count: rateLimit.count + 1, resetTime: rateLimit.resetTime });
  return false;
}

/**
 * POST /api/auth/forgot-password
 * Request a password reset link
 */
export async function POST(request: NextRequest) {
  try {
    // Get IP address for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 'unknown';

    // Check rate limiting
    if (isRateLimited(ip)) {
      // Log rate limit exceeded
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAuthEvent(
          'password_reset_rate_limited',
          undefined,
          {
            ip,
          }
        );
      } catch (logError) {
        console.error('Error logging rate limit:', logError);
      }

      return NextResponse.json(
        { error: 'Too many password reset attempts. Please try again later.' },
        { status: 429 }
      );
    }

    const body = await request.json();

    // Validate request body
    const validatedData = forgotPasswordSchema.parse(body);

    // Also rate limit by email to prevent targeted attacks
    if (isRateLimited(validatedData.email)) {
      return NextResponse.json(
        { error: 'Too many password reset attempts. Please try again later.' },
        { status: 429 }
      );
    }

    const userRepository = getUserRepository();

    // Find user by email
    const user = await userRepository.findByEmail(validatedData.email);

    // If user not found, return success anyway to prevent email enumeration
    if (!user || !user.isActive) {
      // Log attempt for non-existent user
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAuthEvent(
          'password_reset_requested',
          undefined,
          {
            email: validatedData.email,
            status: 'user_not_found',
          }
        );
      } catch (logError) {
        console.error('Error logging password reset request:', logError);
      }

      return NextResponse.json({
        message: 'If an account exists with this email, a password reset link will be sent.',
      });
    }

    // Generate reset token
    const token = randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 3600000); // 1 hour from now

    // Store token in database
    await prisma.passwordResetToken.upsert({
      where: { userId: user.id },
      update: {
        token,
        expires,
      },
      create: {
        userId: user.id,
        token,
        expires,
      },
    });

    // Generate reset URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const resetUrl = `${baseUrl}/auth/reset-password?token=${token}`;

    // Log password reset request
    try {
      const activityLogService = getActivityLogService();
      await activityLogService.logAuthEvent(
        'password_reset_requested',
        user.id,
        {
          email: user.email,
          status: 'email_sent',
        }
      );
    } catch (logError) {
      console.error('Error logging password reset request:', logError);
    }

    // Send email
    try {
      const emailService = getEmailService();
      await emailService.sendTemplateEmail(
        'password-reset',
        {
          name: user.name,
          resetUrl,
        },
        user.email,
        undefined,
        undefined,
        user.id
      );
    } catch (emailError) {
      console.error('Error sending password reset email:', emailError);

      // Log email sending failure
      try {
        const activityLogService = getActivityLogService();
        await activityLogService.logAuthEvent(
          'password_reset_email_failed',
          user.id,
          {
            email: user.email,
            error: emailError instanceof Error ? emailError.message : String(emailError),
          }
        );
      } catch (logError) {
        console.error('Error logging email failure:', logError);
      }

      // Continue anyway, don't expose error to client
    }

    return NextResponse.json({
      message: 'If an account exists with this email, a password reset link will be sent.',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error processing forgot password request:', error);
    return NextResponse.json(
      { error: 'Failed to process forgot password request' },
      { status: 500 }
    );
  }
}
