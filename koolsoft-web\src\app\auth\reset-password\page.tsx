'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';

// Reset password form validation schema
const resetPasswordSchema = z.object({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string().min(8, 'Password must be at least 8 characters'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

// Type for reset password form data
type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

/**
 * Reset Password Page Component
 *
 * This page allows users to reset their password using a token.
 */
export default function ResetPasswordPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  // If no token is provided, show an error
  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <h1 className="text-center text-3xl font-extrabold text-[#0F52BA]">KoolSoft</h1>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-black">
              Invalid Reset Link
            </h2>
          </div>

          <div className="rounded-md bg-[#ef4444] p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-white">
                  The password reset link is invalid or has expired.
                </h3>
                <div className="mt-2 text-sm text-white">
                  <p>
                    Please request a new password reset link.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <Link
              href="/auth/forgot-password"
              className="font-medium text-[#0F52BA] hover:text-blue-700"
            >
              Request a new reset link
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Initialize form with react-hook-form
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: ResetPasswordFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password: data.password,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || 'Failed to reset password');
        return;
      }

      setSuccess(true);

      // Redirect to login page after 2 seconds
      setTimeout(() => {
        router.push('/auth/login');
      }, 2000);
    } catch (error) {
      console.error('Reset password error:', error);
      setError('An error occurred while resetting your password');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h1 className="text-center text-3xl font-extrabold text-[#0F52BA]">KoolSoft</h1>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-black">
            Reset your password
          </h2>
          <p className="mt-2 text-center text-sm text-black">
            Enter your new password below.
          </p>
        </div>

        {success ? (
          <div className="rounded-md bg-[#0F52BA] p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-white">
                  Password reset successful!
                </h3>
                <div className="mt-2 text-sm text-white">
                  <p>
                    Your password has been reset successfully.
                    Redirecting to login page...
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-black">
                New Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  type="password"
                  autoComplete="new-password"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#0F52BA] focus:border-[#0F52BA] sm:text-sm text-black"
                  {...register('password')}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-[#ef4444]">{errors.password.message}</p>
                )}
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Password must be at least 8 characters and include uppercase, lowercase, and numbers.
              </p>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-black">
                Confirm New Password
              </label>
              <div className="mt-1">
                <input
                  id="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-[#0F52BA] focus:border-[#0F52BA] sm:text-sm text-black"
                  {...register('confirmPassword')}
                />
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-[#ef4444]">{errors.confirmPassword.message}</p>
                )}
              </div>
            </div>

            {error && (
              <div className="rounded-md bg-[#ef4444] p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-white">{error}</h3>
                  </div>
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#0F52BA] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0F52BA] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Resetting...' : 'Reset Password'}
              </button>
            </div>

            <div className="flex items-center justify-center">
              <Link
                href="/auth/login"
                className="font-medium text-[#0F52BA] hover:text-blue-700"
              >
                Return to login
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}
