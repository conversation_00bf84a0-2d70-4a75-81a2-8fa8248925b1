# Database Schema Updates

This document tracks updates to the KoolSoft database schema, including new tables, modifications to existing tables, and other schema changes.

## Recent Updates

### Added Reference Data Tables (July 2023)

Two new reference data tables have been added to the database schema:

#### 1. `spare_types` Table

This table stores information about different types of spare parts used in service operations.

**Table Structure:**

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated UUID |
| name | String | Name of the spare type |
| description | String (optional) | Description of the spare type |
| code | String (optional) | Code identifier for the spare type |
| active | Boolean | Whether the spare type is active (default: true) |
| original_id | Integer (optional) | Reference to original ID in legacy system |
| created_at | DateTime | Timestamp when the record was created |
| updated_at | DateTime | Timestamp when the record was last updated |

**Indexes:**
- `name` - For efficient searching by name
- `code` - For efficient searching by code
- `active` - For filtering active/inactive records
- `original_id` - For reference to legacy data

**Sample Data:**
- Electrical Component (Code: ELEC) - Parts related to electrical systems
- Mechanical Component (Code: MECH) - Parts related to mechanical systems
- Plumbing Component (Code: PLMB) - Parts related to plumbing systems
- HVAC Component (Code: HVAC) - Parts related to heating and cooling systems
- Electronic Component (Code: ELTN) - Parts related to electronic systems
- Structural Component (Code: STRC) - Parts related to structural elements
- Consumable (Code: CONS) - Parts that are regularly consumed or replaced
- Fastener (Code: FAST) - Screws, bolts, nuts, and other fastening components
- Gasket (Code: GASK) - Sealing components
- Filter (Code: FLTR) - Filtering components

#### 2. `measurement_types` Table

This table stores information about different types of measurements used in service operations.

**Table Structure:**

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, auto-generated UUID |
| name | String | Name of the measurement type |
| description | String (optional) | Description of the measurement type |
| code | String (optional) | Code identifier for the measurement type |
| active | Boolean | Whether the measurement type is active (default: true) |
| original_id | Integer (optional) | Reference to original ID in legacy system |
| created_at | DateTime | Timestamp when the record was created |
| updated_at | DateTime | Timestamp when the record was last updated |

**Indexes:**
- `name` - For efficient searching by name
- `code` - For efficient searching by code
- `active` - For filtering active/inactive records
- `original_id` - For reference to legacy data

**Sample Data:**
- Length (Code: LEN) - Measurement of physical distance
- Weight (Code: WGT) - Measurement of mass
- Volume (Code: VOL) - Measurement of three-dimensional space
- Temperature (Code: TEMP) - Measurement of heat
- Pressure (Code: PRES) - Measurement of force per unit area
- Flow Rate (Code: FLOW) - Measurement of volume per unit time
- Electrical Current (Code: CURR) - Measurement of electrical flow
- Voltage (Code: VOLT) - Measurement of electrical potential
- Resistance (Code: OHMS) - Measurement of electrical resistance
- Time (Code: TIME) - Measurement of duration

### Prisma Schema Updates

The Prisma schema has been updated to include models for these new tables:

```prisma
model SpareType {
  id          String   @id @default(uuid())
  name        String
  description String?
  code        String?
  active      Boolean  @default(true)
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([code])
  @@index([active])
  @@index([originalId])
  @@map("spare_types")
}

model MeasurementType {
  id          String   @id @default(uuid())
  name        String
  description String?
  code        String?
  active      Boolean  @default(true)
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([code])
  @@index([active])
  @@index([originalId])
  @@map("measurement_types")
}
```

### Reference Data Management

These new tables are now integrated with the Reference Data Management interface at `/reference-data/`. Users can view, create, update, and delete records in these tables through the web interface.

## Complete Reference Data Tables

The KoolSoft database now includes the following reference data tables:

1. `territories` - Geographic regions for business operations
2. `segments` - Customer segmentation categories
3. `competitors` - Competing businesses in the market
4. `service_visit_types` - Types of service visits conducted
5. `complaint_types` - Categories of customer complaints
6. `complaint_nature_types` - Nature or characteristics of complaints
7. `failure_types` - Categories of equipment failures
8. `spare_types` - Categories of spare parts (NEW)
9. `measurement_types` - Types of measurements used (NEW)
10. `priority_types` - Levels of priority for tasks and issues
11. `enquiry_types` - Categories of customer enquiries
12. `deduction_types` - Types of deductions applied
13. `debit_divisions` - Divisions for debit categorization
14. `account_divisions` - Divisions for account categorization
15. `spare_parts` - Replacement parts for equipment
16. `tax_rates` - Different tax rates applied to products
17. `transit_damage_types` - Types of damage that can occur during transit
18. `user_groups` - Groups of users with similar permissions
19. `usp_types` - Unique selling proposition categories
20. `visit_types` - Types of customer visits
21. `divisions` - Business divisions or departments
22. `brands` - Product brands and manufacturers
