const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDuplicates() {
  try {
    console.log('Checking for duplicate records...\n');

    // Check for duplicate customers
    console.log('=== CUSTOMERS ===');
    const customerDuplicates = await prisma.$queryRaw`
      SELECT name, COUNT(*) as count
      FROM customers 
      GROUP BY name 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;
    
    if (customerDuplicates.length > 0) {
      console.log('Found duplicate customers:');
      customerDuplicates.forEach(dup => {
        console.log(`- "${dup.name}": ${dup.count} records`);
      });
    } else {
      console.log('No duplicate customers found');
    }

    // Check for duplicate AMC payments
    console.log('\n=== AMC PAYMENTS ===');
    const paymentDuplicates = await prisma.$queryRaw`
      SELECT amc_contract_id, receipt_no, payment_date, amount, COUNT(*) as count
      FROM amc_payments 
      GROUP BY amc_contract_id, receipt_no, payment_date, amount
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;
    
    if (paymentDuplicates.length > 0) {
      console.log('Found duplicate AMC payments:');
      paymentDuplicates.forEach(dup => {
        console.log(`- Contract: ${dup.amc_contract_id}, Receipt: ${dup.receipt_no}, Date: ${dup.payment_date}, Amount: ${dup.amount}, Count: ${dup.count}`);
      });
    } else {
      console.log('No duplicate AMC payments found');
    }

    // Check for duplicate receipt numbers
    console.log('\n=== DUPLICATE RECEIPT NUMBERS ===');
    const receiptDuplicates = await prisma.$queryRaw`
      SELECT receipt_no, COUNT(*) as count
      FROM amc_payments 
      WHERE receipt_no IS NOT NULL AND receipt_no != ''
      GROUP BY receipt_no 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;
    
    if (receiptDuplicates.length > 0) {
      console.log('Found duplicate receipt numbers:');
      receiptDuplicates.forEach(dup => {
        console.log(`- Receipt No: "${dup.receipt_no}": ${dup.count} records`);
      });
    } else {
      console.log('No duplicate receipt numbers found');
    }

    // Get total counts
    console.log('\n=== TOTAL COUNTS ===');
    const customerCount = await prisma.customer.count();
    const paymentCount = await prisma.amc_payments.count();
    
    console.log(`Total customers: ${customerCount}`);
    console.log(`Total AMC payments: ${paymentCount}`);

    // Check specific customer ID from the URL
    console.log('\n=== SPECIFIC CUSTOMER CHECK ===');
    const specificCustomer = await prisma.customer.findUnique({
      where: { id: '82c3c4ac-3f0c-4aee-b0bb-a5330ba490e9' },
      include: {
        contacts: true,
        amcContracts: {
          include: {
            payments: true
          }
        }
      }
    });

    if (specificCustomer) {
      console.log(`Customer: ${specificCustomer.name}`);
      console.log(`Contacts: ${specificCustomer.contacts.length}`);
      console.log(`AMC Contracts: ${specificCustomer.amcContracts.length}`);
      
      specificCustomer.amcContracts.forEach((contract, index) => {
        console.log(`  Contract ${index + 1}: ${contract.payments.length} payments`);
      });
    } else {
      console.log('Specific customer not found');
    }

  } catch (error) {
    console.error('Error checking duplicates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDuplicates();
