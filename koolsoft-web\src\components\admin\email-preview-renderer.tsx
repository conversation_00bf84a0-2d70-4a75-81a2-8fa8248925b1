'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Loader2 } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface EmailPreviewRendererProps {
  subject: string;
  html: string;
  loading: boolean;
}

/**
 * Email Preview Renderer Component
 * 
 * This component renders the preview of an email template with the provided
 * subject and HTML content.
 */
export function EmailPreviewRenderer({
  subject,
  html,
  loading,
}: EmailPreviewRendererProps) {
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile'>('desktop');
  
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="subject-preview" className="text-black">Subject</Label>
        <Input
          id="subject-preview"
          value={subject}
          readOnly
          className="text-black bg-gray-50"
        />
      </div>
      
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-black">Email Preview</h3>
        <Tabs 
          value={viewMode} 
          onValueChange={(value) => setViewMode(value as 'desktop' | 'mobile')}
          className="w-auto"
        >
          <TabsList>
            <TabsTrigger value="desktop">Desktop</TabsTrigger>
            <TabsTrigger value="mobile">Mobile</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-96">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : html ? (
        <Card className={`overflow-hidden border border-gray-200 ${
          viewMode === 'mobile' ? 'max-w-[375px] mx-auto' : 'w-full'
        }`}>
          <CardContent className="p-0">
            <div className="bg-white">
              <iframe
                srcDoc={html}
                title="Email Preview"
                className="w-full min-h-[600px] border-0"
                sandbox="allow-same-origin"
              />
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="flex justify-center items-center h-96 border border-dashed border-gray-300 rounded-md bg-gray-50">
          <p className="text-gray-500">
            Select a template and enter test data to see the preview
          </p>
        </div>
      )}
    </div>
  );
}
