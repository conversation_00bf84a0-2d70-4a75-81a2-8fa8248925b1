import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/src/generated/prisma';

const prisma = new PrismaClient();

/**
 * GET handler to retrieve a legacy customer and its modern equivalent if it exists
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const legacyId = searchParams.get('legacyId');
    
    if (!legacyId || isNaN(parseInt(legacyId))) {
      return NextResponse.json(
        { error: 'Invalid or missing legacyId parameter' },
        { status: 400 }
      );
    }
    
    // Find the legacy customer
    const legacyCustomer = await prisma.legacyCustomer.findUnique({
      where: { id: parseInt(legacyId) }
    });
    
    if (!legacyCustomer) {
      return NextResponse.json(
        { error: 'Legacy customer not found' },
        { status: 404 }
      );
    }
    
    // Find the modern customer with this originalId
    const modernCustomer = await prisma.customer.findFirst({
      where: { originalId: parseInt(legacyId) },
      include: {
        contacts: true,
        visitCards: true
      }
    });
    
    return NextResponse.json({
      legacyCustomer,
      modernCustomer
    });
  } catch (error) {
    console.error('Error fetching customer data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST handler to create a modern customer from a legacy customer
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { legacyId } = body;
    
    if (!legacyId || isNaN(parseInt(legacyId))) {
      return NextResponse.json(
        { error: 'Invalid or missing legacyId parameter' },
        { status: 400 }
      );
    }
    
    // Find the legacy customer
    const legacyCustomer = await prisma.legacyCustomer.findUnique({
      where: { id: parseInt(legacyId) }
    });
    
    if (!legacyCustomer) {
      return NextResponse.json(
        { error: 'Legacy customer not found' },
        { status: 404 }
      );
    }
    
    // Check if a modern customer with this originalId already exists
    const existingModernCustomer = await prisma.customer.findFirst({
      where: { originalId: legacyCustomer.id }
    });
    
    if (existingModernCustomer) {
      return NextResponse.json(
        { error: 'Modern customer already exists for this legacy ID', customer: existingModernCustomer },
        { status: 409 }
      );
    }
    
    // Create a new modern customer based on the legacy customer data
    const modernCustomer = await prisma.customer.create({
      data: {
        name: legacyCustomer.name || 'Unknown',
        address: legacyCustomer.address,
        location: legacyCustomer.location,
        phone: legacyCustomer.phone1,
        phone1: legacyCustomer.phone1,
        phone2: legacyCustomer.phone2,
        phone3: legacyCustomer.phone3,
        mobile: legacyCustomer.mobile,
        fax: legacyCustomer.fax,
        email: legacyCustomer.email,
        birthDate: legacyCustomer.birthDate ? new Date(legacyCustomer.birthDate) : null,
        birthYear: legacyCustomer.birthYear,
        anniversaryDate: legacyCustomer.anniversaryDate ? new Date(legacyCustomer.anniversaryDate) : null,
        anniversaryYear: legacyCustomer.anniversaryYear,
        segmentId: legacyCustomer.segmentId,
        designation: legacyCustomer.designation,
        visitCardPath: legacyCustomer.visitCardPath,
        originalId: legacyCustomer.id,
      }
    });
    
    return NextResponse.json({
      message: 'Modern customer created successfully',
      customer: modernCustomer
    });
  } catch (error) {
    console.error('Error creating modern customer:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
