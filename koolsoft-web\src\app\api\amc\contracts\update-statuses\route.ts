import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { updateContractStatuses } from '@/lib/jobs/update-contract-status';

/**
 * POST /api/amc/contracts/update-statuses
 * Update statuses of AMC contracts based on their end dates
 *
 * This endpoint finds all ACTIVE contracts with end dates in the past
 * and updates their status to EXPIRED.
 *
 * @requires Authentication and ADMIN or MANAGER role
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      console.log('Update Contract Statuses API: Starting status update process');
      const result = await updateContractStatuses();

      if (result.success) {
        console.log(`Update Contract Statuses API: Successfully updated ${result.updatedCount} contracts`);
        return NextResponse.json({
          message: `Successfully updated ${result.updatedCount} contracts to EXPIRED status`,
          updatedCount: result.updatedCount,
        });
      } else {
        console.error('Update Contract Statuses API: Failed to update contract statuses', result.error);
        return NextResponse.json(
          {
            error: 'Failed to update contract statuses',
            message: result.error,
            details: result.details || {}
          },
          { status: 500 }
        );
      }
    } catch (error) {
      console.error('Error in update-statuses API:', error);
      return NextResponse.json(
        { error: 'Failed to update contract statuses', details: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      );
    }
  }
);
