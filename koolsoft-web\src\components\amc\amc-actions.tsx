'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { PlusCircle, FileDown, AlertTriangle, RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

/**
 * AMC Actions Component
 *
 * This component provides action buttons for the AMC list page.
 */
export function AMCActions() {
  const router = useRouter();
  const { data: session } = useSession();
  const [isUpdatingStatuses, setIsUpdatingStatuses] = useState(false);

  // Handle export to CSV
  const handleExport = async () => {
    try {
      const response = await fetch('/api/amc/contracts/export', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to export AMC contracts');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `amc-contracts-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting AMC contracts:', error);
      showErrorToast('Error', 'Failed to export AMC contracts');
    }
  };

  // Handle view expiring contracts
  const handleViewExpiring = () => {
    router.push('/amc/expiring');
  };

  // Handle update contract statuses
  const handleUpdateStatuses = async () => {
    try {
      setIsUpdatingStatuses(true);
      const response = await fetch('/api/amc/contracts/update-statuses', {
        method: 'POST',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update contract statuses');
      }

      const data = await response.json();
      showSuccessToast('Success', data.message);

      // Refresh the page to show updated statuses
      router.refresh();
    } catch (error) {
      console.error('Error updating contract statuses:', error);
      showErrorToast('Error', error instanceof Error ? error.message : 'Failed to update contract statuses');
    } finally {
      setIsUpdatingStatuses(false);
    }
  };

  // Check if user is admin or manager
  const isAdminOrManager = session?.user?.role === 'ADMIN' || session?.user?.role === 'MANAGER';

  return (
    <div className="flex items-center space-x-2">
      <Button asChild variant="secondary">
        <Link href="/amc/new">
          <PlusCircle className="h-4 w-4 mr-2" />
          New AMC
        </Link>
      </Button>
      <Button variant="outline" onClick={handleExport}>
        <FileDown className="h-4 w-4 mr-2" />
        Export
      </Button>
      <Button variant="outline" onClick={handleViewExpiring}>
        <AlertTriangle className="h-4 w-4 mr-2" />
        Expiring
      </Button>
      {isAdminOrManager && (
        <Button
          variant="outline"
          onClick={handleUpdateStatuses}
          disabled={isUpdatingStatuses}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isUpdatingStatuses ? 'animate-spin' : ''}`} />
          Update Statuses
        </Button>
      )}
    </div>
  );
}
