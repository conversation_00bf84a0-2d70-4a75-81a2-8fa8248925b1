// Script to generate a bcrypt hash for a password
const bcrypt = require('bcrypt');

const password = 'Admin@123';
const saltRounds = 10;

bcrypt.hash(password, saltRounds, function(err, hash) {
  if (err) {
    console.error('Error generating hash:', err);
    return;
  }
  
  console.log('Password:', password);
  console.log('Hashed Password:', hash);
  
  // Generate SQL statement
  console.log('\nSQL Statement:');
  console.log(`UPDATE users SET password = '${hash}' WHERE email = '<EMAIL>';`);
});
