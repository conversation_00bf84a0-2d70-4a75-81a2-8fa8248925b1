import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Role } from './role-check';

/**
 * Check if the user has the required role(s)
 * @param requiredRoles Role or array of roles required for access
 * @param userRole User's current role
 * @returns Boolean indicating if the user has the required role
 */
export function hasRequiredRole(requiredRoles: Role | Role[], userRole?: string): boolean {
  if (!userRole) return false;

  // Convert user role to uppercase for case-insensitive comparison
  const normalizedUserRole = userRole.toUpperCase();

  if (Array.isArray(requiredRoles)) {
    // Convert all required roles to uppercase for case-insensitive comparison
    return requiredRoles.map(role => role.toUpperCase()).includes(normalizedUserRole as Role);
  }

  // Compare with uppercase for case-insensitive comparison
  return normalizedUserRole === requiredRoles.toUpperCase();
}

/**
 * Higher-order function to protect API routes with role-based access control
 * This version takes roles first, then the handler (different from role-check.ts)
 *
 * @param requiredRoles Role or array of roles required for access
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withRoleProtection(
  requiredRoles: Role | Role[],
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: any) => {
    try {
      console.log('withRoleProtection: Checking authentication for route:', request.url);

      // Get the current session
      const session = await getServerSession(authOptions);

      console.log('withRoleProtection: Session result:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        userRole: session?.user?.role,
        requiredRoles
      });

      // Check if the user is authenticated
      if (!session || !session.user) {
        console.log('withRoleProtection: Authentication failed - no session or user');
        return NextResponse.json(
          { error: 'Unauthorized - No valid session found' },
          { status: 401 }
        );
      }

      // Get the user's role
      const userRole = session.user.role;

      // Check if the user has the required role
      const hasRole = hasRequiredRole(requiredRoles, userRole);
      console.log('withRoleProtection: Role check result:', {
        userRole,
        requiredRoles,
        hasRequiredRole: hasRole
      });

      if (!hasRole) {
        console.log('withRoleProtection: Authorization failed - insufficient permissions');
        return NextResponse.json(
          { error: `Forbidden: Insufficient permissions. Required: ${JSON.stringify(requiredRoles)}, Found: ${userRole}` },
          { status: 403 }
        );
      }

      console.log('withRoleProtection: Authentication and authorization successful');

      // User has the required role, proceed with the handler
      return handler(request, context);
    } catch (error) {
      console.error('withRoleProtection: Error during authentication check:', error);
      return NextResponse.json(
        { error: 'Authentication error', details: error instanceof Error ? error.message : 'Unknown error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Higher-order function to protect API routes with admin-only access
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withAdminProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return withRoleProtection(['ADMIN'], handler);
}

/**
 * Higher-order function to protect API routes with manager-only access
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withManagerProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return withRoleProtection(['ADMIN', 'MANAGER'], handler);
}

/**
 * Higher-order function to protect API routes with executive-only access
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withExecutiveProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE'], handler);
}

/**
 * Higher-order function to protect API routes with authentication only
 * @param handler API route handler function
 * @returns Protected API route handler function
 */
export function withAuthProtection(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], handler);
}
