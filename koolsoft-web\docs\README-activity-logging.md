# Activity Logging System Implementation

This README provides an overview of the activity logging system implementation for the KoolSoft modernization project.

## Overview

The activity logging system tracks user actions and system events for auditing and monitoring purposes. It provides:

- Comprehensive logging of user activities
- Logging middleware for API routes
- Logging for authentication events
- Logging for critical operations
- Activity log viewer with filtering and pagination
- Log export functionality
- Protected access control

## Implementation Details

### Database Schema

The activity logs are stored in the `activity_logs` table with the following schema:

- `id`: UUID (Primary key)
- `userId`: UUID (Foreign key to users table)
- `action`: String (The action performed)
- `entityType`: String (The type of entity affected)
- `entityId`: String (The ID of the entity affected)
- `details`: JSON (Additional details about the action)
- `ipAddress`: String (The IP address of the user)
- `userAgent`: String (The user agent of the user)
- `createdAt`: DateTime (When the action was performed)

### Components

The implementation consists of the following components:

1. **Database Schema**: `prisma/schema-updates-activity-logs.prisma`
2. **Repository**: `lib/repositories/activity-log.repository.ts`
3. **Service**: `lib/services/activity-log.service.ts`
4. **Middleware**: `lib/middleware/activity-logger.middleware.ts`
5. **API Routes**:
   - `app/api/admin/activity-logs/route.ts`
   - `app/api/admin/activity-logs/export/route.ts`
6. **UI Components**:
   - `app/admin/activity-logs/page.tsx`
   - `components/admin/activity-log-list.tsx`
   - `components/admin/activity-log-filter-form.tsx`
7. **Authentication Integration**: Updates to `lib/auth.ts`
8. **Scripts**: `scripts/create-activity-logs-table.js` and `scripts/update-prisma-schema-activity-logs.js`
9. **Tests**:
   - `__tests__/services/activity-log.service.test.ts`
   - `__tests__/middleware/activity-logger.middleware.test.ts`
10. **Documentation**: `docs/activity-logging-system.md`

### Installation

To install the activity logging system:

1. Update the Prisma schema:
   ```bash
   npm run db:create-activity-logs
   ```

2. Restart the application:
   ```bash
   npm run dev
   ```

### Usage

#### Logging Activities

```typescript
import { getActivityLogService } from '@/lib/services/activity-log.service';

// Get the activity log service
const activityLogService = getActivityLogService();

// Log a user action
await activityLogService.logAction(
  'create_customer',
  userId,
  'customer',
  customerId,
  { name: customerName, email: customerEmail }
);
```

#### Using the Activity Logger Middleware

```typescript
import { withActivityLogging } from '@/lib/middleware/activity-logger.middleware';

// Wrap an API route handler with activity logging
export const GET = withActivityLogging(
  async (req: NextRequest) => {
    // Handler implementation
    return NextResponse.json({ data: 'example' });
  },
  {
    action: 'view_customers',
    entityType: 'customer',
  }
);
```

### Accessing the Activity Log Viewer

The activity log viewer is available at `/admin/activity-logs` and is only accessible to admin users.

## Testing

To run the tests for the activity logging system:

```bash
npm test -- --testPathPattern=activity-log
```

## Documentation

For detailed documentation, see `docs/activity-logging-system.md`.

## Future Enhancements

Potential future enhancements include:

1. **Real-time Logging**: Implement WebSocket-based real-time activity log updates
2. **Advanced Analytics**: Add analytics and visualization for activity patterns
3. **Automated Alerts**: Set up alerts for suspicious activities
4. **Custom Log Retention**: Implement configurable log retention policies
5. **Log Archiving**: Add functionality to archive logs for long-term storage

## Troubleshooting

### Common Issues

1. **Missing Logs**: Ensure that the activity logging middleware is properly applied to API routes
2. **Performance Issues**: If experiencing performance issues, consider:
   - Implementing batched logging for high-volume operations
   - Adding more specific indexes to the activity_logs table
   - Implementing log rotation for older logs

### Debugging

To enable debug logging:

```typescript
// In your .env file
DEBUG=activity-log:*
```

## Contributing

When contributing to the activity logging system:

1. Follow the established patterns for logging actions
2. Use consistent naming for actions and entity types
3. Add appropriate tests for new functionality
4. Update documentation as needed

## License

This implementation is part of the KoolSoft modernization project and is subject to the same license terms as the main project.
