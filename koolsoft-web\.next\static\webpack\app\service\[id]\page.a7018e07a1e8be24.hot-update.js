"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/service/[id]/page",{

/***/ "(app-pages-browser)/./src/app/service/[id]/page.tsx":
/*!***************************************!*\
  !*** ./src/app/service/[id]/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServiceReportDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,FileText,Settings,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ServiceReportDetailPage() {\n    _s();\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const [serviceReport, setServiceReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [completing, setCompleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ServiceReportDetailPage.useEffect\": ()=>{\n            if (params.id) {\n                loadServiceReport(params.id);\n            }\n        }\n    }[\"ServiceReportDetailPage.useEffect\"], [\n        params.id\n    ]);\n    const loadServiceReport = async (id)=>{\n        try {\n            const response = await fetch(\"/api/service/\".concat(id), {\n                credentials: 'include'\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setServiceReport(data.serviceReport);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to load service report');\n                router.push('/service');\n            }\n        } catch (error) {\n            console.error('Error loading service report:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to load service report');\n            router.push('/service');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/service');\n    };\n    const handleEdit = ()=>{\n        router.push(\"/service/\".concat(params.id, \"/edit\"));\n    };\n    const handleMarkCompleted = async ()=>{\n        if (!serviceReport) return;\n        setCompleting(true);\n        try {\n            const response = await fetch(\"/api/service/\".concat(params.id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    status: 'COMPLETED',\n                    completionDate: new Date().toISOString()\n                })\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success('Service report marked as completed');\n                // Reload the service report data\n                loadServiceReport(params.id);\n            } else {\n                const error = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(error.error || 'Failed to mark service as completed');\n            }\n        } catch (error) {\n            console.error('Error marking service as completed:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to mark service as completed');\n        } finally{\n            setCompleting(false);\n        }\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('Are you sure you want to delete this service report?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/service/\".concat(params.id), {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success('Service report deleted successfully');\n                router.push('/service');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to delete service report');\n            }\n        } catch (error) {\n            console.error('Error deleting service report:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to delete service report');\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusConfig = {\n            OPEN: {\n                variant: 'secondary',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: 'Open'\n            },\n            IN_PROGRESS: {\n                variant: 'default',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                label: 'In Progress'\n            },\n            COMPLETED: {\n                variant: 'default',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                label: 'Completed'\n            },\n            CANCELLED: {\n                variant: 'destructive',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                label: 'Cancelled'\n            },\n            PENDING: {\n                variant: 'secondary',\n                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                label: 'Pending'\n            }\n        };\n        const config = statusConfig[status] || statusConfig.OPEN;\n        const Icon = config.icon;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n            variant: config.variant,\n            className: \"flex items-center gap-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                config.label\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 131,\n            columnNumber: 12\n        }, this);\n    };\n    const getComplaintTypeBadge = (type)=>{\n        const typeConfig = {\n            REPAIR: 'bg-red-100 text-red-800',\n            MAINTENANCE: 'bg-blue-100 text-blue-800',\n            INSTALLATION: 'bg-green-100 text-green-800',\n            INSPECTION: 'bg-yellow-100 text-yellow-800',\n            WARRANTY: 'bg-purple-100 text-purple-800',\n            OTHER: 'bg-gray-100 text-gray-800'\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(typeConfig[type] || typeConfig.OTHER),\n            children: type\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 12\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Loading service report...\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 150,\n            columnNumber: 12\n        }, this);\n    }\n    if (!serviceReport) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Service report not found.\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: handleBack,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            \"Back to Service Reports\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleEdit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"destructive\",\n                                onClick: handleDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                \"Service Report Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Report Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.reportDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this),\n                                            serviceReport.visitDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Visit Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.visitDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 43\n                                            }, this),\n                                            serviceReport.completionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Completion Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(serviceReport.completionDate), 'PPP')\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 48\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Nature of Service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: serviceReport.natureOfService\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1\",\n                                                        children: getStatusBadge(serviceReport.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Complaint Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1\",\n                                                        children: getComplaintTypeBadge(serviceReport.complaintType)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Customer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: serviceReport.customer.name\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.customer.city\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    serviceReport.customer.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.customer.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 50\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-muted-foreground\",\n                                                        children: \"Executive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: serviceReport.executive.name\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    serviceReport.executive.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: serviceReport.executive.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 51\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            serviceReport.actionTaken && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Action Taken\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-1\",\n                                                children: serviceReport.actionTaken\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            serviceReport.remarks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {\n                                        className: \"my-6\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Remarks\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-1\",\n                                                children: serviceReport.remarks\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        className: \"bg-primary text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_FileText_Settings_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                \"Service Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: serviceReport.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-4\",\n                                            children: [\n                                                \"Service Detail \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Machine Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.machineType\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Serial Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.serialNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Problem\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.problem\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Solution\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.solution\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this),\n                                                detail.partReplaced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-muted-foreground\",\n                                                            children: \"Part Replaced\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: detail.partReplaced\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 43\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, detail.id, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 59\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\service\\\\[id]\\\\page.tsx\",\n        lineNumber: 183,\n        columnNumber: 10\n    }, this);\n}\n_s(ServiceReportDetailPage, \"VSdea1A+Y/clvNYryaSXt/iNff8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c1 = ServiceReportDetailPage;\n_s1(ServiceReportDetailPage, \"VSdea1A+Y/clvNYryaSXt/iNff8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c = ServiceReportDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ServiceReportDetailPage\");\nvar _c1;\n$RefreshReg$(_c1, \"ServiceReportDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/service/[id]/page.tsx\n"));

/***/ })

});