"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(rsc)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(rsc)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split('.').map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, 'access_token', // OAuth 2.0\n    'code', // OAuth 2.0\n    'error_description', // OAuth 2.0\n    'error_uri', // OAuth 2.0\n    'error', // OAuth 2.0\n    'expires_in', // OAuth 2.0\n    'id_token', // OIDC Core 1.0\n    'iss', // draft-ietf-oauth-iss-auth-resp\n    'response', // FAPI JARM\n    'session_state', // OIDC Session Management\n    'state', // OAuth 2.0\n    'token_type' // OAuth 2.0\n    );\n}\nfunction authorizationHeaderValue(token, tokenType = 'Bearer') {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: 'openid',\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === 'claims' && typeof value === 'object') {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === 'resource' && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== 'string') {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !('kty' in k))) {\n        throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes('client_secret_post')) {\n                properties.token_endpoint_auth_method = 'client_secret_post';\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError('provide a redirect_uri or redirect_uris, not both');\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError('provide a response_type or response_types, not both');\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== 'string' || !metadata.client_id) {\n            throw new TypeError('client_id is required');\n        }\n        const properties = {\n            grant_types: [\n                'authorization_code'\n            ],\n            id_token_signed_response_alg: 'RS256',\n            authorization_signed_response_alg: 'RS256',\n            response_types: [\n                'code'\n            ],\n            token_endpoint_auth_method: 'client_secret_basic',\n            ...this.fapi1() ? {\n                grant_types: [\n                    'authorization_code',\n                    'implicit'\n                ],\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                response_types: [\n                    'code id_token'\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case 'self_signed_tls_client_auth':\n                case 'tls_client_auth':\n                    break;\n                case 'private_key_jwt':\n                    if (!jwks) {\n                        throw new TypeError('jwks is required');\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError('token_endpoint_auth_method is required');\n                default:\n                    throw new TypeError('invalid or unsupported token_endpoint_auth_method');\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport('token', this.issuer, properties);\n        [\n            'introspection',\n            'revocation'\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        assertIssuerConfiguration(this.issuer, 'authorization_endpoint');\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, '%20');\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join('\\n');\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, 'end_session_endpoint');\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === 'string';\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError('#callbackParams only accepts string urls, http.IncomingMessage or a lookalike');\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case 'GET':\n                    return pickCb(getSearchParams(input.url));\n                case 'POST':\n                    if (input.body === undefined) {\n                        throw new TypeError('incoming message body missing, include a body parser prior to this method call');\n                    }\n                    switch(typeof input.body){\n                        case 'object':\n                        case 'string':\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString('utf-8')));\n                            }\n                            if (typeof input.body === 'string') {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError('invalid IncomingMessage body object');\n                    }\n                default:\n                    throw new TypeError('invalid IncomingMessage method');\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            id_token: [\n                'id_token'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'authorization', checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'token', checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === 'string' && params.id_token.length) {\n            throw new RPError({\n                message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === 'string' && tokenset.id_token.length) {\n                throw new RPError({\n                    message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = 'A128CBC-HS256') {\n        const header = JSON.parse(base64url.decode(jwe.split('.')[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE enc received, expected %s, got: %s',\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: 'enc'\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === 'dir' ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: 'failed to decrypt JWE',\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === 'number' || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: 'missing required JWT property auth_time',\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== 'number') {\n                throw new RPError({\n                    message: 'JWT auth_time claim must be a JSON numeric value',\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === 'number' && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    'too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i',\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    'nonce mismatch, expected %s, got: %s',\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === 'authorization') {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: 'missing required property at_hash',\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: 'missing required property c_hash',\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: 'missing required property s_hash',\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: 's_hash',\n                        source: 'state'\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    'JWT issued too far in the past, now %i, iat %i',\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'at_hash',\n                    source: 'access_token'\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'c_hash',\n                    source: 'code'\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        'iss',\n        'sub',\n        'aud',\n        'exp',\n        'iat'\n    ]) {\n        const isSelfIssued = this.issuer.issuer === 'https://self-issued.me';\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    'failed to decode JWT (%s: %s)',\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWT alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                'sub_jwk'\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace('{tenantid}', payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        'unexpected iss value, expected %s, got: %s',\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== 'number') {\n                throw new RPError({\n                    message: 'JWT iat claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== 'number') {\n                throw new RPError({\n                    message: 'JWT nbf claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        'JWT not active yet, now %i, nbf %i',\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== 'number') {\n                throw new RPError({\n                    message: 'JWT exp claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        'JWT expired, now %i, exp %i',\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: 'missing required JWT property azp',\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            'aud is missing the client_id, expected %s to be included in %j',\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        'aud mismatch, expected %s, got: %s',\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === 'string') {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        'azp mismatch, got: %s',\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, 'public');\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: 'failed to use sub_jwk claim as an asymmetric JSON Web Key',\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: 'failed to match the subject with sub_jwk',\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith('HS')) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== 'none') {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: 'sig'\n            });\n        }\n        if (!keys && header.alg === 'none') {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: 'failed to validate JWT signature',\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError('refresh_token not present in TokenSet');\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: 'refresh_token',\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, 'token', skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            'sub mismatch, expected %s, got: %s',\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? 'DPoP' : accessToken instanceof TokenSet ? accessToken.token_type : 'Bearer' } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError('access_token not present in TokenSet');\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError('no access token provided');\n        } else if (typeof accessToken !== 'string') {\n            throw new TypeError('invalid access token provided');\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: 'buffer',\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers['www-authenticate'];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith('dpop ') && parseWwwAuthenticate(wwwAuthenticate).error === 'use_dpop_nonce') {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = 'GET', via = 'header', tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'userinfo_endpoint');\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== 'GET' && options.method !== 'POST') {\n            throw new TypeError('#userinfo() method can only be POST or a GET');\n        }\n        if (via === 'body' && options.method !== 'POST') {\n            throw new TypeError('can only send body on POST');\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: 'application/jwt'\n            };\n        } else {\n            options.headers = {\n                Accept: 'application/json'\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === 'body') {\n            options.headers.Authorization = undefined;\n            options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n            options.body = new URLSearchParams();\n            options.body.append('access_token', accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === 'GET') {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers['content-type'])) {\n                throw new RPError({\n                    message: 'expected application/jwt response from the userinfo_endpoint',\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: 'failed to parse userinfo JWE payload as JSON',\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, 'response', {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        'userinfo sub mismatch, expected %s, got: %s',\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? 'sha256' : len <= 384 ? 'sha384' : len <= 512 ? 'sha512' : false;\n        if (!hash) {\n            throw new Error('unsupported symmetric encryption key derivation');\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError('client_secret is required');\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const response = await authenticatedPost.call(this, 'token', {\n            form: body,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === 'use_dpop_nonce') {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'device_authorization_endpoint');\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, 'device_authorization', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'revocation_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'revocation', {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'introspection_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'introspection', {\n            form,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, 'registration_endpoint');\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: 'application/json',\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: 'json',\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: 'POST'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: 'GET',\n            url: registrationClientUri,\n            responseType: 'json',\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: 'application/json'\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || 'none', encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || 'A128CBC-HS256' } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError('requestObject must be a plain object');\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: 'oauth-authz-req+jwt'\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === 'none') {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                ''\n            ].join('.');\n        } else {\n            const symmetric = signingAlgorithm.startsWith('HS');\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: 'sig'\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: 'oauth-authz-req+jwt'\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: 'enc'\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === 'dir' ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'pushed_authorization_request_endpoint');\n        const body = {\n            ...'request' in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, 'pushed_authorization_request', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!('expires_in' in responseBody)) {\n            throw new RPError({\n                message: 'expected expires_in in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== 'number') {\n            throw new RPError({\n                message: 'invalid expires_in value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (!('request_uri' in responseBody)) {\n            throw new RPError({\n                message: 'expected request_uri in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== 'string') {\n            throw new RPError({\n                message: 'invalid request_uri value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === 'FAPI1Client';\n    }\n    fapi2() {\n        return this.constructor.name === 'FAPI2Client';\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            'iss',\n            'exp',\n            'aud'\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError('payload must be a plain object');\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === 'node:crypto') {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError('unrecognized crypto runtime');\n        }\n        if (privateKey.type !== 'private') {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError('could not determine DPoP JWS Algorithm');\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash('sha256').update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: 'dpop+jwt',\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        case 'ECDSA':\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case 'P-256':\n                        return 'ES256';\n                    case 'P-384':\n                        return 'ES384';\n                    case 'P-521':\n                        return 'ES512';\n                    default:\n                        break;\n                }\n                break;\n            }\n        case 'RSASSA-PKCS1-v1_5':\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case 'RSA-PSS':\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError('unsupported DPoP private key');\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === 'node:crypto') {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case 'ed25519':\n            case 'ed448':\n                return 'EdDSA';\n            case 'ec':\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case 'rsa':\n            case rsaPssParams && 'rsa-pss':\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError('unsupported DPoP private key');\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === 'object' && privateKeyInput.format === 'jwk' && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === 'rsa-pss') {\n                candidates = candidates.filter((value)=>value.startsWith('PS'));\n            }\n            return [\n                'PS256',\n                'PS384',\n                'PS512',\n                'RS256',\n                'RS384',\n                'RS384'\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return 'PS256';\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.key.crv){\n            case 'P-256':\n                return 'ES256';\n            case 'secp256k1':\n                return 'ES256K';\n            case 'P-384':\n                return 'ES384';\n            case 'P-512':\n                return 'ES512';\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: 'der',\n            type: 'pkcs8'\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return 'ES256';\n        }\n        if (curveOid.equals(p384)) {\n            return 'ES384';\n        }\n        if (curveOid.equals(p521)) {\n            return 'ES512';\n        }\n        if (curveOid.equals(secp256k1)) {\n            return 'ES256K';\n        }\n        throw new TypeError('unsupported DPoP private key curve');\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === 'node:crypto' && typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.format === 'jwk') {\n        return pick(privateKeyInput.key, 'kty', 'crv', 'x', 'y', 'e', 'n');\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), 'kty', 'crv', 'x', 'y', 'e', 'n');\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === 'WebCryptoAPI') {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  inspect\n} = __webpack_require__(/*! util */ \"util\");\nconst {\n  RPError,\n  OPError\n} = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nclass DeviceFlowHandle {\n  #aborted;\n  #client;\n  #clientAssertionPayload;\n  #DPoP;\n  #exchangeBody;\n  #expires_at;\n  #interval;\n  #maxAge;\n  #response;\n  constructor({\n    client,\n    exchangeBody,\n    clientAssertionPayload,\n    response,\n    maxAge,\n    DPoP\n  }) {\n    ['verification_uri', 'user_code', 'device_code'].forEach(prop => {\n      if (typeof response[prop] !== 'string' || !response[prop]) {\n        throw new RPError(`expected ${prop} string to be returned by Device Authorization Response, got %j`, response[prop]);\n      }\n    });\n    if (!Number.isSafeInteger(response.expires_in)) {\n      throw new RPError('expected expires_in number to be returned by Device Authorization Response, got %j', response.expires_in);\n    }\n    this.#expires_at = now() + response.expires_in;\n    this.#client = client;\n    this.#DPoP = DPoP;\n    this.#maxAge = maxAge;\n    this.#exchangeBody = exchangeBody;\n    this.#clientAssertionPayload = clientAssertionPayload;\n    this.#response = response;\n    this.#interval = response.interval * 1000 || 5000;\n  }\n  abort() {\n    this.#aborted = true;\n  }\n  async poll({\n    signal\n  } = {}) {\n    if (signal && signal.aborted || this.#aborted) {\n      throw new RPError('polling aborted');\n    }\n    if (this.expired()) {\n      throw new RPError('the device code %j has expired and the device authorization session has concluded', this.device_code);\n    }\n    await new Promise(resolve => setTimeout(resolve, this.#interval));\n    let tokenset;\n    try {\n      tokenset = await this.#client.grant({\n        ...this.#exchangeBody,\n        grant_type: 'urn:ietf:params:oauth:grant-type:device_code',\n        device_code: this.device_code\n      }, {\n        clientAssertionPayload: this.#clientAssertionPayload,\n        DPoP: this.#DPoP\n      });\n    } catch (err) {\n      switch (err instanceof OPError && err.error) {\n        case 'slow_down':\n          this.#interval += 5000;\n        case 'authorization_pending':\n          return this.poll({\n            signal\n          });\n        default:\n          throw err;\n      }\n    }\n    if ('id_token' in tokenset) {\n      await this.#client.decryptIdToken(tokenset);\n      await this.#client.validateIdToken(tokenset, undefined, 'token', this.#maxAge);\n    }\n    return tokenset;\n  }\n  get device_code() {\n    return this.#response.device_code;\n  }\n  get user_code() {\n    return this.#response.user_code;\n  }\n  get verification_uri() {\n    return this.#response.verification_uri;\n  }\n  get verification_uri_complete() {\n    return this.#response.verification_uri_complete;\n  }\n  get expires_in() {\n    return Math.max.apply(null, [this.#expires_at - now(), 0]);\n  }\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.#response, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true\n    })}`;\n  }\n}\nmodule.exports = DeviceFlowHandle;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZGV2aWNlX2Zsb3dfaGFuZGxlLmpzIiwibWFwcGluZ3MiOiI7O0FBQUEsTUFBTTtFQUFFQTtBQUFRLENBQUMsR0FBR0MsbUJBQU8sQ0FBQyxrQkFBTSxDQUFDO0FBRW5DLE1BQU07RUFBRUMsT0FBTztFQUFFQztBQUFRLENBQUMsR0FBR0YsbUJBQU8sQ0FBQyxrRUFBVSxDQUFDO0FBQ2hELE1BQU1HLEdBQUcsR0FBR0gsbUJBQU8sQ0FBQyxrR0FBMEIsQ0FBQztBQUUvQyxNQUFNSSxnQkFBZ0IsQ0FBQztFQUNyQixDQUFDQyxPQUFPO0VBQ1IsQ0FBQ0MsTUFBTTtFQUNQLENBQUNDLHNCQUFzQjtFQUN2QixDQUFDQyxJQUFJO0VBQ0wsQ0FBQ0MsWUFBWTtFQUNiLENBQUNDLFVBQVU7RUFDWCxDQUFDQyxRQUFRO0VBQ1QsQ0FBQ0MsTUFBTTtFQUNQLENBQUNDLFFBQVE7RUFDVEMsV0FBV0EsQ0FBQztJQUFFUixNQUFNO0lBQUVHLFlBQVk7SUFBRUYsc0JBQXNCO0lBQUVNLFFBQVE7SUFBRUQsTUFBTTtJQUFFSjtFQUFLLENBQUMsRUFBRTtJQUNwRixDQUFDLGtCQUFrQixFQUFFLFdBQVcsRUFBRSxhQUFhLENBQUMsQ0FBQ08sT0FBTyxDQUFFQyxJQUFJLElBQUs7TUFDakUsSUFBSSxPQUFPSCxRQUFRLENBQUNHLElBQUksQ0FBQyxLQUFLLFFBQVEsSUFBSSxDQUFDSCxRQUFRLENBQUNHLElBQUksQ0FBQyxFQUFFO1FBQ3pELE1BQU0sSUFBSWYsT0FBTyxDQUNkLFlBQVdlLElBQUssaUVBQWdFLEVBQ2pGSCxRQUFRLENBQUNHLElBQUksQ0FDZixDQUFDO01BQ0g7SUFDRixDQUFDLENBQUM7SUFFRixJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsYUFBYSxDQUFDTCxRQUFRLENBQUNNLFVBQVUsQ0FBQyxFQUFFO01BQzlDLE1BQU0sSUFBSWxCLE9BQU8sQ0FDZixvRkFBb0YsRUFDcEZZLFFBQVEsQ0FBQ00sVUFDWCxDQUFDO0lBQ0g7SUFFQSxJQUFJLENBQUMsQ0FBQ1QsVUFBVSxHQUFHUCxHQUFHLENBQUMsQ0FBQyxHQUFHVSxRQUFRLENBQUNNLFVBQVU7SUFDOUMsSUFBSSxDQUFDLENBQUNiLE1BQU0sR0FBR0EsTUFBTTtJQUNyQixJQUFJLENBQUMsQ0FBQ0UsSUFBSSxHQUFHQSxJQUFJO0lBQ2pCLElBQUksQ0FBQyxDQUFDSSxNQUFNLEdBQUdBLE1BQU07SUFDckIsSUFBSSxDQUFDLENBQUNILFlBQVksR0FBR0EsWUFBWTtJQUNqQyxJQUFJLENBQUMsQ0FBQ0Ysc0JBQXNCLEdBQUdBLHNCQUFzQjtJQUNyRCxJQUFJLENBQUMsQ0FBQ00sUUFBUSxHQUFHQSxRQUFRO0lBQ3pCLElBQUksQ0FBQyxDQUFDRixRQUFRLEdBQUdFLFFBQVEsQ0FBQ0YsUUFBUSxHQUFHLElBQUksSUFBSSxJQUFJO0VBQ25EO0VBRUFTLEtBQUtBLENBQUEsRUFBRztJQUNOLElBQUksQ0FBQyxDQUFDZixPQUFPLEdBQUcsSUFBSTtFQUN0QjtFQUVBLE1BQU1nQixJQUFJQSxDQUFDO0lBQUVDO0VBQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFO0lBQzFCLElBQUtBLE1BQU0sSUFBSUEsTUFBTSxDQUFDakIsT0FBTyxJQUFLLElBQUksQ0FBQyxDQUFDQSxPQUFPLEVBQUU7TUFDL0MsTUFBTSxJQUFJSixPQUFPLENBQUMsaUJBQWlCLENBQUM7SUFDdEM7SUFFQSxJQUFJLElBQUksQ0FBQ3NCLE9BQU8sQ0FBQyxDQUFDLEVBQUU7TUFDbEIsTUFBTSxJQUFJdEIsT0FBTyxDQUNmLG1GQUFtRixFQUNuRixJQUFJLENBQUN1QixXQUNQLENBQUM7SUFDSDtJQUVBLE1BQU0sSUFBSUMsT0FBTyxDQUFFQyxPQUFPLElBQUtDLFVBQVUsQ0FBQ0QsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFDZixRQUFRLENBQUMsQ0FBQztJQUVuRSxJQUFJaUIsUUFBUTtJQUNaLElBQUk7TUFDRkEsUUFBUSxHQUFHLE1BQU0sSUFBSSxDQUFDLENBQUN0QixNQUFNLENBQUN1QixLQUFLLENBQ2pDO1FBQ0UsR0FBRyxJQUFJLENBQUMsQ0FBQ3BCLFlBQVk7UUFDckJxQixVQUFVLEVBQUUsOENBQThDO1FBQzFETixXQUFXLEVBQUUsSUFBSSxDQUFDQTtNQUNwQixDQUFDLEVBQ0Q7UUFBRWpCLHNCQUFzQixFQUFFLElBQUksQ0FBQyxDQUFDQSxzQkFBc0I7UUFBRUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDQTtNQUFLLENBQzNFLENBQUM7SUFDSCxDQUFDLENBQUMsT0FBT3VCLEdBQUcsRUFBRTtNQUNaLFFBQVFBLEdBQUcsWUFBWTdCLE9BQU8sSUFBSTZCLEdBQUcsQ0FBQ0MsS0FBSztRQUN6QyxLQUFLLFdBQVc7VUFDZCxJQUFJLENBQUMsQ0FBQ3JCLFFBQVEsSUFBSSxJQUFJO1FBQ3hCLEtBQUssdUJBQXVCO1VBQzFCLE9BQU8sSUFBSSxDQUFDVSxJQUFJLENBQUM7WUFBRUM7VUFBTyxDQUFDLENBQUM7UUFDOUI7VUFDRSxNQUFNUyxHQUFHO01BQ2I7SUFDRjtJQUVBLElBQUksVUFBVSxJQUFJSCxRQUFRLEVBQUU7TUFDMUIsTUFBTSxJQUFJLENBQUMsQ0FBQ3RCLE1BQU0sQ0FBQzJCLGNBQWMsQ0FBQ0wsUUFBUSxDQUFDO01BQzNDLE1BQU0sSUFBSSxDQUFDLENBQUN0QixNQUFNLENBQUM0QixlQUFlLENBQUNOLFFBQVEsRUFBRU8sU0FBUyxFQUFFLE9BQU8sRUFBRSxJQUFJLENBQUMsQ0FBQ3ZCLE1BQU0sQ0FBQztJQUNoRjtJQUVBLE9BQU9nQixRQUFRO0VBQ2pCO0VBRUEsSUFBSUosV0FBV0EsQ0FBQSxFQUFHO0lBQ2hCLE9BQU8sSUFBSSxDQUFDLENBQUNYLFFBQVEsQ0FBQ1csV0FBVztFQUNuQztFQUVBLElBQUlZLFNBQVNBLENBQUEsRUFBRztJQUNkLE9BQU8sSUFBSSxDQUFDLENBQUN2QixRQUFRLENBQUN1QixTQUFTO0VBQ2pDO0VBRUEsSUFBSUMsZ0JBQWdCQSxDQUFBLEVBQUc7SUFDckIsT0FBTyxJQUFJLENBQUMsQ0FBQ3hCLFFBQVEsQ0FBQ3dCLGdCQUFnQjtFQUN4QztFQUVBLElBQUlDLHlCQUF5QkEsQ0FBQSxFQUFHO0lBQzlCLE9BQU8sSUFBSSxDQUFDLENBQUN6QixRQUFRLENBQUN5Qix5QkFBeUI7RUFDakQ7RUFFQSxJQUFJbkIsVUFBVUEsQ0FBQSxFQUFHO0lBQ2YsT0FBT29CLElBQUksQ0FBQ0MsR0FBRyxDQUFDQyxLQUFLLENBQUMsSUFBSSxFQUFFLENBQUMsSUFBSSxDQUFDLENBQUMvQixVQUFVLEdBQUdQLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7RUFDNUQ7RUFFQW9CLE9BQU9BLENBQUEsRUFBRztJQUNSLE9BQU8sSUFBSSxDQUFDSixVQUFVLEtBQUssQ0FBQztFQUM5Qjs7RUFFQTtFQUNBLENBQUNwQixPQUFPLENBQUMyQyxNQUFNLElBQUk7SUFDakIsT0FBUSxHQUFFLElBQUksQ0FBQzVCLFdBQVcsQ0FBQzZCLElBQUssSUFBRzVDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQ2MsUUFBUSxFQUFFO01BQ3pEK0IsS0FBSyxFQUFFQyxRQUFRO01BQ2ZDLE1BQU0sRUFBRUMsT0FBTyxDQUFDQyxNQUFNLENBQUNDLEtBQUs7TUFDNUJDLE9BQU8sRUFBRSxLQUFLO01BQ2RDLE1BQU0sRUFBRTtJQUNWLENBQUMsQ0FBRSxFQUFDO0VBQ047QUFDRjtBQUVBQyxNQUFNLENBQUNDLE9BQU8sR0FBR2pELGdCQUFnQiIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcZGV2aWNlX2Zsb3dfaGFuZGxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgaW5zcGVjdCB9ID0gcmVxdWlyZSgndXRpbCcpO1xuXG5jb25zdCB7IFJQRXJyb3IsIE9QRXJyb3IgfSA9IHJlcXVpcmUoJy4vZXJyb3JzJyk7XG5jb25zdCBub3cgPSByZXF1aXJlKCcuL2hlbHBlcnMvdW5peF90aW1lc3RhbXAnKTtcblxuY2xhc3MgRGV2aWNlRmxvd0hhbmRsZSB7XG4gICNhYm9ydGVkO1xuICAjY2xpZW50O1xuICAjY2xpZW50QXNzZXJ0aW9uUGF5bG9hZDtcbiAgI0RQb1A7XG4gICNleGNoYW5nZUJvZHk7XG4gICNleHBpcmVzX2F0O1xuICAjaW50ZXJ2YWw7XG4gICNtYXhBZ2U7XG4gICNyZXNwb25zZTtcbiAgY29uc3RydWN0b3IoeyBjbGllbnQsIGV4Y2hhbmdlQm9keSwgY2xpZW50QXNzZXJ0aW9uUGF5bG9hZCwgcmVzcG9uc2UsIG1heEFnZSwgRFBvUCB9KSB7XG4gICAgWyd2ZXJpZmljYXRpb25fdXJpJywgJ3VzZXJfY29kZScsICdkZXZpY2VfY29kZSddLmZvckVhY2goKHByb3ApID0+IHtcbiAgICAgIGlmICh0eXBlb2YgcmVzcG9uc2VbcHJvcF0gIT09ICdzdHJpbmcnIHx8ICFyZXNwb25zZVtwcm9wXSkge1xuICAgICAgICB0aHJvdyBuZXcgUlBFcnJvcihcbiAgICAgICAgICBgZXhwZWN0ZWQgJHtwcm9wfSBzdHJpbmcgdG8gYmUgcmV0dXJuZWQgYnkgRGV2aWNlIEF1dGhvcml6YXRpb24gUmVzcG9uc2UsIGdvdCAlamAsXG4gICAgICAgICAgcmVzcG9uc2VbcHJvcF0sXG4gICAgICAgICk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBpZiAoIU51bWJlci5pc1NhZmVJbnRlZ2VyKHJlc3BvbnNlLmV4cGlyZXNfaW4pKSB7XG4gICAgICB0aHJvdyBuZXcgUlBFcnJvcihcbiAgICAgICAgJ2V4cGVjdGVkIGV4cGlyZXNfaW4gbnVtYmVyIHRvIGJlIHJldHVybmVkIGJ5IERldmljZSBBdXRob3JpemF0aW9uIFJlc3BvbnNlLCBnb3QgJWonLFxuICAgICAgICByZXNwb25zZS5leHBpcmVzX2luLFxuICAgICAgKTtcbiAgICB9XG5cbiAgICB0aGlzLiNleHBpcmVzX2F0ID0gbm93KCkgKyByZXNwb25zZS5leHBpcmVzX2luO1xuICAgIHRoaXMuI2NsaWVudCA9IGNsaWVudDtcbiAgICB0aGlzLiNEUG9QID0gRFBvUDtcbiAgICB0aGlzLiNtYXhBZ2UgPSBtYXhBZ2U7XG4gICAgdGhpcy4jZXhjaGFuZ2VCb2R5ID0gZXhjaGFuZ2VCb2R5O1xuICAgIHRoaXMuI2NsaWVudEFzc2VydGlvblBheWxvYWQgPSBjbGllbnRBc3NlcnRpb25QYXlsb2FkO1xuICAgIHRoaXMuI3Jlc3BvbnNlID0gcmVzcG9uc2U7XG4gICAgdGhpcy4jaW50ZXJ2YWwgPSByZXNwb25zZS5pbnRlcnZhbCAqIDEwMDAgfHwgNTAwMDtcbiAgfVxuXG4gIGFib3J0KCkge1xuICAgIHRoaXMuI2Fib3J0ZWQgPSB0cnVlO1xuICB9XG5cbiAgYXN5bmMgcG9sbCh7IHNpZ25hbCB9ID0ge30pIHtcbiAgICBpZiAoKHNpZ25hbCAmJiBzaWduYWwuYWJvcnRlZCkgfHwgdGhpcy4jYWJvcnRlZCkge1xuICAgICAgdGhyb3cgbmV3IFJQRXJyb3IoJ3BvbGxpbmcgYWJvcnRlZCcpO1xuICAgIH1cblxuICAgIGlmICh0aGlzLmV4cGlyZWQoKSkge1xuICAgICAgdGhyb3cgbmV3IFJQRXJyb3IoXG4gICAgICAgICd0aGUgZGV2aWNlIGNvZGUgJWogaGFzIGV4cGlyZWQgYW5kIHRoZSBkZXZpY2UgYXV0aG9yaXphdGlvbiBzZXNzaW9uIGhhcyBjb25jbHVkZWQnLFxuICAgICAgICB0aGlzLmRldmljZV9jb2RlLFxuICAgICAgKTtcbiAgICB9XG5cbiAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCB0aGlzLiNpbnRlcnZhbCkpO1xuXG4gICAgbGV0IHRva2Vuc2V0O1xuICAgIHRyeSB7XG4gICAgICB0b2tlbnNldCA9IGF3YWl0IHRoaXMuI2NsaWVudC5ncmFudChcbiAgICAgICAge1xuICAgICAgICAgIC4uLnRoaXMuI2V4Y2hhbmdlQm9keSxcbiAgICAgICAgICBncmFudF90eXBlOiAndXJuOmlldGY6cGFyYW1zOm9hdXRoOmdyYW50LXR5cGU6ZGV2aWNlX2NvZGUnLFxuICAgICAgICAgIGRldmljZV9jb2RlOiB0aGlzLmRldmljZV9jb2RlLFxuICAgICAgICB9LFxuICAgICAgICB7IGNsaWVudEFzc2VydGlvblBheWxvYWQ6IHRoaXMuI2NsaWVudEFzc2VydGlvblBheWxvYWQsIERQb1A6IHRoaXMuI0RQb1AgfSxcbiAgICAgICk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzd2l0Y2ggKGVyciBpbnN0YW5jZW9mIE9QRXJyb3IgJiYgZXJyLmVycm9yKSB7XG4gICAgICAgIGNhc2UgJ3Nsb3dfZG93bic6XG4gICAgICAgICAgdGhpcy4jaW50ZXJ2YWwgKz0gNTAwMDtcbiAgICAgICAgY2FzZSAnYXV0aG9yaXphdGlvbl9wZW5kaW5nJzpcbiAgICAgICAgICByZXR1cm4gdGhpcy5wb2xsKHsgc2lnbmFsIH0pO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHRocm93IGVycjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoJ2lkX3Rva2VuJyBpbiB0b2tlbnNldCkge1xuICAgICAgYXdhaXQgdGhpcy4jY2xpZW50LmRlY3J5cHRJZFRva2VuKHRva2Vuc2V0KTtcbiAgICAgIGF3YWl0IHRoaXMuI2NsaWVudC52YWxpZGF0ZUlkVG9rZW4odG9rZW5zZXQsIHVuZGVmaW5lZCwgJ3Rva2VuJywgdGhpcy4jbWF4QWdlKTtcbiAgICB9XG5cbiAgICByZXR1cm4gdG9rZW5zZXQ7XG4gIH1cblxuICBnZXQgZGV2aWNlX2NvZGUoKSB7XG4gICAgcmV0dXJuIHRoaXMuI3Jlc3BvbnNlLmRldmljZV9jb2RlO1xuICB9XG5cbiAgZ2V0IHVzZXJfY29kZSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcmVzcG9uc2UudXNlcl9jb2RlO1xuICB9XG5cbiAgZ2V0IHZlcmlmaWNhdGlvbl91cmkoKSB7XG4gICAgcmV0dXJuIHRoaXMuI3Jlc3BvbnNlLnZlcmlmaWNhdGlvbl91cmk7XG4gIH1cblxuICBnZXQgdmVyaWZpY2F0aW9uX3VyaV9jb21wbGV0ZSgpIHtcbiAgICByZXR1cm4gdGhpcy4jcmVzcG9uc2UudmVyaWZpY2F0aW9uX3VyaV9jb21wbGV0ZTtcbiAgfVxuXG4gIGdldCBleHBpcmVzX2luKCkge1xuICAgIHJldHVybiBNYXRoLm1heC5hcHBseShudWxsLCBbdGhpcy4jZXhwaXJlc19hdCAtIG5vdygpLCAwXSk7XG4gIH1cblxuICBleHBpcmVkKCkge1xuICAgIHJldHVybiB0aGlzLmV4cGlyZXNfaW4gPT09IDA7XG4gIH1cblxuICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICBbaW5zcGVjdC5jdXN0b21dKCkge1xuICAgIHJldHVybiBgJHt0aGlzLmNvbnN0cnVjdG9yLm5hbWV9ICR7aW5zcGVjdCh0aGlzLiNyZXNwb25zZSwge1xuICAgICAgZGVwdGg6IEluZmluaXR5LFxuICAgICAgY29sb3JzOiBwcm9jZXNzLnN0ZG91dC5pc1RUWSxcbiAgICAgIGNvbXBhY3Q6IGZhbHNlLFxuICAgICAgc29ydGVkOiB0cnVlLFxuICAgIH0pfWA7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBEZXZpY2VGbG93SGFuZGxlO1xuIl0sIm5hbWVzIjpbImluc3BlY3QiLCJyZXF1aXJlIiwiUlBFcnJvciIsIk9QRXJyb3IiLCJub3ciLCJEZXZpY2VGbG93SGFuZGxlIiwiYWJvcnRlZCIsImNsaWVudCIsImNsaWVudEFzc2VydGlvblBheWxvYWQiLCJEUG9QIiwiZXhjaGFuZ2VCb2R5IiwiZXhwaXJlc19hdCIsImludGVydmFsIiwibWF4QWdlIiwicmVzcG9uc2UiLCJjb25zdHJ1Y3RvciIsImZvckVhY2giLCJwcm9wIiwiTnVtYmVyIiwiaXNTYWZlSW50ZWdlciIsImV4cGlyZXNfaW4iLCJhYm9ydCIsInBvbGwiLCJzaWduYWwiLCJleHBpcmVkIiwiZGV2aWNlX2NvZGUiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJ0b2tlbnNldCIsImdyYW50IiwiZ3JhbnRfdHlwZSIsImVyciIsImVycm9yIiwiZGVjcnlwdElkVG9rZW4iLCJ2YWxpZGF0ZUlkVG9rZW4iLCJ1bmRlZmluZWQiLCJ1c2VyX2NvZGUiLCJ2ZXJpZmljYXRpb25fdXJpIiwidmVyaWZpY2F0aW9uX3VyaV9jb21wbGV0ZSIsIk1hdGgiLCJtYXgiLCJhcHBseSIsImN1c3RvbSIsIm5hbWUiLCJkZXB0aCIsIkluZmluaXR5IiwiY29sb3JzIiwicHJvY2VzcyIsInN0ZG91dCIsImlzVFRZIiwiY29tcGFjdCIsInNvcnRlZCIsIm1vZHVsZSIsImV4cG9ydHMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  format\n} = __webpack_require__(/*! util */ \"util\");\nclass OPError extends Error {\n  constructor({\n    error_description,\n    error,\n    error_uri,\n    session_state,\n    state,\n    scope\n  }, response) {\n    super(!error_description ? error : `${error} (${error_description})`);\n    Object.assign(this, {\n      error\n    }, error_description && {\n      error_description\n    }, error_uri && {\n      error_uri\n    }, state && {\n      state\n    }, scope && {\n      scope\n    }, session_state && {\n      session_state\n    });\n    if (response) {\n      Object.defineProperty(this, 'response', {\n        value: response\n      });\n    }\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\nclass RPError extends Error {\n  constructor(...args) {\n    if (typeof args[0] === 'string') {\n      super(format(...args));\n    } else {\n      const {\n        message,\n        printf,\n        response,\n        ...rest\n      } = args[0];\n      if (printf) {\n        super(format(...printf));\n      } else {\n        super(message);\n      }\n      Object.assign(this, rest);\n      if (response) {\n        Object.defineProperty(this, 'response', {\n          value: response\n        });\n      }\n    }\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\nmodule.exports = {\n  OPError,\n  RPError\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\n\nfunction assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n  if (!issuer[`${endpoint}_endpoint`]) return;\n  const eam = `${endpoint}_endpoint_auth_method`;\n  const easa = `${endpoint}_endpoint_auth_signing_alg`;\n  const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n  if (properties[eam] && properties[eam].endsWith('_jwt') && !properties[easa] && !issuer[easavs]) {\n    throw new TypeError(`${easavs} must be configured on the issuer if ${easa} is not defined on a client`);\n  }\n}\nfunction assertIssuerConfiguration(issuer, endpoint) {\n  if (!issuer[endpoint]) {\n    throw new TypeError(`${endpoint} must be configured on the issuer`);\n  }\n}\nmodule.exports = {\n  assertSigningAlgValuesSupport,\n  assertIssuerConfiguration\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("\n\nlet encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input, encoding = 'utf8') => Buffer.from(input, encoding).toString('base64url');\n} else {\n  const fromBase64 = base64 => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input, encoding = 'utf8') => fromBase64(Buffer.from(input, encoding).toString('base64'));\n}\nconst decode = input => Buffer.from(input, 'base64');\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst {\n  RPError\n} = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst {\n  assertIssuerConfiguration\n} = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst {\n  random\n} = __webpack_require__(/*! ./generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst {\n  keystores\n} = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(rsc)/./node_modules/openid-client/lib/helpers/merge.js\");\n\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = value => encodeURIComponent(value).replace(/%20/g, '+');\nasync function clientAssertion(endpoint, payload) {\n  let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n  if (!alg) {\n    assertIssuerConfiguration(this.issuer, `${endpoint}_endpoint_auth_signing_alg_values_supported`);\n  }\n  if (this[`${endpoint}_endpoint_auth_method`] === 'client_secret_jwt') {\n    if (!alg) {\n      const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n      alg = Array.isArray(supported) && supported.find(signAlg => /^HS(?:256|384|512)/.test(signAlg));\n    }\n    if (!alg) {\n      throw new RPError(`failed to determine a JWS Algorithm to use for ${this[`${endpoint}_endpoint_auth_method`]} Client Assertion`);\n    }\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload))).setProtectedHeader({\n      alg\n    }).sign(this.secretForAlg(alg));\n  }\n  const keystore = await keystores.get(this);\n  if (!keystore) {\n    throw new TypeError('no client jwks provided for signing a client assertion with');\n  }\n  if (!alg) {\n    const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n    alg = Array.isArray(supported) && supported.find(signAlg => keystore.get({\n      alg: signAlg,\n      use: 'sig'\n    }));\n  }\n  if (!alg) {\n    throw new RPError(`failed to determine a JWS Algorithm to use for ${this[`${endpoint}_endpoint_auth_method`]} Client Assertion`);\n  }\n  const key = keystore.get({\n    alg,\n    use: 'sig'\n  });\n  if (!key) {\n    throw new RPError(`no key found in client jwks to sign a client assertion with using alg ${alg}`);\n  }\n  return new jose.CompactSign(Buffer.from(JSON.stringify(payload))).setProtectedHeader({\n    alg,\n    kid: key.jwk && key.jwk.kid\n  }).sign(await key.keyObject(alg));\n}\nasync function authFor(endpoint, {\n  clientAssertionPayload\n} = {}) {\n  const authMethod = this[`${endpoint}_endpoint_auth_method`];\n  switch (authMethod) {\n    case 'self_signed_tls_client_auth':\n    case 'tls_client_auth':\n    case 'none':\n      return {\n        form: {\n          client_id: this.client_id\n        }\n      };\n    case 'client_secret_post':\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError('client_secret_post client authentication method requires a client_secret');\n      }\n      return {\n        form: {\n          client_id: this.client_id,\n          client_secret: this.client_secret\n        }\n      };\n    case 'private_key_jwt':\n    case 'client_secret_jwt':\n      {\n        const timestamp = now();\n        const assertion = await clientAssertion.call(this, endpoint, {\n          iat: timestamp,\n          exp: timestamp + 60,\n          jti: random(),\n          iss: this.client_id,\n          sub: this.client_id,\n          aud: this.issuer.issuer,\n          ...clientAssertionPayload\n        });\n        return {\n          form: {\n            client_id: this.client_id,\n            client_assertion: assertion,\n            client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer'\n          }\n        };\n      }\n    case 'client_secret_basic':\n      {\n        // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n        // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n        // > The client identifier is encoded using the\n        // > \"application/x-www-form-urlencoded\" encoding algorithm per\n        // > Appendix B, and the encoded value is used as the username; the client\n        // > password is encoded using the same algorithm and used as the\n        // > password.\n        if (typeof this.client_secret !== 'string') {\n          throw new TypeError('client_secret_basic client authentication method requires a client_secret');\n        }\n        const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n        const value = Buffer.from(encoded).toString('base64');\n        return {\n          headers: {\n            Authorization: `Basic ${value}`\n          }\n        };\n      }\n    default:\n      {\n        throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n      }\n  }\n}\nfunction resolveResponseType() {\n  const {\n    length,\n    0: value\n  } = this.response_types;\n  if (length === 1) {\n    return value;\n  }\n  return undefined;\n}\nfunction resolveRedirectUri() {\n  const {\n    length,\n    0: value\n  } = this.redirect_uris || [];\n  if (length === 1) {\n    return value;\n  }\n  return undefined;\n}\nasync function authenticatedPost(endpoint, opts, {\n  clientAssertionPayload,\n  endpointAuthMethod = endpoint,\n  DPoP\n} = {}) {\n  const auth = await authFor.call(this, endpointAuthMethod, {\n    clientAssertionPayload\n  });\n  const requestOpts = merge(opts, auth);\n  const mTLS = this[`${endpointAuthMethod}_endpoint_auth_method`].includes('tls_client_auth') || endpoint === 'token' && this.tls_client_certificate_bound_access_tokens;\n  let targetUrl;\n  if (mTLS && this.issuer.mtls_endpoint_aliases) {\n    targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n  }\n  targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n  if ('form' in requestOpts) {\n    for (const [key, value] of Object.entries(requestOpts.form)) {\n      if (typeof value === 'undefined') {\n        delete requestOpts.form[key];\n      }\n    }\n  }\n  return request.call(this, {\n    ...requestOpts,\n    method: 'POST',\n    url: targetUrl,\n    headers: {\n      ...(endpoint !== 'revocation' ? {\n        Accept: 'application/json'\n      } : undefined),\n      ...requestOpts.headers\n    }\n  }, {\n    mTLS,\n    DPoP\n  });\n}\nmodule.exports = {\n  resolveResponseType,\n  resolveRedirectUri,\n  authFor,\n  authenticatedPost\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\n\nconst HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\nmodule.exports = {\n  CLOCK_TOLERANCE,\n  HTTP_OPTIONS\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBQSxNQUFNQSxZQUFZLEdBQUdDLE1BQU0sQ0FBQyxDQUFDO0FBQzdCLE1BQU1DLGVBQWUsR0FBR0QsTUFBTSxDQUFDLENBQUM7QUFFaENFLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHO0VBQ2ZGLGVBQWU7RUFDZkY7QUFDRixDQUFDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxjb25zdHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgSFRUUF9PUFRJT05TID0gU3ltYm9sKCk7XG5jb25zdCBDTE9DS19UT0xFUkFOQ0UgPSBTeW1ib2woKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIENMT0NLX1RPTEVSQU5DRSxcbiAgSFRUUF9PUFRJT05TLFxufTtcbiJdLCJuYW1lcyI6WyJIVFRQX09QVElPTlMiLCJTeW1ib2wiLCJDTE9DS19UT0xFUkFOQ0UiLCJtb2R1bGUiLCJleHBvcnRzIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nmodule.exports = token => {\n  if (typeof token !== 'string' || !token) {\n    throw new TypeError('JWT must be a string');\n  }\n  const {\n    0: header,\n    1: payload,\n    2: signature,\n    length\n  } = token.split('.');\n  if (length === 5) {\n    throw new TypeError('encrypted JWTs cannot be decoded');\n  }\n  if (length !== 3) {\n    throw new Error('JWTs must have three components');\n  }\n  try {\n    return {\n      header: JSON.parse(base64url.decode(header)),\n      payload: JSON.parse(base64url.decode(payload)),\n      signature\n    };\n  } catch (err) {\n    throw new Error('JWT is malformed');\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = globalThis.structuredClone || (obj => JSON.parse(JSON.stringify(obj)));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWVwX2Nsb25lLmpzIiwibWFwcGluZ3MiOiI7O0FBQUFBLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHQyxVQUFVLENBQUNDLGVBQWUsS0FBTUMsR0FBRyxJQUFLQyxJQUFJLENBQUNDLEtBQUssQ0FBQ0QsSUFBSSxDQUFDRSxTQUFTLENBQUNILEdBQUcsQ0FBQyxDQUFDLENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGRlZXBfY2xvbmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBnbG9iYWxUaGlzLnN0cnVjdHVyZWRDbG9uZSB8fCAoKG9iaikgPT4gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShvYmopKSk7XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImdsb2JhbFRoaXMiLCJzdHJ1Y3R1cmVkQ2xvbmUiLCJvYmoiLCJKU09OIiwicGFyc2UiLCJzdHJpbmdpZnkiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nfunction defaults(deep, target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (typeof target[key] === 'undefined' && typeof value !== 'undefined') {\n        target[key] = value;\n      }\n      if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n        defaults(true, target[key], value);\n      }\n    }\n  }\n  return target;\n}\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  createHash,\n  randomBytes\n} = __webpack_require__(/*! crypto */ \"crypto\");\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst random = (bytes = 32) => base64url.encode(randomBytes(bytes));\nmodule.exports = {\n  random,\n  state: random,\n  nonce: random,\n  codeVerifier: random,\n  codeChallenge: codeVerifier => base64url.encode(createHash('sha256').update(codeVerifier).digest())\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiI7O0FBQUEsTUFBTTtFQUFFQSxVQUFVO0VBQUVDO0FBQVksQ0FBQyxHQUFHQyxtQkFBTyxDQUFDLHNCQUFRLENBQUM7QUFFckQsTUFBTUMsU0FBUyxHQUFHRCxtQkFBTyxDQUFDLGdGQUFhLENBQUM7QUFFeEMsTUFBTUUsTUFBTSxHQUFHQSxDQUFDQyxLQUFLLEdBQUcsRUFBRSxLQUFLRixTQUFTLENBQUNHLE1BQU0sQ0FBQ0wsV0FBVyxDQUFDSSxLQUFLLENBQUMsQ0FBQztBQUVuRUUsTUFBTSxDQUFDQyxPQUFPLEdBQUc7RUFDZkosTUFBTTtFQUNOSyxLQUFLLEVBQUVMLE1BQU07RUFDYk0sS0FBSyxFQUFFTixNQUFNO0VBQ2JPLFlBQVksRUFBRVAsTUFBTTtFQUNwQlEsYUFBYSxFQUFHRCxZQUFZLElBQzFCUixTQUFTLENBQUNHLE1BQU0sQ0FBQ04sVUFBVSxDQUFDLFFBQVEsQ0FBQyxDQUFDYSxNQUFNLENBQUNGLFlBQVksQ0FBQyxDQUFDRyxNQUFNLENBQUMsQ0FBQztBQUN2RSxDQUFDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxnZW5lcmF0b3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgY3JlYXRlSGFzaCwgcmFuZG9tQnl0ZXMgfSA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuXG5jb25zdCBiYXNlNjR1cmwgPSByZXF1aXJlKCcuL2Jhc2U2NHVybCcpO1xuXG5jb25zdCByYW5kb20gPSAoYnl0ZXMgPSAzMikgPT4gYmFzZTY0dXJsLmVuY29kZShyYW5kb21CeXRlcyhieXRlcykpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgcmFuZG9tLFxuICBzdGF0ZTogcmFuZG9tLFxuICBub25jZTogcmFuZG9tLFxuICBjb2RlVmVyaWZpZXI6IHJhbmRvbSxcbiAgY29kZUNoYWxsZW5nZTogKGNvZGVWZXJpZmllcikgPT5cbiAgICBiYXNlNjR1cmwuZW5jb2RlKGNyZWF0ZUhhc2goJ3NoYTI1NicpLnVwZGF0ZShjb2RlVmVyaWZpZXIpLmRpZ2VzdCgpKSxcbn07XG4iXSwibmFtZXMiOlsiY3JlYXRlSGFzaCIsInJhbmRvbUJ5dGVzIiwicmVxdWlyZSIsImJhc2U2NHVybCIsInJhbmRvbSIsImJ5dGVzIiwiZW5jb2RlIiwibW9kdWxlIiwiZXhwb3J0cyIsInN0YXRlIiwibm9uY2UiLCJjb2RlVmVyaWZpZXIiLCJjb2RlQ2hhbGxlbmdlIiwidXBkYXRlIiwiZGlnZXN0Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nmodule.exports = util.types.isKeyObject || (obj => obj && obj instanceof crypto.KeyObject);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiI7O0FBQUEsTUFBTUEsSUFBSSxHQUFHQyxtQkFBTyxDQUFDLGtCQUFNLENBQUM7QUFDNUIsTUFBTUMsTUFBTSxHQUFHRCxtQkFBTyxDQUFDLHNCQUFRLENBQUM7QUFFaENFLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHSixJQUFJLENBQUNLLEtBQUssQ0FBQ0MsV0FBVyxLQUFNQyxHQUFHLElBQUtBLEdBQUcsSUFBSUEsR0FBRyxZQUFZTCxNQUFNLENBQUNNLFNBQVMsQ0FBQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcaXNfa2V5X29iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB1dGlsID0gcmVxdWlyZSgndXRpbCcpO1xuY29uc3QgY3J5cHRvID0gcmVxdWlyZSgnY3J5cHRvJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gdXRpbC50eXBlcy5pc0tleU9iamVjdCB8fCAoKG9iaikgPT4gb2JqICYmIG9iaiBpbnN0YW5jZW9mIGNyeXB0by5LZXlPYmplY3QpO1xuIl0sIm5hbWVzIjpbInV0aWwiLCJyZXF1aXJlIiwiY3J5cHRvIiwibW9kdWxlIiwiZXhwb3J0cyIsInR5cGVzIiwiaXNLZXlPYmplY3QiLCJvYmoiLCJLZXlPYmplY3QiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = a => !!a && a.constructor === Object;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanMiLCJtYXBwaW5ncyI6Ijs7QUFBQUEsTUFBTSxDQUFDQyxPQUFPLEdBQUlDLENBQUMsSUFBSyxDQUFDLENBQUNBLENBQUMsSUFBSUEsQ0FBQyxDQUFDQyxXQUFXLEtBQUtDLE1BQU0iLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGlzX3BsYWluX29iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IChhKSA9PiAhIWEgJiYgYS5jb25zdHJ1Y3RvciA9PT0gT2JqZWN0O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJhIiwiY29uc3RydWN0b3IiLCJPYmplY3QiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst objectHash = __webpack_require__(/*! object-hash */ \"(rsc)/./node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nconst {\n  RPError\n} = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst {\n  assertIssuerConfiguration\n} = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst {\n  keystores\n} = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = ctx => {\n  if (!caches.has(ctx)) {\n    caches.set(ctx, new LRU({\n      max: 100\n    }));\n  }\n  return caches.get(ctx);\n};\nasync function getKeyStore(reload = false) {\n  assertIssuerConfiguration(this, 'jwks_uri');\n  const keystore = keystores.get(this);\n  const cache = lrus(this);\n  if (reload || !keystore) {\n    if (inFlight.has(this)) {\n      return inFlight.get(this);\n    }\n    cache.reset();\n    inFlight.set(this, (async () => {\n      const response = await request.call(this, {\n        method: 'GET',\n        responseType: 'json',\n        url: this.jwks_uri,\n        headers: {\n          Accept: 'application/json, application/jwk-set+json'\n        }\n      }).finally(() => {\n        inFlight.delete(this);\n      });\n      const jwks = processResponse(response);\n      const joseKeyStore = KeyStore.fromJWKS(jwks, {\n        onlyPublic: true\n      });\n      cache.set('throttle', true, 60 * 1000);\n      keystores.set(this, joseKeyStore);\n      return joseKeyStore;\n    })());\n    return inFlight.get(this);\n  }\n  return keystore;\n}\nasync function queryKeyStore({\n  kid,\n  kty,\n  alg,\n  use\n}, {\n  allowMulti = false\n} = {}) {\n  const cache = lrus(this);\n  const def = {\n    kid,\n    kty,\n    alg,\n    use\n  };\n  const defHash = objectHash(def, {\n    algorithm: 'sha256',\n    ignoreUnknown: true,\n    unorderedArrays: true,\n    unorderedSets: true,\n    respectType: false\n  });\n\n  // refresh keystore on every unknown key but also only upto once every minute\n  const freshJwksUri = cache.get(defHash) || cache.get('throttle');\n  const keystore = await getKeyStore.call(this, !freshJwksUri);\n  const keys = keystore.all(def);\n  delete def.use;\n  if (keys.length === 0) {\n    throw new RPError({\n      printf: [\"no valid key found in issuer's jwks_uri for key parameters %j\", def],\n      jwks: keystore\n    });\n  }\n  if (!allowMulti && keys.length > 1 && !kid) {\n    throw new RPError({\n      printf: [\"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\", def],\n      jwks: keystore\n    });\n  }\n  cache.set(defHash, true);\n  return keys;\n}\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc3N1ZXIuanMiLCJtYXBwaW5ncyI6Ijs7QUFBQSxNQUFNQSxVQUFVLEdBQUdDLG1CQUFPLENBQUMsOERBQWEsQ0FBQztBQUN6QyxNQUFNQyxHQUFHLEdBQUdELG1CQUFPLENBQUMsMERBQVcsQ0FBQztBQUVoQyxNQUFNO0VBQUVFO0FBQVEsQ0FBQyxHQUFHRixtQkFBTyxDQUFDLG1FQUFXLENBQUM7QUFFeEMsTUFBTTtFQUFFRztBQUEwQixDQUFDLEdBQUdILG1CQUFPLENBQUMsMEVBQVUsQ0FBQztBQUN6RCxNQUFNSSxRQUFRLEdBQUdKLG1CQUFPLENBQUMsOEVBQVksQ0FBQztBQUN0QyxNQUFNO0VBQUVLO0FBQVUsQ0FBQyxHQUFHTCxtQkFBTyxDQUFDLGtGQUFjLENBQUM7QUFDN0MsTUFBTU0sZUFBZSxHQUFHTixtQkFBTyxDQUFDLDhGQUFvQixDQUFDO0FBQ3JELE1BQU1PLE9BQU8sR0FBR1AsbUJBQU8sQ0FBQyw0RUFBVyxDQUFDO0FBRXBDLE1BQU1RLFFBQVEsR0FBRyxJQUFJQyxPQUFPLENBQUMsQ0FBQztBQUM5QixNQUFNQyxNQUFNLEdBQUcsSUFBSUQsT0FBTyxDQUFDLENBQUM7QUFDNUIsTUFBTUUsSUFBSSxHQUFJQyxHQUFHLElBQUs7RUFDcEIsSUFBSSxDQUFDRixNQUFNLENBQUNHLEdBQUcsQ0FBQ0QsR0FBRyxDQUFDLEVBQUU7SUFDcEJGLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDRixHQUFHLEVBQUUsSUFBSVgsR0FBRyxDQUFDO01BQUVjLEdBQUcsRUFBRTtJQUFJLENBQUMsQ0FBQyxDQUFDO0VBQ3hDO0VBQ0EsT0FBT0wsTUFBTSxDQUFDTSxHQUFHLENBQUNKLEdBQUcsQ0FBQztBQUN4QixDQUFDO0FBRUQsZUFBZUssV0FBV0EsQ0FBQ0MsTUFBTSxHQUFHLEtBQUssRUFBRTtFQUN6Q2YseUJBQXlCLENBQUMsSUFBSSxFQUFFLFVBQVUsQ0FBQztFQUUzQyxNQUFNZ0IsUUFBUSxHQUFHZCxTQUFTLENBQUNXLEdBQUcsQ0FBQyxJQUFJLENBQUM7RUFDcEMsTUFBTUksS0FBSyxHQUFHVCxJQUFJLENBQUMsSUFBSSxDQUFDO0VBRXhCLElBQUlPLE1BQU0sSUFBSSxDQUFDQyxRQUFRLEVBQUU7SUFDdkIsSUFBSVgsUUFBUSxDQUFDSyxHQUFHLENBQUMsSUFBSSxDQUFDLEVBQUU7TUFDdEIsT0FBT0wsUUFBUSxDQUFDUSxHQUFHLENBQUMsSUFBSSxDQUFDO0lBQzNCO0lBQ0FJLEtBQUssQ0FBQ0MsS0FBSyxDQUFDLENBQUM7SUFDYmIsUUFBUSxDQUFDTSxHQUFHLENBQ1YsSUFBSSxFQUNKLENBQUMsWUFBWTtNQUNYLE1BQU1RLFFBQVEsR0FBRyxNQUFNZixPQUFPLENBQzNCZ0IsSUFBSSxDQUFDLElBQUksRUFBRTtRQUNWQyxNQUFNLEVBQUUsS0FBSztRQUNiQyxZQUFZLEVBQUUsTUFBTTtRQUNwQkMsR0FBRyxFQUFFLElBQUksQ0FBQ0MsUUFBUTtRQUNsQkMsT0FBTyxFQUFFO1VBQ1BDLE1BQU0sRUFBRTtRQUNWO01BQ0YsQ0FBQyxDQUFDLENBQ0RDLE9BQU8sQ0FBQyxNQUFNO1FBQ2J0QixRQUFRLENBQUN1QixNQUFNLENBQUMsSUFBSSxDQUFDO01BQ3ZCLENBQUMsQ0FBQztNQUNKLE1BQU1DLElBQUksR0FBRzFCLGVBQWUsQ0FBQ2dCLFFBQVEsQ0FBQztNQUV0QyxNQUFNVyxZQUFZLEdBQUc3QixRQUFRLENBQUM4QixRQUFRLENBQUNGLElBQUksRUFBRTtRQUFFRyxVQUFVLEVBQUU7TUFBSyxDQUFDLENBQUM7TUFDbEVmLEtBQUssQ0FBQ04sR0FBRyxDQUFDLFVBQVUsRUFBRSxJQUFJLEVBQUUsRUFBRSxHQUFHLElBQUksQ0FBQztNQUN0Q1QsU0FBUyxDQUFDUyxHQUFHLENBQUMsSUFBSSxFQUFFbUIsWUFBWSxDQUFDO01BRWpDLE9BQU9BLFlBQVk7SUFDckIsQ0FBQyxFQUFFLENBQ0wsQ0FBQztJQUVELE9BQU96QixRQUFRLENBQUNRLEdBQUcsQ0FBQyxJQUFJLENBQUM7RUFDM0I7RUFFQSxPQUFPRyxRQUFRO0FBQ2pCO0FBRUEsZUFBZWlCLGFBQWFBLENBQUM7RUFBRUMsR0FBRztFQUFFQyxHQUFHO0VBQUVDLEdBQUc7RUFBRUM7QUFBSSxDQUFDLEVBQUU7RUFBRUMsVUFBVSxHQUFHO0FBQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFO0VBQ2hGLE1BQU1yQixLQUFLLEdBQUdULElBQUksQ0FBQyxJQUFJLENBQUM7RUFFeEIsTUFBTStCLEdBQUcsR0FBRztJQUNWTCxHQUFHO0lBQ0hDLEdBQUc7SUFDSEMsR0FBRztJQUNIQztFQUNGLENBQUM7RUFFRCxNQUFNRyxPQUFPLEdBQUc1QyxVQUFVLENBQUMyQyxHQUFHLEVBQUU7SUFDOUJFLFNBQVMsRUFBRSxRQUFRO0lBQ25CQyxhQUFhLEVBQUUsSUFBSTtJQUNuQkMsZUFBZSxFQUFFLElBQUk7SUFDckJDLGFBQWEsRUFBRSxJQUFJO0lBQ25CQyxXQUFXLEVBQUU7RUFDZixDQUFDLENBQUM7O0VBRUY7RUFDQSxNQUFNQyxZQUFZLEdBQUc3QixLQUFLLENBQUNKLEdBQUcsQ0FBQzJCLE9BQU8sQ0FBQyxJQUFJdkIsS0FBSyxDQUFDSixHQUFHLENBQUMsVUFBVSxDQUFDO0VBRWhFLE1BQU1HLFFBQVEsR0FBRyxNQUFNRixXQUFXLENBQUNNLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQzBCLFlBQVksQ0FBQztFQUM1RCxNQUFNQyxJQUFJLEdBQUcvQixRQUFRLENBQUNnQyxHQUFHLENBQUNULEdBQUcsQ0FBQztFQUU5QixPQUFPQSxHQUFHLENBQUNGLEdBQUc7RUFDZCxJQUFJVSxJQUFJLENBQUNFLE1BQU0sS0FBSyxDQUFDLEVBQUU7SUFDckIsTUFBTSxJQUFJbEQsT0FBTyxDQUFDO01BQ2hCbUQsTUFBTSxFQUFFLENBQUMsK0RBQStELEVBQUVYLEdBQUcsQ0FBQztNQUM5RVYsSUFBSSxFQUFFYjtJQUNSLENBQUMsQ0FBQztFQUNKO0VBRUEsSUFBSSxDQUFDc0IsVUFBVSxJQUFJUyxJQUFJLENBQUNFLE1BQU0sR0FBRyxDQUFDLElBQUksQ0FBQ2YsR0FBRyxFQUFFO0lBQzFDLE1BQU0sSUFBSW5DLE9BQU8sQ0FBQztNQUNoQm1ELE1BQU0sRUFBRSxDQUNOLDRHQUE0RyxFQUM1R1gsR0FBRyxDQUNKO01BQ0RWLElBQUksRUFBRWI7SUFDUixDQUFDLENBQUM7RUFDSjtFQUVBQyxLQUFLLENBQUNOLEdBQUcsQ0FBQzZCLE9BQU8sRUFBRSxJQUFJLENBQUM7RUFFeEIsT0FBT08sSUFBSTtBQUNiO0FBRUFJLDRCQUE0QixHQUFHbEIsYUFBYTtBQUM1Q2tCLHVCQUF1QixHQUFHckMsV0FBVyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcaXNzdWVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG9iamVjdEhhc2ggPSByZXF1aXJlKCdvYmplY3QtaGFzaCcpO1xuY29uc3QgTFJVID0gcmVxdWlyZSgnbHJ1LWNhY2hlJyk7XG5cbmNvbnN0IHsgUlBFcnJvciB9ID0gcmVxdWlyZSgnLi4vZXJyb3JzJyk7XG5cbmNvbnN0IHsgYXNzZXJ0SXNzdWVyQ29uZmlndXJhdGlvbiB9ID0gcmVxdWlyZSgnLi9hc3NlcnQnKTtcbmNvbnN0IEtleVN0b3JlID0gcmVxdWlyZSgnLi9rZXlzdG9yZScpO1xuY29uc3QgeyBrZXlzdG9yZXMgfSA9IHJlcXVpcmUoJy4vd2Vha19jYWNoZScpO1xuY29uc3QgcHJvY2Vzc1Jlc3BvbnNlID0gcmVxdWlyZSgnLi9wcm9jZXNzX3Jlc3BvbnNlJyk7XG5jb25zdCByZXF1ZXN0ID0gcmVxdWlyZSgnLi9yZXF1ZXN0Jyk7XG5cbmNvbnN0IGluRmxpZ2h0ID0gbmV3IFdlYWtNYXAoKTtcbmNvbnN0IGNhY2hlcyA9IG5ldyBXZWFrTWFwKCk7XG5jb25zdCBscnVzID0gKGN0eCkgPT4ge1xuICBpZiAoIWNhY2hlcy5oYXMoY3R4KSkge1xuICAgIGNhY2hlcy5zZXQoY3R4LCBuZXcgTFJVKHsgbWF4OiAxMDAgfSkpO1xuICB9XG4gIHJldHVybiBjYWNoZXMuZ2V0KGN0eCk7XG59O1xuXG5hc3luYyBmdW5jdGlvbiBnZXRLZXlTdG9yZShyZWxvYWQgPSBmYWxzZSkge1xuICBhc3NlcnRJc3N1ZXJDb25maWd1cmF0aW9uKHRoaXMsICdqd2tzX3VyaScpO1xuXG4gIGNvbnN0IGtleXN0b3JlID0ga2V5c3RvcmVzLmdldCh0aGlzKTtcbiAgY29uc3QgY2FjaGUgPSBscnVzKHRoaXMpO1xuXG4gIGlmIChyZWxvYWQgfHwgIWtleXN0b3JlKSB7XG4gICAgaWYgKGluRmxpZ2h0Lmhhcyh0aGlzKSkge1xuICAgICAgcmV0dXJuIGluRmxpZ2h0LmdldCh0aGlzKTtcbiAgICB9XG4gICAgY2FjaGUucmVzZXQoKTtcbiAgICBpbkZsaWdodC5zZXQoXG4gICAgICB0aGlzLFxuICAgICAgKGFzeW5jICgpID0+IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXF1ZXN0XG4gICAgICAgICAgLmNhbGwodGhpcywge1xuICAgICAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgICAgIHJlc3BvbnNlVHlwZTogJ2pzb24nLFxuICAgICAgICAgICAgdXJsOiB0aGlzLmp3a3NfdXJpLFxuICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICBBY2NlcHQ6ICdhcHBsaWNhdGlvbi9qc29uLCBhcHBsaWNhdGlvbi9qd2stc2V0K2pzb24nLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICB9KVxuICAgICAgICAgIC5maW5hbGx5KCgpID0+IHtcbiAgICAgICAgICAgIGluRmxpZ2h0LmRlbGV0ZSh0aGlzKTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgandrcyA9IHByb2Nlc3NSZXNwb25zZShyZXNwb25zZSk7XG5cbiAgICAgICAgY29uc3Qgam9zZUtleVN0b3JlID0gS2V5U3RvcmUuZnJvbUpXS1MoandrcywgeyBvbmx5UHVibGljOiB0cnVlIH0pO1xuICAgICAgICBjYWNoZS5zZXQoJ3Rocm90dGxlJywgdHJ1ZSwgNjAgKiAxMDAwKTtcbiAgICAgICAga2V5c3RvcmVzLnNldCh0aGlzLCBqb3NlS2V5U3RvcmUpO1xuXG4gICAgICAgIHJldHVybiBqb3NlS2V5U3RvcmU7XG4gICAgICB9KSgpLFxuICAgICk7XG5cbiAgICByZXR1cm4gaW5GbGlnaHQuZ2V0KHRoaXMpO1xuICB9XG5cbiAgcmV0dXJuIGtleXN0b3JlO1xufVxuXG5hc3luYyBmdW5jdGlvbiBxdWVyeUtleVN0b3JlKHsga2lkLCBrdHksIGFsZywgdXNlIH0sIHsgYWxsb3dNdWx0aSA9IGZhbHNlIH0gPSB7fSkge1xuICBjb25zdCBjYWNoZSA9IGxydXModGhpcyk7XG5cbiAgY29uc3QgZGVmID0ge1xuICAgIGtpZCxcbiAgICBrdHksXG4gICAgYWxnLFxuICAgIHVzZSxcbiAgfTtcblxuICBjb25zdCBkZWZIYXNoID0gb2JqZWN0SGFzaChkZWYsIHtcbiAgICBhbGdvcml0aG06ICdzaGEyNTYnLFxuICAgIGlnbm9yZVVua25vd246IHRydWUsXG4gICAgdW5vcmRlcmVkQXJyYXlzOiB0cnVlLFxuICAgIHVub3JkZXJlZFNldHM6IHRydWUsXG4gICAgcmVzcGVjdFR5cGU6IGZhbHNlLFxuICB9KTtcblxuICAvLyByZWZyZXNoIGtleXN0b3JlIG9uIGV2ZXJ5IHVua25vd24ga2V5IGJ1dCBhbHNvIG9ubHkgdXB0byBvbmNlIGV2ZXJ5IG1pbnV0ZVxuICBjb25zdCBmcmVzaEp3a3NVcmkgPSBjYWNoZS5nZXQoZGVmSGFzaCkgfHwgY2FjaGUuZ2V0KCd0aHJvdHRsZScpO1xuXG4gIGNvbnN0IGtleXN0b3JlID0gYXdhaXQgZ2V0S2V5U3RvcmUuY2FsbCh0aGlzLCAhZnJlc2hKd2tzVXJpKTtcbiAgY29uc3Qga2V5cyA9IGtleXN0b3JlLmFsbChkZWYpO1xuXG4gIGRlbGV0ZSBkZWYudXNlO1xuICBpZiAoa2V5cy5sZW5ndGggPT09IDApIHtcbiAgICB0aHJvdyBuZXcgUlBFcnJvcih7XG4gICAgICBwcmludGY6IFtcIm5vIHZhbGlkIGtleSBmb3VuZCBpbiBpc3N1ZXIncyBqd2tzX3VyaSBmb3Iga2V5IHBhcmFtZXRlcnMgJWpcIiwgZGVmXSxcbiAgICAgIGp3a3M6IGtleXN0b3JlLFxuICAgIH0pO1xuICB9XG5cbiAgaWYgKCFhbGxvd011bHRpICYmIGtleXMubGVuZ3RoID4gMSAmJiAha2lkKSB7XG4gICAgdGhyb3cgbmV3IFJQRXJyb3Ioe1xuICAgICAgcHJpbnRmOiBbXG4gICAgICAgIFwibXVsdGlwbGUgbWF0Y2hpbmcga2V5cyBmb3VuZCBpbiBpc3N1ZXIncyBqd2tzX3VyaSBmb3Iga2V5IHBhcmFtZXRlcnMgJWosIGtpZCBtdXN0IGJlIHByb3ZpZGVkIGluIHRoaXMgY2FzZVwiLFxuICAgICAgICBkZWYsXG4gICAgICBdLFxuICAgICAgandrczoga2V5c3RvcmUsXG4gICAgfSk7XG4gIH1cblxuICBjYWNoZS5zZXQoZGVmSGFzaCwgdHJ1ZSk7XG5cbiAgcmV0dXJuIGtleXM7XG59XG5cbm1vZHVsZS5leHBvcnRzLnF1ZXJ5S2V5U3RvcmUgPSBxdWVyeUtleVN0b3JlO1xubW9kdWxlLmV4cG9ydHMua2V5c3RvcmUgPSBnZXRLZXlTdG9yZTtcbiJdLCJuYW1lcyI6WyJvYmplY3RIYXNoIiwicmVxdWlyZSIsIkxSVSIsIlJQRXJyb3IiLCJhc3NlcnRJc3N1ZXJDb25maWd1cmF0aW9uIiwiS2V5U3RvcmUiLCJrZXlzdG9yZXMiLCJwcm9jZXNzUmVzcG9uc2UiLCJyZXF1ZXN0IiwiaW5GbGlnaHQiLCJXZWFrTWFwIiwiY2FjaGVzIiwibHJ1cyIsImN0eCIsImhhcyIsInNldCIsIm1heCIsImdldCIsImdldEtleVN0b3JlIiwicmVsb2FkIiwia2V5c3RvcmUiLCJjYWNoZSIsInJlc2V0IiwicmVzcG9uc2UiLCJjYWxsIiwibWV0aG9kIiwicmVzcG9uc2VUeXBlIiwidXJsIiwiandrc191cmkiLCJoZWFkZXJzIiwiQWNjZXB0IiwiZmluYWxseSIsImRlbGV0ZSIsImp3a3MiLCJqb3NlS2V5U3RvcmUiLCJmcm9tSldLUyIsIm9ubHlQdWJsaWMiLCJxdWVyeUtleVN0b3JlIiwia2lkIiwia3R5IiwiYWxnIiwidXNlIiwiYWxsb3dNdWx0aSIsImRlZiIsImRlZkhhc2giLCJhbGdvcml0aG0iLCJpZ25vcmVVbmtub3duIiwidW5vcmRlcmVkQXJyYXlzIiwidW5vcmRlcmVkU2V0cyIsInJlc3BlY3RUeXBlIiwiZnJlc2hKd2tzVXJpIiwia2V5cyIsImFsbCIsImxlbmd0aCIsInByaW50ZiIsIm1vZHVsZSIsImV4cG9ydHMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst internal = Symbol();\nconst keyscore = (key, {\n  alg,\n  use\n}) => {\n  let score = 0;\n  if (alg && key.alg) {\n    score++;\n  }\n  if (use && key.use) {\n    score++;\n  }\n  return score;\n};\nfunction getKtyFromAlg(alg) {\n  switch (typeof alg === 'string' && alg.slice(0, 2)) {\n    case 'RS':\n    case 'PS':\n      return 'RSA';\n    case 'ES':\n      return 'EC';\n    case 'Ed':\n      return 'OKP';\n    default:\n      return undefined;\n  }\n}\nfunction getAlgorithms(use, alg, kty, crv) {\n  // Ed25519, Ed448, and secp256k1 always have \"alg\"\n  // OKP always has \"use\"\n  if (alg) {\n    return new Set([alg]);\n  }\n  switch (kty) {\n    case 'EC':\n      {\n        let algs = [];\n        if (use === 'enc' || use === undefined) {\n          algs = algs.concat(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n        }\n        if (use === 'sig' || use === undefined) {\n          switch (crv) {\n            case 'P-256':\n            case 'P-384':\n              algs = algs.concat([`ES${crv.slice(-3)}`]);\n              break;\n            case 'P-521':\n              algs = algs.concat(['ES512']);\n              break;\n            case 'secp256k1':\n              if (jose.cryptoRuntime === 'node:crypto') {\n                algs = algs.concat(['ES256K']);\n              }\n              break;\n          }\n        }\n        return new Set(algs);\n      }\n    case 'OKP':\n      {\n        return new Set(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n      }\n    case 'RSA':\n      {\n        let algs = [];\n        if (use === 'enc' || use === undefined) {\n          algs = algs.concat(['RSA-OAEP', 'RSA-OAEP-256', 'RSA-OAEP-384', 'RSA-OAEP-512']);\n          if (jose.cryptoRuntime === 'node:crypto') {\n            algs = algs.concat(['RSA1_5']);\n          }\n        }\n        if (use === 'sig' || use === undefined) {\n          algs = algs.concat(['PS256', 'PS384', 'PS512', 'RS256', 'RS384', 'RS512']);\n        }\n        return new Set(algs);\n      }\n    default:\n      throw new Error('unreachable');\n  }\n}\nmodule.exports = class KeyStore {\n  #keys;\n  constructor(i, keys) {\n    if (i !== internal) throw new Error('invalid constructor call');\n    this.#keys = keys;\n  }\n  toJWKS() {\n    return {\n      keys: this.map(({\n        jwk: {\n          d,\n          p,\n          q,\n          dp,\n          dq,\n          qi,\n          ...jwk\n        }\n      }) => jwk)\n    };\n  }\n  all({\n    alg,\n    kid,\n    use\n  } = {}) {\n    if (!use || !alg) {\n      throw new Error();\n    }\n    const kty = getKtyFromAlg(alg);\n    const search = {\n      alg,\n      use\n    };\n    return this.filter(key => {\n      let candidate = true;\n      if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n        candidate = false;\n      }\n      if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n        candidate = false;\n      }\n      if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n        candidate = false;\n      }\n      if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n        candidate = false;\n      } else if (!key.algorithms.has(alg)) {\n        candidate = false;\n      }\n      return candidate;\n    }).sort((first, second) => keyscore(second, search) - keyscore(first, search));\n  }\n  get(...args) {\n    return this.all(...args)[0];\n  }\n  static async fromJWKS(jwks, {\n    onlyPublic = false,\n    onlyPrivate = false\n  } = {}) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some(k => !isPlainObject(k) || !('kty' in k))) {\n      throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n    const keys = [];\n    for (let jwk of jwks.keys) {\n      jwk = clone(jwk);\n      const {\n        kty,\n        kid,\n        crv\n      } = jwk;\n      let {\n        alg,\n        use\n      } = jwk;\n      if (typeof kty !== 'string' || !kty) {\n        continue;\n      }\n      if (use !== undefined && use !== 'sig' && use !== 'enc') {\n        continue;\n      }\n      if (typeof alg !== 'string' && alg !== undefined) {\n        continue;\n      }\n      if (typeof kid !== 'string' && kid !== undefined) {\n        continue;\n      }\n      if (kty === 'EC' && use === 'sig') {\n        switch (crv) {\n          case 'P-256':\n            alg = 'ES256';\n            break;\n          case 'P-384':\n            alg = 'ES384';\n            break;\n          case 'P-521':\n            alg = 'ES512';\n            break;\n          default:\n            break;\n        }\n      }\n      if (crv === 'secp256k1') {\n        use = 'sig';\n        alg = 'ES256K';\n      }\n      if (kty === 'OKP') {\n        switch (crv) {\n          case 'Ed25519':\n          case 'Ed448':\n            use = 'sig';\n            alg = 'EdDSA';\n            break;\n          case 'X25519':\n          case 'X448':\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n      if (alg && !use) {\n        switch (true) {\n          case alg.startsWith('ECDH'):\n            use = 'enc';\n            break;\n          case alg.startsWith('RSA'):\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n      if (onlyPrivate && (jwk.kty === 'oct' || !jwk.d)) {\n        throw new Error('jwks must only contain private keys');\n      }\n      if (onlyPublic && (jwk.d || jwk.k)) {\n        continue;\n      }\n      keys.push({\n        jwk: {\n          ...jwk,\n          alg,\n          use\n        },\n        async keyObject(alg) {\n          if (this[alg]) {\n            return this[alg];\n          }\n          const keyObject = await jose.importJWK(this.jwk, alg);\n          this[alg] = keyObject;\n          return keyObject;\n        },\n        get algorithms() {\n          Object.defineProperty(this, 'algorithms', {\n            value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n            enumerable: true,\n            configurable: false\n          });\n          return this.algorithms;\n        }\n      });\n    }\n    return new this(internal, keys);\n  }\n  filter(...args) {\n    return this.#keys.filter(...args);\n  }\n  find(...args) {\n    return this.#keys.find(...args);\n  }\n  every(...args) {\n    return this.#keys.every(...args);\n  }\n  some(...args) {\n    return this.#keys.some(...args);\n  }\n  map(...args) {\n    return this.#keys.map(...args);\n  }\n  forEach(...args) {\n    return this.#keys.forEach(...args);\n  }\n  reduce(...args) {\n    return this.#keys.reduce(...args);\n  }\n  sort(...args) {\n    return this.#keys.sort(...args);\n  }\n  *[Symbol.iterator]() {\n    for (const key of this.#keys) {\n      yield key;\n    }\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nfunction merge(target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (isPlainObject(target[key]) && isPlainObject(value)) {\n        target[key] = merge(target[key], value);\n      } else if (typeof value !== 'undefined') {\n        target[key] = value;\n      }\n    }\n  }\n  return target;\n}\nmodule.exports = merge;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = function pick(object, ...paths) {\n  const obj = {};\n  for (const path of paths) {\n    if (object[path] !== undefined) {\n      obj[path] = object[path];\n    }\n  }\n  return obj;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzIiwibWFwcGluZ3MiOiI7O0FBQUFBLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHLFNBQVNDLElBQUlBLENBQUNDLE1BQU0sRUFBRSxHQUFHQyxLQUFLLEVBQUU7RUFDL0MsTUFBTUMsR0FBRyxHQUFHLENBQUMsQ0FBQztFQUNkLEtBQUssTUFBTUMsSUFBSSxJQUFJRixLQUFLLEVBQUU7SUFDeEIsSUFBSUQsTUFBTSxDQUFDRyxJQUFJLENBQUMsS0FBS0MsU0FBUyxFQUFFO01BQzlCRixHQUFHLENBQUNDLElBQUksQ0FBQyxHQUFHSCxNQUFNLENBQUNHLElBQUksQ0FBQztJQUMxQjtFQUNGO0VBQ0EsT0FBT0QsR0FBRztBQUNaLENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHBpY2suanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBwaWNrKG9iamVjdCwgLi4ucGF0aHMpIHtcbiAgY29uc3Qgb2JqID0ge307XG4gIGZvciAoY29uc3QgcGF0aCBvZiBwYXRocykge1xuICAgIGlmIChvYmplY3RbcGF0aF0gIT09IHVuZGVmaW5lZCkge1xuICAgICAgb2JqW3BhdGhdID0gb2JqZWN0W3BhdGhdO1xuICAgIH1cbiAgfVxuICByZXR1cm4gb2JqO1xufTtcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwicGljayIsIm9iamVjdCIsInBhdGhzIiwib2JqIiwicGF0aCIsInVuZGVmaW5lZCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  STATUS_CODES\n} = __webpack_require__(/*! http */ \"http\");\nconst {\n  format\n} = __webpack_require__(/*! util */ \"util\");\nconst {\n  OPError\n} = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst throwAuthenticateErrors = response => {\n  const params = parseWwwAuthenticate(response.headers['www-authenticate']);\n  if (params.error) {\n    throw new OPError(params, response);\n  }\n};\nconst isStandardBodyError = response => {\n  let result = false;\n  try {\n    let jsonbody;\n    if (typeof response.body !== 'object' || Buffer.isBuffer(response.body)) {\n      jsonbody = JSON.parse(response.body);\n    } else {\n      jsonbody = response.body;\n    }\n    result = typeof jsonbody.error === 'string' && jsonbody.error.length;\n    if (result) Object.defineProperty(response, 'body', {\n      value: jsonbody,\n      configurable: true\n    });\n  } catch (err) {}\n  return result;\n};\nfunction processResponse(response, {\n  statusCode = 200,\n  body = true,\n  bearer = false\n} = {}) {\n  if (response.statusCode !== statusCode) {\n    if (bearer) {\n      throwAuthenticateErrors(response);\n    }\n    if (isStandardBodyError(response)) {\n      throw new OPError(response.body, response);\n    }\n    throw new OPError({\n      error: format('expected %i %s, got: %i %s', statusCode, STATUS_CODES[statusCode], response.statusCode, STATUS_CODES[response.statusCode])\n    }, response);\n  }\n  if (body && !response.body) {\n    throw new OPError({\n      error: format('expected %i %s with body but no body was returned', statusCode, STATUS_CODES[statusCode])\n    }, response);\n  }\n  return response.body;\n}\nmodule.exports = processResponse;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst {\n  once\n} = __webpack_require__(/*! events */ \"events\");\nconst {\n  URL\n} = __webpack_require__(/*! url */ \"url\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/openid-client/package.json\");\nconst {\n  RPError\n} = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst pick = __webpack_require__(/*! ./pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst {\n  deep: defaultsDeep\n} = __webpack_require__(/*! ./defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst {\n  HTTP_OPTIONS\n} = __webpack_require__(/*! ./consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\nconst allowed = ['agent', 'ca', 'cert', 'crl', 'headers', 'key', 'lookup', 'passphrase', 'pfx', 'timeout'];\nconst setDefaults = (props, options) => {\n  DEFAULT_HTTP_OPTIONS = defaultsDeep({}, props.length ? pick(options, ...props) : options, DEFAULT_HTTP_OPTIONS);\n};\nsetDefaults([], {\n  headers: {\n    'User-Agent': `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n    'Accept-Encoding': 'identity'\n  },\n  timeout: 3500\n});\nfunction send(req, body, contentType) {\n  if (contentType) {\n    req.removeHeader('content-type');\n    req.setHeader('content-type', contentType);\n  }\n  if (body) {\n    req.removeHeader('content-length');\n    req.setHeader('content-length', Buffer.byteLength(body));\n    req.write(body);\n  }\n  req.end();\n}\nconst nonces = new LRU({\n  max: 100\n});\nmodule.exports = async function request(options, {\n  accessToken,\n  mTLS = false,\n  DPoP\n} = {}) {\n  let url;\n  try {\n    url = new URL(options.url);\n    delete options.url;\n    assert(/^(https?:)$/.test(url.protocol));\n  } catch (err) {\n    throw new TypeError('only valid absolute URLs can be requested');\n  }\n  const optsFn = this[HTTP_OPTIONS];\n  let opts = options;\n  const nonceKey = `${url.origin}${url.pathname}`;\n  if (DPoP && 'dpopProof' in this) {\n    opts.headers = opts.headers || {};\n    opts.headers.DPoP = await this.dpopProof({\n      htu: `${url.origin}${url.pathname}`,\n      htm: options.method || 'GET',\n      nonce: nonces.get(nonceKey)\n    }, DPoP, accessToken);\n  }\n  let userOptions;\n  if (optsFn) {\n    userOptions = pick(optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)), ...allowed);\n  }\n  opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n  if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n    throw new TypeError('mutual-TLS certificate and key not set');\n  }\n  if (opts.searchParams) {\n    for (const [key, value] of Object.entries(opts.searchParams)) {\n      url.searchParams.delete(key);\n      url.searchParams.set(key, value);\n    }\n  }\n  let responseType;\n  let form;\n  let json;\n  let body;\n  ({\n    form,\n    responseType,\n    json,\n    body,\n    ...opts\n  } = opts);\n  for (const [key, value] of Object.entries(opts.headers || {})) {\n    if (value === undefined) {\n      delete opts.headers[key];\n    }\n  }\n  let response;\n  const req = (url.protocol === 'https:' ? https.request : http.request)(url.href, opts);\n  return (async () => {\n    if (json) {\n      send(req, JSON.stringify(json), 'application/json');\n    } else if (form) {\n      send(req, querystring.stringify(form), 'application/x-www-form-urlencoded');\n    } else if (body) {\n      send(req, body);\n    } else {\n      send(req);\n    }\n    [response] = await Promise.race([once(req, 'response'), once(req, 'timeout')]);\n\n    // timeout reached\n    if (!response) {\n      req.destroy();\n      throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n    }\n    const parts = [];\n    for await (const part of response) {\n      parts.push(part);\n    }\n    if (parts.length) {\n      switch (responseType) {\n        case 'json':\n          {\n            Object.defineProperty(response, 'body', {\n              get() {\n                let value = Buffer.concat(parts);\n                try {\n                  value = JSON.parse(value);\n                } catch (err) {\n                  Object.defineProperty(err, 'response', {\n                    value: response\n                  });\n                  throw err;\n                } finally {\n                  Object.defineProperty(response, 'body', {\n                    value,\n                    configurable: true\n                  });\n                }\n                return value;\n              },\n              configurable: true\n            });\n            break;\n          }\n        case undefined:\n        case 'buffer':\n          {\n            Object.defineProperty(response, 'body', {\n              get() {\n                const value = Buffer.concat(parts);\n                Object.defineProperty(response, 'body', {\n                  value,\n                  configurable: true\n                });\n                return value;\n              },\n              configurable: true\n            });\n            break;\n          }\n        default:\n          throw new TypeError('unsupported responseType request option');\n      }\n    }\n    return response;\n  })().catch(err => {\n    if (response) Object.defineProperty(err, 'response', {\n      value: response\n    });\n    throw err;\n  }).finally(() => {\n    const dpopNonce = response && response.headers['dpop-nonce'];\n    if (dpopNonce && NQCHAR.test(dpopNonce)) {\n      nonces.set(nonceKey, dpopNonce);\n    }\n  });\n};\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = () => Math.floor(Date.now() / 1000);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcyIsIm1hcHBpbmdzIjoiOztBQUFBQSxNQUFNLENBQUNDLE9BQU8sR0FBRyxNQUFNQyxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcdW5peF90aW1lc3RhbXAuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSAoKSA9PiBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKTtcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiTWF0aCIsImZsb29yIiwiRGF0ZSIsIm5vdyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports.keystores = new WeakMap();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiI7O0FBQUFBLHdCQUF3QixHQUFHLElBQUlHLE9BQU8sQ0FBQyxDQUFDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFx3ZWFrX2NhY2hlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzLmtleXN0b3JlcyA9IG5ldyBXZWFrTWFwKCk7XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImtleXN0b3JlcyIsIldlYWtNYXAiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("\n\n// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\n\nconst PORT = /^\\d+$/;\nfunction hasScheme(input) {\n  if (input.includes('://')) return true;\n  const authority = input.replace(/(\\/|\\?)/g, '#').split('#')[0];\n  if (authority.includes(':')) {\n    const index = authority.indexOf(':');\n    const hostOrPort = authority.slice(index + 1);\n    if (!PORT.test(hostOrPort)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction acctSchemeAssumed(input) {\n  if (!input.includes('@')) return false;\n  const parts = input.split('@');\n  const host = parts[parts.length - 1];\n  return !(host.includes(':') || host.includes('/') || host.includes('?'));\n}\nfunction normalize(input) {\n  if (typeof input !== 'string') {\n    throw new TypeError('input must be a string');\n  }\n  let output;\n  if (hasScheme(input)) {\n    output = input;\n  } else if (acctSchemeAssumed(input)) {\n    output = `acct:${input}`;\n  } else {\n    output = `https://${input}`;\n  }\n  return output.split('#')[0];\n}\nmodule.exports = normalize;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("\n\nconst REGEXP = /(\\w+)=(\"[^\"]*\")/g;\nmodule.exports = wwwAuthenticate => {\n  const params = {};\n  try {\n    while (REGEXP.exec(wwwAuthenticate) !== null) {\n      if (RegExp.$1 && RegExp.$2) {\n        params[RegExp.$1] = RegExp.$2.slice(1, -1);\n      }\n    }\n  } catch (err) {}\n  return params;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyIsIm1hcHBpbmdzIjoiOztBQUFBLE1BQU1BLE1BQU0sR0FBRyxrQkFBa0I7QUFFakNDLE1BQU0sQ0FBQ0MsT0FBTyxHQUFJQyxlQUFlLElBQUs7RUFDcEMsTUFBTUMsTUFBTSxHQUFHLENBQUMsQ0FBQztFQUNqQixJQUFJO0lBQ0YsT0FBT0osTUFBTSxDQUFDSyxJQUFJLENBQUNGLGVBQWUsQ0FBQyxLQUFLLElBQUksRUFBRTtNQUM1QyxJQUFJRyxNQUFNLENBQUNDLEVBQUUsSUFBSUQsTUFBTSxDQUFDRSxFQUFFLEVBQUU7UUFDMUJKLE1BQU0sQ0FBQ0UsTUFBTSxDQUFDQyxFQUFFLENBQUMsR0FBR0QsTUFBTSxDQUFDRSxFQUFFLENBQUNDLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7TUFDNUM7SUFDRjtFQUNGLENBQUMsQ0FBQyxPQUFPQyxHQUFHLEVBQUUsQ0FBQztFQUVmLE9BQU9OLE1BQU07QUFDZixDQUFDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFx3d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBSRUdFWFAgPSAvKFxcdyspPShcIlteXCJdKlwiKS9nO1xuXG5tb2R1bGUuZXhwb3J0cyA9ICh3d3dBdXRoZW50aWNhdGUpID0+IHtcbiAgY29uc3QgcGFyYW1zID0ge307XG4gIHRyeSB7XG4gICAgd2hpbGUgKFJFR0VYUC5leGVjKHd3d0F1dGhlbnRpY2F0ZSkgIT09IG51bGwpIHtcbiAgICAgIGlmIChSZWdFeHAuJDEgJiYgUmVnRXhwLiQyKSB7XG4gICAgICAgIHBhcmFtc1tSZWdFeHAuJDFdID0gUmVnRXhwLiQyLnNsaWNlKDEsIC0xKTtcbiAgICAgIH1cbiAgICB9XG4gIH0gY2F0Y2ggKGVycikge31cblxuICByZXR1cm4gcGFyYW1zO1xufTtcbiJdLCJuYW1lcyI6WyJSRUdFWFAiLCJtb2R1bGUiLCJleHBvcnRzIiwid3d3QXV0aGVudGljYXRlIiwicGFyYW1zIiwiZXhlYyIsIlJlZ0V4cCIsIiQxIiwiJDIiLCJzbGljZSIsImVyciJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Issuer = __webpack_require__(/*! ./issuer */ \"(rsc)/./node_modules/openid-client/lib/issuer.js\");\nconst {\n  OPError,\n  RPError\n} = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(rsc)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst {\n  CLOCK_TOLERANCE,\n  HTTP_OPTIONS\n} = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst {\n  setDefaults\n} = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nmodule.exports = {\n  Issuer,\n  Strategy,\n  TokenSet,\n  errors: {\n    OPError,\n    RPError\n  },\n  custom: {\n    setHttpOptionsDefaults: setDefaults,\n    http_options: HTTP_OPTIONS,\n    clock_tolerance: CLOCK_TOLERANCE\n  },\n  generators\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst {\n  inspect\n} = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst {\n  RPError\n} = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(rsc)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst {\n  keystore\n} = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst AAD_MULTITENANT_DISCOVERY = ['https://login.microsoftonline.com/common/.well-known/openid-configuration', 'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration', 'https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration', 'https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration'];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n  claim_types_supported: ['normal'],\n  claims_parameter_supported: false,\n  grant_types_supported: ['authorization_code', 'implicit'],\n  request_parameter_supported: false,\n  request_uri_parameter_supported: true,\n  require_request_uri_registration: false,\n  response_modes_supported: ['query', 'fragment'],\n  token_endpoint_auth_methods_supported: ['client_secret_basic']\n};\nclass Issuer {\n  #metadata;\n  constructor(meta = {}) {\n    const aadIssValidation = meta[AAD_MULTITENANT];\n    delete meta[AAD_MULTITENANT];\n    ['introspection', 'revocation'].forEach(endpoint => {\n      // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n      // are defined\n      if (meta[`${endpoint}_endpoint`] && meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined && meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined) {\n        if (meta.token_endpoint_auth_methods_supported) {\n          meta[`${endpoint}_endpoint_auth_methods_supported`] = meta.token_endpoint_auth_methods_supported;\n        }\n        if (meta.token_endpoint_auth_signing_alg_values_supported) {\n          meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] = meta.token_endpoint_auth_signing_alg_values_supported;\n        }\n      }\n    });\n    this.#metadata = new Map();\n    Object.entries(meta).forEach(([key, value]) => {\n      this.#metadata.set(key, value);\n      if (!this[key]) {\n        Object.defineProperty(this, key, {\n          get() {\n            return this.#metadata.get(key);\n          },\n          enumerable: true\n        });\n      }\n    });\n    registry.set(this.issuer, this);\n    const Client = getClient(this, aadIssValidation);\n    Object.defineProperties(this, {\n      Client: {\n        value: Client,\n        enumerable: true\n      },\n      FAPI1Client: {\n        value: class FAPI1Client extends Client {},\n        enumerable: true\n      },\n      FAPI2Client: {\n        value: class FAPI2Client extends Client {},\n        enumerable: true\n      }\n    });\n  }\n  get metadata() {\n    return clone(Object.fromEntries(this.#metadata.entries()));\n  }\n  static async webfinger(input) {\n    const resource = webfingerNormalize(input);\n    const {\n      host\n    } = url.parse(resource);\n    const webfingerUrl = `https://${host}/.well-known/webfinger`;\n    const response = await request.call(this, {\n      method: 'GET',\n      url: webfingerUrl,\n      responseType: 'json',\n      searchParams: {\n        resource,\n        rel: 'http://openid.net/specs/connect/1.0/issuer'\n      },\n      headers: {\n        Accept: 'application/json'\n      }\n    });\n    const body = processResponse(response);\n    const location = Array.isArray(body.links) && body.links.find(link => typeof link === 'object' && link.rel === 'http://openid.net/specs/connect/1.0/issuer' && link.href);\n    if (!location) {\n      throw new RPError({\n        message: 'no issuer found in webfinger response',\n        body\n      });\n    }\n    if (typeof location.href !== 'string' || !location.href.startsWith('https://')) {\n      throw new RPError({\n        printf: ['invalid issuer location %s', location.href],\n        body\n      });\n    }\n    const expectedIssuer = location.href;\n    if (registry.has(expectedIssuer)) {\n      return registry.get(expectedIssuer);\n    }\n    const issuer = await this.discover(expectedIssuer);\n    if (issuer.issuer !== expectedIssuer) {\n      registry.del(issuer.issuer);\n      throw new RPError('discovered issuer mismatch, expected %s, got: %s', expectedIssuer, issuer.issuer);\n    }\n    return issuer;\n  }\n  static async discover(uri) {\n    const wellKnownUri = resolveWellKnownUri(uri);\n    const response = await request.call(this, {\n      method: 'GET',\n      responseType: 'json',\n      url: wellKnownUri,\n      headers: {\n        Accept: 'application/json'\n      }\n    });\n    const body = processResponse(response);\n    return new Issuer({\n      ...ISSUER_DEFAULTS,\n      ...body,\n      [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find(discoveryURL => wellKnownUri.startsWith(discoveryURL))\n    });\n  }\n  async reloadJwksUri() {\n    await keystore.call(this, true);\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.metadata, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true\n    })}`;\n  }\n}\nfunction resolveWellKnownUri(uri) {\n  const parsed = url.parse(uri);\n  if (parsed.pathname.includes('/.well-known/')) {\n    return uri;\n  } else {\n    let pathname;\n    if (parsed.pathname.endsWith('/')) {\n      pathname = `${parsed.pathname}.well-known/openid-configuration`;\n    } else {\n      pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n    }\n    return url.format({\n      ...parsed,\n      pathname\n    });\n  }\n}\nmodule.exports = Issuer;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyLmpzIiwibWFwcGluZ3MiOiI7O0FBQUEsTUFBTTtFQUFFQTtBQUFRLENBQUMsR0FBR0MsbUJBQU8sQ0FBQyxrQkFBTSxDQUFDO0FBQ25DLE1BQU1DLEdBQUcsR0FBR0QsbUJBQU8sQ0FBQyxnQkFBSyxDQUFDO0FBRTFCLE1BQU07RUFBRUU7QUFBUSxDQUFDLEdBQUdGLG1CQUFPLENBQUMsa0VBQVUsQ0FBQztBQUN2QyxNQUFNRyxTQUFTLEdBQUdILG1CQUFPLENBQUMsa0VBQVUsQ0FBQztBQUNyQyxNQUFNSSxRQUFRLEdBQUdKLG1CQUFPLENBQUMsb0ZBQW1CLENBQUM7QUFDN0MsTUFBTUssZUFBZSxHQUFHTCxtQkFBTyxDQUFDLHNHQUE0QixDQUFDO0FBQzdELE1BQU1NLGtCQUFrQixHQUFHTixtQkFBTyxDQUFDLDRHQUErQixDQUFDO0FBQ25FLE1BQU1PLE9BQU8sR0FBR1AsbUJBQU8sQ0FBQyxvRkFBbUIsQ0FBQztBQUM1QyxNQUFNUSxLQUFLLEdBQUdSLG1CQUFPLENBQUMsMEZBQXNCLENBQUM7QUFDN0MsTUFBTTtFQUFFUztBQUFTLENBQUMsR0FBR1QsbUJBQU8sQ0FBQyxrRkFBa0IsQ0FBQztBQUVoRCxNQUFNVSx5QkFBeUIsR0FBRyxDQUNoQywyRUFBMkUsRUFDM0UsZ0ZBQWdGLEVBQ2hGLHVGQUF1RixFQUN2RixtRkFBbUYsQ0FDcEY7QUFDRCxNQUFNQyxlQUFlLEdBQUdDLE1BQU0sQ0FBQyxDQUFDO0FBQ2hDLE1BQU1DLGVBQWUsR0FBRztFQUN0QkMscUJBQXFCLEVBQUUsQ0FBQyxRQUFRLENBQUM7RUFDakNDLDBCQUEwQixFQUFFLEtBQUs7RUFDakNDLHFCQUFxQixFQUFFLENBQUMsb0JBQW9CLEVBQUUsVUFBVSxDQUFDO0VBQ3pEQywyQkFBMkIsRUFBRSxLQUFLO0VBQ2xDQywrQkFBK0IsRUFBRSxJQUFJO0VBQ3JDQyxnQ0FBZ0MsRUFBRSxLQUFLO0VBQ3ZDQyx3QkFBd0IsRUFBRSxDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUM7RUFDL0NDLHFDQUFxQyxFQUFFLENBQUMscUJBQXFCO0FBQy9ELENBQUM7QUFFRCxNQUFNQyxNQUFNLENBQUM7RUFDWCxDQUFDQyxRQUFRO0VBQ1RDLFdBQVdBLENBQUNDLElBQUksR0FBRyxDQUFDLENBQUMsRUFBRTtJQUNyQixNQUFNQyxnQkFBZ0IsR0FBR0QsSUFBSSxDQUFDZCxlQUFlLENBQUM7SUFDOUMsT0FBT2MsSUFBSSxDQUFDZCxlQUFlLENBQUM7SUFDNUIsQ0FBQyxlQUFlLEVBQUUsWUFBWSxDQUFDLENBQUNnQixPQUFPLENBQUVDLFFBQVEsSUFBSztNQUNwRDtNQUNBO01BQ0EsSUFDRUgsSUFBSSxDQUFFLEdBQUVHLFFBQVMsV0FBVSxDQUFDLElBQzVCSCxJQUFJLENBQUUsR0FBRUcsUUFBUyxrQ0FBaUMsQ0FBQyxLQUFLQyxTQUFTLElBQ2pFSixJQUFJLENBQUUsR0FBRUcsUUFBUyw2Q0FBNEMsQ0FBQyxLQUFLQyxTQUFTLEVBQzVFO1FBQ0EsSUFBSUosSUFBSSxDQUFDSixxQ0FBcUMsRUFBRTtVQUM5Q0ksSUFBSSxDQUFFLEdBQUVHLFFBQVMsa0NBQWlDLENBQUMsR0FDakRILElBQUksQ0FBQ0oscUNBQXFDO1FBQzlDO1FBQ0EsSUFBSUksSUFBSSxDQUFDSyxnREFBZ0QsRUFBRTtVQUN6REwsSUFBSSxDQUFFLEdBQUVHLFFBQVMsNkNBQTRDLENBQUMsR0FDNURILElBQUksQ0FBQ0ssZ0RBQWdEO1FBQ3pEO01BQ0Y7SUFDRixDQUFDLENBQUM7SUFFRixJQUFJLENBQUMsQ0FBQ1AsUUFBUSxHQUFHLElBQUlRLEdBQUcsQ0FBQyxDQUFDO0lBRTFCQyxNQUFNLENBQUNDLE9BQU8sQ0FBQ1IsSUFBSSxDQUFDLENBQUNFLE9BQU8sQ0FBQyxDQUFDLENBQUNPLEdBQUcsRUFBRUMsS0FBSyxDQUFDLEtBQUs7TUFDN0MsSUFBSSxDQUFDLENBQUNaLFFBQVEsQ0FBQ2EsR0FBRyxDQUFDRixHQUFHLEVBQUVDLEtBQUssQ0FBQztNQUM5QixJQUFJLENBQUMsSUFBSSxDQUFDRCxHQUFHLENBQUMsRUFBRTtRQUNkRixNQUFNLENBQUNLLGNBQWMsQ0FBQyxJQUFJLEVBQUVILEdBQUcsRUFBRTtVQUMvQkksR0FBR0EsQ0FBQSxFQUFHO1lBQ0osT0FBTyxJQUFJLENBQUMsQ0FBQ2YsUUFBUSxDQUFDZSxHQUFHLENBQUNKLEdBQUcsQ0FBQztVQUNoQyxDQUFDO1VBQ0RLLFVBQVUsRUFBRTtRQUNkLENBQUMsQ0FBQztNQUNKO0lBQ0YsQ0FBQyxDQUFDO0lBRUZuQyxRQUFRLENBQUNnQyxHQUFHLENBQUMsSUFBSSxDQUFDSSxNQUFNLEVBQUUsSUFBSSxDQUFDO0lBRS9CLE1BQU1DLE1BQU0sR0FBR3RDLFNBQVMsQ0FBQyxJQUFJLEVBQUV1QixnQkFBZ0IsQ0FBQztJQUVoRE0sTUFBTSxDQUFDVSxnQkFBZ0IsQ0FBQyxJQUFJLEVBQUU7TUFDNUJELE1BQU0sRUFBRTtRQUFFTixLQUFLLEVBQUVNLE1BQU07UUFBRUYsVUFBVSxFQUFFO01BQUssQ0FBQztNQUMzQ0ksV0FBVyxFQUFFO1FBQUVSLEtBQUssRUFBRSxNQUFNUSxXQUFXLFNBQVNGLE1BQU0sQ0FBQyxFQUFFO1FBQUVGLFVBQVUsRUFBRTtNQUFLLENBQUM7TUFDN0VLLFdBQVcsRUFBRTtRQUFFVCxLQUFLLEVBQUUsTUFBTVMsV0FBVyxTQUFTSCxNQUFNLENBQUMsRUFBRTtRQUFFRixVQUFVLEVBQUU7TUFBSztJQUM5RSxDQUFDLENBQUM7RUFDSjtFQUVBLElBQUloQixRQUFRQSxDQUFBLEVBQUc7SUFDYixPQUFPZixLQUFLLENBQUN3QixNQUFNLENBQUNhLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQ3RCLFFBQVEsQ0FBQ1UsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDO0VBQzVEO0VBRUEsYUFBYWEsU0FBU0EsQ0FBQ0MsS0FBSyxFQUFFO0lBQzVCLE1BQU1DLFFBQVEsR0FBRzFDLGtCQUFrQixDQUFDeUMsS0FBSyxDQUFDO0lBQzFDLE1BQU07TUFBRUU7SUFBSyxDQUFDLEdBQUdoRCxHQUFHLENBQUNpRCxLQUFLLENBQUNGLFFBQVEsQ0FBQztJQUNwQyxNQUFNRyxZQUFZLEdBQUksV0FBVUYsSUFBSyx3QkFBdUI7SUFFNUQsTUFBTUcsUUFBUSxHQUFHLE1BQU03QyxPQUFPLENBQUM4QyxJQUFJLENBQUMsSUFBSSxFQUFFO01BQ3hDQyxNQUFNLEVBQUUsS0FBSztNQUNickQsR0FBRyxFQUFFa0QsWUFBWTtNQUNqQkksWUFBWSxFQUFFLE1BQU07TUFDcEJDLFlBQVksRUFBRTtRQUFFUixRQUFRO1FBQUVTLEdBQUcsRUFBRTtNQUE2QyxDQUFDO01BQzdFQyxPQUFPLEVBQUU7UUFDUEMsTUFBTSxFQUFFO01BQ1Y7SUFDRixDQUFDLENBQUM7SUFDRixNQUFNQyxJQUFJLEdBQUd2RCxlQUFlLENBQUMrQyxRQUFRLENBQUM7SUFFdEMsTUFBTVMsUUFBUSxHQUNaQyxLQUFLLENBQUNDLE9BQU8sQ0FBQ0gsSUFBSSxDQUFDSSxLQUFLLENBQUMsSUFDekJKLElBQUksQ0FBQ0ksS0FBSyxDQUFDQyxJQUFJLENBQ1pDLElBQUksSUFDSCxPQUFPQSxJQUFJLEtBQUssUUFBUSxJQUN4QkEsSUFBSSxDQUFDVCxHQUFHLEtBQUssNENBQTRDLElBQ3pEUyxJQUFJLENBQUNDLElBQ1QsQ0FBQztJQUVILElBQUksQ0FBQ04sUUFBUSxFQUFFO01BQ2IsTUFBTSxJQUFJM0QsT0FBTyxDQUFDO1FBQ2hCa0UsT0FBTyxFQUFFLHVDQUF1QztRQUNoRFI7TUFDRixDQUFDLENBQUM7SUFDSjtJQUVBLElBQUksT0FBT0MsUUFBUSxDQUFDTSxJQUFJLEtBQUssUUFBUSxJQUFJLENBQUNOLFFBQVEsQ0FBQ00sSUFBSSxDQUFDRSxVQUFVLENBQUMsVUFBVSxDQUFDLEVBQUU7TUFDOUUsTUFBTSxJQUFJbkUsT0FBTyxDQUFDO1FBQ2hCb0UsTUFBTSxFQUFFLENBQUMsNEJBQTRCLEVBQUVULFFBQVEsQ0FBQ00sSUFBSSxDQUFDO1FBQ3JEUDtNQUNGLENBQUMsQ0FBQztJQUNKO0lBRUEsTUFBTVcsY0FBYyxHQUFHVixRQUFRLENBQUNNLElBQUk7SUFDcEMsSUFBSS9ELFFBQVEsQ0FBQ29FLEdBQUcsQ0FBQ0QsY0FBYyxDQUFDLEVBQUU7TUFDaEMsT0FBT25FLFFBQVEsQ0FBQ2tDLEdBQUcsQ0FBQ2lDLGNBQWMsQ0FBQztJQUNyQztJQUVBLE1BQU0vQixNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUNpQyxRQUFRLENBQUNGLGNBQWMsQ0FBQztJQUVsRCxJQUFJL0IsTUFBTSxDQUFDQSxNQUFNLEtBQUsrQixjQUFjLEVBQUU7TUFDcENuRSxRQUFRLENBQUNzRSxHQUFHLENBQUNsQyxNQUFNLENBQUNBLE1BQU0sQ0FBQztNQUMzQixNQUFNLElBQUl0QyxPQUFPLENBQ2Ysa0RBQWtELEVBQ2xEcUUsY0FBYyxFQUNkL0IsTUFBTSxDQUFDQSxNQUNULENBQUM7SUFDSDtJQUNBLE9BQU9BLE1BQU07RUFDZjtFQUVBLGFBQWFpQyxRQUFRQSxDQUFDRSxHQUFHLEVBQUU7SUFDekIsTUFBTUMsWUFBWSxHQUFHQyxtQkFBbUIsQ0FBQ0YsR0FBRyxDQUFDO0lBRTdDLE1BQU12QixRQUFRLEdBQUcsTUFBTTdDLE9BQU8sQ0FBQzhDLElBQUksQ0FBQyxJQUFJLEVBQUU7TUFDeENDLE1BQU0sRUFBRSxLQUFLO01BQ2JDLFlBQVksRUFBRSxNQUFNO01BQ3BCdEQsR0FBRyxFQUFFMkUsWUFBWTtNQUNqQmxCLE9BQU8sRUFBRTtRQUNQQyxNQUFNLEVBQUU7TUFDVjtJQUNGLENBQUMsQ0FBQztJQUNGLE1BQU1DLElBQUksR0FBR3ZELGVBQWUsQ0FBQytDLFFBQVEsQ0FBQztJQUN0QyxPQUFPLElBQUk5QixNQUFNLENBQUM7TUFDaEIsR0FBR1QsZUFBZTtNQUNsQixHQUFHK0MsSUFBSTtNQUNQLENBQUNqRCxlQUFlLEdBQUcsQ0FBQyxDQUFDRCx5QkFBeUIsQ0FBQ3VELElBQUksQ0FBRWEsWUFBWSxJQUMvREYsWUFBWSxDQUFDUCxVQUFVLENBQUNTLFlBQVksQ0FDdEM7SUFDRixDQUFDLENBQUM7RUFDSjtFQUVBLE1BQU1DLGFBQWFBLENBQUEsRUFBRztJQUNwQixNQUFNdEUsUUFBUSxDQUFDNEMsSUFBSSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUM7RUFDakM7O0VBRUE7RUFDQSxDQUFDdEQsT0FBTyxDQUFDaUYsTUFBTSxJQUFJO0lBQ2pCLE9BQVEsR0FBRSxJQUFJLENBQUN4RCxXQUFXLENBQUN5RCxJQUFLLElBQUdsRixPQUFPLENBQUMsSUFBSSxDQUFDd0IsUUFBUSxFQUFFO01BQ3hEMkQsS0FBSyxFQUFFQyxRQUFRO01BQ2ZDLE1BQU0sRUFBRUMsT0FBTyxDQUFDQyxNQUFNLENBQUNDLEtBQUs7TUFDNUJDLE9BQU8sRUFBRSxLQUFLO01BQ2RDLE1BQU0sRUFBRTtJQUNWLENBQUMsQ0FBRSxFQUFDO0VBQ047QUFDRjtBQUVBLFNBQVNaLG1CQUFtQkEsQ0FBQ0YsR0FBRyxFQUFFO0VBQ2hDLE1BQU1lLE1BQU0sR0FBR3pGLEdBQUcsQ0FBQ2lELEtBQUssQ0FBQ3lCLEdBQUcsQ0FBQztFQUM3QixJQUFJZSxNQUFNLENBQUNDLFFBQVEsQ0FBQ0MsUUFBUSxDQUFDLGVBQWUsQ0FBQyxFQUFFO0lBQzdDLE9BQU9qQixHQUFHO0VBQ1osQ0FBQyxNQUFNO0lBQ0wsSUFBSWdCLFFBQVE7SUFDWixJQUFJRCxNQUFNLENBQUNDLFFBQVEsQ0FBQ0UsUUFBUSxDQUFDLEdBQUcsQ0FBQyxFQUFFO01BQ2pDRixRQUFRLEdBQUksR0FBRUQsTUFBTSxDQUFDQyxRQUFTLGtDQUFpQztJQUNqRSxDQUFDLE1BQU07TUFDTEEsUUFBUSxHQUFJLEdBQUVELE1BQU0sQ0FBQ0MsUUFBUyxtQ0FBa0M7SUFDbEU7SUFDQSxPQUFPMUYsR0FBRyxDQUFDNkYsTUFBTSxDQUFDO01BQUUsR0FBR0osTUFBTTtNQUFFQztJQUFTLENBQUMsQ0FBQztFQUM1QztBQUNGO0FBRUFJLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHMUUsTUFBTSIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaXNzdWVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgaW5zcGVjdCB9ID0gcmVxdWlyZSgndXRpbCcpO1xuY29uc3QgdXJsID0gcmVxdWlyZSgndXJsJyk7XG5cbmNvbnN0IHsgUlBFcnJvciB9ID0gcmVxdWlyZSgnLi9lcnJvcnMnKTtcbmNvbnN0IGdldENsaWVudCA9IHJlcXVpcmUoJy4vY2xpZW50Jyk7XG5jb25zdCByZWdpc3RyeSA9IHJlcXVpcmUoJy4vaXNzdWVyX3JlZ2lzdHJ5Jyk7XG5jb25zdCBwcm9jZXNzUmVzcG9uc2UgPSByZXF1aXJlKCcuL2hlbHBlcnMvcHJvY2Vzc19yZXNwb25zZScpO1xuY29uc3Qgd2ViZmluZ2VyTm9ybWFsaXplID0gcmVxdWlyZSgnLi9oZWxwZXJzL3dlYmZpbmdlcl9ub3JtYWxpemUnKTtcbmNvbnN0IHJlcXVlc3QgPSByZXF1aXJlKCcuL2hlbHBlcnMvcmVxdWVzdCcpO1xuY29uc3QgY2xvbmUgPSByZXF1aXJlKCcuL2hlbHBlcnMvZGVlcF9jbG9uZScpO1xuY29uc3QgeyBrZXlzdG9yZSB9ID0gcmVxdWlyZSgnLi9oZWxwZXJzL2lzc3VlcicpO1xuXG5jb25zdCBBQURfTVVMVElURU5BTlRfRElTQ09WRVJZID0gW1xuICAnaHR0cHM6Ly9sb2dpbi5taWNyb3NvZnRvbmxpbmUuY29tL2NvbW1vbi8ud2VsbC1rbm93bi9vcGVuaWQtY29uZmlndXJhdGlvbicsXG4gICdodHRwczovL2xvZ2luLm1pY3Jvc29mdG9ubGluZS5jb20vY29tbW9uL3YyLjAvLndlbGwta25vd24vb3BlbmlkLWNvbmZpZ3VyYXRpb24nLFxuICAnaHR0cHM6Ly9sb2dpbi5taWNyb3NvZnRvbmxpbmUuY29tL29yZ2FuaXphdGlvbnMvdjIuMC8ud2VsbC1rbm93bi9vcGVuaWQtY29uZmlndXJhdGlvbicsXG4gICdodHRwczovL2xvZ2luLm1pY3Jvc29mdG9ubGluZS5jb20vY29uc3VtZXJzL3YyLjAvLndlbGwta25vd24vb3BlbmlkLWNvbmZpZ3VyYXRpb24nLFxuXTtcbmNvbnN0IEFBRF9NVUxUSVRFTkFOVCA9IFN5bWJvbCgpO1xuY29uc3QgSVNTVUVSX0RFRkFVTFRTID0ge1xuICBjbGFpbV90eXBlc19zdXBwb3J0ZWQ6IFsnbm9ybWFsJ10sXG4gIGNsYWltc19wYXJhbWV0ZXJfc3VwcG9ydGVkOiBmYWxzZSxcbiAgZ3JhbnRfdHlwZXNfc3VwcG9ydGVkOiBbJ2F1dGhvcml6YXRpb25fY29kZScsICdpbXBsaWNpdCddLFxuICByZXF1ZXN0X3BhcmFtZXRlcl9zdXBwb3J0ZWQ6IGZhbHNlLFxuICByZXF1ZXN0X3VyaV9wYXJhbWV0ZXJfc3VwcG9ydGVkOiB0cnVlLFxuICByZXF1aXJlX3JlcXVlc3RfdXJpX3JlZ2lzdHJhdGlvbjogZmFsc2UsXG4gIHJlc3BvbnNlX21vZGVzX3N1cHBvcnRlZDogWydxdWVyeScsICdmcmFnbWVudCddLFxuICB0b2tlbl9lbmRwb2ludF9hdXRoX21ldGhvZHNfc3VwcG9ydGVkOiBbJ2NsaWVudF9zZWNyZXRfYmFzaWMnXSxcbn07XG5cbmNsYXNzIElzc3VlciB7XG4gICNtZXRhZGF0YTtcbiAgY29uc3RydWN0b3IobWV0YSA9IHt9KSB7XG4gICAgY29uc3QgYWFkSXNzVmFsaWRhdGlvbiA9IG1ldGFbQUFEX01VTFRJVEVOQU5UXTtcbiAgICBkZWxldGUgbWV0YVtBQURfTVVMVElURU5BTlRdO1xuICAgIFsnaW50cm9zcGVjdGlvbicsICdyZXZvY2F0aW9uJ10uZm9yRWFjaCgoZW5kcG9pbnQpID0+IHtcbiAgICAgIC8vIGlmIGludHJvL3Jldm9jYXRpb24gZW5kcG9pbnQgYXV0aCBzcGVjaWZpYyBtZXRhIGlzIG1pc3NpbmcgdXNlIHRoZSB0b2tlbiBvbmVzIGlmIHRoZXlcbiAgICAgIC8vIGFyZSBkZWZpbmVkXG4gICAgICBpZiAoXG4gICAgICAgIG1ldGFbYCR7ZW5kcG9pbnR9X2VuZHBvaW50YF0gJiZcbiAgICAgICAgbWV0YVtgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9tZXRob2RzX3N1cHBvcnRlZGBdID09PSB1bmRlZmluZWQgJiZcbiAgICAgICAgbWV0YVtgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9zaWduaW5nX2FsZ192YWx1ZXNfc3VwcG9ydGVkYF0gPT09IHVuZGVmaW5lZFxuICAgICAgKSB7XG4gICAgICAgIGlmIChtZXRhLnRva2VuX2VuZHBvaW50X2F1dGhfbWV0aG9kc19zdXBwb3J0ZWQpIHtcbiAgICAgICAgICBtZXRhW2Ake2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX21ldGhvZHNfc3VwcG9ydGVkYF0gPVxuICAgICAgICAgICAgbWV0YS50b2tlbl9lbmRwb2ludF9hdXRoX21ldGhvZHNfc3VwcG9ydGVkO1xuICAgICAgICB9XG4gICAgICAgIGlmIChtZXRhLnRva2VuX2VuZHBvaW50X2F1dGhfc2lnbmluZ19hbGdfdmFsdWVzX3N1cHBvcnRlZCkge1xuICAgICAgICAgIG1ldGFbYCR7ZW5kcG9pbnR9X2VuZHBvaW50X2F1dGhfc2lnbmluZ19hbGdfdmFsdWVzX3N1cHBvcnRlZGBdID1cbiAgICAgICAgICAgIG1ldGEudG9rZW5fZW5kcG9pbnRfYXV0aF9zaWduaW5nX2FsZ192YWx1ZXNfc3VwcG9ydGVkO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICB0aGlzLiNtZXRhZGF0YSA9IG5ldyBNYXAoKTtcblxuICAgIE9iamVjdC5lbnRyaWVzKG1ldGEpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4ge1xuICAgICAgdGhpcy4jbWV0YWRhdGEuc2V0KGtleSwgdmFsdWUpO1xuICAgICAgaWYgKCF0aGlzW2tleV0pIHtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIGtleSwge1xuICAgICAgICAgIGdldCgpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLiNtZXRhZGF0YS5nZXQoa2V5KTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgcmVnaXN0cnkuc2V0KHRoaXMuaXNzdWVyLCB0aGlzKTtcblxuICAgIGNvbnN0IENsaWVudCA9IGdldENsaWVudCh0aGlzLCBhYWRJc3NWYWxpZGF0aW9uKTtcblxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHRoaXMsIHtcbiAgICAgIENsaWVudDogeyB2YWx1ZTogQ2xpZW50LCBlbnVtZXJhYmxlOiB0cnVlIH0sXG4gICAgICBGQVBJMUNsaWVudDogeyB2YWx1ZTogY2xhc3MgRkFQSTFDbGllbnQgZXh0ZW5kcyBDbGllbnQge30sIGVudW1lcmFibGU6IHRydWUgfSxcbiAgICAgIEZBUEkyQ2xpZW50OiB7IHZhbHVlOiBjbGFzcyBGQVBJMkNsaWVudCBleHRlbmRzIENsaWVudCB7fSwgZW51bWVyYWJsZTogdHJ1ZSB9LFxuICAgIH0pO1xuICB9XG5cbiAgZ2V0IG1ldGFkYXRhKCkge1xuICAgIHJldHVybiBjbG9uZShPYmplY3QuZnJvbUVudHJpZXModGhpcy4jbWV0YWRhdGEuZW50cmllcygpKSk7XG4gIH1cblxuICBzdGF0aWMgYXN5bmMgd2ViZmluZ2VyKGlucHV0KSB7XG4gICAgY29uc3QgcmVzb3VyY2UgPSB3ZWJmaW5nZXJOb3JtYWxpemUoaW5wdXQpO1xuICAgIGNvbnN0IHsgaG9zdCB9ID0gdXJsLnBhcnNlKHJlc291cmNlKTtcbiAgICBjb25zdCB3ZWJmaW5nZXJVcmwgPSBgaHR0cHM6Ly8ke2hvc3R9Ly53ZWxsLWtub3duL3dlYmZpbmdlcmA7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlcXVlc3QuY2FsbCh0aGlzLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgdXJsOiB3ZWJmaW5nZXJVcmwsXG4gICAgICByZXNwb25zZVR5cGU6ICdqc29uJyxcbiAgICAgIHNlYXJjaFBhcmFtczogeyByZXNvdXJjZSwgcmVsOiAnaHR0cDovL29wZW5pZC5uZXQvc3BlY3MvY29ubmVjdC8xLjAvaXNzdWVyJyB9LFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBBY2NlcHQ6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgfSk7XG4gICAgY29uc3QgYm9keSA9IHByb2Nlc3NSZXNwb25zZShyZXNwb25zZSk7XG5cbiAgICBjb25zdCBsb2NhdGlvbiA9XG4gICAgICBBcnJheS5pc0FycmF5KGJvZHkubGlua3MpICYmXG4gICAgICBib2R5LmxpbmtzLmZpbmQoXG4gICAgICAgIChsaW5rKSA9PlxuICAgICAgICAgIHR5cGVvZiBsaW5rID09PSAnb2JqZWN0JyAmJlxuICAgICAgICAgIGxpbmsucmVsID09PSAnaHR0cDovL29wZW5pZC5uZXQvc3BlY3MvY29ubmVjdC8xLjAvaXNzdWVyJyAmJlxuICAgICAgICAgIGxpbmsuaHJlZixcbiAgICAgICk7XG5cbiAgICBpZiAoIWxvY2F0aW9uKSB7XG4gICAgICB0aHJvdyBuZXcgUlBFcnJvcih7XG4gICAgICAgIG1lc3NhZ2U6ICdubyBpc3N1ZXIgZm91bmQgaW4gd2ViZmluZ2VyIHJlc3BvbnNlJyxcbiAgICAgICAgYm9keSxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGlmICh0eXBlb2YgbG9jYXRpb24uaHJlZiAhPT0gJ3N0cmluZycgfHwgIWxvY2F0aW9uLmhyZWYuc3RhcnRzV2l0aCgnaHR0cHM6Ly8nKSkge1xuICAgICAgdGhyb3cgbmV3IFJQRXJyb3Ioe1xuICAgICAgICBwcmludGY6IFsnaW52YWxpZCBpc3N1ZXIgbG9jYXRpb24gJXMnLCBsb2NhdGlvbi5ocmVmXSxcbiAgICAgICAgYm9keSxcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGNvbnN0IGV4cGVjdGVkSXNzdWVyID0gbG9jYXRpb24uaHJlZjtcbiAgICBpZiAocmVnaXN0cnkuaGFzKGV4cGVjdGVkSXNzdWVyKSkge1xuICAgICAgcmV0dXJuIHJlZ2lzdHJ5LmdldChleHBlY3RlZElzc3Vlcik7XG4gICAgfVxuXG4gICAgY29uc3QgaXNzdWVyID0gYXdhaXQgdGhpcy5kaXNjb3ZlcihleHBlY3RlZElzc3Vlcik7XG5cbiAgICBpZiAoaXNzdWVyLmlzc3VlciAhPT0gZXhwZWN0ZWRJc3N1ZXIpIHtcbiAgICAgIHJlZ2lzdHJ5LmRlbChpc3N1ZXIuaXNzdWVyKTtcbiAgICAgIHRocm93IG5ldyBSUEVycm9yKFxuICAgICAgICAnZGlzY292ZXJlZCBpc3N1ZXIgbWlzbWF0Y2gsIGV4cGVjdGVkICVzLCBnb3Q6ICVzJyxcbiAgICAgICAgZXhwZWN0ZWRJc3N1ZXIsXG4gICAgICAgIGlzc3Vlci5pc3N1ZXIsXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gaXNzdWVyO1xuICB9XG5cbiAgc3RhdGljIGFzeW5jIGRpc2NvdmVyKHVyaSkge1xuICAgIGNvbnN0IHdlbGxLbm93blVyaSA9IHJlc29sdmVXZWxsS25vd25VcmkodXJpKTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdC5jYWxsKHRoaXMsIHtcbiAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICByZXNwb25zZVR5cGU6ICdqc29uJyxcbiAgICAgIHVybDogd2VsbEtub3duVXJpLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBBY2NlcHQ6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgfSk7XG4gICAgY29uc3QgYm9keSA9IHByb2Nlc3NSZXNwb25zZShyZXNwb25zZSk7XG4gICAgcmV0dXJuIG5ldyBJc3N1ZXIoe1xuICAgICAgLi4uSVNTVUVSX0RFRkFVTFRTLFxuICAgICAgLi4uYm9keSxcbiAgICAgIFtBQURfTVVMVElURU5BTlRdOiAhIUFBRF9NVUxUSVRFTkFOVF9ESVNDT1ZFUlkuZmluZCgoZGlzY292ZXJ5VVJMKSA9PlxuICAgICAgICB3ZWxsS25vd25Vcmkuc3RhcnRzV2l0aChkaXNjb3ZlcnlVUkwpLFxuICAgICAgKSxcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIHJlbG9hZEp3a3NVcmkoKSB7XG4gICAgYXdhaXQga2V5c3RvcmUuY2FsbCh0aGlzLCB0cnVlKTtcbiAgfVxuXG4gIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovXG4gIFtpbnNwZWN0LmN1c3RvbV0oKSB7XG4gICAgcmV0dXJuIGAke3RoaXMuY29uc3RydWN0b3IubmFtZX0gJHtpbnNwZWN0KHRoaXMubWV0YWRhdGEsIHtcbiAgICAgIGRlcHRoOiBJbmZpbml0eSxcbiAgICAgIGNvbG9yczogcHJvY2Vzcy5zdGRvdXQuaXNUVFksXG4gICAgICBjb21wYWN0OiBmYWxzZSxcbiAgICAgIHNvcnRlZDogdHJ1ZSxcbiAgICB9KX1gO1xuICB9XG59XG5cbmZ1bmN0aW9uIHJlc29sdmVXZWxsS25vd25VcmkodXJpKSB7XG4gIGNvbnN0IHBhcnNlZCA9IHVybC5wYXJzZSh1cmkpO1xuICBpZiAocGFyc2VkLnBhdGhuYW1lLmluY2x1ZGVzKCcvLndlbGwta25vd24vJykpIHtcbiAgICByZXR1cm4gdXJpO1xuICB9IGVsc2Uge1xuICAgIGxldCBwYXRobmFtZTtcbiAgICBpZiAocGFyc2VkLnBhdGhuYW1lLmVuZHNXaXRoKCcvJykpIHtcbiAgICAgIHBhdGhuYW1lID0gYCR7cGFyc2VkLnBhdGhuYW1lfS53ZWxsLWtub3duL29wZW5pZC1jb25maWd1cmF0aW9uYDtcbiAgICB9IGVsc2Uge1xuICAgICAgcGF0aG5hbWUgPSBgJHtwYXJzZWQucGF0aG5hbWV9Ly53ZWxsLWtub3duL29wZW5pZC1jb25maWd1cmF0aW9uYDtcbiAgICB9XG4gICAgcmV0dXJuIHVybC5mb3JtYXQoeyAuLi5wYXJzZWQsIHBhdGhuYW1lIH0pO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gSXNzdWVyO1xuIl0sIm5hbWVzIjpbImluc3BlY3QiLCJyZXF1aXJlIiwidXJsIiwiUlBFcnJvciIsImdldENsaWVudCIsInJlZ2lzdHJ5IiwicHJvY2Vzc1Jlc3BvbnNlIiwid2ViZmluZ2VyTm9ybWFsaXplIiwicmVxdWVzdCIsImNsb25lIiwia2V5c3RvcmUiLCJBQURfTVVMVElURU5BTlRfRElTQ09WRVJZIiwiQUFEX01VTFRJVEVOQU5UIiwiU3ltYm9sIiwiSVNTVUVSX0RFRkFVTFRTIiwiY2xhaW1fdHlwZXNfc3VwcG9ydGVkIiwiY2xhaW1zX3BhcmFtZXRlcl9zdXBwb3J0ZWQiLCJncmFudF90eXBlc19zdXBwb3J0ZWQiLCJyZXF1ZXN0X3BhcmFtZXRlcl9zdXBwb3J0ZWQiLCJyZXF1ZXN0X3VyaV9wYXJhbWV0ZXJfc3VwcG9ydGVkIiwicmVxdWlyZV9yZXF1ZXN0X3VyaV9yZWdpc3RyYXRpb24iLCJyZXNwb25zZV9tb2Rlc19zdXBwb3J0ZWQiLCJ0b2tlbl9lbmRwb2ludF9hdXRoX21ldGhvZHNfc3VwcG9ydGVkIiwiSXNzdWVyIiwibWV0YWRhdGEiLCJjb25zdHJ1Y3RvciIsIm1ldGEiLCJhYWRJc3NWYWxpZGF0aW9uIiwiZm9yRWFjaCIsImVuZHBvaW50IiwidW5kZWZpbmVkIiwidG9rZW5fZW5kcG9pbnRfYXV0aF9zaWduaW5nX2FsZ192YWx1ZXNfc3VwcG9ydGVkIiwiTWFwIiwiT2JqZWN0IiwiZW50cmllcyIsImtleSIsInZhbHVlIiwic2V0IiwiZGVmaW5lUHJvcGVydHkiLCJnZXQiLCJlbnVtZXJhYmxlIiwiaXNzdWVyIiwiQ2xpZW50IiwiZGVmaW5lUHJvcGVydGllcyIsIkZBUEkxQ2xpZW50IiwiRkFQSTJDbGllbnQiLCJmcm9tRW50cmllcyIsIndlYmZpbmdlciIsImlucHV0IiwicmVzb3VyY2UiLCJob3N0IiwicGFyc2UiLCJ3ZWJmaW5nZXJVcmwiLCJyZXNwb25zZSIsImNhbGwiLCJtZXRob2QiLCJyZXNwb25zZVR5cGUiLCJzZWFyY2hQYXJhbXMiLCJyZWwiLCJoZWFkZXJzIiwiQWNjZXB0IiwiYm9keSIsImxvY2F0aW9uIiwiQXJyYXkiLCJpc0FycmF5IiwibGlua3MiLCJmaW5kIiwibGluayIsImhyZWYiLCJtZXNzYWdlIiwic3RhcnRzV2l0aCIsInByaW50ZiIsImV4cGVjdGVkSXNzdWVyIiwiaGFzIiwiZGlzY292ZXIiLCJkZWwiLCJ1cmkiLCJ3ZWxsS25vd25VcmkiLCJyZXNvbHZlV2VsbEtub3duVXJpIiwiZGlzY292ZXJ5VVJMIiwicmVsb2FkSndrc1VyaSIsImN1c3RvbSIsIm5hbWUiLCJkZXB0aCIsIkluZmluaXR5IiwiY29sb3JzIiwicHJvY2VzcyIsInN0ZG91dCIsImlzVFRZIiwiY29tcGFjdCIsInNvcnRlZCIsInBhcnNlZCIsInBhdGhuYW1lIiwiaW5jbHVkZXMiLCJlbmRzV2l0aCIsImZvcm1hdCIsIm1vZHVsZSIsImV4cG9ydHMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\nmodule.exports = new LRU({\n  max: 100\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiI7O0FBQUEsTUFBTUEsR0FBRyxHQUFHQyxtQkFBTyxDQUFDLDBEQUFXLENBQUM7QUFFaENDLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHLElBQUlILEdBQUcsQ0FBQztFQUFFSSxHQUFHLEVBQUU7QUFBSSxDQUFDLENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGlzc3Vlcl9yZWdpc3RyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBMUlUgPSByZXF1aXJlKCdscnUtY2FjaGUnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBuZXcgTFJVKHsgbWF4OiAxMDAgfSk7XG4iXSwibmFtZXMiOlsiTFJVIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJtYXgiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst url = __webpack_require__(/*! url */ \"url\");\nconst {\n  format\n} = __webpack_require__(/*! util */ \"util\");\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst {\n  RPError,\n  OPError\n} = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst {\n  BaseClient\n} = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst {\n  random,\n  codeChallenge\n} = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst {\n  resolveResponseType,\n  resolveRedirectUri\n} = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nfunction verified(err, user, info = {}) {\n  if (err) {\n    this.error(err);\n  } else if (!user) {\n    this.fail(info);\n  } else {\n    this.success(user, info);\n  }\n}\nfunction OpenIDConnectStrategy({\n  client,\n  params = {},\n  passReqToCallback = false,\n  sessionKey,\n  usePKCE = true,\n  extras = {}\n} = {}, verify) {\n  if (!(client instanceof BaseClient)) {\n    throw new TypeError('client must be an instance of openid-client Client');\n  }\n  if (typeof verify !== 'function') {\n    throw new TypeError('verify callback must be a function');\n  }\n  if (!client.issuer || !client.issuer.issuer) {\n    throw new TypeError('client must have an issuer with an identifier');\n  }\n  this._client = client;\n  this._issuer = client.issuer;\n  this._verify = verify;\n  this._passReqToCallback = passReqToCallback;\n  this._usePKCE = usePKCE;\n  this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n  this._params = cloneDeep(params);\n\n  // state and nonce are handled in authenticate()\n  delete this._params.state;\n  delete this._params.nonce;\n  this._extras = cloneDeep(extras);\n  if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n  if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n  if (!this._params.scope) this._params.scope = 'openid';\n  if (this._usePKCE === true) {\n    const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported) ? this._issuer.code_challenge_methods_supported : false;\n    if (supportedMethods && supportedMethods.includes('S256')) {\n      this._usePKCE = 'S256';\n    } else if (supportedMethods && supportedMethods.includes('plain')) {\n      this._usePKCE = 'plain';\n    } else if (supportedMethods) {\n      throw new TypeError('neither code_challenge_method supported by the client is supported by the issuer');\n    } else {\n      this._usePKCE = 'S256';\n    }\n  } else if (typeof this._usePKCE === 'string' && !['plain', 'S256'].includes(this._usePKCE)) {\n    throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n  }\n  this.name = url.parse(client.issuer.issuer).hostname;\n}\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n  (async () => {\n    const client = this._client;\n    if (!req.session) {\n      throw new TypeError('authentication requires session support');\n    }\n    const reqParams = client.callbackParams(req);\n    const sessionKey = this._key;\n    const {\n      0: parameter,\n      length\n    } = Object.keys(reqParams);\n\n    /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */\n    if (length === 0 || length === 1 && parameter === 'iss') {\n      // provide options object with extra authentication parameters\n      const params = {\n        state: random(),\n        ...this._params,\n        ...options\n      };\n      if (!params.nonce && params.response_type.includes('id_token')) {\n        params.nonce = random();\n      }\n      req.session[sessionKey] = pick(params, 'nonce', 'state', 'max_age', 'response_type');\n      if (this._usePKCE && params.response_type.includes('code')) {\n        const verifier = random();\n        req.session[sessionKey].code_verifier = verifier;\n        switch (this._usePKCE) {\n          case 'S256':\n            params.code_challenge = codeChallenge(verifier);\n            params.code_challenge_method = 'S256';\n            break;\n          case 'plain':\n            params.code_challenge = verifier;\n            break;\n        }\n      }\n      this.redirect(client.authorizationUrl(params));\n      return;\n    }\n    /* end authentication request */\n\n    /* start authentication response */\n\n    const session = req.session[sessionKey];\n    if (Object.keys(session || {}).length === 0) {\n      throw new Error(format('did not find expected authorization request details in session, req.session[\"%s\"] is %j', sessionKey, session));\n    }\n    const {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType\n    } = session;\n    try {\n      delete req.session[sessionKey];\n    } catch (err) {}\n    const opts = {\n      redirect_uri: this._params.redirect_uri,\n      ...options\n    };\n    const checks = {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType\n    };\n    const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n    const passReq = this._passReqToCallback;\n    const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n    const args = [tokenset, verified.bind(this)];\n    if (loadUserinfo) {\n      if (!tokenset.access_token) {\n        throw new RPError({\n          message: 'expected access_token to be returned when asking for userinfo in verify callback',\n          tokenset\n        });\n      }\n      const userinfo = await client.userinfo(tokenset);\n      args.splice(1, 0, userinfo);\n    }\n    if (passReq) {\n      args.unshift(req);\n    }\n    this._verify(...args);\n    /* end authentication response */\n  })().catch(error => {\n    if (error instanceof OPError && error.error !== 'server_error' && !error.error.startsWith('invalid') || error instanceof RPError) {\n      this.fail(error);\n    } else {\n      this.error(error);\n    }\n  });\n};\nmodule.exports = OpenIDConnectStrategy;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nclass TokenSet {\n  constructor(values) {\n    Object.assign(this, values);\n    const {\n      constructor,\n      ...properties\n    } = Object.getOwnPropertyDescriptors(this.constructor.prototype);\n    Object.defineProperties(this, properties);\n  }\n  set expires_in(value) {\n    this.expires_at = now() + Number(value);\n  }\n  get expires_in() {\n    return Math.max.apply(null, [this.expires_at - now(), 0]);\n  }\n  expired() {\n    return this.expires_in === 0;\n  }\n  claims() {\n    if (!this.id_token) {\n      throw new TypeError('id_token not present in TokenSet');\n    }\n    return JSON.parse(base64url.decode(this.id_token.split('.')[1]));\n  }\n}\nmodule.exports = TokenSet;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;