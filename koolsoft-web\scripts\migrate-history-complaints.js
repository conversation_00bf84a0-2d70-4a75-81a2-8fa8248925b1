/**
 * <PERSON><PERSON>t to migrate complaint data from legacy History_Complaint table to modern history_complaints table
 * 
 * This script addresses the issue where the complaint data wasn't properly migrated during the initial migration.
 */

const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

async function migrateHistoryComplaints() {
  try {
    console.log('Starting migration of history complaints...');

    // Check if the History_Complaint table exists
    try {
      // Get legacy complaints
      const legacyComplaints = await prisma.$queryRawUnsafe(`SELECT * FROM "History_Complaint"`);
      console.log(`Found ${legacyComplaints.length} legacy complaints to migrate`);

      // Check if complaints already migrated
      const existingComplaints = await prisma.$queryRawUnsafe(`
        SELECT COUNT(*) as count FROM "history_complaints"
      `);
      
      if (existingComplaints[0].count > 0) {
        console.log(`Found ${existingComplaints[0].count} existing complaints. This script will add new records, not replace existing ones.`);
      }

      // Get mapping of original card numbers to new history card IDs
      const historyCardMapping = await prisma.$queryRaw`
        SELECT "id", "original_id" FROM "history_cards"
      `;

      const cardMap = {};
      historyCardMapping.forEach(card => {
        cardMap[card.original_id] = card.id;
      });

      let migratedCount = 0;
      let skippedCount = 0;

      // Migrate each complaint
      for (const complaint of legacyComplaints) {
        // Get the history card ID for this complaint
        const historyCardId = cardMap[complaint.Card_No];
        
        if (!historyCardId) {
          console.log(`Skipping complaint for card ${complaint.Card_No} - history card not found`);
          skippedCount++;
          continue;
        }

        // Create complaint data
        const complaintData = {
          historyCardId: historyCardId,
          complaintDate: complaint.Calldate ? new Date(complaint.Calldate).toISOString() : null,
          description: complaint.Nat_Comp || 'No description',
          complaintType: complaint.Action || null,
          resolution: null, // No direct field in legacy table
          resolutionDate: complaint.Visitdate ? new Date(complaint.Visitdate).toISOString() : null,
          originalCardNo: complaint.Card_No,
          originalComplaintId: complaint.CompID || null,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Generate a UUID for the complaint
        const complaintId = await prisma.$queryRawUnsafe(`SELECT gen_random_uuid() as id`).then(result => result[0].id);

        // Insert the complaint
        await prisma.$executeRawUnsafe(`
          INSERT INTO "history_complaints" (
            "id", "history_card_id", "complaint_date", "description", "complaint_type", 
            "resolution", "resolution_date", "original_card_no", "original_complaint_id", 
            "created_at", "updated_at"
          ) VALUES (
            '${complaintId}',
            '${complaintData.historyCardId}',
            ${complaintData.complaintDate ? `'${complaintData.complaintDate}'` : 'NULL'},
            '${complaintData.description.replace(/'/g, "''")}',
            ${complaintData.complaintType ? `'${complaintData.complaintType.replace(/'/g, "''")}'` : 'NULL'},
            NULL,
            ${complaintData.resolutionDate ? `'${complaintData.resolutionDate}'` : 'NULL'},
            ${complaintData.originalCardNo},
            ${complaintData.originalComplaintId ? complaintData.originalComplaintId : 'NULL'},
            '${complaintData.createdAt.toISOString()}',
            '${complaintData.updatedAt.toISOString()}'
          )
        `);

        migratedCount++;
        if (migratedCount % 100 === 0) {
          console.log(`Migrated ${migratedCount} complaints so far...`);
        }
      }

      console.log(`Migration completed: ${migratedCount} complaints migrated, ${skippedCount} skipped`);
    } catch (error) {
      console.error('Error accessing History_Complaint table:', error);
      console.error(error.stack);
    }
  } catch (error) {
    console.error('Error migrating history complaints:', error);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateHistoryComplaints();
