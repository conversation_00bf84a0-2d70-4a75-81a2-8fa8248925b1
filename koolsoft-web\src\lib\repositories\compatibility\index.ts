/**
 * Repository Compatibility Layer
 *
 * This file provides a compatibility layer between the legacy JavaScript repositories
 * and the modern TypeScript repositories. It allows for a gradual migration from the
 * legacy repositories to the modern repositories without breaking existing code.
 */

import { getRepositoryFactory } from '../repository.factory';
import { getCustomerRepository } from '../index';
import { getAMCContractRepository } from '../index';
import { getAMCMachineRepository } from '../index';

/**
 * Legacy-compatible Customer Repository
 *
 * This object provides the same API as the legacy CustomerRepository,
 * but uses the modern repository implementation internally.
 */
export const LegacyCustomerRepository = {
  /**
   * Get all customers from the modern table
   * @param options - Query options
   * @returns List of customers
   */
  async getAllCustomers(options = {}) {
    const repository = getCustomerRepository();
    const {
      skip = 0,
      take = 50,
      orderBy = { name: 'asc' },
      where = {},
      includeContacts = false,
      includeVisitCards = false
    } = options;

    // Convert options to modern repository format
    const include: any = {};
    if (includeContacts) include.contacts = true;
    if (includeVisitCards) include.visitCards = true;

    return repository.findWithOptions({
      skip,
      take,
      orderBy,
      where,
      include
    });
  },

  /**
   * Get a customer by ID from the modern table
   * @param id - Customer ID
   * @param options - Query options
   * @returns Customer data
   */
  async getCustomerById(id: string, options = {}) {
    const repository = getCustomerRepository();
    const {
      includeContacts = false,
      includeVisitCards = false,
      includeAMCContracts = false,
      includeWarranties = false
    } = options;

    // Convert options to modern repository format
    const include: any = {};
    if (includeContacts) include.contacts = true;
    if (includeVisitCards) include.visitCards = true;
    if (includeAMCContracts) include.amcContracts = true;
    if (includeWarranties) include.warranties = true;

    return repository.findById(id, { include });
  },

  /**
   * Get a customer by original ID from the modern table
   * @param originalId - Original customer ID from legacy table
   * @param options - Query options
   * @returns Customer data
   */
  async getCustomerByOriginalId(originalId: number, options = {}) {
    const repository = getCustomerRepository();
    const {
      includeContacts = false,
      includeVisitCards = false,
      includeAMCContracts = false,
      includeWarranties = false
    } = options;

    // Convert options to modern repository format
    const include: any = {};
    if (includeContacts) include.contacts = true;
    if (includeVisitCards) include.visitCards = true;
    if (includeAMCContracts) include.amcContracts = true;
    if (includeWarranties) include.warranties = true;

    return repository.findByOriginalId(originalId, { include });
  },

  /**
   * Get a customer from the legacy table
   * @param id - Legacy customer ID
   * @returns Legacy customer data
   */
  async getLegacyCustomer(id: number) {
    const repository = getCustomerRepository();
    return repository.findLegacyById(id);
  },

  /**
   * Create a new customer in both modern and legacy tables
   * @param customerData - Customer data
   * @returns Created customer
   */
  async createCustomer(customerData: any) {
    const repository = getCustomerRepository();
    return repository.createWithLegacy(customerData);
  },

  /**
   * Update a customer in both modern and legacy tables
   * @param id - Modern customer ID
   * @param customerData - Updated customer data
   * @returns Updated customer
   */
  async updateCustomer(id: string, customerData: any) {
    const repository = getCustomerRepository();
    return repository.updateWithLegacy(id, customerData);
  }
};

/**
 * Legacy-compatible AMC Contract Repository
 *
 * This object provides the same API as the legacy AMCContractRepository,
 * but uses the modern repository implementation internally.
 */
export const LegacyAMCContractRepository = {
  /**
   * Get all AMC contracts
   * @param options - Query options
   * @returns List of AMC contracts
   */
  async getAllAMCContracts(options = {}) {
    const repository = getAMCContractRepository();
    // Convert options to modern repository format
    return repository.findAll(options);
  },

  /**
   * Get an AMC contract by ID
   * @param id - AMC contract ID
   * @param options - Query options
   * @returns AMC contract data
   */
  async getAMCContractById(id: string, options = {}) {
    const repository = getAMCContractRepository();
    // Convert options to modern repository format
    return repository.findById(id, options);
  },

  /**
   * Create a new AMC contract
   * @param contractData - AMC contract data
   * @returns Created AMC contract
   */
  async createAMCContract(contractData: any) {
    const repository = getAMCContractRepository();
    return repository.create(contractData);
  },

  /**
   * Update an AMC contract
   * @param id - AMC contract ID
   * @param contractData - Updated AMC contract data
   * @returns Updated AMC contract
   */
  async updateAMCContract(id: string, contractData: any) {
    const repository = getAMCContractRepository();
    return repository.update(id, contractData);
  }
};

/**
 * Legacy-compatible Machine Repository
 *
 * This object provides the same API as the legacy MachineRepository,
 * but uses the modern repository implementation internally.
 */
export const LegacyMachineRepository = {
  /**
   * Get all AMC machines
   * @param options - Query options
   * @returns List of AMC machines
   */
  async getAllAMCMachines(options = {}) {
    const repository = getAMCMachineRepository();
    // Convert options to modern repository format
    return repository.findAll(options);
  },

  /**
   * Create a new AMC machine
   * @param machineData - AMC machine data
   * @returns Created AMC machine
   */
  async createAMCMachine(machineData: any) {
    const repository = getAMCMachineRepository();
    return repository.create(machineData);
  }
};

// Export other compatibility repositories as needed
export const LegacyComponentRepository = {
  // Implement compatibility methods
};

export const LegacyWarrantyRepository = {
  // Implement compatibility methods
};
