'use client';

import React from 'react';
import { Check, User, FileText, Settings, Calendar, CreditCard, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AMCFormStep, useAMCForm } from '@/contexts/amc-form-context';

// Define step configuration
const stepConfig = [
  {
    step: AMCFormStep.CUSTOMER_DETAILS,
    title: 'Customer Details',
    description: 'Select customer and basic information',
    icon: User,
  },
  {
    step: AMCFormStep.CONTRACT_DETAILS,
    title: 'Contract Details',
    description: 'Contract dates, amount, and terms',
    icon: FileText,
  },
  {
    step: AMCFormStep.MACHINE_MANAGEMENT,
    title: 'Machine Management',
    description: 'Add machines covered under AMC',
    icon: Settings,
  },
  {
    step: AMCFormStep.SERVICE_SCHEDULING,
    title: 'Service Scheduling',
    description: 'Schedule service dates',
    icon: Calendar,
  },
  {
    step: AMCFormStep.PAYMENT_DIVISION,
    title: 'Payment & Division',
    description: 'Payment terms and division assignment',
    icon: CreditCard,
  },
  {
    step: AMCFormStep.REVIEW_SUBMIT,
    title: 'Review & Submit',
    description: 'Review all details and submit',
    icon: Eye,
  },
];

interface AMCFormStepperProps {
  className?: string;
}

export function AMCFormStepper({ className }: AMCFormStepperProps) {
  const { state, goToStep, canProceedToStep } = useAMCForm();

  const getStepStatus = (step: AMCFormStep) => {
    if (step < state.currentStep) {
      return state.stepValidation[step] ? 'completed' : 'error';
    } else if (step === state.currentStep) {
      return 'current';
    } else {
      return canProceedToStep(step) ? 'upcoming' : 'disabled';
    }
  };

  const handleStepClick = (step: AMCFormStep) => {
    const status = getStepStatus(step);
    if (status !== 'disabled') {
      goToStep(step);
    }
  };

  return (
    <div className={cn('w-full', className)}>
      <nav aria-label="Progress">
        <ol className="space-y-4 md:flex md:space-y-0 md:space-x-8">
          {stepConfig.map((config, index) => {
            const status = getStepStatus(config.step);
            const Icon = config.icon;
            
            return (
              <li key={config.step} className="md:flex-1">
                <button
                  onClick={() => handleStepClick(config.step)}
                  disabled={status === 'disabled'}
                  className={cn(
                    'group flex w-full flex-col border-l-4 py-2 pl-4 transition-colors md:border-l-0 md:border-t-4 md:pb-0 md:pl-0 md:pt-4',
                    {
                      'border-primary bg-primary/5 hover:bg-primary/10': status === 'current',
                      'border-green-600 bg-green-50 hover:bg-green-100': status === 'completed',
                      'border-red-600 bg-red-50 hover:bg-red-100': status === 'error',
                      'border-gray-200 hover:border-gray-300': status === 'upcoming',
                      'border-gray-200 cursor-not-allowed opacity-50': status === 'disabled',
                    }
                  )}
                >
                  <span className="flex items-center text-sm font-medium">
                    <span
                      className={cn(
                        'flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full border-2',
                        {
                          'border-primary bg-primary text-white': status === 'current',
                          'border-green-600 bg-green-600 text-white': status === 'completed',
                          'border-red-600 bg-red-600 text-white': status === 'error',
                          'border-gray-300 bg-white text-gray-500': status === 'upcoming',
                          'border-gray-200 bg-gray-100 text-gray-400': status === 'disabled',
                        }
                      )}
                    >
                      {status === 'completed' ? (
                        <Check className="h-6 w-6" />
                      ) : (
                        <Icon className="h-6 w-6" />
                      )}
                    </span>
                    <span
                      className={cn(
                        'ml-4 text-sm font-medium',
                        {
                          'text-primary': status === 'current',
                          'text-green-600': status === 'completed',
                          'text-red-600': status === 'error',
                          'text-gray-500': status === 'upcoming',
                          'text-gray-400': status === 'disabled',
                        }
                      )}
                    >
                      {config.title}
                    </span>
                  </span>
                  <span
                    className={cn(
                      'ml-14 mt-1 text-sm',
                      {
                        'text-primary/70': status === 'current',
                        'text-green-600/70': status === 'completed',
                        'text-red-600/70': status === 'error',
                        'text-gray-500': status === 'upcoming',
                        'text-gray-400': status === 'disabled',
                      }
                    )}
                  >
                    {config.description}
                  </span>
                </button>
              </li>
            );
          })}
        </ol>
      </nav>
      
      {/* Progress bar */}
      <div className="mt-6 md:mt-8">
        <div className="flex items-center">
          <div className="flex-1">
            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-primary transition-all duration-300 ease-in-out"
                style={{
                  width: `${((state.currentStep - 1) / (stepConfig.length - 1)) * 100}%`,
                }}
              />
            </div>
          </div>
          <span className="ml-4 text-sm font-medium text-gray-600">
            Step {state.currentStep} of {stepConfig.length}
          </span>
        </div>
      </div>
    </div>
  );
}
