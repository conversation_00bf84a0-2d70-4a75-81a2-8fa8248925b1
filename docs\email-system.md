# Email System Documentation

This document provides comprehensive information about the email system implemented in the KoolSoft modernization project.

## Overview

The KoolSoft email system is designed to handle all email-related functionality, including:

1. Email template management
2. Email sending
3. Email logging
4. Email preview

The system uses a modern architecture with:
- Prisma ORM for database access
- Repository pattern for data access
- React components for email templates
- Next.js API routes for backend functionality

## Database Schema

The email system uses two main tables:

### Email Templates Table

```prisma
model EmailTemplate {
  id          String       @id @default(uuid())
  name        String       @unique
  subject     String
  bodyHtml    String       @map("body_html")
  bodyText    String       @map("body_text")
  description String?
  variables   String[]
  category    String?
  isActive    Boolean      @default(true) @map("is_active")
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")
  emailLogs   EmailLog[]

  @@index([name])
  @@index([category])
  @@index([isActive])
  @@map("email_templates")
}
```

### Email Logs Table

```prisma
model EmailLog {
  id           String         @id @default(uuid())
  templateId   String?        @map("template_id")
  recipient    String
  subject      String
  bodyHtml     String?        @map("body_html")
  bodyText     String?        @map("body_text")
  cc           String[]
  bcc          String[]
  status       String
  errorMessage String?        @map("error_message")
  sentAt       DateTime?      @map("sent_at")
  createdAt    DateTime       @default(now()) @map("created_at")
  updatedAt    DateTime       @updatedAt @map("updated_at")
  template     EmailTemplate? @relation(fields: [templateId], references: [id], onDelete: SetNull)

  @@index([templateId])
  @@index([recipient])
  @@index([status])
  @@index([sentAt])
  @@map("email_logs")
}
```

## Repository Pattern

The email system uses the repository pattern for data access. The main repositories are:

1. `EmailTemplateRepository` - Handles operations on email templates
2. `EmailLogRepository` - Handles operations on email logs

These repositories extend the base `PrismaRepository` class and provide specialized methods for email-related operations.

### Example Usage

```typescript
import { getEmailTemplateRepository } from '@/lib/repositories';

// Get the email template repository
const emailTemplateRepository = getEmailTemplateRepository();

// Find a template by name
const template = await emailTemplateRepository.findByName('welcome-email');

// Create a new template
const newTemplate = await emailTemplateRepository.create({
  name: 'password-reset',
  subject: 'Reset Your Password',
  bodyHtml: '<p>Click the link to reset your password: {{resetLink}}</p>',
  bodyText: 'Click the link to reset your password: {{resetLink}}',
  description: 'Password reset email template',
  variables: ['resetLink', 'userName'],
  isActive: true,
});
```

## API Routes

The email system provides several API routes for managing email templates and sending emails:

1. `/api/email/templates` - CRUD operations for email templates
2. `/api/email/templates/[id]` - Operations on a specific template
3. `/api/email/preview` - Preview an email template with test data
4. `/api/email/send` - Send an email using a template

### Example API Requests

#### Get All Templates

```
GET /api/email/templates
```

#### Create a Template

```
POST /api/email/templates
Content-Type: application/json

{
  "name": "welcome-email",
  "subject": "Welcome to KoolSoft",
  "bodyHtml": "<p>Welcome, {{name}}!</p>",
  "bodyText": "Welcome, {{name}}!",
  "description": "Welcome email for new users",
  "variables": ["name"],
  "isActive": true
}
```

#### Preview a Template

```
POST /api/email/preview
Content-Type: application/json

{
  "templateId": "template-uuid",
  "data": {
    "name": "John Doe",
    "resetLink": "https://example.com/reset"
  }
}
```

## User Interface

The email system includes several user interface components:

1. Email Templates List - View and manage all email templates
2. Email Template Editor - Create and edit email templates
3. Email Preview - Preview how emails will look with test data

### Email Preview Interface

The email preview interface allows users to:
- Select a template from a dropdown
- Enter test data for template variables
- See a real-time preview of the email
- Switch between desktop and mobile views

## Best Practices

When working with the email system, follow these best practices:

1. **Template Variables**: Use the `{{variableName}}` syntax for template variables
2. **HTML and Text Versions**: Always provide both HTML and text versions of email content
3. **Testing**: Use the preview interface to test templates before sending
4. **Logging**: All sent emails are automatically logged for tracking
5. **Error Handling**: Implement proper error handling when sending emails

## Troubleshooting

Common issues and their solutions:

1. **Template Not Found**: Ensure the template name is correct and the template exists
2. **Variable Errors**: Check that all required variables are provided in the data object
3. **Sending Failures**: Verify SMTP settings and network connectivity
4. **Repository Errors**: Ensure the Prisma client is properly initialized

## Future Enhancements

Planned enhancements for the email system:

1. Email scheduling for automated notifications
2. Attachment support
3. Email analytics and tracking
4. Template categories and organization
5. Rich text editor for template creation
