# Prisma Schema Configuration and Troubleshooting

This document provides detailed information about the Prisma schema configuration for the KoolSoft modernization project, including common issues and their solutions.

## Overview

The KoolSoft modernization project uses Prisma ORM to interact with the PostgreSQL database. The Prisma schema is configured to map to the legacy database tables (uppercase names like "CUSTOMERS") while we gradually transition to the modern schema (lowercase names like "customers").

## Prisma Schema Structure

The Prisma schema consists of:

1. **Data Source**: Configures the database connection
2. **Generator**: Configures the Prisma Client generation
3. **Models**: Define the database tables and their relationships

```prisma
// Example of the Prisma schema structure
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

// Models go here...
```

## Legacy Models Configuration

Legacy models are mapped to uppercase table names in the database using the `@@map` attribute:

```prisma
model LegacyCustomer {
  id              Int     @id @default(0) @map("ID")
  name            String? @map("Name") @db.VarChar(50)
  address         String? @map("CustAdd") @db.VarChar(255)
  location        String? @map("Location") @db.VarChar(50)
  phone1          String? @map("CustPhone1") @db.VarChar(15)
  phone2          String? @map("CustPhone2") @db.VarChar(15)
  phone3          String? @map("CustPhone3") @db.VarChar(15)
  mobile          String? @map("MobileNo") @db.VarChar(15)
  fax             String? @map("CustFax") @db.VarChar(15)
  email           String? @map("CustEmail") @db.VarChar(50)
  birthDate       String? @map("BirthDate") @db.VarChar(50)
  anniversaryDate String? @map("AnnDate") @db.VarChar(50)
  birthYear       String? @map("BirthYear") @db.VarChar(50)
  anniversaryYear String? @map("AnnYear") @db.VarChar(50)
  segmentId       Int?    @default(0) @map("SegId")
  designation     String? @map("Desig") @db.VarChar(50)
  visitCardPath   String? @map("VisitCard") @db.VarChar(50)

  @@index([id], map: "CUSTOMERS_ID")
  @@index([segmentId], map: "CUSTOMERS_SegId")
  @@map("CUSTOMERS")
}
```

Key points:
- The model name is `LegacyCustomer` but it maps to the "CUSTOMERS" table in the database
- Each field uses `@map` to specify the actual column name in the database
- Indexes are defined with `@@index` and can also be mapped to specific index names

## Modern Models Configuration (Future Implementation)

Modern models will be mapped to lowercase table names in the database:

```prisma
model Customer {
  id              String    @id @default(uuid())
  name            String
  address         String?
  city            String?
  state           String?
  pinCode         String?   @map("pin_code")
  phone           String?
  fax             String?
  email           String?
  mobile          String?
  website         String?
  freshness       Int?      @default(0)
  lastContact     DateTime? @map("last_contact")
  isActive        Boolean   @default(true) @map("is_active")
  originalId      Int?      @map("original_id")
  // other fields...

  @@index([name], map: "customers_name_idx")
  @@index([originalId], map: "customers_original_id_idx")
  @@map("customers")
}
```

Key points:
- The model name is `Customer` and it maps to the "customers" table in the database
- Fields use camelCase in the code but map to snake_case in the database
- The `originalId` field references the ID from the legacy table

## Common Issues and Solutions

### 1. "Column does not exist" errors

**Issue**: Error messages like "The column CUSTOMERS.OriginalID does not exist in the current database"

**Solution**:
- Ensure you're using the correct field names (camelCase property names from the Prisma schema, not database column names)
- Check that the `@map` attribute correctly maps to the actual column name in the database
- Verify the column exists in the database using a database client or `prisma db pull`

```typescript
// INCORRECT
const customer = await prisma.legacyCustomer.findUnique({
  where: { OriginalID: 123 } // Error: Column doesn't exist
});

// CORRECT
const customer = await prisma.legacyCustomer.findUnique({
  where: { id: 123 } // Uses the field name from the Prisma model
});
```

### 2. "Model not found" errors

**Issue**: Error messages like "Unknown model 'Customer'"

**Solution**:
- Ensure you're using the correct model name as defined in the Prisma schema
- Regenerate the Prisma client after making changes to the schema
- Check for typos in the model name

```typescript
// INCORRECT
const customer = await prisma.customer.findUnique({
  where: { id: 123 }
});

// CORRECT
const customer = await prisma.legacyCustomer.findUnique({
  where: { id: 123 }
});
```

### 3. Type errors with ID fields

**Issue**: Type errors when working with ID fields

**Solution**:
- Pay attention to the ID field types (Int for legacy models, String for modern models)
- Convert IDs to the correct type before using them in queries

```typescript
// INCORRECT
const customerId = req.query.id; // String from URL
const customer = await prisma.legacyCustomer.findUnique({
  where: { id: customerId } // Error: Type 'string' is not assignable to type 'number'
});

// CORRECT
const customerId = parseInt(req.query.id as string);
const customer = await prisma.legacyCustomer.findUnique({
  where: { id: customerId }
});
```

### 4. Relationship issues

**Issue**: Unable to query related records

**Solution**:
- Remember that relationships between tables in the legacy schema are not automatically handled by Prisma
- You need to manually query related records

```typescript
// INCORRECT - Legacy models don't have automatic relations
const customer = await prisma.legacyCustomer.findUnique({
  where: { id: customerId },
  include: { amcContracts: true } // Error: Unknown include field
});

// CORRECT
const customer = await prisma.legacyCustomer.findUnique({
  where: { id: customerId }
});

const amcContracts = await prisma.amcContract.findMany({
  where: { customerId: customer.id }
});
```

## Updating the Prisma Schema

When you need to update the Prisma schema:

1. Edit the `schema.prisma` file
2. Regenerate the Prisma client:
   ```bash
   npx prisma generate
   ```
3. If you're adding new tables or fields to the database:
   ```bash
   npx prisma db push
   ```

## Introspecting the Database

If you need to update the Prisma schema based on the current database structure:

```bash
npx prisma db pull
```

**Note**: This will overwrite your existing schema.prisma file. It's recommended to:
1. Make a backup of your schema.prisma file
2. Run `npx prisma db pull --print` to see the changes without applying them
3. Manually merge the changes into your schema.prisma file

## Verifying the Schema

To verify that your Prisma schema correctly maps to the database:

1. Run `npx prisma validate` to check for syntax errors
2. Run `npx prisma db pull --print` to see how Prisma would introspect your database
3. Create a simple test script to query each model

## Conclusion

Following these guidelines will help you avoid common issues with the Prisma schema in the KoolSoft modernization project. Remember to always use the legacy models (e.g., `prisma.legacyCustomer`) until the full migration to the modern schema is complete.
