import { NextRequest, NextResponse } from 'next/server';
import { getHistoryDetailRepository } from '@/lib/repositories';
import { z } from 'zod';

/**
 * History detail update schema
 */
const updateHistoryDetailSchema = z.object({
  customerId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
  date: z.coerce.date().optional(),
  type: z.string().optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
  followUpDate: z.coerce.date().optional(),
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']).optional(),
});

/**
 * GET /api/history/[id]
 * Get a specific history detail by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const historyDetailRepository = getHistoryDetailRepository();

    // Get history detail with all related data
    const historyDetail = await historyDetailRepository.findWithRelations(id);

    if (!historyDetail) {
      return NextResponse.json(
        { error: 'History detail not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(historyDetail);
  } catch (error) {
    console.error('Error fetching history detail:', error);
    return NextResponse.json(
      { error: 'Failed to fetch history detail' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/history/[id]
 * Update a specific history detail
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // Validate request body
    const validatedData = updateHistoryDetailSchema.parse(body);

    const historyDetailRepository = getHistoryDetailRepository();

    // Check if history detail exists
    const existingHistoryDetail = await historyDetailRepository.findById(id);

    if (!existingHistoryDetail) {
      return NextResponse.json(
        { error: 'History detail not found' },
        { status: 404 }
      );
    }

    // Update history detail
    const updatedHistoryDetail = await historyDetailRepository.update(id, validatedData);

    return NextResponse.json(updatedHistoryDetail);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating history detail:', error);
    return NextResponse.json(
      { error: 'Failed to update history detail' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/history/[id]
 * Delete a specific history detail
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const historyDetailRepository = getHistoryDetailRepository();

    // Check if history detail exists
    const existingHistoryDetail = await historyDetailRepository.findById(id);

    if (!existingHistoryDetail) {
      return NextResponse.json(
        { error: 'History detail not found' },
        { status: 404 }
      );
    }

    // Delete history detail
    await historyDetailRepository.delete(id);

    return NextResponse.json(
      { message: 'History detail deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting history detail:', error);
    return NextResponse.json(
      { error: 'Failed to delete history detail' },
      { status: 500 }
    );
  }
}
