# Customer Name and Address Column Swap

## Issue Description

During the data migration from the legacy Microsoft Access database to the PostgreSQL database, the contents of the 'name' and 'address' columns in the customers table were inadvertently swapped. This resulted in:

- Customer names being stored in the 'address' column
- Customer addresses being stored in the 'name' column

This mismatch caused confusion in the application, as the UI would display addresses as names and vice versa.

## Solution

To fix this issue, we created a database migration that swaps the column names to match their actual content:

1. Rename 'name' to 'temp_name' (temporary column)
2. <PERSON>ame 'address' to 'name'
3. Rename 'temp_name' to 'address'

This approach preserves all data while correcting the column names to match their content.

## Implementation

### SQL Migration

The SQL migration is stored in `prisma/migrations/20250515000000_swap_customer_name_address/migration.sql`:

```sql
-- Step 1: Rename 'name' to 'temp_name'
ALTER TABLE customers RENAME COLUMN name TO temp_name;

-- Step 2: Rename 'address' to 'name'
ALTER TABLE customers RENAME COLUMN address TO name;

-- Step 3: Rename 'temp_name' to 'address'
ALTER TABLE customers RENAME COLUMN temp_name TO address;
```

### Execution Script

A JavaScript script was created to execute this migration: `scripts/swap-customer-name-address.js`

## Verification

After running the migration, we verified that:

1. The 'name' column now contains the customer names (previously in 'address')
2. The 'address' column now contains the customer addresses (previously in 'name')
3. All application functionality continues to work correctly

## Impact on Application

Since the Prisma schema and application code already use the correct field names ('name' for customer names and 'address' for addresses), no changes to the application code were required. The migration simply ensures that the database column names match their actual content.

## Execution Date

This migration was executed on May 15, 2025.

## Related Files

- `prisma/migrations/20250515000000_swap_customer_name_address/migration.sql` - SQL migration
- `scripts/swap-customer-name-address.js` - Execution script
- `prisma/schema.prisma` - Prisma schema (no changes required)
