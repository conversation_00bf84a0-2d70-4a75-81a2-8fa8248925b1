'use client';

import { useState, useEffect, useCallback } from 'react';
import { showErrorToast } from '@/lib/toast';

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  mobile?: string;
  city?: string;
  state?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CustomerFilters {
  name?: string;
  email?: string;
  phone?: string;
  city?: string;
  state?: string;
  pinCode?: string;
  location?: string;
  isActive?: string;
  segment?: string;
  createdAfter?: string;
  createdBefore?: string;
  searchAll?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface PaginationState {
  skip: number;
  take: number;
  total: number;
}

interface UseCustomersReturn {
  customers: Customer[];
  isLoading: boolean;
  error: Error | null;
  pagination: PaginationState;
  filters: CustomerFilters;
  setFilters: (filters: CustomerFilters) => void;
  setPagination: (pagination: Partial<PaginationState>) => void;
  refreshCustomers: () => Promise<void>;
  deleteCustomer: (id: string) => Promise<boolean>;
}

/**
 * Custom hook for fetching and managing customer data
 */
export function useCustomers(): UseCustomersReturn {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [pagination, setPaginationState] = useState<PaginationState>({
    skip: 0,
    take: 10,
    total: 0,
  });
  const [filters, setFiltersState] = useState<CustomerFilters>({
    sortBy: 'name',
    sortOrder: 'asc',
  });

  // Function to fetch customers
  const fetchCustomers = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('skip', pagination.skip.toString());
      params.append('take', pagination.take.toString());

      // Add all filter parameters
      if (filters.name) params.append('name', filters.name);
      if (filters.email) params.append('email', filters.email);
      if (filters.phone) params.append('phone', filters.phone);
      if (filters.city) params.append('city', filters.city);
      if (filters.state) params.append('state', filters.state);
      if (filters.pinCode) params.append('pinCode', filters.pinCode);
      if (filters.location) params.append('location', filters.location);
      if (filters.isActive) params.append('isActive', filters.isActive);
      if (filters.segment) params.append('segment', filters.segment);
      if (filters.createdAfter) params.append('createdAfter', filters.createdAfter);
      if (filters.createdBefore) params.append('createdBefore', filters.createdBefore);
      if (filters.searchAll) params.append('searchAll', filters.searchAll);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);

      // Fetch customers from API with credentials included for authentication
      const response = await fetch(`/api/customers/search?${params.toString()}`, {
        credentials: 'include', // Include credentials for authentication
      });

      if (!response.ok) {
        throw new Error('Failed to fetch customers');
      }

      const data = await response.json();
      setCustomers(data.customers);
      setPaginationState(prev => ({
        ...prev,
        total: data.meta.total,
      }));
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      showErrorToast('Error', 'Failed to fetch customers');
    } finally {
      setIsLoading(false);
    }
  }, [pagination.skip, pagination.take, filters]);

  // Function to delete a customer
  const deleteCustomer = async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'DELETE',
        credentials: 'include', // Include credentials for authentication
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete customer');
      }

      // Refresh the customer list
      await fetchCustomers();
      return true;
    } catch (err) {
      showErrorToast('Error', err instanceof Error ? err.message : 'Failed to delete customer');
      return false;
    }
  };

  // Set filters with callback
  const setFilters = useCallback((newFilters: CustomerFilters) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
    // Reset pagination to first page when filters change
    setPaginationState(prev => ({ ...prev, skip: 0 }));
  }, []);

  // Set pagination with callback
  const setPagination = useCallback((newPagination: Partial<PaginationState>) => {
    setPaginationState(prev => ({ ...prev, ...newPagination }));
  }, []);

  // Fetch customers when dependencies change
  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  return {
    customers,
    isLoading,
    error,
    pagination,
    filters,
    setFilters,
    setPagination,
    refreshCustomers: fetchCustomers,
    deleteCustomer,
  };
}
