'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminGate } from '@/components/auth/role-gate';
import Link from 'next/link';
import { ActivityLogList } from '@/components/admin/activity-log-list';
import { ActivityLogFilterForm } from '@/components/admin/activity-log-filter-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
// Import only the client-safe methods from the activity log service
import { getActivityLogService } from '@/lib/services/activity-log.service';

/**
 * Activity Log Page
 *
 * This page is only accessible to admin users.
 * It provides functionality to view and filter activity logs:
 * - View logs with pagination, sorting, and filtering
 * - Filter by user, action, entity type, and date range
 * - Export logs to CSV
 */
export default function ActivityLogPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [logs, setLogs] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    skip: 0,
    take: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({
    userId: 'all',
    action: 'all',
    entityType: 'all',
    entityId: '',
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    search: '',
  });

  // Fetch logs on component mount and when filters/pagination change
  useEffect(() => {
    fetchLogs();
  }, [pagination.skip, pagination.take, filters]);

  // Fetch logs from the API
  const fetchLogs = async () => {
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams({
        skip: pagination.skip.toString(),
        take: pagination.take.toString(),
      });

      if (filters.userId && filters.userId !== 'all') {
        queryParams.append('userId', filters.userId);
      }

      if (filters.action && filters.action !== 'all') {
        queryParams.append('action', filters.action);
      }

      if (filters.entityType && filters.entityType !== 'all') {
        queryParams.append('entityType', filters.entityType);
      }

      if (filters.entityId) {
        queryParams.append('entityId', filters.entityId);
      }

      if (filters.startDate) {
        queryParams.append('startDate', filters.startDate.toISOString());
      }

      if (filters.endDate) {
        queryParams.append('endDate', filters.endDate.toISOString());
      }

      if (filters.search) {
        queryParams.append('search', filters.search);
      }

      const response = await fetch(`/api/admin/activity-logs?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch activity logs');
      }

      const data = await response.json();
      setLogs(data.logs);
      setPagination(prev => ({
        ...prev,
        total: data.meta.total,
      }));
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch activity logs. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle filter change
  const handleFilterChange = (newFilters: any) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
    }));
    setPagination(prev => ({
      ...prev,
      skip: 0, // Reset to first page on new filter
    }));
  };

  // Handle pagination change
  const handlePaginationChange = (skip: number, take: number) => {
    setPagination(prev => ({
      ...prev,
      skip,
      take,
    }));
  };

  // Handle export to CSV
  const handleExport = async () => {
    try {
      const queryParams = new URLSearchParams();

      if (filters.userId) {
        queryParams.append('userId', filters.userId);
      }

      if (filters.action) {
        queryParams.append('action', filters.action);
      }

      if (filters.entityType) {
        queryParams.append('entityType', filters.entityType);
      }

      if (filters.entityId) {
        queryParams.append('entityId', filters.entityId);
      }

      if (filters.startDate) {
        queryParams.append('startDate', filters.startDate.toISOString());
      }

      if (filters.endDate) {
        queryParams.append('endDate', filters.endDate.toISOString());
      }

      if (filters.search) {
        queryParams.append('search', filters.search);
      }

      // Set format to CSV
      queryParams.append('format', 'csv');

      // Open in new tab
      window.open(`/api/admin/activity-logs/export?${queryParams.toString()}`, '_blank');
    } catch (error) {
      console.error('Error exporting activity logs:', error);
      toast({
        title: 'Error',
        description: 'Failed to export activity logs. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <AdminGate fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
          <p className="text-gray-500">You do not have permission to access this page</p>
          <div className="mt-4">
            <Link href="/dashboard" className="text-blue-600 hover:text-blue-500">
              Return to Dashboard
            </Link>
          </div>
        </div>
      </div>
    }>
      <div>
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div>
                  <CardTitle className="text-2xl font-bold text-black">System Activity Logs</CardTitle>
                  <CardDescription>
                    View and filter user activity logs
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={handleExport}
                    variant="outline"
                    size="sm"
                  >
                    Export to CSV
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <ActivityLogFilterForm
                  onFilterChange={handleFilterChange}
                  commonActions={getActivityLogService().getCommonActions()}
                  commonEntityTypes={getActivityLogService().getCommonEntityTypes()}
                />
                <ActivityLogList
                  logs={logs}
                  isLoading={isLoading}
                  pagination={pagination}
                  onPaginationChange={handlePaginationChange}
                  onRefresh={fetchLogs}
                />
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </AdminGate>
  );
}
