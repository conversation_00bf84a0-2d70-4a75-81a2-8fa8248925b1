'use client';

import { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  ChevronLeft, 
  ChevronRight, 
  MoreHorizontal, 
  RefreshCw,
  User,
  FileText,
  Clock,
  Globe,
  Info
} from 'lucide-react';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';

interface ActivityLogListProps {
  logs: any[];
  isLoading: boolean;
  pagination: {
    skip: number;
    take: number;
    total: number;
  };
  onPaginationChange: (skip: number, take: number) => void;
  onRefresh: () => void;
}

export function ActivityLogList({
  logs,
  isLoading,
  pagination,
  onPaginationChange,
  onRefresh
}: ActivityLogListProps) {
  const [sortField, setSortField] = useState<string | null>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Calculate pagination values
  const { skip, take, total } = pagination;
  const currentPage = Math.floor(skip / take) + 1;
  const totalPages = Math.ceil(total / take);
  const showingFrom = skip + 1;
  const showingTo = Math.min(skip + take, total);

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort indicator
  const getSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // Get action badge color
  const getActionBadgeColor = (action: string) => {
    if (action.includes('login')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    } else if (action.includes('logout')) {
      return 'bg-purple-100 text-purple-800 border-purple-200';
    } else if (action.includes('create')) {
      return 'bg-green-100 text-green-800 border-green-200';
    } else if (action.includes('update')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    } else if (action.includes('delete')) {
      return 'bg-red-100 text-red-800 border-red-200';
    } else if (action.includes('failed')) {
      return 'bg-red-100 text-red-800 border-red-200';
    } else {
      return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get entity type badge color
  const getEntityTypeBadgeColor = (entityType: string) => {
    switch (entityType) {
      case 'user':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'customer':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'amc_contract':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'auth':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'system':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format details as string
  const formatDetails = (details: any) => {
    if (!details) return '';
    
    try {
      if (typeof details === 'string') {
        return details;
      }
      
      return JSON.stringify(details, null, 2);
    } catch (error) {
      return String(details);
    }
  };

  // Sort logs
  const sortedLogs = [...logs].sort((a, b) => {
    if (!sortField) return 0;
    
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (aValue === bValue) return 0;
    
    const direction = sortDirection === 'asc' ? 1 : -1;
    
    if (aValue === null) return 1 * direction;
    if (bValue === null) return -1 * direction;
    
    return aValue < bValue ? -1 * direction : 1 * direction;
  });

  return (
    <div className="space-y-4 mt-6">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          {isLoading ? (
            'Loading activity logs...'
          ) : (
            `Showing ${showingFrom} to ${showingTo} of ${total} logs`
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          disabled={isLoading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead 
                className="cursor-pointer"
                onClick={() => handleSort('createdAt')}
              >
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  Timestamp {getSortIndicator('createdAt')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => handleSort('action')}
              >
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-1" />
                  Action {getSortIndicator('action')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => handleSort('user.name')}
              >
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-1" />
                  User {getSortIndicator('user.name')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => handleSort('entityType')}
              >
                <div className="flex items-center">
                  <Info className="h-4 w-4 mr-1" />
                  Entity Type {getSortIndicator('entityType')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => handleSort('ipAddress')}
              >
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  IP Address {getSortIndicator('ipAddress')}
                </div>
              </TableHead>
              <TableHead className="text-right">Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-10">
                  Loading activity logs...
                </TableCell>
              </TableRow>
            ) : sortedLogs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-10">
                  No activity logs found
                </TableCell>
              </TableRow>
            ) : (
              sortedLogs.map(log => (
                <TableRow key={log.id}>
                  <TableCell className="whitespace-nowrap">
                    {formatDate(log.createdAt, 'MMM dd, yyyy HH:mm:ss')}
                  </TableCell>
                  <TableCell>
                    <Badge className={`${getActionBadgeColor(log.action)}`}>
                      {log.action}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {log.user ? (
                      <Link href={`/admin/users?id=${log.userId}`} className="text-blue-600 hover:underline">
                        {log.user.name}
                      </Link>
                    ) : (
                      <span className="text-gray-500">System</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {log.entityType ? (
                      <Badge className={`${getEntityTypeBadgeColor(log.entityType)}`}>
                        {log.entityType}
                      </Badge>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {log.ipAddress || '-'}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">View details</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[300px]">
                        <DropdownMenuLabel>Log Details</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <div className="p-2 max-h-[300px] overflow-auto">
                          <pre className="text-xs whitespace-pre-wrap">
                            {formatDetails(log.details)}
                          </pre>
                        </div>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="flex-1 text-sm text-gray-500">
          Page {currentPage} of {totalPages || 1}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPaginationChange(Math.max(0, skip - take), take)}
            disabled={currentPage <= 1 || isLoading}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPaginationChange(skip + take, take)}
            disabled={currentPage >= totalPages || isLoading}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
