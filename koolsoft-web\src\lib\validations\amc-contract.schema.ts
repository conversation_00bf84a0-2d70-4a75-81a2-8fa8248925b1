import { z } from 'zod';

/**
 * Base AMC Contract schema
 */
export const amcContractBaseSchema = z.object({
  customerId: z.string().uuid({ message: 'Valid customer ID is required' }),
  contactPersonId: z.string().uuid({ message: 'Valid contact person ID is required' }).optional(),
  executiveId: z.string().uuid({ message: 'Valid executive ID is required' }).optional(),
  natureOfService: z.string().min(1, { message: 'Nature of service is required' }).optional(),
  startDate: z.coerce.date({ message: 'Valid start date is required' }),
  endDate: z.coerce.date({ message: 'Valid end date is required' }),
  warningDate: z.coerce.date({ message: 'Valid warning date is required' }).optional(),
  amount: z.number({
    required_error: 'Amount is required',
    invalid_type_error: 'Amount must be a number'
  }).nonnegative({ message: 'Amount must be a positive number' }),
  bslDebit: z.number({ invalid_type_error: 'BSL debit must be a number' }).optional(),
  previousAmount: z.number({ invalid_type_error: 'Previous amount must be a number' }).optional(),
  amcPeriod: z.number({ invalid_type_error: 'AMC period must be a number' }).optional(),
  yearOfCommencement: z.number({ invalid_type_error: 'Year of commencement must be a number' }).optional(),
  numberOfMachines: z.number({ invalid_type_error: 'Number of machines must be a number' }).int().nonnegative().optional(),
  numberOfServices: z.number({ invalid_type_error: 'Number of services must be a number' }).int().nonnegative().optional(),
  renewalFlag: z.boolean().optional(),
  blstrFlag: z.string().optional(),
  paidAmount: z.number({ invalid_type_error: 'Paid amount must be a number' }).nonnegative().optional(),
  balanceAmount: z.number({ invalid_type_error: 'Balance amount must be a number' }).optional(),
  totalTonnage: z.number({ invalid_type_error: 'Total tonnage must be a number' }).optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED', 'RENEWED'], {
    errorMap: () => ({ message: 'Status must be one of: ACTIVE, EXPIRED, PENDING, CANCELLED, RENEWED' })
  }).default('ACTIVE'),
  contractNumber: z.string().optional(),
  remarks: z.string().optional(),
  originalId: z.number().int().optional(),
  originalAmcId: z.number().int().optional(),
});

/**
 * AMC Machine schema
 */
export const amcMachineSchema = z.object({
  id: z.string().uuid().optional(),
  productId: z.string().uuid({ message: 'Valid product ID is required' }).optional(),
  modelId: z.string().uuid({ message: 'Valid model ID is required' }).optional(),
  brandId: z.string().uuid({ message: 'Valid brand ID is required' }).optional(),
  serialNumber: z.string().min(1, { message: 'Serial number is required' }).optional(),
  location: z.string().optional(),
  installationDate: z.coerce.date().optional(),
  tonnage: z.number().nonnegative().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).default('ACTIVE'),
  originalAmcId: z.number().int().optional(),
  originalAssetNo: z.number().int().optional(),
});

/**
 * AMC Service Date schema
 */
export const amcServiceDateSchema = z.object({
  id: z.string().uuid().optional(),
  scheduledDate: z.coerce.date({ message: 'Valid scheduled date is required' }),
  completedDate: z.coerce.date().optional(),
  status: z.enum(['SCHEDULED', 'COMPLETED', 'MISSED'], {
    errorMap: () => ({ message: 'Status must be one of: SCHEDULED, COMPLETED, MISSED' })
  }).default('SCHEDULED'),
  technicianId: z.string().uuid().optional(),
  remarks: z.string().optional(),
  originalId: z.number().int().optional(),
});

/**
 * AMC Payment schema
 */
export const amcPaymentSchema = z.object({
  id: z.string().uuid().optional(),
  paymentDate: z.coerce.date({ message: 'Valid payment date is required' }),
  amount: z.number({
    required_error: 'Amount is required',
    invalid_type_error: 'Amount must be a number'
  }).nonnegative({ message: 'Amount must be a positive number' }),
  paymentMode: z.enum(['CASH', 'CHEQUE', 'BANK_TRANSFER', 'ONLINE'], {
    errorMap: () => ({ message: 'Payment mode must be one of: CASH, CHEQUE, BANK_TRANSFER, ONLINE' })
  }),
  receiptNo: z.string().optional(),
  particulars: z.string().optional(),
  originalAmcId: z.number().int().optional(),
  originalReceiptNo: z.number().int().optional(),
});

/**
 * Payment creation schema for API endpoints
 */
export const createPaymentSchema = z.object({
  amcContractId: z.string().uuid({ message: 'Valid AMC contract ID is required' }),
  paymentDate: z.coerce.date({ message: 'Valid payment date is required' }),
  amount: z.number({
    required_error: 'Amount is required',
    invalid_type_error: 'Amount must be a number'
  }).positive({ message: 'Amount must be greater than 0' }),
  paymentMode: z.enum(['CASH', 'CHEQUE', 'BANK_TRANSFER', 'ONLINE'], {
    errorMap: () => ({ message: 'Payment mode must be one of: CASH, CHEQUE, BANK_TRANSFER, ONLINE' })
  }),
  receiptNo: z.string().optional(),
  particulars: z.string().optional(),
});

/**
 * Payment update schema for API endpoints
 */
export const updatePaymentSchema = createPaymentSchema.partial().omit({ amcContractId: true });

/**
 * Payment filter schema for API endpoints
 */
export const paymentFilterSchema = z.object({
  amcContractId: z.string().uuid().optional(),
  paymentMode: z.string().optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  amountMin: z.number().nonnegative().optional(),
  amountMax: z.number().nonnegative().optional(),
  skip: z.number().int().nonnegative().default(0),
  take: z.number().int().positive().max(100).default(10),
  orderBy: z.enum(['paymentDate', 'amount', 'receiptNo']).default('paymentDate'),
  orderDirection: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * AMC Division schema
 */
export const amcDivisionSchema = z.object({
  id: z.string().uuid().optional(),
  divisionId: z.string().uuid({ message: 'Valid division ID is required' }),
  percentage: z.number().min(0).max(100, { message: 'Percentage must be between 0 and 100' }),
  isPrimary: z.boolean().default(false),
  originalId: z.number().int().optional(),
});

/**
 * AMC Contract creation schema
 */
export const createAMCContractSchema = amcContractBaseSchema.extend({
  machines: z.array(amcMachineSchema).optional(),
  serviceDates: z.array(amcServiceDateSchema).optional(),
  payments: z.array(amcPaymentSchema).optional(),
  divisions: z.array(amcDivisionSchema).optional(),
}).refine(
  (data) => {
    // Ensure end date is after start date
    return data.endDate > data.startDate;
  },
  {
    message: 'End date must be after start date',
    path: ['endDate'],
  }
).refine(
  (data) => {
    // If divisions are provided, ensure percentages sum to 100
    if (data.divisions && data.divisions.length > 0) {
      const totalPercentage = data.divisions.reduce((sum, div) => sum + div.percentage, 0);
      return totalPercentage === 100;
    }
    return true;
  },
  {
    message: 'Division percentages must sum to 100%',
    path: ['divisions'],
  }
);

/**
 * AMC Contract update schema
 */
export const updateAMCContractSchema = amcContractBaseSchema
  .partial()
  .extend({
    machines: z.array(amcMachineSchema).optional(),
    serviceDates: z.array(amcServiceDateSchema).optional(),
    payments: z.array(amcPaymentSchema).optional(),
    divisions: z.array(amcDivisionSchema).optional(),
  })
  .refine(
    (data) => {
      // If both dates are provided, ensure end date is after start date
      if (data.startDate && data.endDate) {
        return data.endDate > data.startDate;
      }
      return true;
    },
    {
      message: 'End date must be after start date',
      path: ['endDate'],
    }
  )
  .refine(
    (data) => {
      // If divisions are provided, ensure percentages sum to 100
      if (data.divisions && data.divisions.length > 0) {
        const totalPercentage = data.divisions.reduce((sum, div) => sum + div.percentage, 0);
        return totalPercentage === 100;
      }
      return true;
    },
    {
      message: 'Division percentages must sum to 100%',
      path: ['divisions'],
    }
  );

/**
 * AMC Contract filter schema
 */
export const amcContractFilterSchema = z.object({
  customerId: z.string().uuid().optional().nullable(),
  executiveId: z.string().uuid().optional().nullable(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED', 'RENEWED']).optional().nullable(),
  startDateFrom: z.coerce.date().optional().nullable(),
  startDateTo: z.coerce.date().optional().nullable(),
  endDateFrom: z.coerce.date().optional().nullable(),
  endDateTo: z.coerce.date().optional().nullable(),
  search: z.string().optional().nullable(),
});
