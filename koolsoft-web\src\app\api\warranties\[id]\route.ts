import { NextRequest, NextResponse } from 'next/server';
import { getWarrantyRepository } from '@/lib/repositories';
import { updateWarrantySchema } from '@/lib/validations/warranty.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { z } from 'zod';

/**
 * GET /api/warranties/[id]
 * Get a specific warranty by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const warrantyRepository = getWarrantyRepository();

      // Get warranty with all related data
      const warranty = await warrantyRepository.findWithRelations(id);

      if (!warranty) {
        return NextResponse.json(
          { error: 'Warranty not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(warranty);
    } catch (error) {
      console.error('Error fetching warranty:', error);
      return NextResponse.json(
        { error: 'Failed to fetch warranty' },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/warranties/[id]
 * Update a specific warranty
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = updateWarrantySchema.parse({ ...body, id });

        const warrantyRepository = getWarrantyRepository();

        // Check if warranty exists
        const existingWarranty = await warrantyRepository.findById(id);
        if (!existingWarranty) {
          return NextResponse.json(
            { error: 'Warranty not found' },
            { status: 404 }
          );
        }

        // Remove id from update data
        const { id: _, ...updateData } = validatedData;

        // Update warranty
        const updatedWarranty = await warrantyRepository.update(id, updateData);

        return NextResponse.json(updatedWarranty);
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error updating warranty:', error);
      
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          return NextResponse.json(
            { error: 'A warranty with this information already exists' },
            { status: 409 }
          );
        }
        if (error.code === 'P2003') {
          return NextResponse.json(
            { error: 'Referenced customer, executive, or contact person does not exist' },
            { status: 400 }
          );
        }
        if (error.code === 'P2025') {
          return NextResponse.json(
            { error: 'Warranty not found' },
            { status: 404 }
          );
        }
      }
      
      return NextResponse.json(
        { error: 'Failed to update warranty' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/warranties/[id]
 * Delete a specific warranty
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const warrantyRepository = getWarrantyRepository();

      // Check if warranty exists
      const existingWarranty = await warrantyRepository.findById(id);
      if (!existingWarranty) {
        return NextResponse.json(
          { error: 'Warranty not found' },
          { status: 404 }
        );
      }

      // Check if warranty has related data that prevents deletion
      const warrantyWithRelations = await warrantyRepository.findWithRelations(id);
      if (warrantyWithRelations?.machines && warrantyWithRelations.machines.length > 0) {
        return NextResponse.json(
          { error: 'Cannot delete warranty with associated machines. Please remove machines first.' },
          { status: 400 }
        );
      }

      // Delete warranty
      await warrantyRepository.delete(id);

      return NextResponse.json(
        { message: 'Warranty deleted successfully' },
        { status: 200 }
      );
    } catch (error) {
      console.error('Error deleting warranty:', error);
      
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          return NextResponse.json(
            { error: 'Warranty not found' },
            { status: 404 }
          );
        }
        if (error.code === 'P2003') {
          return NextResponse.json(
            { error: 'Cannot delete warranty due to related records' },
            { status: 400 }
          );
        }
      }
      
      return NextResponse.json(
        { error: 'Failed to delete warranty' },
        { status: 500 }
      );
    }
  }
);
