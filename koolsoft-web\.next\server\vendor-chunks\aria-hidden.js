"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/aria-hidden";
exports.ids = ["vendor-chunks/aria-hidden"];
exports.modules = {

/***/ "(ssr)/./node_modules/aria-hidden/dist/es2015/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/aria-hidden/dist/es2015/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hideOthers: () => (/* binding */ hideOthers),\n/* harmony export */   inertOthers: () => (/* binding */ inertOthers),\n/* harmony export */   supportsInert: () => (/* binding */ supportsInert),\n/* harmony export */   suppressOthers: () => (/* binding */ suppressOthers)\n/* harmony export */ });\nvar getDefaultParent = function (originalTarget) {\n  if (typeof document === 'undefined') {\n    return null;\n  }\n  var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n  return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n  return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n  return targets.map(function (target) {\n    if (parent.contains(target)) {\n      return target;\n    }\n    var correctedTarget = unwrapHost(target);\n    if (correctedTarget && parent.contains(correctedTarget)) {\n      return correctedTarget;\n    }\n    console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n    return null;\n  }).filter(function (x) {\n    return Boolean(x);\n  });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n  var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n  if (!markerMap[markerName]) {\n    markerMap[markerName] = new WeakMap();\n  }\n  var markerCounter = markerMap[markerName];\n  var hiddenNodes = [];\n  var elementsToKeep = new Set();\n  var elementsToStop = new Set(targets);\n  var keep = function (el) {\n    if (!el || elementsToKeep.has(el)) {\n      return;\n    }\n    elementsToKeep.add(el);\n    keep(el.parentNode);\n  };\n  targets.forEach(keep);\n  var deep = function (parent) {\n    if (!parent || elementsToStop.has(parent)) {\n      return;\n    }\n    Array.prototype.forEach.call(parent.children, function (node) {\n      if (elementsToKeep.has(node)) {\n        deep(node);\n      } else {\n        try {\n          var attr = node.getAttribute(controlAttribute);\n          var alreadyHidden = attr !== null && attr !== 'false';\n          var counterValue = (counterMap.get(node) || 0) + 1;\n          var markerValue = (markerCounter.get(node) || 0) + 1;\n          counterMap.set(node, counterValue);\n          markerCounter.set(node, markerValue);\n          hiddenNodes.push(node);\n          if (counterValue === 1 && alreadyHidden) {\n            uncontrolledNodes.set(node, true);\n          }\n          if (markerValue === 1) {\n            node.setAttribute(markerName, 'true');\n          }\n          if (!alreadyHidden) {\n            node.setAttribute(controlAttribute, 'true');\n          }\n        } catch (e) {\n          console.error('aria-hidden: cannot operate on ', node, e);\n        }\n      }\n    });\n  };\n  deep(parentNode);\n  elementsToKeep.clear();\n  lockCount++;\n  return function () {\n    hiddenNodes.forEach(function (node) {\n      var counterValue = counterMap.get(node) - 1;\n      var markerValue = markerCounter.get(node) - 1;\n      counterMap.set(node, counterValue);\n      markerCounter.set(node, markerValue);\n      if (!counterValue) {\n        if (!uncontrolledNodes.has(node)) {\n          node.removeAttribute(controlAttribute);\n        }\n        uncontrolledNodes.delete(node);\n      }\n      if (!markerValue) {\n        node.removeAttribute(markerName);\n      }\n    });\n    lockCount--;\n    if (!lockCount) {\n      // clear\n      counterMap = new WeakMap();\n      counterMap = new WeakMap();\n      uncontrolledNodes = new WeakMap();\n      markerMap = {};\n    }\n  };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nvar hideOthers = function (originalTarget, parentNode, markerName) {\n  if (markerName === void 0) {\n    markerName = 'data-aria-hidden';\n  }\n  var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n  var activeParentNode = parentNode || getDefaultParent(originalTarget);\n  if (!activeParentNode) {\n    return function () {\n      return null;\n    };\n  }\n  // we should not hide ariaLive elements - https://github.com/theKashey/aria-hidden/issues/10\n  targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live]')));\n  return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nvar inertOthers = function (originalTarget, parentNode, markerName) {\n  if (markerName === void 0) {\n    markerName = 'data-inert-ed';\n  }\n  var activeParentNode = parentNode || getDefaultParent(originalTarget);\n  if (!activeParentNode) {\n    return function () {\n      return null;\n    };\n  }\n  return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nvar supportsInert = function () {\n  return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nvar suppressOthers = function (originalTarget, parentNode, markerName) {\n  if (markerName === void 0) {\n    markerName = 'data-suppressed';\n  }\n  return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\n");

/***/ })

};
;