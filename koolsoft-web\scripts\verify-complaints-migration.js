/**
 * <PERSON><PERSON><PERSON> to verify the migration of complaint data
 */

const { PrismaClient } = require('../src/generated/prisma');
const prisma = new PrismaClient();

async function verifyMigration() {
  try {
    console.log('Verifying complaint data migration...');

    // Check count in legacy table
    const legacyCount = await prisma.$queryRawUnsafe(`
      SELECT COUNT(*) as count FROM "History_Complaint"
    `);
    console.log(`Legacy History_Complaint table count: ${legacyCount[0].count}`);

    // Check count in modern table
    const modernCount = await prisma.$queryRawUnsafe(`
      SELECT COUNT(*) as count FROM "history_complaints"
    `);
    console.log(`Modern history_complaints table count: ${modernCount[0].count}`);

    // Check relationship integrity
    const orphanedCount = await prisma.$queryRawUnsafe(`
      SELECT COUNT(*) as count 
      FROM "history_complaints" hc
      LEFT JOIN "history_cards" h ON hc."history_card_id" = h."id"
      WHERE h."id" IS NULL
    `);
    console.log(`Orphaned complaints (no valid history card): ${orphanedCount[0].count}`);

    // Sample data from both tables
    console.log('\nSample data from legacy table:');
    const legacySample = await prisma.$queryRawUnsafe(`
      SELECT * FROM "History_Complaint" LIMIT 3
    `);
    console.log(JSON.stringify(legacySample, null, 2));

    console.log('\nSample data from modern table:');
    const modernSample = await prisma.$queryRawUnsafe(`
      SELECT * FROM "history_complaints" LIMIT 3
    `);
    console.log(JSON.stringify(modernSample, null, 2));

    console.log('\nVerification completed!');
  } catch (error) {
    console.error('Error verifying migration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the verification
verifyMigration();
