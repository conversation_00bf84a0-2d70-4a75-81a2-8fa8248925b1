'use client';

import { useAuth } from '@/lib/hooks/useAuth';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Home, ShieldCheck, LayoutDashboard } from 'lucide-react';

/**
 * Warranty Layout Component
 *
 * This component provides a consistent layout for all warranty-related pages.
 */
export default function WarrantyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isLoading, isAuthenticated, hasRole } = useAuth();
  const pathname = usePathname();
  const router = useRouter();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      const callbackUrl = encodeURIComponent(pathname);
      router.push(`/auth/login?callbackUrl=${callbackUrl}`);
    }
  }, [isLoading, isAuthenticated, pathname, router]);

  // Check if user has required role
  useEffect(() => {
    if (!isLoading && isAuthenticated && !hasRole(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'])) {
      router.push('/dashboard');
    }
  }, [isLoading, isAuthenticated, hasRole, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Loading...</h2>
          <p className="text-gray-500">Please wait while we load your dashboard</p>
        </div>
      </div>
    );
  }
  
  // If not authenticated, show nothing (will be redirected by useEffect)
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Authentication Required</h2>
          <p className="text-gray-500">Please log in to access this page</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard" className="text-gray-500 hover:text-gray-700">
              <Home className="h-5 w-5" />
            </Link>
            <span className="text-gray-400">/</span>
            <Link href="/warranty" className="text-gray-900 font-medium">
              Warranty
            </Link>
            {pathname !== '/warranty' && (
              <>
                <span className="text-gray-400">/</span>
                <span className="text-gray-700">
                  {pathname.includes('/edit') ? 'Edit Warranty' : 
                   pathname.includes('/new') ? 'New Warranty' : 'Warranty Details'}
                </span>
              </>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">Welcome, {user?.name}</span>
            <Link
              href="/dashboard"
              className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-[#0F52BA] hover:bg-[#0F52BA]/90"
            >
              <LayoutDashboard className="h-4 w-4 mr-1" />
              Dashboard
            </Link>
          </div>
        </div>
      </header>

      <main>
        {children}
      </main>
    </div>
  );
}
