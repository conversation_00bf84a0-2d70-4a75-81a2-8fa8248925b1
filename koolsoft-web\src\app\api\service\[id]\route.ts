import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getServiceReportRepository } from '@/lib/repositories';
import { updateServiceReportSchema } from '@/lib/validations/service.schema';

/**
 * GET /api/service/[id]
 * Get a specific service report by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const serviceReportRepository = getServiceReportRepository();

      // Get service report with all related data
      const serviceReport = await serviceReportRepository.findWithRelations(id);

      if (!serviceReport) {
        return NextResponse.json(
          { error: 'Service report not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ serviceReport });
    } catch (error) {
      console.error('Error fetching service report:', error);
      return NextResponse.json(
        { error: 'Failed to fetch service report' },
        { status: 500 }
      );
    }
  }
);

/**
 * PATCH /api/service/[id]
 * Update a specific service report
 */
export const PATCH = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateServiceReportSchema.parse({
        ...body,
        id,
      });

      const serviceReportRepository = getServiceReportRepository();

      // Check if service report exists
      const existingServiceReport = await serviceReportRepository.findById(id);
      if (!existingServiceReport) {
        return NextResponse.json(
          { error: 'Service report not found' },
          { status: 404 }
        );
      }

      // Extract details from the validated data
      const { details, ...serviceReportData } = validatedData;

      // Update the service report
      const updatedServiceReport = await serviceReportRepository.update(id, serviceReportData);

      // If details are provided, handle them separately
      if (details && details.length > 0) {
        // This would require additional logic to handle detail updates
        // For now, we'll just update the main service report
        console.log('Service details update not implemented in this version');
      }

      // Get the updated service report with relations
      const serviceReportWithRelations = await serviceReportRepository.findWithRelations(id);

      return NextResponse.json({
        message: 'Service report updated successfully',
        serviceReport: serviceReportWithRelations,
      });
    } catch (error) {
      console.error('Error updating service report:', error);

      if (error instanceof Error) {
        // Handle validation errors
        if (error.message.includes('validation')) {
          return NextResponse.json(
            { error: 'Validation failed', details: error.message },
            { status: 400 }
          );
        }

        // Handle database constraint errors
        if (error.message.includes('foreign key constraint')) {
          return NextResponse.json(
            { error: 'Invalid customer or executive ID' },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Failed to update service report' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/service/[id]
 * Delete a specific service report
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const serviceReportRepository = getServiceReportRepository();

      // Check if service report exists
      const existingServiceReport = await serviceReportRepository.findById(id);
      if (!existingServiceReport) {
        return NextResponse.json(
          { error: 'Service report not found' },
          { status: 404 }
        );
      }

      // Delete the service report (cascade will handle details)
      await serviceReportRepository.delete(id);

      return NextResponse.json({
        message: 'Service report deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting service report:', error);

      if (error instanceof Error) {
        // Handle foreign key constraint errors
        if (error.message.includes('foreign key constraint')) {
          return NextResponse.json(
            { error: 'Cannot delete service report: it is referenced by other records' },
            { status: 409 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Failed to delete service report' },
        { status: 500 }
      );
    }
  }
);
