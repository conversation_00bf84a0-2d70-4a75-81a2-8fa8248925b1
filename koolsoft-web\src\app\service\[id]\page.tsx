'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import {
  ArrowLeft,
  Edit,
  Trash2,
  FileText,
  User,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Wrench,
  Settings,
  Check
} from 'lucide-react';
import { format } from 'date-fns';

interface ServiceReport {
  id: string;
  reportDate: string;
  visitDate?: string;
  completionDate?: string;
  natureOfService: string;
  complaintType: string;
  actionTaken?: string;
  remarks?: string;
  status: string;
  customer: {
    id: string;
    name: string;
    city: string;
    phone?: string;
  };
  executive: {
    id: string;
    name: string;
    phone?: string;
  };
  details: Array<{
    id: string;
    machineType: string;
    serialNumber: string;
    problem: string;
    solution: string;
    partReplaced?: string;
  }>;
}

export default function ServiceReportDetailPage() {
  const router = useRouter();
  const params = useParams();
  const [serviceReport, setServiceReport] = useState<ServiceReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [completing, setCompleting] = useState(false);

  useEffect(() => {
    if (params.id) {
      loadServiceReport(params.id as string);
    }
  }, [params.id]);

  const loadServiceReport = async (id: string) => {
    try {
      const response = await fetch(`/api/service/${id}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setServiceReport(data.serviceReport);
      } else {
        toast.error('Failed to load service report');
        router.push('/service');
      }
    } catch (error) {
      console.error('Error loading service report:', error);
      toast.error('Failed to load service report');
      router.push('/service');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/service');
  };

  const handleEdit = () => {
    router.push(`/service/${params.id}/edit`);
  };

  const handleMarkCompleted = async () => {
    if (!serviceReport) return;

    setCompleting(true);
    try {
      const response = await fetch(`/api/service/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          status: 'COMPLETED',
          completionDate: new Date().toISOString(),
        }),
      });

      if (response.ok) {
        toast.success('Service report marked as completed');
        // Reload the service report data
        loadServiceReport(params.id as string);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to mark service as completed');
      }
    } catch (error) {
      console.error('Error marking service as completed:', error);
      toast.error('Failed to mark service as completed');
    } finally {
      setCompleting(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this service report?')) {
      return;
    }

    try {
      const response = await fetch(`/api/service/${params.id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        toast.success('Service report deleted successfully');
        router.push('/service');
      } else {
        toast.error('Failed to delete service report');
      }
    } catch (error) {
      console.error('Error deleting service report:', error);
      toast.error('Failed to delete service report');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      OPEN: { variant: 'secondary' as const, icon: AlertCircle, label: 'Open' },
      IN_PROGRESS: { variant: 'default' as const, icon: Clock, label: 'In Progress' },
      COMPLETED: { variant: 'default' as const, icon: CheckCircle, label: 'Completed' },
      CANCELLED: { variant: 'destructive' as const, icon: XCircle, label: 'Cancelled' },
      PENDING: { variant: 'secondary' as const, icon: Clock, label: 'Pending' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.OPEN;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getComplaintTypeBadge = (type: string) => {
    const typeConfig = {
      REPAIR: 'bg-red-100 text-red-800',
      MAINTENANCE: 'bg-blue-100 text-blue-800',
      INSTALLATION: 'bg-green-100 text-green-800',
      INSPECTION: 'bg-yellow-100 text-yellow-800',
      WARRANTY: 'bg-purple-100 text-purple-800',
      OTHER: 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeConfig[type as keyof typeof typeConfig] || typeConfig.OTHER}`}>
        {type}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Service Reports
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <div className="text-sm text-muted-foreground">Loading service report...</div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!serviceReport) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Service Reports
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-muted-foreground">Service report not found.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Service Reports
        </Button>
        <div className="flex items-center gap-2">
          {serviceReport.status !== 'COMPLETED' && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="default"
                  className="bg-green-600 hover:bg-green-700"
                  disabled={completing}
                >
                  <Check className="h-4 w-4 mr-2" />
                  {completing ? 'Completing...' : 'Mark as Completed'}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Mark Service as Completed</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to mark this service report as completed? This will set the completion date to today and update the status.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleMarkCompleted}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Mark as Completed
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Service Report Information */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Service Report Details
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Report Date</label>
                <p className="text-sm">{format(new Date(serviceReport.reportDate), 'PPP')}</p>
              </div>
              
              {serviceReport.visitDate && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Visit Date</label>
                  <p className="text-sm">{format(new Date(serviceReport.visitDate), 'PPP')}</p>
                </div>
              )}
              
              {serviceReport.completionDate && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Completion Date</label>
                  <p className="text-sm">{format(new Date(serviceReport.completionDate), 'PPP')}</p>
                </div>
              )}

              <div>
                <label className="text-sm font-medium text-muted-foreground">Nature of Service</label>
                <p className="text-sm">{serviceReport.natureOfService}</p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Status</label>
                <div className="mt-1">
                  {getStatusBadge(serviceReport.status)}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Complaint Type</label>
                <div className="mt-1">
                  {getComplaintTypeBadge(serviceReport.complaintType)}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Customer</label>
                <p className="text-sm font-medium">{serviceReport.customer.name}</p>
                <p className="text-xs text-muted-foreground">{serviceReport.customer.city}</p>
                {serviceReport.customer.phone && (
                  <p className="text-xs text-muted-foreground">{serviceReport.customer.phone}</p>
                )}
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Executive</label>
                <p className="text-sm font-medium">{serviceReport.executive.name}</p>
                {serviceReport.executive.phone && (
                  <p className="text-xs text-muted-foreground">{serviceReport.executive.phone}</p>
                )}
              </div>
            </div>
          </div>

          {serviceReport.actionTaken && (
            <>
              <Separator className="my-6" />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Action Taken</label>
                <p className="text-sm mt-1">{serviceReport.actionTaken}</p>
              </div>
            </>
          )}

          {serviceReport.remarks && (
            <>
              <Separator className="my-6" />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Remarks</label>
                <p className="text-sm mt-1">{serviceReport.remarks}</p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Service Details */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Service Details
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-6">
            {serviceReport.details.map((detail, index) => (
              <div key={detail.id} className="border rounded-lg p-4">
                <h4 className="font-medium mb-4">Service Detail {index + 1}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Machine Type</label>
                    <p className="text-sm">{detail.machineType}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Serial Number</label>
                    <p className="text-sm">{detail.serialNumber}</p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">Problem</label>
                    <p className="text-sm">{detail.problem}</p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">Solution</label>
                    <p className="text-sm">{detail.solution}</p>
                  </div>
                  {detail.partReplaced && (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-muted-foreground">Part Replaced</label>
                      <p className="text-sm">{detail.partReplaced}</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
