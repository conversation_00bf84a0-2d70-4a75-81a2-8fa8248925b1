'use client';

import { useState } from 'react';
import { useCustomers } from '@/lib/hooks/useCustomers';
import { CustomerFilterForm } from '@/components/customers/customer-filter-form';
import { CustomerList } from '@/components/customers/customer-list';
import { CustomerActions } from '@/components/customers/customer-actions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * Customer List Page
 *
 * This page displays a list of customers with filtering, sorting, and pagination.
 */
export default function CustomersPage() {
  const [activeTab, setActiveTab] = useState('all');

  const {
    customers,
    isLoading,
    pagination,
    filters,
    setFilters,
    setPagination,
    refreshCustomers,
    deleteCustomer
  } = useCustomers();

  // Handle filter change
  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  // Handle pagination change
  const handlePaginationChange = (skip: number, take: number) => {
    setPagination({ skip, take });
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === 'all') {
      setFilters({ isActive: undefined });
    } else if (value === 'active') {
      setFilters({ isActive: 'true' });
    } else if (value === 'inactive') {
      setFilters({ isActive: 'false' });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle>Customer Management</CardTitle>
            <CardDescription className="text-gray-100">
              View, search, and manage all your customers
            </CardDescription>
          </div>
          <CustomerActions />
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Customers</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="inactive">Inactive</TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="space-y-4">
              <CustomerFilterForm
                onFilter={handleFilterChange}
                initialValues={filters}
              />
              <CustomerList
                customers={customers}
                isLoading={isLoading}
                pagination={pagination}
                onPaginationChange={handlePaginationChange}
                onRefresh={refreshCustomers}
                onDelete={deleteCustomer}
              />
            </TabsContent>
            <TabsContent value="active" className="space-y-4">
              <CustomerFilterForm
                onFilter={handleFilterChange}
                initialValues={{ ...filters, isActive: 'true' }}
              />
              <CustomerList
                customers={customers}
                isLoading={isLoading}
                pagination={pagination}
                onPaginationChange={handlePaginationChange}
                onRefresh={refreshCustomers}
                onDelete={deleteCustomer}
              />
            </TabsContent>
            <TabsContent value="inactive" className="space-y-4">
              <CustomerFilterForm
                onFilter={handleFilterChange}
                initialValues={{ ...filters, isActive: 'false' }}
              />
              <CustomerList
                customers={customers}
                isLoading={isLoading}
                pagination={pagination}
                onPaginationChange={handlePaginationChange}
                onRefresh={refreshCustomers}
                onDelete={deleteCustomer}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
