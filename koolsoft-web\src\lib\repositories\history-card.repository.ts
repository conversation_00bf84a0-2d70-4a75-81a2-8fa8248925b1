import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * History Card Repository
 *
 * This repository handles database operations for the History Card entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class HistoryCardRepository extends PrismaRepository<
  Prisma.history_cardsGetPayload<{}>,
  string,
  Prisma.history_cardsCreateInput,
  Prisma.history_cardsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('history_cards');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find history cards by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history cards
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<Prisma.history_cardsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Find history cards by AMC contract ID
   * @param amcId AMC contract ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history cards
   */
  async findByAmcId(amcId: string, skip?: number, take?: number): Promise<Prisma.history_cardsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { amcId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Find history cards by warranty ID
   * @param warrantyId Warranty ID (inWarrantyId)
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history cards
   */
  async findByWarrantyId(warrantyId: string, skip?: number, take?: number): Promise<Prisma.history_cardsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { inWarrantyId: warrantyId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Find history cards by out warranty ID
   * @param outWarrantyId Out warranty ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history cards
   */
  async findByOutWarrantyId(outWarrantyId: string, skip?: number, take?: number): Promise<Prisma.history_cardsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { outWarrantyId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Find history card with all related data
   * @param id History card ID
   * @returns Promise resolving to the history card with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        customer: true,
        repairs: {
          orderBy: { repairDate: 'desc' }
        },
        maintenance: {
          orderBy: { maintenanceDate: 'desc' }
        },
        complaints: {
          orderBy: { complaintDate: 'desc' }
        },
        amcDetails: {
          orderBy: { startDate: 'desc' }
        },
        addonDetails: {
          orderBy: { addonDate: 'desc' }
        },
        waterWashes: {
          orderBy: { washDate: 'desc' }
        },
        audits: {
          orderBy: { auditDate: 'desc' }
        },
        componentReplacements: {
          orderBy: { replacementDate: 'desc' }
        },
      },
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.history_cardsGetPayload<{}>,
    string,
    Prisma.history_cardsCreateInput,
    Prisma.history_cardsUpdateInput
  > {
    const repo = new HistoryCardRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
