import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Mail, 
  Phone, 
  Globe, 
  MapPin, 
  Calendar, 
  FileText, 
  Building, 
  User 
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface CustomerOverviewProps {
  customer: any;
}

/**
 * Customer Overview Component
 *
 * This component displays the basic information of a customer.
 */
export function CustomerOverview({ customer }: CustomerOverviewProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Basic details about the customer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Contact Information</h3>
              
              {customer.email && (
                <div className="flex items-start">
                  <Mail className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Email</p>
                    <p className="text-sm text-gray-700">{customer.email}</p>
                  </div>
                </div>
              )}
              
              {customer.phone && (
                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Phone</p>
                    <p className="text-sm text-gray-700">{customer.phone}</p>
                  </div>
                </div>
              )}
              
              {customer.mobile && (
                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Mobile</p>
                    <p className="text-sm text-gray-700">{customer.mobile}</p>
                  </div>
                </div>
              )}
              
              {customer.website && (
                <div className="flex items-start">
                  <Globe className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Website</p>
                    <p className="text-sm text-gray-700">
                      <a 
                        href={customer.website.startsWith('http') ? customer.website : `https://${customer.website}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {customer.website}
                      </a>
                    </p>
                  </div>
                </div>
              )}
            </div>
            
            {/* Address Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Address Information</h3>
              
              {customer.address && (
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Address</p>
                    <p className="text-sm text-gray-700">{customer.address}</p>
                  </div>
                </div>
              )}
              
              {(customer.city || customer.state || customer.pinCode) && (
                <div className="flex items-start">
                  <Building className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">City/State/Pin</p>
                    <p className="text-sm text-gray-700">
                      {[
                        customer.city,
                        customer.state,
                        customer.pinCode
                      ].filter(Boolean).join(', ')}
                    </p>
                  </div>
                </div>
              )}
              
              {customer.location && (
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Location</p>
                    <p className="text-sm text-gray-700">{customer.location}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Additional Information</CardTitle>
          <CardDescription>
            Additional details and metadata
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Additional Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Additional Details</h3>
              
              {customer.designation && (
                <div className="flex items-start">
                  <User className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Designation</p>
                    <p className="text-sm text-gray-700">{customer.designation}</p>
                  </div>
                </div>
              )}
              
              {customer.birthDate && (
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Birth Date</p>
                    <p className="text-sm text-gray-700">{formatDate(customer.birthDate)}</p>
                  </div>
                </div>
              )}
              
              {customer.anniversaryDate && (
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Anniversary Date</p>
                    <p className="text-sm text-gray-700">{formatDate(customer.anniversaryDate)}</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* System Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">System Information</h3>
              
              {customer.originalId && (
                <div className="flex items-start">
                  <FileText className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Original ID</p>
                    <p className="text-sm text-gray-700">{customer.originalId}</p>
                  </div>
                </div>
              )}
              
              {customer.createdAt && (
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Created At</p>
                    <p className="text-sm text-gray-700">{formatDate(customer.createdAt)}</p>
                  </div>
                </div>
              )}
              
              {customer.updatedAt && (
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Last Updated</p>
                    <p className="text-sm text-gray-700">{formatDate(customer.updatedAt)}</p>
                  </div>
                </div>
              )}
              
              {customer.lastContact && (
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Last Contact</p>
                    <p className="text-sm text-gray-700">{formatDate(customer.lastContact)}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
