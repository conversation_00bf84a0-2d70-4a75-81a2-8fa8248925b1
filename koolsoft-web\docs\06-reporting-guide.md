# KoolSoft Reporting Migration Guide

This document provides a comprehensive inventory of all Crystal Reports in the KoolSoft application and detailed implementation strategies for migrating them to modern web-based reports in the Next.js application.

## Crystal Reports Inventory

The KoolSoft application uses Crystal Reports extensively for reporting functionality. Below is a complete inventory of all reports identified in the codebase:

### AMC Module Reports

| Report Name | File Name | Description | Parameters | Complexity |
|-------------|-----------|-------------|------------|------------|
| AMC Summary | AMCSummary.rpt | Summary of all AMC contracts | Date Range, Executive, Division | Medium |
| AMC Detail | AMCDetail.rpt | Detailed report of a specific AMC contract | AMC ID | High |
| AMC Payment | AMCPayment.rpt | Payment details for AMC contracts | Date Range, Payment Mode | Medium |
| AMC Renewal Due | AMCRenewalDue.rpt | List of AMCs due for renewal | Days to Expiry | Low |
| AMC Service Schedule | AMCServiceSchedule.rpt | Service schedule for AMCs | Date Range | Medium |
| AMC Machine List | AMCMachineList.rpt | List of machines under AMC | Brand, Model | Medium |
| AMC Division-wise | AMCDivisionWise.rpt | AMCs grouped by division | Division | Low |
| AMC Executive-wise | AMCExecutiveWise.rpt | AMCs grouped by executive | Executive | Low |
| AMC Expiry | AMCExpiry.rpt | List of expired AMCs | Date Range | Low |
| AMC Component List | AMCComponentList.rpt | List of components in AMC machines | Component Type | Medium |

### In-Warranty Module Reports

| Report Name | File Name | Description | Parameters | Complexity |
|-------------|-----------|-------------|------------|------------|
| In-Warranty Summary | INWSummary.rpt | Summary of all in-warranty products | Date Range, Brand | Medium |
| In-Warranty Detail | INWDetail.rpt | Detailed report of a specific in-warranty | In-Warranty ID | High |
| In-Warranty Expiry | INWExpiry.rpt | List of warranties nearing expiry | Days to Expiry | Low |
| BLUESTAR Warranty | BLUESTARWarranty.rpt | Special report for BLUESTAR products | Date Range | Medium |
| In-Warranty Service | INWService.rpt | Service history for in-warranty products | Date Range | Medium |
| In-Warranty Component | INWComponent.rpt | Component details for in-warranty products | Component Type | Medium |

### Out-of-Warranty Module Reports

| Report Name | File Name | Description | Parameters | Complexity |
|-------------|-----------|-------------|------------|------------|
| Out-Warranty Summary | OTWSummary.rpt | Summary of all out-of-warranty products | Date Range, Brand | Medium |
| Out-Warranty Detail | OTWDetail.rpt | Detailed report of a specific out-of-warranty | Out-Warranty ID | High |
| Out-Warranty Payment | OTWPayment.rpt | Payment details for out-of-warranty services | Date Range, Payment Mode | Medium |
| Out-Warranty Service | OTWService.rpt | Service history for out-of-warranty products | Date Range | Medium |
| Out-Warranty Component | OTWComponent.rpt | Component details for out-of-warranty products | Component Type | Medium |

### Service Module Reports

| Report Name | File Name | Description | Parameters | Complexity |
|-------------|-----------|-------------|------------|------------|
| Service Summary | ServiceSummary.rpt | Summary of all service reports | Date Range, Service Type | Medium |
| Service Detail | ServiceDetail.rpt | Detailed report of a specific service | Service ID | High |
| Service by Executive | ServiceByExecutive.rpt | Services grouped by executive | Executive, Date Range | Medium |
| Service by Customer | ServiceByCustomer.rpt | Services grouped by customer | Customer, Date Range | Medium |
| Service by Product | ServiceByProduct.rpt | Services grouped by product | Product, Date Range | Medium |
| Service Complaint Analysis | ServiceComplaintAnalysis.rpt | Analysis of service complaints | Date Range | High |

### Sales Module Reports

| Report Name | File Name | Description | Parameters | Complexity |
|-------------|-----------|-------------|------------|------------|
| Sales Lead Summary | SalesLeadSummary.rpt | Summary of all sales leads | Date Range, Status | Medium |
| Sales Pipeline | SalesPipeline.rpt | Sales pipeline analysis | Date Range | High |
| Sales Forecast | SalesForecast.rpt | Sales forecast report | Date Range, Product | High |
| Sales by Executive | SalesByExecutive.rpt | Sales grouped by executive | Executive, Date Range | Medium |
| Sales by Product | SalesByProduct.rpt | Sales grouped by product | Product, Date Range | Medium |
| Sales Quotation | SalesQuotation.rpt | Quotation generation report | Quotation ID | Medium |
| Sales Order | SalesOrder.rpt | Order generation report | Order ID | Medium |

### Administrative Reports

| Report Name | File Name | Description | Parameters | Complexity |
|-------------|-----------|-------------|------------|------------|
| Customer List | CustomerList.rpt | List of all customers | Customer Type | Low |
| Executive Performance | ExecutivePerformance.rpt | Performance analysis of executives | Date Range, Executive | High |
| Product Inventory | ProductInventory.rpt | Inventory status of products | Brand, Model | Medium |
| Financial Summary | FinancialSummary.rpt | Financial summary report | Date Range | High |
| User Activity | UserActivity.rpt | User activity log report | Date Range, User | Medium |
| History Card | HistoryCard.rpt | History card report | Card ID | High |

## Migration Strategy

### 1. Technology Stack for Report Migration

The following technologies will be used for implementing the modern reporting system:

- **React-based UI Components**: For report viewers and parameter forms
- **React-PDF**: For generating PDF reports
- **Chart.js/Recharts**: For data visualization and charts
- **React-Table**: For tabular data display
- **jsPDF**: For additional PDF generation capabilities
- **ExcelJS**: For Excel export functionality
- **Prisma**: For database queries to fetch report data

### 2. Report Component Architecture

Each report will be implemented using a modular architecture:

```
reports/
├── components/
│   ├── common/
│   │   ├── ReportHeader.tsx
│   │   ├── ReportFooter.tsx
│   │   ├── ReportTable.tsx
│   │   ├── ReportChart.tsx
│   │   └── ReportParameters.tsx
│   ├── amc/
│   │   ├── AMCSummaryReport.tsx
│   │   ├── AMCDetailReport.tsx
│   │   └── ...
│   ├── warranty/
│   │   ├── INWSummaryReport.tsx
│   │   ├── OTWSummaryReport.tsx
│   │   └── ...
│   └── ...
├── hooks/
│   ├── useReportData.ts
│   ├── useReportParameters.ts
│   └── useReportExport.ts
├── utils/
│   ├── reportFormatters.ts
│   ├── reportCalculations.ts
│   └── reportExporters.ts
└── api/
    ├── amc-reports.ts
    ├── warranty-reports.ts
    └── ...
```

### 3. Report Parameter Handling

Each report will have a parameter form component that:

1. Displays the appropriate input fields for the report parameters
2. Validates parameter values
3. Submits parameters to the report data fetching function
4. Preserves parameter state for report refreshes

Example parameter form implementation:

```tsx
// components/common/ReportParameters.tsx
import { useState } from 'react';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { Select } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

export function ReportParameters({
  reportType,
  onSubmit,
  defaultValues = {}
}) {
  const [parameters, setParameters] = useState(defaultValues);

  // Different parameter sets based on report type
  const renderParameters = () => {
    switch (reportType) {
      case 'AMCSummary':
        return (
          <>
            <DateRangePicker
              label="Date Range"
              value={parameters.dateRange}
              onChange={(dateRange) => setParameters({ ...parameters, dateRange })}
            />
            <Select
              label="Executive"
              options={executiveOptions}
              value={parameters.executive}
              onChange={(executive) => setParameters({ ...parameters, executive })}
            />
            <Select
              label="Division"
              options={divisionOptions}
              value={parameters.division}
              onChange={(division) => setParameters({ ...parameters, division })}
            />
          </>
        );
      // Other report types...
      default:
        return null;
    }
  };

  return (
    <div className="bg-white p-4 rounded-md shadow-sm">
      <h3 className="text-lg font-medium mb-4">Report Parameters</h3>
      <div className="space-y-4">
        {renderParameters()}
      </div>
      <div className="mt-6">
        <Button onClick={() => onSubmit(parameters)}>Generate Report</Button>
      </div>
    </div>
  );
}
```

### 4. Report Data Fetching

Each report will have a dedicated API endpoint that:

1. Accepts report parameters
2. Constructs and executes the appropriate database queries
3. Processes and formats the data for the report
4. Returns the structured data for rendering

Example API implementation:

```typescript
// app/api/reports/amc-summary/route.ts
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { dateRange, executive, division } = await request.json();

    // Construct date filters
    const startDate = dateRange?.from ? new Date(dateRange.from) : undefined;
    const endDate = dateRange?.to ? new Date(dateRange.to) : undefined;

    // Build query filters
    const filters: any = {};

    if (startDate && endDate) {
      filters.startDate = { gte: startDate };
      filters.endDate = { lte: endDate };
    }

    if (executive) {
      filters.executiveId = executive;
    }

    // For division, we need to join with AMCDivision
    const divisionFilter = division ? {
      divisions: {
        some: {
          divisionId: division
        }
      }
    } : {};

    // Execute query
    const amcContracts = await prisma.aMCContract.findMany({
      where: {
        ...filters,
        ...divisionFilter
      },
      include: {
        customer: true,
        machines: {
          include: {
            model: {
              include: {
                product: true
              }
            }
          }
        },
        payments: true,
        divisions: true
      }
    });

    // Process data for report
    const reportData = {
      summary: {
        totalContracts: amcContracts.length,
        totalValue: amcContracts.reduce((sum, contract) => sum + Number(contract.amount), 0),
        averageValue: amcContracts.length > 0
          ? amcContracts.reduce((sum, contract) => sum + Number(contract.amount), 0) / amcContracts.length
          : 0
      },
      contracts: amcContracts.map(contract => ({
        id: contract.id,
        customer: contract.customer.name,
        startDate: contract.startDate,
        endDate: contract.endDate,
        amount: contract.amount,
        machines: contract.machines.length,
        payments: contract.payments.reduce((sum, payment) => sum + Number(payment.amount), 0),
        balance: Number(contract.amount) - contract.payments.reduce((sum, payment) => sum + Number(payment.amount), 0)
      }))
    };

    return NextResponse.json(reportData);
  } catch (error) {
    console.error('Error generating AMC Summary report:', error);
    return NextResponse.json({ error: 'Failed to generate report' }, { status: 500 });
  }
}
```

### 5. Report Rendering

Each report will have a dedicated component that:

1. Renders the report header with title, date, and logo
2. Displays the report data in appropriate format (tables, charts)
3. Implements pagination for large reports
4. Provides export options (PDF, Excel)

Example report component:

```tsx
// components/amc/AMCSummaryReport.tsx
import { useState } from 'react';
import { ReportHeader } from '@/components/reports/common/ReportHeader';
import { ReportFooter } from '@/components/reports/common/ReportFooter';
import { ReportTable } from '@/components/reports/common/ReportTable';
import { ReportChart } from '@/components/reports/common/ReportChart';
import { Button } from '@/components/ui/button';
import { exportToPdf, exportToExcel } from '@/lib/reports/exporters';

export function AMCSummaryReport({ data, parameters }) {
  const [page, setPage] = useState(1);
  const pageSize = 10;

  const paginatedContracts = data.contracts.slice(
    (page - 1) * pageSize,
    page * pageSize
  );

  const totalPages = Math.ceil(data.contracts.length / pageSize);

  const handleExportPdf = () => {
    exportToPdf('AMC Summary Report', data, parameters);
  };

  const handleExportExcel = () => {
    exportToExcel('AMC Summary Report', data, parameters);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <ReportHeader
        title="AMC Summary Report"
        parameters={parameters}
      />

      <div className="mt-6 mb-8">
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-md">
            <h3 className="text-sm font-medium text-gray-500">Total Contracts</h3>
            <p className="text-2xl font-bold">{data.summary.totalContracts}</p>
          </div>
          <div className="bg-green-50 p-4 rounded-md">
            <h3 className="text-sm font-medium text-gray-500">Total Value</h3>
            <p className="text-2xl font-bold">₹{data.summary.totalValue.toLocaleString()}</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-md">
            <h3 className="text-sm font-medium text-gray-500">Average Value</h3>
            <p className="text-2xl font-bold">₹{data.summary.averageValue.toLocaleString()}</p>
          </div>
        </div>
      </div>

      <ReportChart
        type="bar"
        data={{
          labels: data.contracts.map(c => c.customer).slice(0, 10),
          datasets: [{
            label: 'AMC Value',
            data: data.contracts.map(c => c.amount).slice(0, 10),
            backgroundColor: 'rgba(59, 130, 246, 0.5)'
          }]
        }}
        options={{
          plugins: {
            title: {
              display: true,
              text: 'Top 10 AMCs by Value'
            }
          }
        }}
      />

      <div className="mt-8">
        <ReportTable
          columns={[
            { header: 'Customer', accessor: 'customer' },
            { header: 'Start Date', accessor: 'startDate', type: 'date' },
            { header: 'End Date', accessor: 'endDate', type: 'date' },
            { header: 'Amount', accessor: 'amount', type: 'currency' },
            { header: 'Machines', accessor: 'machines' },
            { header: 'Payments', accessor: 'payments', type: 'currency' },
            { header: 'Balance', accessor: 'balance', type: 'currency' }
          ]}
          data={paginatedContracts}
        />
      </div>

      <div className="mt-4 flex justify-between items-center">
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => setPage(Math.max(1, page - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="py-2 px-4">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPage(Math.min(totalPages, page + 1))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportPdf}>
            Export PDF
          </Button>
          <Button variant="outline" onClick={handleExportExcel}>
            Export Excel
          </Button>
        </div>
      </div>

      <ReportFooter />
    </div>
  );
}
```

### 6. Report Export Functionality

The reporting system will include utilities for exporting reports to different formats:

```typescript
// lib/reports/exporters.ts
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

export function exportToPdf(title, data, parameters) {
  const doc = new jsPDF();

  // Add report title
  doc.setFontSize(18);
  doc.text(title, 14, 22);

  // Add parameters
  doc.setFontSize(10);
  let yPos = 30;
  Object.entries(parameters).forEach(([key, value]) => {
    doc.text(`${key}: ${formatParameterValue(key, value)}`, 14, yPos);
    yPos += 5;
  });

  // Add summary section
  if (data.summary) {
    yPos += 5;
    doc.setFontSize(12);
    doc.text('Summary', 14, yPos);
    yPos += 5;

    Object.entries(data.summary).forEach(([key, value]) => {
      doc.setFontSize(10);
      doc.text(`${formatSummaryKey(key)}: ${value}`, 14, yPos);
      yPos += 5;
    });
  }

  // Add table data
  if (data.contracts) {
    yPos += 5;
    doc.autoTable({
      startY: yPos,
      head: [['Customer', 'Start Date', 'End Date', 'Amount', 'Machines', 'Payments', 'Balance']],
      body: data.contracts.map(contract => [
        contract.customer,
        formatDate(contract.startDate),
        formatDate(contract.endDate),
        formatCurrency(contract.amount),
        contract.machines,
        formatCurrency(contract.payments),
        formatCurrency(contract.balance)
      ])
    });
  }

  // Add footer
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.text(
      `Page ${i} of ${pageCount} - Generated on ${new Date().toLocaleDateString()}`,
      doc.internal.pageSize.width / 2,
      doc.internal.pageSize.height - 10,
      { align: 'center' }
    );
  }

  // Save the PDF
  doc.save(`${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`);
}

export function exportToExcel(title, data, parameters) {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(title);

  // Add title
  worksheet.mergeCells('A1:G1');
  const titleCell = worksheet.getCell('A1');
  titleCell.value = title;
  titleCell.font = { size: 16, bold: true };
  titleCell.alignment = { horizontal: 'center' };

  // Add parameters
  let rowIndex = 2;
  Object.entries(parameters).forEach(([key, value]) => {
    worksheet.getCell(`A${rowIndex}`).value = `${key}:`;
    worksheet.getCell(`B${rowIndex}`).value = formatParameterValue(key, value);
    rowIndex++;
  });

  // Add summary
  if (data.summary) {
    rowIndex++;
    worksheet.getCell(`A${rowIndex}`).value = 'Summary';
    worksheet.getCell(`A${rowIndex}`).font = { bold: true };
    rowIndex++;

    Object.entries(data.summary).forEach(([key, value]) => {
      worksheet.getCell(`A${rowIndex}`).value = formatSummaryKey(key);
      worksheet.getCell(`B${rowIndex}`).value = value;
      rowIndex++;
    });
  }

  // Add table data
  if (data.contracts) {
    rowIndex += 2;

    // Add headers
    const headers = ['Customer', 'Start Date', 'End Date', 'Amount', 'Machines', 'Payments', 'Balance'];
    headers.forEach((header, i) => {
      worksheet.getCell(`${String.fromCharCode(65 + i)}${rowIndex}`).value = header;
      worksheet.getCell(`${String.fromCharCode(65 + i)}${rowIndex}`).font = { bold: true };
    });

    // Add data
    data.contracts.forEach((contract, index) => {
      rowIndex++;
      worksheet.getCell(`A${rowIndex}`).value = contract.customer;
      worksheet.getCell(`B${rowIndex}`).value = new Date(contract.startDate);
      worksheet.getCell(`C${rowIndex}`).value = new Date(contract.endDate);
      worksheet.getCell(`D${rowIndex}`).value = contract.amount;
      worksheet.getCell(`E${rowIndex}`).value = contract.machines;
      worksheet.getCell(`F${rowIndex}`).value = contract.payments;
      worksheet.getCell(`G${rowIndex}`).value = contract.balance;
    });

    // Format date columns
    worksheet.getColumn('B').numFmt = 'dd/mm/yyyy';
    worksheet.getColumn('C').numFmt = 'dd/mm/yyyy';

    // Format currency columns
    worksheet.getColumn('D').numFmt = '₹#,##0.00';
    worksheet.getColumn('F').numFmt = '₹#,##0.00';
    worksheet.getColumn('G').numFmt = '₹#,##0.00';
  }

  // Auto-size columns
  worksheet.columns.forEach(column => {
    column.width = 15;
  });

  // Generate Excel file
  workbook.xlsx.writeBuffer().then(buffer => {
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.xlsx`);
  });
}

// Helper functions
function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString();
}

function formatCurrency(value) {
  return `₹${Number(value).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
}

function formatParameterValue(key, value) {
  if (key.toLowerCase().includes('date')) {
    if (typeof value === 'object' && value.from && value.to) {
      return `${formatDate(value.from)} to ${formatDate(value.to)}`;
    }
    return formatDate(value);
  }
  return value;
}

function formatSummaryKey(key) {
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase());
}
```

### 7. Email Integration

Reports can be sent via email using the email functionality described in the UI Design Guidelines. The integration allows users to:

1. Send reports directly from the report viewer
2. Schedule reports to be sent automatically
3. Customize email templates for different report types

Example implementation for sending a report by email:

```typescript
// lib/reports/emailSender.ts
import { sendEmail } from '@/lib/email/sendEmail';
import { ReportEmail } from '@/components/email/ReportEmail';
import { exportToPdf } from './exporters';

export async function sendReportByEmail({
  reportType,
  reportData,
  title,
  parameters,
  recipients,
}) {
  // Generate PDF attachment
  const pdfBuffer = await exportToPdf(title, reportData, parameters, true);

  // Format date for the email
  const reportDate = new Date().toLocaleDateString();

  // Create email component
  const emailComponent = (
    <ReportEmail
      recipientName="User"
      reportName={title}
      reportDate={reportDate}
      reportDescription={`${title} generated with the specified parameters.`}
      reportUrl={`${process.env.NEXT_PUBLIC_APP_URL}/reports/${reportType}?params=${encodeURIComponent(JSON.stringify(parameters))}`}
      attachmentName={`${title.replace(/\s+/g, '_')}_${reportDate.replace(/\//g, '-')}.pdf`}
    />
  );

  // Send email
  return sendEmail({
    to: recipients || parameters.email || process.env.DEFAULT_REPORT_RECIPIENT,
    subject: `${title} - ${reportDate}`,
    component: emailComponent,
    attachments: [
      {
        filename: `${title.replace(/\s+/g, '_')}_${reportDate.replace(/\//g, '-')}.pdf`,
        content: pdfBuffer,
        contentType: 'application/pdf',
      }
    ],
  });
}
```

For implementation details of the email functionality, refer to the [Email Functionality Components](./05-ui-design-guidelines.md#email-functionality-components) section in the UI Design Guidelines.

## Implementation Plan

The implementation of the reporting system will follow these steps:

1. **Phase 1: Core Infrastructure**
   - Implement common report components
   - Create report parameter handling system
   - Develop export functionality
   - Set up report API routes

2. **Phase 2: High-Priority Reports**
   - Implement AMC Summary and Detail reports
   - Implement In-Warranty and Out-Warranty Summary reports
   - Implement Service Summary report
   - Implement Sales Summary report

3. **Phase 3: Medium-Priority Reports**
   - Implement remaining AMC reports
   - Implement remaining Warranty reports
   - Implement remaining Service reports
   - Implement remaining Sales reports

4. **Phase 4: Low-Priority Reports**
   - Implement Administrative reports
   - Implement specialized reports

5. **Phase 5: Testing and Optimization**
   - Compare report outputs with legacy Crystal Reports
   - Optimize performance for large datasets
   - Implement caching for frequently accessed reports
   - Add scheduled report generation

## Testing Strategy

Each report will be tested against the legacy Crystal Reports output to ensure accuracy:

1. Generate report in legacy system with specific parameters
2. Generate same report in new system with identical parameters
3. Compare data points, calculations, and formatting
4. Document any discrepancies and resolve them

## Conclusion

This comprehensive reporting migration strategy provides a clear path for transitioning from Crystal Reports to a modern web-based reporting system. By following this guide, the KoolSoft application will maintain all existing reporting capabilities while gaining the benefits of a modern, responsive, and web-accessible reporting system.
