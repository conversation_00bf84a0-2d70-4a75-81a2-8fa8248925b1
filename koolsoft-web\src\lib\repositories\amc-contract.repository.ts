import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * AMC Contract Repository
 *
 * This repository handles database operations for the AMC Contract entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class AMCContractRepository extends PrismaRepository<
  Prisma.amc_contractsGetPayload<{}>,
  string,
  Prisma.amc_contractsCreateInput,
  Prisma.amc_contractsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('amc_contracts');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find AMC contracts by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of AMC contracts
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<Prisma.amc_contractsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { startDate: 'desc' },
    });
  }

  /**
   * Find active AMC contracts (end date in the future)
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of active AMC contracts
   */
  async findActive(skip?: number, take?: number): Promise<Prisma.amc_contractsGetPayload<{}>[]> {
    return this.model.findMany({
      where: {
        endDate: {
          gte: new Date(),
        },
        status: 'ACTIVE',
      },
      skip,
      take,
      orderBy: { endDate: 'asc' },
    });
  }

  /**
   * Find expiring AMC contracts (end date within the next X days)
   * @param days Number of days to look ahead
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of expiring AMC contracts
   */
  async findExpiring(days: number = 30, skip?: number, take?: number): Promise<Prisma.amc_contractsGetPayload<{}>[]> {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + days);

    return this.model.findMany({
      where: {
        endDate: {
          gte: today,
          lte: futureDate,
        },
        status: 'ACTIVE',
      },
      skip,
      take,
      orderBy: { endDate: 'asc' },
    });
  }

  /**
   * Find AMC contracts by status
   * @param status Contract status
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of AMC contracts
   */
  async findByStatus(status: string, skip?: number, take?: number): Promise<Prisma.amc_contractsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { status },
      skip,
      take,
      orderBy: { startDate: 'desc' },
    });
  }

  /**
   * Find AMC contract by original ID
   * @param originalId Original ID from legacy table
   * @returns Promise resolving to the AMC contract or null if not found
   */
  async findByOriginalId(originalId: number): Promise<Prisma.amc_contractsGetPayload<{}> | null> {
    return this.model.findFirst({
      where: { originalId },
    });
  }

  /**
   * Find AMC contract with all related data
   * @param id AMC contract ID
   * @returns Promise resolving to the AMC contract with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        customer: true,
        contactPerson: true,
        machines: {
          include: {
            product: true,
            model: true,
            brand: true,
            components: true,
          },
        },
        serviceDates: true,
        payments: true,
        divisions: {
          include: {
            division: true,
          },
        },
      },
    });
  }

  /**
   * Find AMC contracts by executive ID
   * @param executiveId Executive ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of AMC contracts
   */
  async findByExecutiveId(executiveId: string, skip?: number, take?: number): Promise<Prisma.amc_contractsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { executiveId },
      skip,
      take,
      orderBy: { startDate: 'desc' },
    });
  }

  /**
   * Find AMC contracts with filter, pagination, and sorting
   * @param filter Filter condition
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Sorting criteria
   * @returns Promise resolving to an array of AMC contracts
   */
  async findWithFilter(
    filter: any,
    skip?: number,
    take?: number,
    orderBy?: any
  ): Promise<Prisma.amc_contractsGetPayload<{}>[]> {
    console.log('AMCContractRepository.findWithFilter: Starting with params:', {
      filter,
      skip,
      take,
      orderBy
    });

    try {
      console.log('AMCContractRepository.findWithFilter: Checking model availability');
      if (!this.model) {
        console.error('AMCContractRepository.findWithFilter: Model is not available');
        return [];
      }

      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);

      console.log('AMCContractRepository.findWithFilter: Executing findMany query');
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { startDate: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          contactPerson: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
        },
      });

      console.log(`AMCContractRepository.findWithFilter: Query successful, returned ${result.length} records`);
      return result;
    } catch (error) {
      console.error('AMCContractRepository.findWithFilter: Error executing query:', error);

      // Provide more detailed error messages for common Prisma errors
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new Error(`Foreign key constraint failed: ${error.meta?.field_name || 'unknown field'}`);
        } else if (error.code === 'P2025') {
          throw new Error(`Record not found: ${error.meta?.cause || 'unknown cause'}`);
        } else if (error.code === 'P2001') {
          throw new Error(`The record searched for does not exist: ${error.meta?.model_name || 'unknown model'}`);
        } else if (error.code === 'P2006') {
          throw new Error(`Invalid value provided: ${error.meta?.target || 'unknown field'}`);
        } else if (error.code === 'P2022') {
          throw new Error(`Unknown field in query: ${error.meta?.field_name || 'unknown field'}`);
        }
      }

      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to search AMC contracts: ${errorMessage}`);
    }
  }

  /**
   * Validate filter object to ensure it only contains valid fields
   * @param filter Filter object to validate
   * @throws Error if filter contains invalid fields
   */
  private validateFilter(filter: any): void {
    // Skip validation for empty filter
    if (!filter || Object.keys(filter).length === 0) {
      return;
    }

    // Check for OR condition (used in text search)
    if (filter.OR && Array.isArray(filter.OR)) {
      filter.OR.forEach((condition: any) => {
        this.validateFilterCondition(condition);
      });
    }

    // Validate the main filter
    this.validateFilterCondition(filter);
  }

  /**
   * Validate a single filter condition
   * @param condition Filter condition to validate
   * @throws Error if condition contains invalid fields
   */
  private validateFilterCondition(condition: any): void {
    // Skip validation for empty condition
    if (!condition || Object.keys(condition).length === 0) {
      return;
    }

    // Valid top-level fields in amc_contracts
    const validFields = [
      'id', 'customerId', 'executiveId', 'contactPersonId', 'natureOfService',
      'startDate', 'endDate', 'warningDate', 'amount', 'bslDebit', 'previousAmount',
      'amcPeriod', 'yearOfCommencement', 'numberOfMachines', 'numberOfServices',
      'renewalFlag', 'blstrFlag', 'paidAmount', 'fresh', 'numberOfInstallments',
      'paymentMode', 'totalTonnage', 'category', 'status', 'originalId',
      'createdAt', 'updatedAt', 'OR', 'AND', 'NOT', 'customer', 'contactPerson'
    ];

    // Check each field in the condition
    Object.keys(condition).forEach(key => {
      if (!validFields.includes(key)) {
        console.warn(`AMCContractRepository.validateFilter: Unknown field in filter: ${key}`);
      }
    });
  }

  /**
   * Count AMC contracts with filter
   * @param filter Filter condition
   * @returns Promise resolving to the count of AMC contracts
   */
  async countWithFilter(filter: any): Promise<number> {
    console.log('AMCContractRepository.countWithFilter: Starting with filter:', filter);

    try {
      console.log('AMCContractRepository.countWithFilter: Checking model availability');
      if (!this.model) {
        console.error('AMCContractRepository.countWithFilter: Model is not available');
        return 0;
      }

      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);

      console.log('AMCContractRepository.countWithFilter: Executing count query');
      const count = await this.model.count({
        where: filter,
      });

      console.log(`AMCContractRepository.countWithFilter: Query successful, count: ${count}`);
      return count;
    } catch (error) {
      console.error('AMCContractRepository.countWithFilter: Error executing query:', error);

      // Provide more detailed error messages for common Prisma errors
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new Error(`Foreign key constraint failed: ${error.meta?.field_name || 'unknown field'}`);
        } else if (error.code === 'P2025') {
          throw new Error(`Record not found: ${error.meta?.cause || 'unknown cause'}`);
        } else if (error.code === 'P2001') {
          throw new Error(`The record searched for does not exist: ${error.meta?.model_name || 'unknown model'}`);
        } else if (error.code === 'P2006') {
          throw new Error(`Invalid value provided: ${error.meta?.target || 'unknown field'}`);
        } else if (error.code === 'P2022') {
          throw new Error(`Unknown field in query: ${error.meta?.field_name || 'unknown field'}`);
        }
      }

      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to count AMC contracts: ${errorMessage}`);
    }
  }

  /**
   * Create AMC contract with related data in a transaction
   * @param data AMC contract data with related entities
   * @returns Promise resolving to the created AMC contract
   */
  async createWithRelations(data: any): Promise<any> {
    const {
      machines,
      serviceDates,
      payments,
      divisions,
      ...contractData
    } = data;

    return this.prisma.$transaction(async (tx) => {
      // Create the AMC contract
      const contract = await tx.amc_contracts.create({
        data: contractData,
      });

      // Create machines if provided
      if (machines && machines.length > 0) {
        await tx.amc_machines.createMany({
          data: machines.map((machine: any) => ({
            ...machine,
            amcContractId: contract.id,
          })),
        });
      }

      // Create service dates if provided
      if (serviceDates && serviceDates.length > 0) {
        await tx.amc_service_dates.createMany({
          data: serviceDates.map((serviceDate: any) => ({
            ...serviceDate,
            amcContractId: contract.id,
          })),
        });
      }

      // Create payments if provided
      if (payments && payments.length > 0) {
        await tx.amc_payments.createMany({
          data: payments.map((payment: any) => ({
            ...payment,
            amcContractId: contract.id,
          })),
        });
      }

      // Create divisions if provided
      if (divisions && divisions.length > 0) {
        await tx.amc_divisions.createMany({
          data: divisions.map((division: any) => ({
            ...division,
            amcContractId: contract.id,
          })),
        });
      }

      // Return the created contract with relations
      return this.findWithRelations(contract.id);
    });
  }

  /**
   * Update AMC contract with related data in a transaction
   * @param id AMC contract ID
   * @param data AMC contract data with related entities
   * @returns Promise resolving to the updated AMC contract
   */
  async updateWithRelations(id: string, data: any): Promise<any> {
    const {
      machines,
      serviceDates,
      payments,
      divisions,
      ...contractData
    } = data;

    return this.prisma.$transaction(async (tx) => {
      // Update the AMC contract
      await tx.amc_contracts.update({
        where: { id },
        data: contractData,
      });

      // Handle machines if provided
      if (machines) {
        // Delete existing machines not in the new list
        if (machines.length > 0) {
          const machineIds = machines
            .filter((m: any) => m.id)
            .map((m: any) => m.id);

          await tx.amc_machines.deleteMany({
            where: {
              amcContractId: id,
              id: { notIn: machineIds },
            },
          });
        } else {
          // Delete all machines if empty array provided
          await tx.amc_machines.deleteMany({
            where: { amcContractId: id },
          });
        }

        // Create or update machines
        for (const machine of machines) {
          if (machine.id) {
            // Update existing machine
            const { id: machineId, ...machineData } = machine;
            await tx.amc_machines.update({
              where: { id: machineId },
              data: machineData,
            });
          } else {
            // Create new machine
            await tx.amc_machines.create({
              data: {
                ...machine,
                amcContractId: id,
              },
            });
          }
        }
      }

      // Handle service dates if provided
      if (serviceDates) {
        // Similar implementation as machines
        if (serviceDates.length > 0) {
          const serviceDateIds = serviceDates
            .filter((sd: any) => sd.id)
            .map((sd: any) => sd.id);

          await tx.amc_service_dates.deleteMany({
            where: {
              amcContractId: id,
              id: { notIn: serviceDateIds },
            },
          });
        } else {
          await tx.amc_service_dates.deleteMany({
            where: { amcContractId: id },
          });
        }

        for (const serviceDate of serviceDates) {
          if (serviceDate.id) {
            const { id: serviceDateId, ...serviceDateData } = serviceDate;
            await tx.amc_service_dates.update({
              where: { id: serviceDateId },
              data: serviceDateData,
            });
          } else {
            await tx.amc_service_dates.create({
              data: {
                ...serviceDate,
                amcContractId: id,
              },
            });
          }
        }
      }

      // Handle payments if provided
      if (payments) {
        // Similar implementation as machines
        if (payments.length > 0) {
          const paymentIds = payments
            .filter((p: any) => p.id)
            .map((p: any) => p.id);

          await tx.amc_payments.deleteMany({
            where: {
              amcContractId: id,
              id: { notIn: paymentIds },
            },
          });
        } else {
          await tx.amc_payments.deleteMany({
            where: { amcContractId: id },
          });
        }

        for (const payment of payments) {
          if (payment.id) {
            const { id: paymentId, ...paymentData } = payment;
            await tx.amc_payments.update({
              where: { id: paymentId },
              data: paymentData,
            });
          } else {
            await tx.amc_payments.create({
              data: {
                ...payment,
                amcContractId: id,
              },
            });
          }
        }
      }

      // Handle divisions if provided
      if (divisions) {
        // Similar implementation as machines
        if (divisions.length > 0) {
          const divisionIds = divisions
            .filter((d: any) => d.id)
            .map((d: any) => d.id);

          await tx.amc_divisions.deleteMany({
            where: {
              amcContractId: id,
              id: { notIn: divisionIds },
            },
          });
        } else {
          await tx.amc_divisions.deleteMany({
            where: { amcContractId: id },
          });
        }

        for (const division of divisions) {
          if (division.id) {
            const { id: divisionId, ...divisionData } = division;
            await tx.amc_divisions.update({
              where: { id: divisionId },
              data: divisionData,
            });
          } else {
            await tx.amc_divisions.create({
              data: {
                ...division,
                amcContractId: id,
              },
            });
          }
        }
      }

      // Return the updated contract with relations
      return this.findWithRelations(id);
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.amc_contractsGetPayload<{}>,
    string,
    Prisma.amc_contractsCreateInput,
    Prisma.amc_contractsUpdateInput
  > {
    const repo = new AMCContractRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
