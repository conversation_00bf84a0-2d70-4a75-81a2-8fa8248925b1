import { useState, useCallback } from 'react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

export interface Component {
  id: string;
  machineId: string;
  componentNo?: number;
  serialNumber?: string;
  warrantyDate?: Date;
  section?: string;
  originalAmcId?: number;
  originalAssetNo?: number;
  originalComponentNo?: number;
  createdAt: Date;
  updatedAt: Date;
  machine?: {
    id: string;
    serialNumber?: string;
    location?: string;
    amcContract?: {
      id: string;
      contractNumber?: string;
      customer?: {
        id: string;
        name: string;
      };
    };
    product?: {
      id: string;
      name: string;
    };
    model?: {
      id: string;
      name: string;
    };
    brand?: {
      id: string;
      name: string;
    };
  };
}

export interface ComponentStatistics {
  total: number;
  activeWarranty: number;
  expiredWarranty: number;
  expiringSoon: number;
}

export interface CreateComponentData {
  machineId: string;
  componentNo?: number;
  serialNumber?: string;
  warrantyDate?: Date;
  section?: string;
  originalAmcId?: number;
  originalAssetNo?: number;
  originalComponentNo?: number;
}

export interface UpdateComponentData {
  machineId?: string;
  componentNo?: number;
  serialNumber?: string;
  warrantyDate?: Date;
  section?: string;
  originalAmcId?: number;
  originalAssetNo?: number;
  originalComponentNo?: number;
}

export interface ComponentQueryParams {
  query?: string;
  machineId?: string;
  componentNo?: number;
  warrantyStatus?: 'ACTIVE' | 'EXPIRED' | 'EXPIRING_SOON';
  skip?: number;
  take?: number;
  orderBy?: 'createdAt' | 'updatedAt' | 'serialNumber' | 'componentNo' | 'warrantyDate';
  orderDirection?: 'asc' | 'desc';
}

export function useComponents() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchComponents = useCallback(async (params?: ComponentQueryParams) => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();

      if (params?.query) searchParams.append('query', params.query);
      if (params?.machineId) searchParams.append('machineId', params.machineId);
      if (params?.componentNo !== undefined) searchParams.append('componentNo', params.componentNo.toString());
      if (params?.warrantyStatus) searchParams.append('warrantyStatus', params.warrantyStatus);
      if (params?.skip !== undefined) searchParams.append('skip', params.skip.toString());
      if (params?.take !== undefined) searchParams.append('take', params.take.toString());
      if (params?.orderBy) searchParams.append('orderBy', params.orderBy);
      if (params?.orderDirection) searchParams.append('orderDirection', params.orderDirection);

      const response = await fetch(`/api/amc/components?${searchParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch components');
      }

      const data = await response.json();
      return {
        components: data.components.map((component: any) => ({
          ...component,
          warrantyDate: component.warrantyDate ? new Date(component.warrantyDate) : undefined,
          createdAt: new Date(component.createdAt),
          updatedAt: new Date(component.updatedAt),
        })) as Component[],
        meta: data.meta,
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch components';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchMachineComponents = useCallback(async (
    machineId: string,
    params?: {
      skip?: number;
      take?: number;
      orderBy?: 'createdAt' | 'updatedAt' | 'serialNumber' | 'componentNo' | 'warrantyDate';
      orderDirection?: 'asc' | 'desc';
      includeStatistics?: boolean;
    }
  ) => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();

      if (params?.skip !== undefined) searchParams.append('skip', params.skip.toString());
      if (params?.take !== undefined) searchParams.append('take', params.take.toString());
      if (params?.orderBy) searchParams.append('orderBy', params.orderBy);
      if (params?.orderDirection) searchParams.append('orderDirection', params.orderDirection);
      if (params?.includeStatistics) searchParams.append('includeStatistics', 'true');

      const response = await fetch(`/api/amc/machines/${machineId}/components?${searchParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch machine components');
      }

      const data = await response.json();
      return {
        components: data.components.map((component: any) => ({
          ...component,
          warrantyDate: component.warrantyDate ? new Date(component.warrantyDate) : undefined,
          createdAt: new Date(component.createdAt),
          updatedAt: new Date(component.updatedAt),
        })) as Component[],
        meta: data.meta,
        statistics: data.statistics as ComponentStatistics | undefined,
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch machine components';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const createComponent = useCallback(async (data: CreateComponentData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/amc/components', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create component');
      }

      const result = await response.json();
      showSuccessToast('Success', 'Component created successfully');
      
      return {
        ...result.component,
        warrantyDate: result.component.warrantyDate ? new Date(result.component.warrantyDate) : undefined,
        createdAt: new Date(result.component.createdAt),
        updatedAt: new Date(result.component.updatedAt),
      } as Component;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create component';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateComponent = useCallback(async (id: string, data: UpdateComponentData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/amc/components/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update component');
      }

      const result = await response.json();
      showSuccessToast('Success', 'Component updated successfully');
      
      return {
        ...result.component,
        warrantyDate: result.component.warrantyDate ? new Date(result.component.warrantyDate) : undefined,
        createdAt: new Date(result.component.createdAt),
        updatedAt: new Date(result.component.updatedAt),
      } as Component;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update component';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteComponent = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/amc/components/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete component');
      }

      showSuccessToast('Success', 'Component deleted successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete component';
      setError(errorMessage);
      showErrorToast('Error', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const validateSerialNumber = useCallback(async (serialNumber: string, excludeId?: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/amc/components/validate-serial', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ serialNumber, excludeId }),
      });

      if (!response.ok) {
        throw new Error('Failed to validate serial number');
      }

      const result = await response.json();
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate serial number';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    fetchComponents,
    fetchMachineComponents,
    createComponent,
    updateComponent,
    deleteComponent,
    validateSerialNumber,
  };
}
