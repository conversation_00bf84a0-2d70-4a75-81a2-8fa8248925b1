'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminGate } from '@/components/auth/role-gate';
import Link from 'next/link';
import { UserList } from '@/components/admin/user-list';
import { UserCreateForm } from '@/components/admin/user-create-form';
import { UserEditForm } from '@/components/admin/user-edit-form';
import { BulkActionForm } from '@/components/admin/bulk-action-form';
import { SearchForm } from '@/components/admin/search-form';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

/**
 * User Management Page
 *
 * This page is only accessible to admin users.
 * It provides functionality to manage users:
 * - View users with pagination, sorting, and filtering
 * - Create new users
 * - Edit existing users
 * - Activate/deactivate users
 * - Perform bulk operations
 */
export default function UserManagementPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('list');
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<any[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    skip: 0,
    take: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    role: 'all',
    activeOnly: false,
    search: '',
  });

  // Fetch users on component mount and when filters/pagination change
  useEffect(() => {
    fetchUsers();
  }, [pagination.skip, pagination.take, filters]);

  // Fetch users from the API
  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams({
        skip: pagination.skip.toString(),
        take: pagination.take.toString(),
      });

      if (filters.role && filters.role !== 'all') {
        queryParams.append('role', filters.role);
      }

      if (filters.activeOnly) {
        queryParams.append('activeOnly', 'true');
      }

      if (filters.search) {
        queryParams.append('search', filters.search);
      }

      const response = await fetch(`/api/admin/users?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Include credentials to ensure cookies are sent with the request
        credentials: 'include',
      });

      if (!response.ok) {
        // Try to get more detailed error information
        const errorData = await response.json().catch(() => null);
        const errorMessage = errorData?.error || 'Failed to fetch users';

        if (response.status === 401) {
          showErrorToast('Authentication Error', 'You need to be logged in to access this resource');
          // Redirect to login page if not authenticated
          router.push('/auth/login?callbackUrl=/admin/users');
          return;
        }

        if (response.status === 403) {
          showErrorToast('Authorization Error', 'You do not have permission to access this resource');
          return;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      setUsers(data.users || []);
      setPagination(prev => ({
        ...prev,
        total: data.meta?.total || 0,
      }));
    } catch (error) {
      console.error('Error fetching users:', error);
      showErrorToast('Error', error instanceof Error ? error.message : 'Failed to fetch users. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle user selection for editing
  const handleEditUser = (user: any) => {
    setSelectedUser(user);
    setActiveTab('edit');
  };

  // Handle user creation success
  const handleUserCreated = () => {
    showSuccessToast('Success', 'User created successfully.');
    fetchUsers();
    setActiveTab('list');
  };

  // Handle user update success
  const handleUserUpdated = () => {
    showSuccessToast('Success', 'User updated successfully.');
    fetchUsers();
    setActiveTab('list');
    setSelectedUser(null);
  };

  // Handle bulk action success
  const handleBulkActionSuccess = () => {
    showSuccessToast('Success', 'Bulk action completed successfully.');
    fetchUsers();
    setSelectedUsers([]);
  };

  // Handle search
  const handleSearch = (searchParams: any) => {
    setFilters(prev => ({
      ...prev,
      ...searchParams,
    }));
    setPagination(prev => ({
      ...prev,
      skip: 0, // Reset to first page on new search
    }));
  };

  // Handle pagination change
  const handlePaginationChange = (skip: number, take: number) => {
    setPagination(prev => ({
      ...prev,
      skip,
      take,
    }));
  };

  return (
    <AdminGate fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
          <p className="text-gray-500">You do not have permission to access this page</p>
          <div className="mt-4">
            <Link href="/dashboard" className="text-blue-600 hover:text-blue-500">
              Return to Dashboard
            </Link>
          </div>
        </div>
      </div>
    }>
      <div>
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="mb-4">
                <TabsList>
                  <TabsTrigger value="list">User List</TabsTrigger>
                  <TabsTrigger value="create">Create User</TabsTrigger>
                  {selectedUser && <TabsTrigger value="edit">Edit User</TabsTrigger>}
                </TabsList>
              </div>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div>
                    <CardTitle className="text-2xl font-bold text-black">
                      {activeTab === 'list' && 'User Management'}
                      {activeTab === 'create' && 'Create New User'}
                      {activeTab === 'edit' && 'Edit User'}
                      {activeTab === 'bulk' && 'Bulk Actions'}
                    </CardTitle>
                    <CardDescription>
                      {activeTab === 'list' && 'Manage all users in the system'}
                      {activeTab === 'create' && 'Create a new user account'}
                      {activeTab === 'edit' && 'Edit an existing user account'}
                      {activeTab === 'bulk' && 'Perform actions on multiple users'}
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    {selectedUsers.length > 0 && (
                      <Button
                        variant="outline"
                        onClick={() => setActiveTab('bulk')}
                      >
                        Bulk Actions ({selectedUsers.length})
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {activeTab === 'list' && (
                    <>
                      <SearchForm onSearch={handleSearch} />
                      <UserList
                        users={users}
                        isLoading={isLoading}
                        pagination={pagination}
                        onPaginationChange={handlePaginationChange}
                        onEditUser={handleEditUser}
                        selectedUsers={selectedUsers}
                        onSelectUsers={setSelectedUsers}
                        onRefresh={fetchUsers}
                      />
                    </>
                  )}

                  {activeTab === 'create' && (
                    <UserCreateForm onSuccess={handleUserCreated} />
                  )}

                  {activeTab === 'edit' && selectedUser && (
                    <UserEditForm user={selectedUser} onSuccess={handleUserUpdated} />
                  )}

                  {activeTab === 'bulk' && (
                    <BulkActionForm
                      selectedUsers={selectedUsers}
                      onSuccess={handleBulkActionSuccess}
                      onCancel={() => setActiveTab('list')}
                    />
                  )}
                </CardContent>
              </Card>
            </Tabs>
          </div>
        </main>
      </div>
    </AdminGate>
  );
}
