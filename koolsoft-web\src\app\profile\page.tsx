'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout';
import { ProfileForm } from '@/components/profile/profile-form';
import { PasswordForm } from '@/components/profile/password-form';
import { AccountSettingsForm } from '@/components/profile/account-settings-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { User } from 'lucide-react';

/**
 * User Profile Page
 *
 * This page allows users to view and edit their profile information,
 * change their password, and manage their account settings.
 */
export default function ProfilePage() {
  const { user: authUser, isLoading: authLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(0);
  const [user, setUser] = useState(authUser);
  const [isLoading, setIsLoading] = useState(authLoading);

  // Fetch additional user data from the API
  useEffect(() => {
    if (authUser && authUser.id) {
      setIsLoading(true);
      fetch('/api/users/me')
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch user data');
          }
          return response.json();
        })
        .then(userData => {
          console.log('Fetched user data:', userData);
          setUser(userData);
        })
        .catch(error => {
          console.error('Error fetching user data:', error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      setUser(authUser);
      setIsLoading(authLoading);
    }
  }, [authUser, authLoading]);

  // Redirect to login page if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login?callbackUrl=/profile');
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Loading...</h2>
          <p className="text-gray-500">Please wait while we load your profile</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  // Define breadcrumbs for the profile page
  const breadcrumbs = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'My Profile', href: '/profile', icon: <User className="h-4 w-4" />, current: true }
  ];

  return (
    <DashboardLayout
      title="My Profile"
      breadcrumbs={breadcrumbs}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
    >
      <div className="space-y-6">
        <Card className="shadow-md">
          <CardHeader className="bg-primary text-white">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-white">Profile Settings</CardTitle>
                <CardDescription className="text-gray-100">
                  Manage your account settings and preferences
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <Tabs value={String(activeTab)} onValueChange={(value) => setActiveTab(Number(value))} className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="0">Personal Information</TabsTrigger>
                <TabsTrigger value="1">Change Password</TabsTrigger>
                <TabsTrigger value="2">Account Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="0">
                <ProfileForm user={user} />
              </TabsContent>
              <TabsContent value="1">
                <PasswordForm />
              </TabsContent>
              <TabsContent value="2">
                <AccountSettingsForm user={user} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
