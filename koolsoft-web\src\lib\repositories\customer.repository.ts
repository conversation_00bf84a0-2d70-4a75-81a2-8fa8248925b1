import { PrismaClient, Customer, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Customer Repository
 *
 * This repository handles database operations for the Customer entity.
 */
export class CustomerRepository extends PrismaRepository<
  Customer,
  string,
  Prisma.CustomerCreateInput,
  Prisma.CustomerUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('customer');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find customers by name (partial match)
   * @param name Customer name
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of customers
   */
  async findByName(name: string, skip?: number, take?: number): Promise<Customer[]> {
    return this.model.findMany({
      where: {
        name: {
          contains: name,
          mode: 'insensitive',
        },
      },
      skip,
      take,
    });
  }

  /**
   * Find customers by email
   * @param email Customer email
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of customers
   */
  async findByEmail(email: string, skip?: number, take?: number): Promise<Customer[]> {
    return this.model.findMany({
      where: {
        email: {
          contains: email,
          mode: 'insensitive',
        },
      },
      skip,
      take,
    });
  }

  /**
   * Find customers by phone or mobile
   * @param phone Phone or mobile number
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of customers
   */
  async findByPhone(phone: string, skip?: number, take?: number): Promise<Customer[]> {
    return this.model.findMany({
      where: {
        OR: [
          {
            phone: {
              contains: phone,
            },
          },
          {
            mobile: {
              contains: phone,
            },
          },
        ],
      },
      skip,
      take,
    });
  }

  /**
   * Find customers by city
   * @param city City name
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of customers
   */
  async findByCity(city: string, skip?: number, take?: number): Promise<Customer[]> {
    return this.model.findMany({
      where: {
        city: {
          equals: city,
          mode: 'insensitive',
        },
      },
      skip,
      take,
    });
  }

  /**
   * Find active customers
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of active customers
   */
  async findActive(skip?: number, take?: number): Promise<Customer[]> {
    return this.model.findMany({
      where: { isActive: true },
      skip,
      take,
    });
  }

  /**
   * Find customer with contacts
   * @param id Customer ID
   * @returns Promise resolving to the customer with contacts or null if not found
   */
  async findWithContacts(id: string): Promise<(Customer & { contacts: any[] }) | null> {
    return this.model.findUnique({
      where: { id },
      include: { contacts: true },
    });
  }

  /**
   * Find customer with all related data
   * @param id Customer ID
   * @returns Promise resolving to the customer with all related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        contacts: true,
        visitCards: true,
        amcContracts: {
          include: {
            machines: true,
            serviceDates: true,
            payments: true,
          },
        },
        warranties: true,
        // Note: History cards are fetched separately in the API route
        // due to the need for additional processing and data transformation
      },
    });
  }

  /**
   * Find customers by filter with pagination and sorting
   * @param filter Filter condition
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Sorting criteria
   * @returns Promise resolving to an array of customers
   */
  async findBy(filter: any, skip?: number, take?: number, orderBy?: any): Promise<Customer[]> {
    return this.model.findMany({
      where: filter,
      skip,
      take,
      orderBy: orderBy || { name: 'asc' },
    });
  }

  /**
   * Count customers by filter
   * @param filter Filter condition
   * @returns Promise resolving to the count of customers
   */
  async count(filter: any): Promise<number> {
    return this.model.count({
      where: filter,
    });
  }

  /**
   * Check if a customer has related records
   * @param id Customer ID
   * @returns Promise resolving to a boolean indicating if the customer has related records
   */
  async hasRelatedRecords(id: string): Promise<boolean> {
    const [
      amcContractsCount,
      warrantiesCount,
      serviceReportsCount,
      historyCardsCount,
      salesLeadsCount,
    ] = await Promise.all([
      this.prisma.amc_contracts.count({ where: { customerId: id } }),
      this.prisma.warranties.count({ where: { customerId: id } }),
      this.prisma.service_reports.count({ where: { customerId: id } }),
      this.prisma.history_cards.count({ where: { customerId: id } }),
      this.prisma.sales_leads.count({ where: { customerId: id } }),
    ]);

    return (
      amcContractsCount > 0 ||
      warrantiesCount > 0 ||
      serviceReportsCount > 0 ||
      historyCardsCount > 0 ||
      salesLeadsCount > 0
    );
  }

  /**
   * Find customer by original ID
   * @param originalId Original ID from legacy system
   * @returns Promise resolving to the customer or null if not found
   */
  async findByOriginalId(originalId: number): Promise<Customer | null> {
    return this.model.findFirst({
      where: { originalId },
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<Customer, string, Prisma.CustomerCreateInput, Prisma.CustomerUpdateInput> {
    const repo = new CustomerRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
