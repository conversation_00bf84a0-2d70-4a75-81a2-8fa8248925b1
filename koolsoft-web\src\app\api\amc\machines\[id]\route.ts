import { NextRequest, NextResponse } from 'next/server';
import { getAMCMachineRepository } from '@/lib/repositories';
import { z } from 'zod';

/**
 * AMC Machine update schema
 */
const updateAMCMachineSchema = z.object({
  amcContractId: z.string().uuid().optional(),
  productId: z.string().uuid().optional(),
  modelId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  serialNumber: z.string().optional(),
  location: z.string().optional(),
  installationDate: z.coerce.date().optional(),
  tonnage: z.number().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).optional(),
});

/**
 * GET /api/amc/machines/[id]
 * Get a specific AMC machine by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const amcMachineRepository = getAMCMachineRepository();

    // Get AMC machine with all related data
    const machine = await amcMachineRepository.findWithRelations(id);

    if (!machine) {
      return NextResponse.json(
        { error: 'AMC machine not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(machine);
  } catch (error) {
    console.error('Error fetching AMC machine:', error);
    return NextResponse.json(
      { error: 'Failed to fetch AMC machine' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/amc/machines/[id]
 * Update a specific AMC machine
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // Validate request body
    const validatedData = updateAMCMachineSchema.parse(body);

    const amcMachineRepository = getAMCMachineRepository();

    // Check if AMC machine exists
    const existingMachine = await amcMachineRepository.findById(id);

    if (!existingMachine) {
      return NextResponse.json(
        { error: 'AMC machine not found' },
        { status: 404 }
      );
    }

    // Update AMC machine
    const updatedMachine = await amcMachineRepository.update(id, validatedData);

    return NextResponse.json(updatedMachine);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating AMC machine:', error);
    return NextResponse.json(
      { error: 'Failed to update AMC machine' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/amc/machines/[id]
 * Delete a specific AMC machine
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const amcMachineRepository = getAMCMachineRepository();

    // Check if AMC machine exists
    const existingMachine = await amcMachineRepository.findById(id);

    if (!existingMachine) {
      return NextResponse.json(
        { error: 'AMC machine not found' },
        { status: 404 }
      );
    }

    // Delete AMC machine
    await amcMachineRepository.delete(id);

    return NextResponse.json(
      { message: 'AMC machine deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting AMC machine:', error);
    return NextResponse.json(
      { error: 'Failed to delete AMC machine' },
      { status: 500 }
    );
  }
}
