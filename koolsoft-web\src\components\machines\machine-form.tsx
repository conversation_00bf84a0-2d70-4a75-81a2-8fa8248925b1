'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { machineFormSchema, type MachineFormData } from '@/lib/validations/machine';
import { useBrands, useProducts, useModels } from '@/lib/hooks/useReferenceData';
import { Machine } from '@/lib/hooks/useMachines';

interface MachineFormProps {
  machine?: Machine;
  amcContractId: string;
  onSubmit: (data: MachineFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export function MachineForm({
  machine,
  amcContractId,
  onSubmit,
  onCancel,
  isSubmitting = false,
}: MachineFormProps) {
  const [selectedBrandId, setSelectedBrandId] = useState<string | undefined>(machine?.brandId);
  const [selectedProductId, setSelectedProductId] = useState<string | undefined>(machine?.productId);

  const { brands, isLoading: brandsLoading } = useBrands();
  const { products, isLoading: productsLoading } = useProducts(selectedBrandId);
  const { models, isLoading: modelsLoading } = useModels(selectedProductId);

  const form = useForm<MachineFormData>({
    resolver: zodResolver(machineFormSchema),
    defaultValues: {
      amcContractId,
      productId: machine?.productId || undefined,
      modelId: machine?.modelId || undefined,
      brandId: machine?.brandId || undefined,
      serialNumber: machine?.serialNumber || '',
      location: machine?.location || '',
      installationDate: machine?.installationDate ? new Date(machine.installationDate) : undefined,
      tonnage: machine?.tonnage || undefined,
      status: machine?.status || 'ACTIVE',
      originalAmcId: machine?.originalAmcId || undefined,
      originalAssetNo: machine?.originalAssetNo || undefined,
    },
  });

  // Reset dependent fields when brand changes
  useEffect(() => {
    if (selectedBrandId !== form.getValues('brandId')) {
      form.setValue('productId', undefined);
      form.setValue('modelId', undefined);
      setSelectedProductId(undefined);
    }
  }, [selectedBrandId, form]);

  // Reset model when product changes
  useEffect(() => {
    if (selectedProductId !== form.getValues('productId')) {
      form.setValue('modelId', undefined);
    }
  }, [selectedProductId, form]);

  const handleSubmit = async (data: MachineFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Error submitting machine form:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{machine ? 'Edit Machine' : 'Add New Machine'}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Brand Selection */}
              <FormField
                control={form.control}
                name="brandId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                        setSelectedBrandId(value);
                      }}
                      disabled={brandsLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select brand" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {brands.map((brand) => (
                          <SelectItem key={brand.id} value={brand.id}>
                            {brand.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Product Selection */}
              <FormField
                control={form.control}
                name="productId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Product</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                        setSelectedProductId(value);
                      }}
                      disabled={productsLoading || !selectedBrandId}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {products.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            {product.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Model Selection */}
              <FormField
                control={form.control}
                name="modelId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Model</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={modelsLoading || !selectedProductId}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex flex-col">
                              <span>{model.name}</span>
                              {model.tonnage && (
                                <span className="text-sm text-muted-foreground">
                                  {model.tonnage} TR
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Serial Number */}
              <FormField
                control={form.control}
                name="serialNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Serial Number *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter serial number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location */}
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter installation location" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Installation Date */}
              <FormField
                control={form.control}
                name="installationDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Installation Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tonnage */}
              <FormField
                control={form.control}
                name="tonnage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tonnage (TR)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        placeholder="Enter tonnage"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ACTIVE">Active</SelectItem>
                        <SelectItem value="INACTIVE">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {machine ? 'Update Machine' : 'Add Machine'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
