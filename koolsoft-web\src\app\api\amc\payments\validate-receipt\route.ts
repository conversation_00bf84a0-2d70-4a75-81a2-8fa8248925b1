import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCPaymentRepository } from '@/lib/repositories';

/**
 * GET /api/amc/payments/validate-receipt
 * Validate if a receipt number is unique
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const receiptNo = searchParams.get('receiptNo');
      const excludeId = searchParams.get('excludeId');

      if (!receiptNo) {
        return NextResponse.json(
          { error: 'Receipt number is required' },
          { status: 400 }
        );
      }

      const amcPaymentRepository = getAMCPaymentRepository();
      const isUnique = await amcPaymentRepository.isReceiptNumberUnique(receiptNo, excludeId || undefined);

      return NextResponse.json({ isUnique });
    } catch (error) {
      console.error('Error validating receipt number:', error);
      return NextResponse.json(
        { error: 'Failed to validate receipt number' },
        { status: 500 }
      );
    }
  }
);
