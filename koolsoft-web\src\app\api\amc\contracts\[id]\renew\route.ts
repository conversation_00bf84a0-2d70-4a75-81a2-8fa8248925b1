import { NextRequest, NextResponse } from 'next/server';
import { getAMCContractRepository } from '@/lib/repositories';
import { createAMCContractSchema } from '@/lib/validations/amc-contract.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getEmailService } from '@/lib/services/email.service';
import { z } from 'zod';

/**
 * AMC Contract renewal schema
 */
const renewAMCContractSchema = z.object({
  startDate: z.coerce.date({ message: 'Valid start date is required' }),
  endDate: z.coerce.date({ message: 'Valid end date is required' }),
  amount: z.number({ 
    required_error: 'Amount is required',
    invalid_type_error: 'Amount must be a number'
  }).nonnegative({ message: 'Amount must be a positive number' }),
  warningDate: z.coerce.date().optional(),
  numberOfServices: z.number().int().nonnegative().optional(),
  remarks: z.string().optional(),
  carryForwardMachines: z.boolean().default(true),
  carryForwardDivisions: z.boolean().default(true),
}).refine(
  (data) => {
    // Ensure end date is after start date
    return data.endDate > data.startDate;
  },
  {
    message: 'End date must be after start date',
    path: ['endDate'],
  }
);

/**
 * POST /api/amc/contracts/[id]/renew
 * Renew an AMC contract
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = renewAMCContractSchema.parse(body);

        const amcContractRepository = getAMCContractRepository();

        // Get the original contract with relations
        const originalContract = await amcContractRepository.findWithRelations(id);

        if (!originalContract) {
          return NextResponse.json(
            { error: 'AMC contract not found' },
            { status: 404 }
          );
        }

        // Create new contract data based on original contract
        const newContractData = {
          customerId: originalContract.customerId,
          contactPersonId: originalContract.contactPersonId,
          executiveId: originalContract.executiveId,
          natureOfService: originalContract.natureOfService,
          startDate: validatedData.startDate,
          endDate: validatedData.endDate,
          warningDate: validatedData.warningDate,
          amount: validatedData.amount,
          numberOfServices: validatedData.numberOfServices || originalContract.numberOfServices,
          numberOfMachines: originalContract.numberOfMachines,
          totalTonnage: originalContract.totalTonnage,
          status: 'ACTIVE',
          renewalFlag: true,
          previousAmount: originalContract.amount,
          remarks: validatedData.remarks || `Renewed from contract ${originalContract.contractNumber || originalContract.id}`,
          originalId: originalContract.originalId,
        };

        // Prepare related data
        let machines = [];
        let divisions = [];

        // Carry forward machines if requested
        if (validatedData.carryForwardMachines && originalContract.machines) {
          machines = originalContract.machines.map((machine: any) => {
            // Create a new machine object without the id
            const { id, createdAt, updatedAt, ...machineData } = machine;
            return machineData;
          });
        }

        // Carry forward divisions if requested
        if (validatedData.carryForwardDivisions && originalContract.divisions) {
          divisions = originalContract.divisions.map((division: any) => {
            // Create a new division object without the id
            const { id, createdAt, updatedAt, ...divisionData } = division;
            return divisionData;
          });
        }

        // Create the renewed contract with related data
        const renewedContract = await amcContractRepository.createWithRelations({
          ...newContractData,
          machines,
          divisions,
        });

        // Update the original contract to mark it as renewed
        await amcContractRepository.update(id, {
          status: 'RENEWED',
        });

        // Send email notification to customer
        try {
          if (originalContract.customer?.email) {
            const emailService = getEmailService();
            await emailService.sendTemplateEmail(
              'amc_renewal',
              {
                name: originalContract.customer.name || 'Valued Customer',
                contractNumber: renewedContract.contractNumber || renewedContract.id,
                product: originalContract.natureOfService || 'AMC Services',
                expiryDate: new Date(validatedData.endDate).toLocaleDateString(),
                startDate: new Date(validatedData.startDate).toLocaleDateString(),
                amount: validatedData.amount.toLocaleString(),
              },
              originalContract.customer.email,
              undefined,
              undefined,
              originalContract.customerId
            );
          }
        } catch (emailError) {
          console.error('Error sending renewal notification email:', emailError);
          // Don't fail the renewal if email fails
        }

        return NextResponse.json(renewedContract, { status: 201 });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error renewing AMC contract:', error);
      return NextResponse.json(
        { error: 'Failed to renew AMC contract' },
        { status: 500 }
      );
    }
  }
);
