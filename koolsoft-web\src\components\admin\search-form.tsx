'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, X } from 'lucide-react';

// Define the search form schema
const searchFormSchema = z.object({
  search: z.string().optional(),
  role: z.string().default('all'),
  activeOnly: z.boolean().default(false),
});

// Explicitly define the interface to match the schema
interface SearchFormValues {
  search?: string;
  role: string;
  activeOnly: boolean;
}

interface SearchFormProps {
  onSearch: (values: SearchFormValues) => void;
}

export function SearchForm({ onSearch }: SearchFormProps) {
  const [expanded, setExpanded] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<SearchFormValues>({
    resolver: zodResolver(searchFormSchema) as any, // Type assertion to fix TypeScript errors
    defaultValues: {
      search: '',
      role: 'all',
      activeOnly: false,
    },
  });

  // Handle form submission
  const onSubmit = (values: SearchFormValues) => {
    onSearch(values);
  };

  // Handle form reset
  const handleReset = () => {
    form.reset({
      search: '',
      role: 'all',
      activeOnly: false,
    });
    onSearch({
      search: '',
      role: 'all',
      activeOnly: false,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit as any)} className="space-y-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <FormField
            control={form.control as any}
            name="search"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-text-secondary" />
                    <Input
                      placeholder="Search users by name or email..."
                      className="pl-9"
                      {...field}
                    />
                    {field.value && (
                      <button
                        type="button"
                        onClick={() => {
                          field.onChange('');
                          form.handleSubmit(onSubmit as any)();
                        }}
                        className="absolute right-2.5 top-2.5 text-text-secondary hover:text-text-primary"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <Button
            type="button"
            variant="outline"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? 'Hide Filters' : 'Show Filters'}
          </Button>

          <Button type="submit">Search</Button>

          <Button
            type="button"
            variant="outline"
            onClick={handleReset}
          >
            Reset
          </Button>
        </div>

        {expanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-md bg-muted-background">
            <FormField
              control={form.control as any}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      <SelectItem value="ADMIN">Admin</SelectItem>
                      <SelectItem value="MANAGER">Manager</SelectItem>
                      <SelectItem value="EXECUTIVE">Executive</SelectItem>
                      <SelectItem value="USER">User</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control as any}
              name="activeOnly"
              render={({ field }) => (
                <FormItem className="flex flex-row items-end space-x-2">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="cursor-pointer">
                    Show active users only
                  </FormLabel>
                </FormItem>
              )}
            />
          </div>
        )}
      </form>
    </Form>
  );
}
