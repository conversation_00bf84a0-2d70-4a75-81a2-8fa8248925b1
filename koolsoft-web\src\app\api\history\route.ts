import { NextRequest, NextResponse } from 'next/server';
import { getHistoryDetailRepository } from '@/lib/repositories';
import { z } from 'zod';

/**
 * History detail creation schema
 */
const createHistoryDetailSchema = z.object({
  customerId: z.string().uuid(),
  userId: z.string().uuid(),
  date: z.coerce.date(),
  type: z.string(),
  description: z.string(),
  notes: z.string().optional(),
  followUpDate: z.coerce.date().optional(),
  status: z.enum(['PENDING', 'COMPLETED', 'CANCELLED']).default('PENDING'),
  originalId: z.number().int().optional(),
});

/**
 * GET /api/history
 * Get all history details with optional pagination and filtering
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');
    const customerId = searchParams.get('customerId');
    const userId = searchParams.get('userId');
    const type = searchParams.get('type');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    const historyDetailRepository = getHistoryDetailRepository();
    
    let historyDetails = [];
    let total = 0;
    
    if (customerId) {
      // Get history details for a specific customer
      historyDetails = await historyDetailRepository.findByCustomerId(customerId, skip, take);
      total = await historyDetailRepository.count({ customerId });
    } else if (userId) {
      // Get history details for a specific user
      historyDetails = await historyDetailRepository.findByUserId(userId, skip, take);
      total = await historyDetailRepository.count({ userId });
    } else if (type) {
      // Get history details by type
      historyDetails = await historyDetailRepository.findByType(type, skip, take);
      total = await historyDetailRepository.count({ type });
    } else if (startDate && endDate) {
      // Get history details by date range
      const start = new Date(startDate);
      const end = new Date(endDate);
      historyDetails = await historyDetailRepository.findByDateRange(start, end, skip, take);
      total = await historyDetailRepository.count({
        date: {
          gte: start,
          lte: end,
        },
      });
    } else {
      // Get all history details
      historyDetails = await historyDetailRepository.findAll(skip, take);
      total = await historyDetailRepository.count();
    }
    
    return NextResponse.json({
      historyDetails,
      meta: {
        total,
        skip,
        take,
      },
    });
  } catch (error) {
    console.error('Error fetching history details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch history details' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/history
 * Create a new history detail
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = createHistoryDetailSchema.parse(body);
    
    const historyDetailRepository = getHistoryDetailRepository();
    
    // Create history detail
    const historyDetail = await historyDetailRepository.create(validatedData);
    
    return NextResponse.json(historyDetail, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    console.error('Error creating history detail:', error);
    return NextResponse.json(
      { error: 'Failed to create history detail' },
      { status: 500 }
    );
  }
}
