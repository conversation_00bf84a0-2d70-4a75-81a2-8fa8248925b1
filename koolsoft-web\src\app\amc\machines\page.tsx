'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Plus, 
  Settings, 
  Eye, 
  Edit, 
  Trash2,
  Filter,
  Download
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { showErrorToast, showSuccessToast } from '@/lib/toast';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react';

interface Machine {
  id: string;
  serialNumber?: string;
  location?: string;
  installationDate?: string;
  status?: string;
  amcContract?: {
    id: string;
    contractNumber?: string;
    customer?: {
      name: string;
    };
  };
  product?: {
    name: string;
  };
  model?: {
    name: string;
  };
  brand?: {
    name: string;
  };
  createdAt: string;
}

/**
 * Machines List Page
 * 
 * This page displays a list of all machines with filtering and management capabilities.
 */
export default function MachinesPage() {
  const [machines, setMachines] = useState<Machine[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredMachines, setFilteredMachines] = useState<Machine[]>([]);

  // Fetch machines
  useEffect(() => {
    const fetchMachines = async () => {
      try {
        setIsLoading(true);

        const response = await fetch('/api/amc/machines', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch machines');
        }

        const data = await response.json();
        setMachines(data.machines || data || []);
      } catch (error) {
        console.error('Error fetching machines:', error);
        showErrorToast('Error', 'Failed to fetch machines');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMachines();
  }, []);

  // Filter machines based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredMachines(machines);
    } else {
      const filtered = machines.filter(machine =>
        machine.serialNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        machine.location?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        machine.brand?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        machine.model?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        machine.amcContract?.customer?.name?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredMachines(filtered);
    }
  }, [machines, searchQuery]);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'INACTIVE':
        return 'bg-red-100 text-red-800';
      case 'MAINTENANCE':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle machine deletion
  const handleDeleteMachine = async (machineId: string) => {
    if (!confirm('Are you sure you want to delete this machine?')) {
      return;
    }

    try {
      const response = await fetch(`/api/amc/machines/${machineId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete machine');
      }

      setMachines(machines.filter(machine => machine.id !== machineId));
      showSuccessToast('Success', 'Machine deleted successfully');
    } catch (error) {
      console.error('Error deleting machine:', error);
      showErrorToast('Error', 'Failed to delete machine');
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Machine Management ({filteredMachines.length})</span>
          </CardTitle>
          <div className="flex space-x-2">
            <Button variant="secondary" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button asChild variant="secondary" size="sm">
              <Link href="/amc/machines/new">
                <Plus className="h-4 w-4 mr-2" />
                Add Machine
              </Link>
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search machines by serial number, location, brand, model, or customer..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Machines Table */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-6 space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-32" />
                </div>
              ))}
            </div>
          ) : filteredMachines.length === 0 ? (
            <div className="text-center py-12">
              <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-black text-lg font-medium">No machines found</p>
              <p className="text-black text-sm mt-1">
                {searchQuery ? 'Try adjusting your search criteria' : 'Get started by adding your first machine'}
              </p>
              {!searchQuery && (
                <Button asChild className="mt-4">
                  <Link href="/amc/machines/new">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Machine
                  </Link>
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-black">Serial Number</TableHead>
                  <TableHead className="text-black">Brand/Model</TableHead>
                  <TableHead className="text-black">Location</TableHead>
                  <TableHead className="text-black">Customer</TableHead>
                  <TableHead className="text-black">Status</TableHead>
                  <TableHead className="text-black">Installation Date</TableHead>
                  <TableHead className="text-black">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMachines.map((machine) => (
                  <TableRow key={machine.id}>
                    <TableCell className="text-black font-medium">
                      {machine.serialNumber || 'N/A'}
                    </TableCell>
                    <TableCell className="text-black">
                      {machine.brand?.name || 'N/A'} / {machine.model?.name || 'N/A'}
                    </TableCell>
                    <TableCell className="text-black">
                      {machine.location || 'Not specified'}
                    </TableCell>
                    <TableCell className="text-black">
                      {machine.amcContract?.customer?.name || 'No contract'}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(machine.status || 'UNKNOWN')}>
                        {machine.status || 'UNKNOWN'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-black">
                      {machine.installationDate 
                        ? format(new Date(machine.installationDate), 'MMM dd, yyyy')
                        : 'N/A'
                      }
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/amc/machines/${machine.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/amc/machines/${machine.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteMachine(machine.id)}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
