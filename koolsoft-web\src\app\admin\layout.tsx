'use client';

import { AdminLayout as AdminLayoutComponent } from '@/components/layout';
import { AdminGate } from '@/components/auth/role-gate';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { User, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/hooks/useAuth';

/**
 * Admin Layout Component
 *
 * This component provides a consistent layout for all admin pages
 * by using the standardized AdminLayout component.
 */
export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { logout } = useAuth();

  // Get the title based on the current path
  const getTitle = () => {
    if (pathname === '/admin') {
      return 'Admin Dashboard';
    }
    return pathname.split('/').pop()?.split('-').map(
      word => word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ') || 'Admin';
  };

  // Profile actions for admin layout
  const profileActions = (
    <div className="flex items-center space-x-2">
      <Button asChild variant="outline" size="sm">
        <Link href="/profile">
          <User className="h-4 w-4 mr-1" />
          My Profile
        </Link>
      </Button>
      <Button variant="destructive" size="sm" asChild>
        <Link href="/api/auth/signout">
          <LogOut className="h-4 w-4 mr-1" />
          Sign Out
        </Link>
      </Button>
    </div>
  );

  return (
    <AdminGate fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
          <p className="text-gray-500">You do not have permission to access this page</p>
          <div className="mt-4">
            <Link href="/dashboard" className="text-blue-600 hover:text-blue-500">
              Return to Dashboard
            </Link>
          </div>
        </div>
      </div>
    }>
      <AdminLayoutComponent
        title={getTitle()}
        actions={profileActions}
      >
        {children}
      </AdminLayoutComponent>
    </AdminGate>
  );
}
