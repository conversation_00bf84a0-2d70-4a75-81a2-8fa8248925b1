import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Mail, Phone, User } from 'lucide-react';

interface Contact {
  id: string;
  name: string;
  designation: string;
  phone: string;
  email: string;
  isPrimary: boolean;
}

interface CustomerContactsProps {
  contacts: Contact[];
}

/**
 * Customer Contacts Component
 *
 * This component displays the contacts associated with a customer.
 */
export function CustomerContacts({ contacts }: CustomerContactsProps) {
  // Deduplicate contacts by name and phone
  const uniqueContacts = contacts.reduce((acc, current) => {
    // Check if we already have a contact with the same name and phone
    const x = acc.find(item =>
      item.name === current.name &&
      item.phone === current.phone
    );

    // If not found, add it to the accumulator
    if (!x) {
      return acc.concat([current]);
    } else {
      // If found but the current one is primary, replace the existing one
      if (current.isPrimary && !x.isPrimary) {
        return acc.map(item =>
          (item.name === current.name && item.phone === current.phone)
            ? current
            : item
        );
      }
      return acc;
    }
  }, [] as Contact[]);

  // Sort contacts with primary contacts first
  const sortedContacts = [...uniqueContacts].sort((a, b) => {
    if (a.isPrimary && !b.isPrimary) return -1;
    if (!a.isPrimary && b.isPrimary) return 1;
    return a.name.localeCompare(b.name);
  });

  if (!sortedContacts || sortedContacts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Contacts</CardTitle>
          <CardDescription>
            Contact persons associated with this customer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-gray-500">
            No contacts found for this customer.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Contacts</CardTitle>
        <CardDescription>
          Contact persons associated with this customer
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Designation</TableHead>
              <TableHead>Contact Information</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedContacts.map((contact) => (
              <TableRow key={contact.id}>
                <TableCell className="font-medium">{contact.name}</TableCell>
                <TableCell>{contact.designation || '-'}</TableCell>
                <TableCell>
                  <div className="space-y-1">
                    {contact.email && (
                      <div className="flex items-center text-sm">
                        <Mail className="h-4 w-4 mr-2 text-gray-500" />
                        <span>{contact.email}</span>
                      </div>
                    )}
                    {contact.phone && (
                      <div className="flex items-center text-sm">
                        <Phone className="h-4 w-4 mr-2 text-gray-500" />
                        <span>{contact.phone}</span>
                      </div>
                    )}
                    {!contact.email && !contact.phone && (
                      <span className="text-gray-500">No contact information</span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {contact.isPrimary ? (
                    <Badge className="bg-blue-500 text-white">Primary Contact</Badge>
                  ) : (
                    <Badge variant="outline">Secondary Contact</Badge>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
