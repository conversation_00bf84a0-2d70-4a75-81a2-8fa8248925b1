import { NextRequest, NextResponse } from 'next/server';
import { getAMCServiceDateRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';

/**
 * Complete service date schema
 */
const completeServiceDateSchema = z.object({
  completedDate: z.coerce.date().optional(),
  remarks: z.string().optional(),
  technicianId: z.string().uuid().optional(),
});

/**
 * POST /api/amc/service-dates/[id]/complete
 * Mark a service date as completed
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = completeServiceDateSchema.parse(body);

      const amcServiceDateRepository = getAMCServiceDateRepository();

      // Check if service date exists
      const existingServiceDate = await amcServiceDateRepository.findById(id);
      if (!existingServiceDate) {
        return NextResponse.json(
          { error: 'Service date not found' },
          { status: 404 }
        );
      }

      // Check if already completed
      if (existingServiceDate.completedDate) {
        return NextResponse.json(
          { error: 'Service date is already completed' },
          { status: 400 }
        );
      }

      // Mark as completed
      const completedDate = validatedData.completedDate || new Date();
      const updatedServiceDate = await amcServiceDateRepository.markCompleted(
        id,
        completedDate,
        'C' // C for Completed
      );

      return NextResponse.json({
        message: 'Service date marked as completed successfully',
        serviceDate: updatedServiceDate,
      });
    } catch (error) {
      console.error('Error completing service date:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to complete service date' },
        { status: 500 }
      );
    }
  }
);
