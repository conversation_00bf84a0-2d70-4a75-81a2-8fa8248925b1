import { NextRequest, NextResponse } from 'next/server';
import { getActivityLogService } from '@/lib/services/activity-log.service';
import { withActivityLogging } from '@/lib/middleware/activity-logger.middleware';
import { withAdminProtection } from '@/lib/auth/role-check';

/**
 * GET /api/admin/activity-logs
 *
 * Fetch activity logs with filtering and pagination
 *
 * Query parameters:
 * - skip: Number of records to skip
 * - take: Maximum number of records to return
 * - userId: Filter by user ID
 * - action: Filter by action
 * - entityType: Filter by entity type
 * - entityId: Filter by entity ID
 * - startDate: Filter by start date
 * - endDate: Filter by end date
 * - search: Search term
 */
async function getActivityLogs(req: NextRequest) {

  try {
    // Parse query parameters
    const url = new URL(req.url);
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const take = parseInt(url.searchParams.get('take') || '20');

    // Parse filter parameters
    const userId = url.searchParams.get('userId') === 'all' ? undefined : url.searchParams.get('userId') || undefined;
    const action = url.searchParams.get('action') === 'all' ? undefined : url.searchParams.get('action') || undefined;
    const entityType = url.searchParams.get('entityType') === 'all' ? undefined : url.searchParams.get('entityType') || undefined;
    const entityId = url.searchParams.get('entityId') || undefined;
    const search = url.searchParams.get('search') || undefined;

    // Parse date parameters
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (url.searchParams.get('startDate')) {
      startDate = new Date(url.searchParams.get('startDate') as string);
    }

    if (url.searchParams.get('endDate')) {
      endDate = new Date(url.searchParams.get('endDate') as string);
      // Set end date to end of day
      endDate.setHours(23, 59, 59, 999);
    }

    // Get activity log service
    const activityLogService = getActivityLogService();

    // Fetch logs with filters
    const { logs, total } = await activityLogService.findLogs(
      {
        userId,
        action,
        entityType,
        entityId,
        startDate,
        endDate,
        search,
      },
      skip,
      take
    );

    // Return logs with pagination metadata
    return NextResponse.json({
      logs,
      meta: {
        skip,
        take,
        total,
      },
    });
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch activity logs' },
      { status: 500 }
    );
  }
}

// Wrap the handler with admin protection and activity logging middleware
export const GET = withAdminProtection(
  withActivityLogging(getActivityLogs, {
    action: 'view_activity_logs',
    entityType: 'activity_log',
  })
);
