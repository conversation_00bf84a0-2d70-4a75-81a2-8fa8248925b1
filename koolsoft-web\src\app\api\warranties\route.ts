import { NextRequest, NextResponse } from 'next/server';
import { getWarrantyRepository } from '@/lib/repositories';
import { createWarrantySchema, warrantyFilterSchema } from '@/lib/validations/warranty.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { z } from 'zod';

/**
 * GET /api/warranties
 * Get warranties with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const queryParams = {
        customerId: searchParams.get('customerId') || undefined,
        executiveId: searchParams.get('executiveId') || undefined,
        status: searchParams.get('status') || undefined,
        bslNo: searchParams.get('bslNo') || undefined,
        serialNumber: searchParams.get('serialNumber') || undefined,
        installDateFrom: searchParams.get('installDateFrom') ? new Date(searchParams.get('installDateFrom')!) : undefined,
        installDateTo: searchParams.get('installDateTo') ? new Date(searchParams.get('installDateTo')!) : undefined,
        warrantyDateFrom: searchParams.get('warrantyDateFrom') ? new Date(searchParams.get('warrantyDateFrom')!) : undefined,
        warrantyDateTo: searchParams.get('warrantyDateTo') ? new Date(searchParams.get('warrantyDateTo')!) : undefined,
        search: searchParams.get('search') || undefined,
        skip: parseInt(searchParams.get('skip') || '0'),
        take: parseInt(searchParams.get('take') || '10'),
        sortBy: searchParams.get('sortBy') || 'installDate',
        sortOrder: searchParams.get('sortOrder') || 'desc',
      };

      try {
        const validatedParams = warrantyFilterSchema.parse(queryParams);
        
        const warrantyRepository = getWarrantyRepository();
        
        // Build filter object
        const filter: Record<string, any> = {};
        
        if (validatedParams.customerId) {
          filter.customerId = validatedParams.customerId;
        }
        
        if (validatedParams.executiveId) {
          filter.executiveId = validatedParams.executiveId;
        }
        
        if (validatedParams.status) {
          filter.status = validatedParams.status;
        }
        
        if (validatedParams.bslNo) {
          filter.bslNo = {
            contains: validatedParams.bslNo,
            mode: 'insensitive',
          };
        }
        
        if (validatedParams.installDateFrom || validatedParams.installDateTo) {
          filter.installDate = {};
          if (validatedParams.installDateFrom) {
            filter.installDate.gte = validatedParams.installDateFrom;
          }
          if (validatedParams.installDateTo) {
            filter.installDate.lte = validatedParams.installDateTo;
          }
        }
        
        if (validatedParams.warrantyDateFrom || validatedParams.warrantyDateTo) {
          filter.warrantyDate = {};
          if (validatedParams.warrantyDateFrom) {
            filter.warrantyDate.gte = validatedParams.warrantyDateFrom;
          }
          if (validatedParams.warrantyDateTo) {
            filter.warrantyDate.lte = validatedParams.warrantyDateTo;
          }
        }
        
        if (validatedParams.search) {
          filter.OR = [
            {
              bslNo: {
                contains: validatedParams.search,
                mode: 'insensitive',
              },
            },
            {
              customer: {
                name: {
                  contains: validatedParams.search,
                  mode: 'insensitive',
                },
              },
            },
            {
              machines: {
                some: {
                  serialNumber: {
                    contains: validatedParams.search,
                    mode: 'insensitive',
                  },
                },
              },
            },
          ];
        }
        
        // Build order by object
        const orderBy: Record<string, any> = {};
        if (validatedParams.sortBy.includes('.')) {
          const [relation, field] = validatedParams.sortBy.split('.');
          orderBy[relation] = { [field]: validatedParams.sortOrder };
        } else {
          orderBy[validatedParams.sortBy] = validatedParams.sortOrder;
        }
        
        // Get warranties with filter
        const warranties = await warrantyRepository.findWithFilter(
          filter,
          validatedParams.skip,
          validatedParams.take,
          orderBy
        );
        
        // Get total count for pagination
        const totalCount = await warrantyRepository.countWithFilter(filter);
        
        return NextResponse.json({
          warranties,
          pagination: {
            skip: validatedParams.skip,
            take: validatedParams.take,
            total: totalCount,
            hasMore: validatedParams.skip + validatedParams.take < totalCount,
          },
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Invalid query parameters', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error fetching warranties:', error);
      return NextResponse.json(
        { error: 'Failed to fetch warranties' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/warranties
 * Create a new warranty
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = createWarrantySchema.parse(body);

        const warrantyRepository = getWarrantyRepository();

        // Create warranty with related data
        const warranty = await warrantyRepository.createWithRelations(validatedData);

        return NextResponse.json(warranty, { status: 201 });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error creating warranty:', error);
      
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          return NextResponse.json(
            { error: 'A warranty with this information already exists' },
            { status: 409 }
          );
        }
        if (error.code === 'P2003') {
          return NextResponse.json(
            { error: 'Referenced customer, executive, or contact person does not exist' },
            { status: 400 }
          );
        }
      }
      
      return NextResponse.json(
        { error: 'Failed to create warranty' },
        { status: 500 }
      );
    }
  }
);
