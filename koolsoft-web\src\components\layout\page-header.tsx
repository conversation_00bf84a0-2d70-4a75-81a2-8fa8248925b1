'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Home, LayoutDashboard, Settings, User, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { useAuth } from '@/lib/hooks/useAuth';

export interface BreadcrumbItemType {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  current?: boolean;
}

interface PageHeaderProps {
  title: string;
  breadcrumbs?: BreadcrumbItemType[];
  actions?: React.ReactNode;
  showDashboardLink?: boolean;
  showAdminLink?: boolean;
}

/**
 * PageHeader Component
 *
 * A standardized header component for all pages in the application.
 * Includes breadcrumb navigation, page title, and action buttons.
 */
export function PageHeader({
  title,
  breadcrumbs,
  actions,
  showDashboardLink = true,
  showAdminLink = false,
}: PageHeaderProps) {
  const { user, isAdmin, logout } = useAuth();
  const pathname = usePathname();
  const router = useRouter();

  // Default breadcrumbs if none provided
  const defaultBreadcrumbs = breadcrumbs || [
    { label: 'Dashboard', href: '/dashboard', icon: <Home className="h-4 w-4" /> },
    { label: title, current: true }
  ];

  return (
    <header className="bg-white shadow">
      <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col space-y-3 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
          <div>
            {breadcrumbs && (
              <Breadcrumb>
                <BreadcrumbList>
                  {defaultBreadcrumbs.map((item, index) => (
                    <React.Fragment key={index}>
                      {index > 0 && <BreadcrumbSeparator />}
                      <BreadcrumbItem className="breadcrumb-item">
                        {item.current ? (
                          <span className="text-gray-900 font-medium flex items-center">
                            {item.icon && <span className="mr-2">{item.icon}</span>}
                            {item.label}
                          </span>
                        ) : (
                          <BreadcrumbLink href={item.href || '#'} className="flex items-center">
                            {item.icon && <span className="mr-2">{item.icon}</span>}
                            {item.label}
                          </BreadcrumbLink>
                        )}
                      </BreadcrumbItem>
                    </React.Fragment>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>
            )}
            {/* Only show title if it's not the same as the last breadcrumb item */}
            {(!breadcrumbs ||
              (defaultBreadcrumbs.length > 0 &&
               defaultBreadcrumbs[defaultBreadcrumbs.length - 1].label !== title)) && (
              <h1 className="text-xl font-bold text-gray-900 mt-1" id="page-title">{title}</h1>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {user && <span className="text-gray-700">Welcome, {user.name}</span>}

            <div className="flex space-x-2">
              {/* Always use provided actions */}
              {actions}

              {showDashboardLink && pathname !== '/dashboard' && (
                <Button asChild variant="default" size="sm">
                  <Link href="/dashboard">
                    <LayoutDashboard className="h-4 w-4 mr-1" />
                    Dashboard
                  </Link>
                </Button>
              )}

              {showAdminLink && isAdmin() && pathname !== '/admin' && (
                <Button asChild variant="default" size="sm">
                  <Link href="/admin">
                    <Settings className="h-4 w-4 mr-1" />
                    Admin
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
