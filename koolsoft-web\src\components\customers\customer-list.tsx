'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { showSuccessToast, showErrorToast } from '@/lib/toast';
import {
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  RefreshCw,
  Check,
  X,
  Edit,
  Eye,
  Trash,
  Mail,
  Phone
} from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialog<PERSON>itle } from '@/components/ui/alert-dialog';

// Define the customer type
interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  mobile?: string;
  city?: string;
  state?: string;
  location?: string;  // Added location field
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Define the pagination type
interface Pagination {
  skip: number;
  take: number;
  total: number;
}

// Define the component props
interface CustomerListProps {
  customers: Customer[];
  isLoading: boolean;
  pagination: Pagination;
  onPaginationChange: (skip: number, take: number) => void;
  onRefresh: () => void;
  onDelete: (id: string) => Promise<boolean>;
}

/**
 * Customer List Component
 *
 * This component displays a list of customers with pagination and actions.
 */
export function CustomerList({
  customers,
  isLoading,
  pagination,
  onPaginationChange,
  onRefresh,
  onDelete
}: CustomerListProps) {
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [deleteCustomerId, setDeleteCustomerId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Calculate pagination values
  const { skip, take, total } = pagination;
  const currentPage = Math.floor(skip / take) + 1;
  const totalPages = Math.ceil(total / take);
  const showingFrom = total === 0 ? 0 : skip + 1;
  const showingTo = Math.min(skip + take, total);

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort indicator
  const getSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };

  // Handle delete customer
  const handleDeleteCustomer = async () => {
    if (!deleteCustomerId) return;

    setIsDeleting(true);
    try {
      const success = await onDelete(deleteCustomerId);
      if (success) {
        showSuccessToast('Success', 'Customer deleted successfully');
      }
    } finally {
      setIsDeleting(false);
      setDeleteCustomerId(null);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Showing {showingFrom} to {showingTo} of {total} customers
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('name')}
              >
                Name {getSortIndicator('name')}
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('email')}
              >
                Contact Info {getSortIndicator('email')}
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('location')}
              >
                Location {getSortIndicator('location')}
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('isActive')}
              >
                Status {getSortIndicator('isActive')}
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <div className="flex justify-center items-center">
                    <RefreshCw className="h-6 w-6 animate-spin text-primary mr-2" />
                    <span>Loading customers...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : customers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  No customers found. Try adjusting your filters.
                </TableCell>
              </TableRow>
            ) : (
              customers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell className="font-medium">{customer.name}</TableCell>
                  <TableCell>
                    <div className="flex flex-col space-y-1">
                      {customer.email && (
                        <div className="flex items-center text-sm">
                          <Mail className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{customer.email}</span>
                        </div>
                      )}
                      {customer.phone && (
                        <div className="flex items-center text-sm">
                          <Phone className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{customer.phone}</span>
                        </div>
                      )}
                      {!customer.email && !customer.phone && (
                        <span className="text-gray-500 text-sm">No contact info</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {customer.city && customer.state ? (
                      `${customer.city}, ${customer.state}`
                    ) : customer.city ? (
                      customer.city
                    ) : customer.state ? (
                      customer.state
                    ) : customer.location ? (
                      customer.location
                    ) : (
                      <span className="text-gray-500 text-sm">No location info</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {customer.isActive ? (
                      <Badge variant="default" className="bg-green-500">
                        <Check className="h-3 w-3 mr-1" /> Active
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-gray-500">
                        <X className="h-3 w-3 mr-1" /> Inactive
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem asChild>
                          <Link href={`/customers/${customer.id}`}>
                            <Eye className="h-4 w-4 mr-2" /> View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/customers/${customer.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" /> Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => setDeleteCustomerId(customer.id)}
                        >
                          <Trash className="h-4 w-4 mr-2" /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="flex-1 text-sm text-gray-500">
          Page {currentPage} of {totalPages || 1}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPaginationChange(Math.max(0, skip - take), take)}
            disabled={currentPage <= 1 || isLoading}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPaginationChange(skip + take, take)}
            disabled={currentPage >= totalPages || isLoading}
          >
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteCustomerId} onOpenChange={(open) => !open && setDeleteCustomerId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the customer and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteCustomer}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
